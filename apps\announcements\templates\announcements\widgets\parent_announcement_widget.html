{% load static humanize i18n %}

<!-- Parent Announcements Widget -->
<div class="card h-100 shadow-sm">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="bi bi-bell-fill me-2"></i>
            {% trans "School Announcements" %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if announcements %}
            <div class="list-group list-group-flush">
                {% for announcement in announcements %}
                <div class="list-group-item border-0 {% if announcement.is_sticky %}bg-light border-start border-warning border-3{% endif %}">
                    <div class="d-flex w-100 justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold text-primary">
                                {% if announcement.is_sticky %}
                                    <i class="bi bi-star-fill text-warning me-1" title="{% trans 'Important' %}"></i>
                                {% endif %}
                                {{ announcement.title }}
                            </h6>
                            <p class="mb-2 text-dark">
                                {{ announcement.content|truncatewords:20|linebreaksbr }}
                            </p>
                            <small class="text-muted">
                                <i class="bi bi-calendar3 me-1"></i>
                                {{ announcement.publish_date|date:"M d, Y" }}
                                {% if announcement.expiry_date %}
                                    <span class="mx-1">•</span>
                                    <i class="bi bi-clock me-1"></i>
                                    {% trans "Valid until" %} {{ announcement.expiry_date|date:"M d, Y" }}
                                {% endif %}
                            </small>
                        </div>
                        {% if announcement.is_sticky %}
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="bi bi-star-fill"></i>
                            </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="card-body text-center py-4">
                <div class="text-muted">
                    <i class="bi bi-bell display-4 mb-3 text-muted"></i>
                    <p class="mb-0">{% trans "No announcements from the school at this time." %}</p>
                    <small class="text-muted">{% trans "Check back later for updates from your school." %}</small>
                </div>
            </div>
        {% endif %}
    </div>
    {% if announcements and announcements|length >= 3 %}
        <div class="card-footer text-center bg-light">
            <small class="text-muted">
                <i class="bi bi-info-circle me-1"></i>
                {% trans "Showing recent announcements" %}
            </small>
        </div>
    {% endif %}
</div>

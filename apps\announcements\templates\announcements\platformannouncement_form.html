{# templates/announcements/platformannouncement_form.html #}
{% extends "announcements/_platform_announcement_base.html" %}
{% load widget_tweaks %} {# For rendering form fields if you use it #}

{% block announcement_page_title %}{{ form_mode }} Platform Announcement{% endblock %}

{% block announcement_content %}
<div class="card shadow-sm">
    <div class="card-header">
        <h5>{{ form_mode }} Announcement</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                {{ form.title|add_class:"form-control" }}
                {% if form.title.help_text %}<small class="form-text text-muted">{{ form.title.help_text }}</small>{% endif %}
                {% for error in form.title.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>

            <div class="mb-3">
                <label for="{{ form.content.id_for_label }}" class="form-label">{{ form.content.label }}</label>
                {{ form.content|add_class:"form-control" }} {# Widget already has rows:10 #}
                {% if form.content.help_text %}<small class="form-text text-muted">{{ form.content.help_text }}</small>{% endif %}
                {% for error in form.content.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.publish_date.id_for_label }}" class="form-label">{{ form.publish_date.label }}</label>
                    {{ form.publish_date|attr:"type:datetime-local" }} {# Widget already sets type #}
                    {% if form.publish_date.help_text %}<small class="form-text text-muted">{{ form.publish_date.help_text }}</small>{% endif %}
                    {% for error in form.publish_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                <div class="col-md-6 mb-3">
                    <label for="{{ form.expiry_date.id_for_label }}" class="form-label">{{ form.expiry_date.label }}</label>
                    {{ form.expiry_date|attr:"type:datetime-local" }} {# Widget already sets type #}
                    {% if form.expiry_date.help_text %}<small class="form-text text-muted">{{ form.expiry_date.help_text }}</small>{% endif %}
                    {% for error in form.expiry_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
            </div>
            
            <div class="mb-3 form-check">
                {{ form.is_published|add_class:"form-check-input" }}
                <label for="{{ form.is_published.id_for_label }}" class="form-check-label">{{ form.is_published.label }}</label>
                {% if form.is_published.help_text %}<small class="form-text text-muted d-block">{{ form.is_published.help_text }}</small>{% endif %}
                {% for error in form.is_published.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <hr>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save-fill me-1"></i> Save Announcement
            </button>
            <a href="{% url 'announcements:platform_announcement_list' %}" class="btn btn-outline-secondary">Cancel</a>
        </form>
    </div>
</div>
{% endblock %}
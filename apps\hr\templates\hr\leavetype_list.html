{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{% trans "Manage Leave Types" %}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
{% endblock %}

{# CORRECTED BLOCK NAME to match tenant_base.html #}
{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="pagetitle mb-3">
        <h1>{% trans "Leave Types Management" %}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% trans "Leave Types" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    <div class="d-flex justify-content-end mb-3">
        {% if perms.hr.add_leavetype %}
            <a href="{% url 'hr:leavetype_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> {% trans "Add New Leave Type" %}
            </a>
        {% endif %}
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Configured Leave Types" %}</h6>
        </div>
        <div class="card-body p-0">
            {% with leavetype_list=leave_types|default:object_list %}
            {% if leavetype_list %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Days Allocated" %}</th>
                            <th class="text-center">{% trans "Is Paid" %}</th>
                            <th class="text-center">{% trans "Gender Specific" %}</th>
                            <th>{% trans "Applicable Gender" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lt in leavetype_list %}
                        <tr>
                            <td><strong>{{ lt.name }}</strong></td>
                            <td>{{ lt.days_allocated|default:"N/A" }}</td>
                            <td class="text-center">{% if lt.is_paid %}<i class="bi bi-check-circle-fill text-success" title="Yes"></i>{% else %}<i class="bi bi-x-circle-fill text-danger" title="No"></i>{% endif %}</td>
                            <td class="text-center">{% if lt.is_gender_specific %}<i class="bi bi-check-circle-fill text-success" title="Yes"></i>{% else %}<i class="bi bi-x-circle text-muted" title="No"></i>{% endif %}</td>
                            <td>{{ lt.get_applicable_gender_display|default:"All" }}</td>
                            <td class="text-center">
                                {% if perms.hr.change_leavetype %}
                                <a href="{% url 'hr:leavetype_update' pk=lt.pk %}" class="btn btn-sm btn-outline-primary me-1" title="Edit"><i class="bi bi-pencil-square"></i></a>
                                {% endif %}
                                {% if perms.hr.delete_leavetype %}
                                <a href="{% url 'hr:leavetype_delete' pk=lt.pk %}" class="btn btn-sm btn-outline-danger" title="Delete"><i class="bi bi-trash"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center p-4">
                <p class="text-muted">{% trans "No leave types have been defined yet." %}</p>
                {% if perms.hr.add_leavetype %}
                <a href="{% url 'hr:leavetype_create' %}" class="btn btn-primary btn-sm mt-2">
                    <i class="bi bi-plus-circle me-1"></i> {% trans "Create the first one" %}
                </a>
                {% endif %}
            </div>
            {% endif %}
            {% endwith %}
        </div>
        {% if is_paginated %}
            <div class="card-footer bg-light">
                {% include "partials/_pagination.html" %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}














{% comment %} {% extends "tenant_base.html" %}
{% load static humanize %}
{% block title %}Leave Types{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>Leave Types Management</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Leave Types</li>
            </ol>
        </nav>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if perms.hr.add_leavetype %}
    <div class="mb-3">
        <a href="{% url 'hr:leavetype_create' %}" class="btn btn-success">
            <i class="bi bi-plus-circle me-1"></i> Add New Leave Type
        </a>
    </div>
    {% endif %}

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">Available Leave Types</h5>
            {% if leave_types %}
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Max Days</th>
                            <th>Paid</th>
                            <th>Gender Specific</th>
                            <th>Applicable Gender</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lt in leave_types %}
                        <tr>
                            <td>{{ lt.name }}</td>
                            <td>{{ lt.max_days_allowed|default:"N/A" }}</td>
                            <td>{% if lt.is_paid %}<i class="bi bi-check-circle-fill text-success"></i> Yes{% else %}<i class="bi bi-x-circle-fill text-danger"></i> No{% endif %}</td>
                            <td>{% if lt.is_gender_specific %}<i class="bi bi-check-circle-fill text-success"></i> Yes{% else %}<i class="bi bi-x-circle-fill text-muted"></i> No{% endif %}</td>
                            <td>{{ lt.get_applicable_gender_display|default_if_none:"N/A" }}</td>
                            <td>
                                {% if perms.hr.change_leavetype %}
                                <a href="{% url 'hr:leavetype_update' lt.pk %}" class="btn btn-sm btn-outline-primary me-1"><i class="bi bi-pencil-square"></i> Edit</a>
                                {% endif %}
                                {% if perms.hr.delete_leavetype %}
                                <a href="{% url 'hr:leavetype_delete' lt.pk %}" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i> Delete</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">No leave types defined yet.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}



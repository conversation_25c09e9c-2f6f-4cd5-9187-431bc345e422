{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_confirm_delete.html #}
{% extends "tenant_base.html" %}

{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Deletion") }}{% endblock tenant_page_title %}

{% block tenant_specific_content %}
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm border-danger">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">{{ view_title }}</h4>
                    </div>
                    <div class="card-body">
                        <p class="lead">{% trans "Are you sure you want to delete the following Draft Journal Entry?" %}</p>
                        <hr>
                        <dl class="row">
                            <dt class="col-sm-4">{% trans "JE ID" %}:</dt>
                            <dd class="col-sm-8">JE-{{ journal_entry.pk|stringformat:"05d" }}</dd>

                            <dt class="col-sm-4">{% trans "Date" %}:</dt>
                            <dd class="col-sm-8">{{ journal_entry.date|date:"d M Y" }}</dd>

                            <dt class="col-sm-4">{% trans "Description" %}:</dt>
                            <dd class="col-sm-8">{{ journal_entry.description }}</dd>
                            
                            <dt class="col-sm-4">{% trans "Total Debits" %}:</dt>
                            <dd class="col-sm-8">{{ school_profile.currency_symbol|default:"$" }}{{ journal_entry.total_debits|floatformat:2|intcomma }}</dd>

                            <dt class="col-sm-4">{% trans "Total Credits" %}:</dt>
                            <dd class="col-sm-8">{{ school_profile.currency_symbol|default:"$" }}{{ journal_entry.total_credits|floatformat:2|intcomma }}</dd>
                        </dl>
                        <hr>
                        <p class="text-danger"><i class="bi bi-exclamation-triangle-fill me-1"></i> <strong>{% trans "This action cannot be undone." %}</strong></p>
                        
                        <form method="post">
                            {% csrf_token %}
                            <div class="d-flex justify-content-end">
                                <a href="{% url 'accounting:journalentry_detail' pk=journal_entry.pk %}" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, Delete Draft" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock tenant_specific_content %}



















{% comment %} {# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Delete Journal Entry") }}{% endblock %}

{% block tenant_specific_content %} {# Match block in tenant_base.html #}
<div class="container mt-4">
    {% if breadcrumbs %} {# Optional: Assuming you have a breadcrumbs variable or include #}
        {% include "partials/_breadcrumb.html" with breadcrumbs=breadcrumbs %}
    {% else %}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'accounting:journalentry_list' %}">{% trans "Journal Entries" %}</a></li>
                {% if journal_entry %}
                <li class="breadcrumb-item"><a href="{% url 'accounting:journalentry_detail' pk=journal_entry.pk %}">{{ journal_entry.entry_number|default_if_none:journal_entry.pk|stringformat:"05d" }}</a></li>
                {% endif %}
                <li class="breadcrumb-item active" aria-current="page">{% trans "Confirm Delete" %}</li>
            </ol>
        </nav>
    {% endif %}

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white"> {# Danger theme for deletion #}
                    <h4 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>{{ view_title|default:_("Confirm Deletion") }}</h4>
                </div>
                <div class="card-body">
                    {% if journal_entry %}
                        <p>
                            {% blocktrans with je_number=journal_entry.entry_number|default_if_none:journal_entry.pk|stringformat:"05d" je_date=journal_entry.date|date:"d M Y" je_narration=journal_entry.narration %}
                            Are you sure you want to permanently delete the <strong>DRAFT</strong> Journal Entry: <strong>{{ je_number }}</strong>
                            dated <strong>{{ je_date }}</strong>
                            with narration: "<em>{{ je_narration|truncatewords:25 }}</em>"?
                            {% endblocktrans %}
                        </p>
                        <p class="text-danger fw-bold">
                            {% trans "This action will permanently remove the DRAFT journal entry and cannot be undone." %}
                        </p>
                        <p class="text-muted small">
                            {% trans "Note: This option is typically only available for DRAFT entries. Posted entries should generally be reversed, not deleted, to maintain a clear audit trail." %}
                        </p>

                        <form method="post">
                            {% csrf_token %}
                            <hr>
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{% url 'accounting:journalentry_detail' pk=journal_entry.pk %}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-1"></i> {% trans "No, Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, Delete This Draft" %}
                                </button>
                            </div>
                        </form>
                    {% else %}
                        <div class="alert alert-warning">{% trans "Journal entry details not found or not specified." %}</div>
                        <a href="{% url 'accounting:journalentry_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Back to Journal Entries" %}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

 {% endcomment %}




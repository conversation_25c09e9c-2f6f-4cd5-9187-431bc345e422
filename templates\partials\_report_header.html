{# templates/partials/_report_header.html #}
{% load i18n %}
<div class="pagetitle mb-3">
    <div class="d-flex align-items-center">
        {% if report_icon_class %} {# Pass this from child template or view context #}
            <i class="{{ report_icon_class }} me-3" style="font-size: 2.5rem; color: var(--bs-primary);"></i>
        {% else %}
            <i class="bi bi-file-earmark-text me-3" style="font-size: 2.5rem; color: var(--bs-secondary);"></i> {# Default icon #}
        {% endif %}
        <div>
            <h1>{{ view_title|default:"Report" }}</h1>
            {% if report_tenant_name %}
                <h2 class="h5 text-muted">{{ report_tenant_name }}</h2>
            {% endif %}
        </div>
    </div>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="#">{% trans "Reports" %}</a></li> {# Link to a reports index page #}
            <li class="breadcrumb-item active">{{ view_title|truncatechars:30 }}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->



{# D:\school_fees_saas_v2\apps\fees\templates\fees\form_base.html #}
{% extends "tenant_base.html" %}
{% load static core_tags %}

{% block title %}{{ view_title|default:"Form" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title }}</h4>
                </div>
                <div class="card-body p-4">
                    <h5>{{ form_title|default:"Details" }}</h5>
                    <hr>
                    <form method="post" novalidate>
                        {% csrf_token %}
                        {{ form.as_p }} {# Simple rendering for now #}
                        <div class="mt-4 d-flex justify-content-end">
                            <a href="{{ back_url|default:"#" }}" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-success">
                                {% if object %}Update{% else %}Create{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
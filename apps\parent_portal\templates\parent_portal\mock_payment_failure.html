{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\mock_payment_failure.html #}
{% extends "parent_portal_base.html" %}
{% load static i18n %}

{% block parent_portal_page_title %}{% trans "Payment Failed" %}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-5 text-center">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-danger">
                <div class="card-body p-5">
                    <i class="bi bi-x-octagon-fill text-danger display-1 mb-3"></i>
                    <h2 class="card-title">{% trans "Payment Failed" %}</h2>
                    <p class="lead">
                        {% blocktrans %}Your simulated payment was not successful or was cancelled.{% endblocktrans %}
                    </p>
                    {% if messages %}
                        {% for message in messages %} {# Display specific error messages if any #}
                            <p class="text-danger">{{ message }}</p>
                        {% endfor %}
                    {% endif %}
                    <hr>
                    <p>{% trans "No amount has been charged. If you continue to experience issues, please contact the school." %}</p>
                    <div class="mt-4">
                        <a href="{% url 'parent_portal:select_invoices_for_payment' %}" class="btn btn-warning me-2">
                            <i class="bi bi-arrow-clockwise me-1"></i> {% trans "Try Payment Again" %}
                        </a>
                        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-house-door-fill me-1"></i> {% trans "Back to Dashboard" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



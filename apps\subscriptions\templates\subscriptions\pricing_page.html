{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\pricing_page.html #}
{% extends "public_base.html" %}
{% load static i18n humanize %} 
{# Removed math_filters as calculations are now in the view #}

{% block public_page_title %}{{ view_title|default:_("Plans & Pricing") }}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .plan-card {
            border: 1px solid #dee2e6; /* Slightly softer border */
            border-radius: 0.75rem; /* More rounded */
            transition: all 0.3s ease-in-out;
            height: 100%; 
            display: flex; /* For footer alignment */
            flex-direction: column; /* For footer alignment */
        }
        .plan-card:hover {
            box-shadow: 0 0.75rem 1.5rem rgba(0,0,0,.1)!important; /* Softer hover shadow */
            transform: translateY(-5px);
        }
        .plan-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: calc(0.75rem - 1px); /* Match card rounding */
            border-top-right-radius: calc(0.75rem - 1px);
        }
        .plan-card .price {
            font-size: 2.8rem; /* Slightly larger */
            font-weight: 700; /* Bolder */
            color: var(--bs-primary); /* Use Bootstrap primary color */
        }
        .plan-card .price-cycle {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 400;
        }
        .plan-card ul.features {
            list-style: none;
            padding-left: 0;
            margin-bottom: 1.5rem; /* More space before button */
        }
        .plan-card ul.features li {
            padding: 0.6rem 0;
            border-bottom: 1px solid #f1f1f1; /* Lighter separator */
            font-size: 0.95rem;
        }
        .plan-card ul.features li:last-child {
            border-bottom: none;
        }
        .plan-card .btn-select-plan {
            font-weight: 500; /* Standard button weight */
            width: 100%; /* Make buttons full width of their column in btn-group */
        }
        .current-plan-highlight {
            border: 3px solid var(--bs-success) !important; /* Green for current plan */
            box-shadow: 0 0.5rem 1rem rgba(25,135,84,.35)!important; /* Greenish shadow */
        }
        .plan-card .card-body {
            display: flex;
            flex-direction: column;
            flex-grow: 1; /* Ensure card body takes available space */
        }
        .plan-card .card-footer-action { /* New class for the button div */
            margin-top: auto; /* Pushes button to bottom */
            padding-top: 1rem; /* Space above button */
        }
        .plan-pricing-options {
            min-height: 40px; /* Reserve space for annual price line */
        }
        .btn-group .btn { /* Ensure buttons in group don't have individual rounded corners */
            border-radius: 0;
        }
        .btn-group .btn:first-child {
            border-top-left-radius: .375rem;
            border-bottom-left-radius: .375rem;
        }
        .btn-group .btn:last-child {
            border-top-right-radius: .375rem;
            border-bottom-right-radius: .375rem;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">{{ view_title|default:_("Choose Your Perfect Plan") }}</h1>
        <p class="lead text-muted">{% trans "Simple, transparent pricing for schools of all sizes. Get started today!" %}</p>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if plans_data %} {# Iterate through plans_data from the view #}
    <div class="row justify-content-center g-4">
        {% for plan_item in plans_data %}
            {% with plan=plan_item.instance %} {# Get the actual plan instance #}
            <div class="col-lg-4 col-md-6 d-flex"> {# Added d-flex for equal height cards in Bootstrap 5 #}
                <div class="card plan-card text-center shadow-sm {% if current_tenant_subscription_plan_pk == plan.pk %}current-plan-highlight{% endif %}">
                    <div class="card-header py-3">
                        <h4 class="my-0 fw-semibold">{{ plan.name }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="plan-pricing-options">
                            <h1 class="card-title pricing-card-title price mt-3 mb-0">
                                {{ platform_currency_symbol }}{{ plan.price_monthly|floatformat:2|intcomma }}
                                <small class="text-muted fw-light price-cycle">/ {% trans "month" %}</small>
                            </h1>
                            {% if plan.price_annually > 0 %}
                                <p class="text-muted small mt-1">
                                    {% trans "Or" %} {{ platform_currency_symbol }}{{ plan.price_annually|floatformat:2|intcomma }} {% trans "billed annually" %}
                                    {% if plan_item.show_savings %}
                                        <span class="d-block text-success fw-bold">
                                            ({% blocktrans with savings=plan_item.savings_if_annual|floatformat:2|intcomma currency=platform_currency_symbol %}Save {{ currency }}{{ savings }}{% endblocktrans %})
                                        </span>
                                    {% endif %}
                                </p>
                            {% endif %}
                        </div>
                        
                        <p class="mt-3 mb-4 text-muted">{{ plan.description|default:"Comprehensive features for your school."|truncatewords:15 }}</p>
                        
                        <ul class="features mb-4 text-start"> {# text-start for feature list items #}
                            {% if plan.trial_period_days > 0 %}
                                <li class="fw-bold"><i class="bi bi-patch-check-fill text-primary me-2"></i>{% blocktrans with days=plan.trial_period_days %}{{ days }}-day free trial{% endblocktrans %}</li>
                            {% endif %}
                            <li>
                                <i class="bi bi-people text-muted me-2"></i>
                                {% if plan.max_students %}{% blocktrans with count=plan.max_students %}{{ count }} Students{% endblocktrans %}{% else %}{% trans "Unlimited Students" %}{% endif %}
                            </li>
                            <li>
                                <i class="bi bi-person-badge text-muted me-2"></i>
                                {% if plan.max_staff %}{% blocktrans with count=plan.max_staff %}{{ count }} Staff{% endblocktrans %}{% else %}{% trans "Unlimited Staff" %}{% endif %}
                            </li>
                            {% for feature in plan.features.all|slice:":4" %} {# Show top 4 features #}
                                <li><i class="bi bi-check-lg text-muted me-2"></i>{{ feature.name }}</li>
                            {% empty %}
                                <li><i class="bi bi-check-lg text-muted me-2"></i>{% trans "Core Platform Features" %}</li>
                            {% endfor %}
                            {% if plan.features.all.count > 4 %}
                                <li class="text-muted"><i class="bi bi-three-dots text-muted me-2"></i>{% trans "And more..." %}</li>
                            {% endif %}
                        </ul>
                        
                        <div class="card-footer-action"> {# Using the new class to push to bottom #}
                            {% if current_tenant_subscription_plan_pk == plan.pk and current_tenant_name_for_cta %}
                                <a href="{% url 'subscriptions:subscription_details' %}" class="btn btn-lg btn-success btn-select-plan w-100">
                                    <i class="bi bi-check-circle-fill me-1"></i> {% trans "Your Current Plan" %}
                                </a>
                            {% elif tenant_can_change_plan and current_tenant_name_for_cta %}
                                {# Existing tenant choosing a new plan - link to initiate_checkout #}
                                <div class="btn-group w-100" role="group" aria-label="Plan Billing Cycle Options">
                                    {% if plan.price_monthly > 0 %}
                                    <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='monthly' %}" 
                                        class="btn btn-primary btn-select-plan">
                                        {% trans "Choose Monthly" %}
                                    </a>
                                    {% endif %}
                                    {% if plan.price_annually > 0 %}
                                    <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='annually' %}" 
                                        class="btn btn-outline-primary btn-select-plan">
                                        {% trans "Choose Annually" %}
                                        {% if plan_item.show_savings %}<small class="d-block">({% trans "Save" %} {{ platform_currency_symbol }}{{ plan_item.savings_if_annual|floatformat:2|intcomma }})</small>{% endif %}
                                    </a>
                                    {% endif %}
                                </div>
                            {% else %}
                                {# Public user, not logged in or not in tenant context - link to registration #}
                                <a href="{% url 'tenants:register_school' %}?plan={{ plan.slug }}" class="btn btn-lg btn-primary btn-select-plan w-100">
                                    <i class="bi bi-rocket-launch-fill me-1"></i> {% trans "Get Started with This Plan" %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endwith %}
        {% endfor %}
    </div>
    {% else %}
    <div class="alert alert-warning text-center" role="alert">
        {% trans "No subscription plans are currently available. Please check back later or contact support." %}
    </div>
    {% endif %}

    <div class="text-center mt-5">
        <p class="text-muted">{% trans "Need a custom solution or have questions? Feel free to" %} <a href="{% url 'public_site:contact' %}">{% trans "contact us" %}</a>.</p>
    </div>
</div>
{% endblock content %}


# D:\school_fees_saas_V2\apps\payments\urls.py
from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Payment Method URLs
    path('methods/', views.PaymentMethodListView.as_view(), name='payment_method_list'),
    path('methods/new/', views.PaymentMethodCreateView.as_view(), name='payment_method_create'),
    path('methods/<int:pk>/edit/', views.PaymentMethodUpdateView.as_view(), name='payment_method_update'),
    path('methods/<int:pk>/delete/', views.PaymentMethodDeleteView.as_view(), name='payment_method_delete'),

    # Payment URLs
    path('', views.PaymentListView.as_view(), name='payment_list'), # List of all payments
    path('record/', views.RecordPaymentView.as_view(), name='record_payment'), # Record a new payment
    # path('payments/<int:pk>/', views.PaymentDetailView.as_view(), name='payment_detail'), # If you create a detail view
    path('receipt/<int:pk>/pdf/', views.generate_payment_receipt_pdf, name='generate_receipt_pdf'), # Function view
    # path('payments/<int:pk>/delete/', views.PaymentDeleteView.as_view(), name='payment_delete'), # If you implement delete/void
    
    path('<int:pk>/receipt/pdf/', views.PaymentReceiptPDFView.as_view(), name='payment_receipt_pdf'),

    # Public receipt access for parents (with permission checks)
    path('receipt/<int:pk>/pdf/public/', views.PaymentReceiptPDFPublicView.as_view(), name='payment_receipt_pdf_public'),

]





# # D:\school_fees_saas_v2\apps\payments\urls.py
# from django.urls import path
# from . import views

# app_name = 'payments'

# urlpatterns = [
#     # Payment Methods
#     path('methods/', views.PaymentMethodListView.as_view(), name='payment_method_list'),
#     path('methods/new/', views.PaymentMethodCreateView.as_view(), name='payment_method_create'),
#     path('methods/<int:pk>/edit/', views.PaymentMethodUpdateView.as_view(), name='payment_method_update'),
#     path('methods/<int:pk>/delete/', views.PaymentMethodDeleteView.as_view(), name='payment_method_delete'),

#     # Payments
#     path('', views.PaymentListView.as_view(), name='payment_list'), # List of all payments
#     path('record/', views.RecordPaymentView.as_view(), name='record_payment'),
#     # path('payments/<int:pk>/', views.PaymentDetailView.as_view(), name='payment_detail'), # Optional detail view
#     path('receipt/<int:pk>/pdf/', views.generate_payment_receipt_pdf, name='payment_receipt_pdf'),
# ]



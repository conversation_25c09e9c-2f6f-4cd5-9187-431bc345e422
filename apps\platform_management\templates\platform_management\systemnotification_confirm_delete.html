{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\systemnotification_confirm_delete.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Confirm Delete Notification") }}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:systemnotification_list' %}">{% trans "System Notifications" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Confirm Delete" %}</li>
        </ol>
    </nav>
</div>

<div class="card border-danger shadow-sm">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">{% trans "Confirm Deletion" %}</h5>
    </div>
    <div class="card-body">
        <p class="card-text mt-3">
            {% blocktrans with title=notification.title trimmed %} {# Using 'notification' as context_object_name #}
            Are you sure you want to delete the system notification titled "<strong>{{ title }}</strong>"?
            {% endblocktrans %}
        </p>
        <p class="text-danger"><strong>{% trans "This action cannot be undone." %}</strong></p>
        
        <form method="post">
            {% csrf_token %}
            <div class="text-end mt-4">
                <a href="{% url 'platform_management:systemnotification_list' %}" class="btn btn-secondary me-2">{% trans "Cancel" %}</a>
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, Delete Notification" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock platform_admin_page_content %}


# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('subscriptions', '0001_initial'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='school',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subscription', to='tenants.school'),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='features',
            field=models.ManyToManyField(blank=True, related_name='plans', to='subscriptions.feature'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='subscriptions.subscriptionplan'),
        ),
    ]

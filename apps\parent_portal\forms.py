# apps/parent_portal/forms.py
from django import forms
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm, PasswordResetForm, SetPasswordForm
from apps.students.models import ParentUser # Import ParentUser



class ParentLoginForm(AuthenticationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = "Parent Email"
        self.fields['username'].widget = forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your parent email address',
                'id': 'floatingParentEmail',
                'autofocus': True,
                'autocomplete': 'email',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter the email address associated with your parent account'
            }
        )
        self.fields['password'].label = "Password"
        self.fields['password'].widget = forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your secure password',
                'id': 'floatingParentPassword',
                'autocomplete': 'current-password',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter your parent account password'
            }
        )

# class ParentLoginForm(AuthenticationForm):
#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         self.fields['username'].label = "Your Email Address"
#         self.fields['username'].widget = forms.EmailInput(
#             attrs={'class': 'form-control form-control-lg mb-2', 'placeholder': '<EMAIL>', 'autocomplete': 'email'}
#         )
#         self.fields['password'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'Password', 'autocomplete': 'current-password'})


# For Parent Self-Registration (if implemented)
class ParentRegistrationForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-control form-control-lg', 'autocomplete': 'new-password'}), label="Password")
    password_confirm = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-control form-control-lg', 'autocomplete': 'new-password'}), label="Confirm Password")
    # Add field for student linking code or similar verification
    # student_linking_code = forms.CharField(max_length=20, required=True, label="Student Linking Code")

    class Meta:
        model = ParentUser
        fields = ['email', 'first_name', 'last_name', 'phone_number', 'password', 'password_confirm'] # Add student_linking_code here
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-control form-control-lg'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        # Check uniqueness within the current tenant (form needs tenant context or view handles)
        # For now, basic check. Tenant-specific check would be better in view.
        if ParentUser.objects.filter(email__iexact=email).exists():
            raise forms.ValidationError("An account with this email already exists.")
        return email
    
    def clean_password_confirm(self):
        pw1 = self.cleaned_data.get("password")
        pw2 = self.cleaned_data.get("password_confirm")
        if pw1 and pw2 and pw1 != pw2:
            raise forms.ValidationError("Passwords do not match.")
        return pw2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password"])
        if commit:
            user.save()
        return user

class ParentProfileUpdateForm(forms.ModelForm): # Assuming this is what you had
    class Meta:
        model = ParentUser # <<<--- CORRECT USER MODEL ---<<<
        fields = ['first_name', 'last_name', 'phone_number', 
                'address_line1', 'city', 'profile_picture'] # Match fields in ParentUser
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'profile_picture': forms.ClearableFileInput(attrs={'class': 'form-control'}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If you want to make email read-only in the form:
        if 'email' in self.fields:
            self.fields['email'].widget.attrs['readonly'] = True
            self.fields['email'].widget.attrs['class'] = 'form-control' # Ensure styling
            self.fields['email'].help_text = "Email cannot be changed here. Contact support if needed."
        
        # You can add more custom styling or logic here if needed
        for field_name, field in self.fields.items():
            if not field.widget.attrs.get('class'): # Add form-control if not already set
                field.widget.attrs['class'] = 'form-control'
            field.widget.attrs['aria-describedby'] = f'{field_name}_help_text'


class ParentPasswordChangeForm(PasswordChangeForm):
    def __init__(self, user, *args, **kwargs):
        super().__init__(user, *args, **kwargs)
        for fieldname, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control form-control-lg'
            field.help_text = '' # Remove default help text for cleaner look
            
            
            


# D:\school_fees_saas_v2\apps\parent_portal\forms.py

import logging
from django import forms
from django.db.models import Q, F, ExpressionWrapper, DecimalField
from django.utils.translation import gettext_lazy as _
from apps.fees.models import Invoice
from apps.students.models import ParentUser

logger = logging.getLogger(__name__)

class SelectInvoicesForPaymentForm(forms.Form):
    selected_invoices = forms.ModelMultipleChoiceField(
        queryset=Invoice.objects.none(), # Populated dynamically in __init__
        widget=forms.CheckboxSelectMultiple, # The template will render this manually for style
        label=_("Select Invoices to Pay"),
        required=True, # Set to True, and customize the error message
        error_messages={'required': _("Please select at least one invoice.")}
    )

    def __init__(self, *args, **kwargs):
        # Extract custom kwargs passed from the view before calling super()
        parent = kwargs.pop('parent', None)
        preselected_invoice_id = kwargs.pop('preselected_invoice_id', None)
        select_all = kwargs.pop('select_all', False)
        
        super().__init__(*args, **kwargs)

        if not parent or not isinstance(parent, ParentUser):
            logger.warning("SelectInvoicesForPaymentForm initialized without a valid parent.")
            return # The queryset will remain empty, and the form will show no options

        try:
            # Get the primary keys of all children linked to this parent
            children_pks = parent.children.filter(is_active=True).values_list('pk', flat=True)
            
            if not children_pks.exists():
                logger.debug(f"Parent {parent.email} has no active children, no invoices to show.")
                return # Queryset remains empty

            outstanding_statuses = [
                Invoice.InvoiceStatus.SENT,
                Invoice.InvoiceStatus.PARTIALLY_PAID,
                Invoice.InvoiceStatus.OVERDUE
            ]
            
            # --- Build the queryset for the form field ---
            # We only want to show invoices that have a balance > 0
            # Invoice model has @property balance_due = (subtotal_amount - total_concession_amount) - amount_paid
            # We need to use the actual database fields, not the property
            queryset = Invoice.objects.annotate(
                balance=ExpressionWrapper(
                    (F('subtotal_amount') - F('total_concession_amount')) - F('amount_paid'),
                    output_field=DecimalField()
                )
            ).filter(
                student_id__in=children_pks,
                status__in=outstanding_statuses,
                balance__gt=0 # Only show invoices with a balance due
            ).select_related('student').order_by('student__first_name', 'due_date')

            self.fields['selected_invoices'].queryset = queryset
            
            # --- Handle pre-selection logic ---
            if select_all:
                # If 'select=all' was passed from the dashboard button, pre-select all.
                self.initial['selected_invoices'] = queryset
                logger.debug(f"Pre-selecting all {queryset.count()} outstanding invoices for parent {parent.email}.")
            elif preselected_invoice_id:
                # If a single invoice was pre-selected from a "Pay Now" button
                try:
                    # We still filter against the main queryset to ensure the parent owns it
                    preselected_invoice = queryset.filter(pk=preselected_invoice_id)
                    if preselected_invoice.exists():
                        self.initial['selected_invoices'] = preselected_invoice
                        logger.debug(f"Pre-selecting invoice PK {preselected_invoice_id} for parent {parent.email}.")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid preselected_invoice_id '{preselected_invoice_id}' passed to form.")
                    pass # Ignore invalid IDs

        except Exception as e:
            logger.error(f"Error initializing SelectInvoicesForPaymentForm queryset for parent {parent.email}: {e}", exc_info=True)
            # Queryset will remain Invoice.objects.none()

    # The clean method is not strictly necessary anymore because:
    # 1. `required=True` on the field will automatically handle the "nothing selected" case.
    # 2. The view's `form_valid` method will double-check that the total amount > 0 before proceeding.
    # This keeps the form's responsibility clean.
    # def clean_selected_invoices(self):
    #     ...
    
    
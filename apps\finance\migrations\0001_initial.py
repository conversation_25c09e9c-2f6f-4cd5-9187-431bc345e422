# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.core.validators
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FinancePermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_finance_module', 'Can view the main Finance module and navbar link'), ('manage_budgets', 'Can create and manage budgets'), ('manage_expenses', 'Can record and approve expenses'), ('view_general_ledger_report', 'Can view the General Ledger report'), ('view_account_ledger_report', 'Can view the Account Ledger report')],
                'managed': False,
                'default_permissions': (),
            },
        ),
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('financial_year_start', models.DateField()),
                ('financial_year_end', models.DateField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=False, help_text='Is this the currently active budget for reporting?')),
            ],
            options={
                'ordering': ['-financial_year_start', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BudgetAmount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('budgeted_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Budgeted Amount',
                'verbose_name_plural': 'Budgeted Amounts',
                'ordering': ['academic_year', 'term', 'budget_item'],
            },
        ),
        migrations.CreateModel(
            name='BudgetItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='Name of the budget line item.', max_length=150, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('budget_item_type', models.CharField(choices=[('INCOME', 'Income'), ('EXPENSE', 'Expense')], max_length=10)),
            ],
            options={
                'verbose_name': 'Budget Item Category',
                'verbose_name_plural': 'Budget Item Categories',
                'ordering': ['budget_item_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('expense_date', models.DateField(default=django.utils.timezone.now)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('description', models.TextField(help_text='Detailed description of the expense.')),
                ('reference_number', models.CharField(blank=True, help_text='e.g., Invoice # from vendor, Receipt #', max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Expense Record',
                'verbose_name_plural': 'Expense Records',
                'ordering': ['-expense_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='e.g., Office Supplies, Utilities, Salaries', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Expense Category',
                'verbose_name_plural': 'Expense Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='Name of the supplier or vendor.', max_length=150, unique=True)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=30, null=True)),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True)),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state_province', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Vendor/Supplier',
                'verbose_name_plural': 'Vendors/Suppliers',
                'ordering': ['name'],
            },
        ),
    ]

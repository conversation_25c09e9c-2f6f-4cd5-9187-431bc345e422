# In your signals.py where SchoolProfile is created
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_tenants.utils import schema_context
from apps.tenants.models import School # Your Tenant model
from apps.schools.models import SchoolProfile # Your SchoolProfile model

import logging
logger = logging.getLogger(__name__)

@receiver(post_save, sender=School) # Or settings.TENANT_MODEL
def create_tenant_school_profile(sender, instance, created, **kwargs):
    if created: # Only on new School creation
        # 'instance' here is the School object
        # Create the profile within the new tenant's schema
        with schema_context(instance.schema_name):
            # Create the profile within the tenant schema (no foreign key needed)
            # Each tenant schema has exactly one SchoolProfile instance
            profile, profile_created = SchoolProfile.objects.get_or_create(
                defaults={
                    # Add any other essential defaults for SchoolProfile here
                    'school_name_override': instance.name, # Example default
                    'school_name_on_reports': instance.name,
                    # ...
                }
            )
            if profile_created:
                logger.info(f"SchoolProfile created for new tenant: {instance.name} in schema {instance.schema_name}")
            else:
                # This case (profile_created=False) should not happen if school is PK
                # because get_or_create with a PK would always get or error.
                # If school is not PK but just OneToOne, get_or_create ensures one exists.
                logger.warning(f"SchoolProfile already existed for tenant: {instance.name} during initial creation signal.") 
                
"""
Safe tenant schema creation command that handles problematic migrations.
This command creates a tenant schema and runs migrations while avoiding known conflicts.
"""
import psycopg
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
from django.db import connection
from django_tenants.utils import schema_context


class Command(BaseCommand):
    help = 'Safely create a tenant schema and run migrations, handling known conflicts'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name to create')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        
        self.stdout.write(f"🔧 Creating safe tenant schema: {schema_name}")
        
        try:
            # Step 1: Create the schema
            self._create_schema(schema_name)
            
            # Step 2: Pre-mark problematic migrations
            self._premark_problematic_migrations(schema_name)
            
            # Step 3: Run migrations safely
            self._run_migrations(schema_name)
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ Successfully created tenant schema: {schema_name}")
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create tenant schema {schema_name}: {e}")
            )
            # Try to clean up the schema if it was created
            try:
                self._drop_schema(schema_name)
            except:
                pass
            raise

    def _create_schema(self, schema_name):
        """Create the database schema"""
        self.stdout.write(f"📁 Creating database schema: {schema_name}")
        
        db_settings = settings.DATABASES['default']
        conn = psycopg.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD']
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        try:
            # Create the schema
            cur.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}"')
            self.stdout.write(f"  ✅ Schema {schema_name} created")
        finally:
            conn.close()

    def _premark_problematic_migrations(self, schema_name):
        """Pre-mark problematic migrations as applied"""
        self.stdout.write(f"🔧 Pre-marking problematic migrations for {schema_name}")
        
        db_settings = settings.DATABASES['default']
        conn = psycopg.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD']
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        try:
            # Create django_migrations table in the schema
            cur.execute(f"""
                CREATE TABLE IF NOT EXISTS "{schema_name}".django_migrations (
                    id SERIAL PRIMARY KEY,
                    app VARCHAR(255) NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    applied TIMESTAMP WITH TIME ZONE NOT NULL,
                    UNIQUE(app, name)
                )
            """)
            
            # List of problematic migrations to pre-mark
            # Only mark migrations that are known to be empty or cause conflicts
            problematic_migrations = [
                # Schools app migrations that cause conflicts (these are now empty)
                ('schools', '0003_remove_school_foreign_key'),
                ('schools', '0005_remove_tenant_fk_from_invoice_sequence'),

                # Fees app migrations that cause conflicts (these are now empty)
                ('fees', '0002_add_term_to_studentconcession'),
                ('fees', '0003_feestructure_total_amount'),
            ]
            
            for app_name, migration_name in problematic_migrations:
                # Check if migration already exists
                cur.execute(f"""
                    SELECT name FROM "{schema_name}".django_migrations 
                    WHERE app = %s AND name = %s
                """, (app_name, migration_name))
                
                if not cur.fetchone():
                    # Mark as applied
                    cur.execute(f"""
                        INSERT INTO "{schema_name}".django_migrations (app, name, applied)
                        VALUES (%s, %s, NOW())
                        ON CONFLICT (app, name) DO NOTHING
                    """, (app_name, migration_name))
                    
                    self.stdout.write(f"  ✅ Pre-marked {app_name}.{migration_name}")
                    
        finally:
            conn.close()

    def _run_migrations(self, schema_name):
        """Run migrations in the schema context"""
        self.stdout.write(f"🚀 Running migrations for {schema_name}")
        
        # Use schema context to run migrations
        with schema_context(schema_name):
            call_command('migrate', verbosity=1, interactive=False)
            
        self.stdout.write(f"  ✅ Migrations completed for {schema_name}")

    def _drop_schema(self, schema_name):
        """Drop the schema if creation failed"""
        self.stdout.write(f"🗑️  Cleaning up failed schema: {schema_name}")
        
        db_settings = settings.DATABASES['default']
        conn = psycopg.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD']
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        try:
            cur.execute(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE')
            self.stdout.write(f"  ✅ Cleaned up schema {schema_name}")
        finally:
            conn.close()

# D:\school_fees_saas_v2\apps\platform_management\urls.py
from django.urls import path, include
from . import views # This imports the views module

app_name = 'platform_management'

urlpatterns = [
    path('', views.platform_dashboard_view, name='dashboard'), # <<< CORRECTED
    
    # Platform Announcements URLs (from previous consolidated views.py)
    # path('announcements/', views.PlatformAnnouncementListView.as_view(), name='platformannouncement_list'),
    # path('announcements/create/', views.PlatformAnnouncementCreateView.as_view(), name='platformannouncement_create'),
    # path('announcements/<int:pk>/update/', views.PlatformAnnouncementUpdateView.as_view(), name='platformannouncement_update'),
    # path('announcements/<int:pk>/delete/', views.PlatformAnnouncementDeleteView.as_view(), name='platformannouncement_delete'),

    
    path('platform/', views.PlatformAnnouncementListView.as_view(), name='platform_announcement_list'),
    path('platform/create/', views.PlatformAnnouncementCreateView.as_view(), name='platform_announcement_create'),
    # path('platform/<int:pk>/', views.PlatformAnnouncementDetailView.as_view(), name='platform_announcement_detail'),
    path('platform/<int:pk>/update/', views.PlatformAnnouncementUpdateView.as_view(), name='platform_announcement_update'),
    path('platform/<int:pk>/delete/', views.PlatformAnnouncementDeleteView.as_view(), name='platform_announcement_delete'),
    
    
    path('maintenance/toggle/', views.maintenance_mode_toggle_view, name='maintenance_mode_toggle'), 
    
    path('announcements/', views.PlatformAnnouncementListView.as_view(), name='platformannouncement_list'),
    
    # path('platform/', include('apps.platform_management.urls', namespace='platform_management')),
    
    # Platform Settings URLs
    path('settings/', views.platform_setting_list_view, name='platformsetting_list'),
    path('settings/create/', views.platform_setting_create_view, name='platformsetting_create'), # Added create URL
    path('settings/<int:pk>/update/', views.platform_setting_update_view, name='platformsetting_update'),
    path('settings/<int:pk>/delete/', views.PlatformSettingDeleteView.as_view(), name='platformsetting_delete'), # <<< NEW

    # System Notifications URLs
    path('system-notifications/', views.system_notification_list_view, name='systemnotification_list'),
    path('system-notifications/create/', views.system_notification_create_view, name='systemnotification_create'),
    # You'll need to add Update and Delete URLs/views for SystemNotification if you want them
    path('system-notifications/<int:pk>/update/', views.SystemNotificationUpdateView.as_view(), name='systemnotification_update'),
    path('system-notifications/<int:pk>/delete/', views.SystemNotificationDeleteView.as_view(), name='systemnotification_delete'),

    # Audit Logs URL
    path('audit-logs/', views.audit_log_list_view, name='auditlog_list'),

    # Maintenance Mode URL
    path('maintenance-mode/', views.maintenance_mode_update_view, name='maintenance_mode_update'), # Changed from toggle to update
]
















# from django.urls import path
# from . import views

# app_name = 'platform_management'

# urlpatterns = [
#     path('', views.platform_dashboard, name='dashboard'),
#     path('settings/', views.settings_list, name='settings_list'),
#     path('settings/<int:pk>/', views.setting_detail, name='setting_detail'),
#     path('notifications/', views.notifications_list, name='notifications_list'),
#     path('notifications/create/', views.create_notification, name='create_notification'),
#     path('audit-logs/', views.audit_logs, name='audit_logs'),
#     path('maintenance/', views.maintenance_mode, name='maintenance_mode'),
# ]



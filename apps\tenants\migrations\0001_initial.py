# Generated by Django 5.1.9 on 2025-06-18 20:41

import django_tenants.postgresql_backend.base
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('is_primary', models.<PERSON>olean<PERSON>ield(db_index=True, default=True)),
            ],
            options={
                'verbose_name': 'School Domain',
                'verbose_name_plural': 'School Domains',
                'ordering': ['domain'],
            },
        ),
        migrations.CreateModel(
            name='School',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schema_name', models.Char<PERSON>ield(db_index=True, max_length=63, unique=True, validators=[django_tenants.postgresql_backend.base._check_schema_name])),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100, unique=True, verbose_name='School Name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly version of name. Auto-generated if blank. Used for schema name if schema_name not set.', max_length=100, unique=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tenant account is active on the platform (e.g., subscription valid).', verbose_name='Platform Active Status')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'School (Tenant)',
                'verbose_name_plural': 'Schools (Tenants)',
                'ordering': ['name'],
            },
        ),
    ]

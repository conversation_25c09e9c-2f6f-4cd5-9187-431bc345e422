# Generated by Django 5.1.9 on 2025-07-07 18:43

from django.db import migrations, models
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0011_alter_payslip_adjustments'),
    ]

    operations = [
        migrations.AddField(
            model_name='leavetype',
            name='max_days_per_year_grant',
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text='For non-accruing leave types, how many days are granted per year (e.g., 15 sick days).',
                max_digits=5,
                null=True,
                verbose_name='Max Days Granted Annually'
            ),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_frequency',
            field=models.CharField(
                choices=[('NONE', 'No Accrual (Manually Granted)'), ('MONTHLY', 'Monthly'), ('ANNUALLY', 'Annually')],
                default='NONE',
                help_text="Choose 'No Accrual' for leave that is granted manually or all at once per year.",
                max_length=20,
                verbose_name='Accrual Frequency'
            ),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_rate',
            field=models.DecimalField(
                decimal_places=2,
                default=Decimal('0.00'),
                help_text='If accruing, number of days added per period (e.g., 1.75 for monthly).',
                max_digits=5,
                verbose_name='Accrual Rate'
            ),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_accrual_balance',
            field=models.PositiveIntegerField(
                blank=True,
                help_text='The maximum number of leave days a staff member can accumulate. Leave blank for no limit.',
                null=True,
                verbose_name='Maximum Balance'
            ),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='prorate_accrual',
            field=models.BooleanField(
                default=True,
                help_text='If accruing, new staff hired mid-period will receive a prorated amount for their first accrual.',
                verbose_name='Prorate for New Staff'
            ),
        ),
    ]

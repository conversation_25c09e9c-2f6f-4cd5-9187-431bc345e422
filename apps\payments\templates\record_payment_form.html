{% comment %} {# D:\school_fees_saas_v2\templates\payments\record_payment_form.html #}
{% extends "tenant_base.html" %}

{% load static widget_tweaks humanize %}

{% block title %}{{ view_title|default:"Record Payment" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .payment-form-page-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding-top: 2rem;
            padding-bottom: 4rem;
            min-height: calc(100vh - 56px - 60px);
        }
        
        .payment-form-card {
            background-color: #ffffff;
            border: none;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
            z-index: 1000;
        }
        
        .payment-form-card .card-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            padding: 1.5rem 2rem;
        }
        .payment-form-card .card-header h4 {
            color: white;
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .payment-form-card .card-body {
            padding: 2rem 1.75rem;
        }
        
        .payment-form-card .form-label {
            font-weight: 500;
            margin-bottom: 0.375rem;
            color: #343a40;
            font-size: 0.9rem;
        }
        
        .payment-form-card .form-control,
        .payment-form-card .form-select {
            font-size: 1rem;
            border-radius: 0.5rem;
            border: 2px solid #e0e0e0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background-color: #fafafa;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 1001 !important;
        }
        
        .payment-form-card .form-control:focus,
        .payment-form-card .form-select:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
            background-color: white;
            transform: translateY(-1px);
            z-index: 1002 !important;
        }
        
        .payment-form-card .input-group-text {
            background-color: #e9ecef;
            border-color: #ced4da;
            color: #495057;
            font-size: 0.9rem;
            border-radius: 0.3rem 0 0 0.3rem;
            z-index: 1001;
        }
        
        .payment-form-card .input-group > .form-control {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .payment-form-card .form-text {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .payment-form-card .btn {
            padding: 0.6rem 1.25rem;
            font-size: 0.9rem;
            font-weight: 500;
            border-radius: 0.3rem;
        }
        
        .payment-form-card .btn-success {
            background-color: #2dce89;
            border-color: #2dce89;
        }
        
        .payment-form-card .btn-success:hover {
            background-color: #26ab74;
            border-color: #24a06d;
        }
        
        .payment-form-card .btn-outline-secondary {
            border-color: #adb5bd;
            color: #495057;
        }
        
        .payment-form-card .btn-outline-secondary:hover {
            background-color: #e9ecef;
            color: #343a40;
        }

        .errorlist {
            color: var(--bs-danger);
            font-size: 0.875em;
            list-style: none;
            padding-left: 0;
            margin-top: .25rem;
        }
        
        .invalid-feedback.d-block {
            display: block !important;
        }

        /* Force form elements to be interactive */
        .payment-form input,
        .payment-form select,
        .payment-form textarea {
            pointer-events: auto !important;
            user-select: auto !important;
            position: relative !important;
            z-index: 1001 !important;
        }

        /* Prevent any potential modal or overlay interference */
        .payment-form-page-container {
            position: relative;
            z-index: 999;
        }

        /* CRITICAL: Disable Bootstrap dropdown auto-close when clicking inside payment form */
        .payment-form-page-container .dropdown-menu {
            pointer-events: none;
        }
        
        /* Re-enable pointer events for dropdown items that should work */
        .payment-form-page-container .dropdown-menu .dropdown-item {
            pointer-events: auto;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="payment-form-page-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card payment-form-card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-credit-card-fill me-2"></i>{{ view_title|default:"Record Payment" }}</h4>
                    </div>
                    <div class="card-body">
                        {% include "partials/_messages.html" %}

                        <form method="post" novalidate class="payment-form" id="paymentForm">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger py-2 mb-3">
                                    {% for error in form.non_field_errors %}
                                        {{ error }}{% if not forloop.last %}<br>{% endif %}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <fieldset class="mb-4">
                                <legend class="h6 text-muted border-bottom pb-2 mb-3">Payer & Invoice Information</legend>
                                {% if form.student %}
                                <div class="mb-3">
                                    {{ form.student.label_tag|add_class:"form-label" }}
                                    {% render_field form.student class+="form-select student-select" data-placeholder="Select Student..." %}
                                    {% for error in form.student.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    {% if form.student.help_text %}<small class="form-text">{{ form.student.help_text|safe }}</small>{% endif %}
                                </div>
                                {% endif %}

                                {% if form.invoice %}
                                <div class="mb-3">
                                    {{ form.invoice.label_tag|add_class:"form-label" }}
                                    {% render_field form.invoice class+="form-select invoice-select" data-placeholder="Select Invoice (Optional)..." %}
                                    {% for error in form.invoice.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    {% if form.invoice.help_text %}<small class="form-text">{{ form.invoice.help_text|safe }}</small>{% endif %}
                                </div>
                                {% endif %}
                            </fieldset>

                            <fieldset class="mb-4">
                                <legend class="h6 text-muted border-bottom pb-2 mb-3">Payment Details</legend>
                                <div class="row g-3">
                                    <div class="col-md-6 mb-3">
                                        {{ form.amount.label_tag|add_class:"form-label" }}
                                        <div class="input-group">
                                            <span class="input-group-text">{{ request.tenant.profile.currency_symbol|default:"$" }}</span>
                                            {% render_field form.amount class+="form-control text-end" step="0.01" placeholder="0.00" %}
                                        </div>
                                        {% for error in form.amount.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        {{ form.payment_date.label_tag|add_class:"form-label" }}
                                        {% render_field form.payment_date class+="form-control" %}
                                        {% for error in form.payment_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                
                                    <div class="col-md-6 mb-3">
                                        {{ form.payment_method.label_tag|add_class:"form-label" }}
                                        {% render_field form.payment_method class+="form-select" data-placeholder="Select Payment Method..." %}
                                        {% for error in form.payment_method.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        {{ form.academic_year.label_tag|add_class:"form-label" %}
                                        {% render_field form.academic_year class+="form-select" data-placeholder="Select Academic Year..." %}
                                        {% for error in form.academic_year.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    {{ form.reference_number.label_tag|add_class:"form-label" }}
                                    {% render_field form.reference_number class+="form-control" placeholder="e.g., Cheque No., Transaction ID" %}
                                    {% for error in form.reference_number.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    {% if form.reference_number.help_text %}<small class="form-text">{{ form.reference_number.help_text|safe }}</small>{% endif %}
                                </div>
                            </fieldset>
                            
                            <fieldset>
                                <legend class="h6 text-muted border-bottom pb-2 mb-3">Additional Information</legend>
                                <div class="mb-3">
                                    {{ form.payment_type.label_tag|add_class:"form-label" }}
                                    {% render_field form.payment_type class+="form-select" %}
                                    {% for error in form.payment_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                                <div class="mb-3">
                                    {{ form.status.label_tag|add_class:"form-label" }}
                                    {% render_field form.status class+="form-select" %}
                                    {% for error in form.status.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                                <div class="mb-3">
                                    {{ form.notes.label_tag|add_class:"form-label" }}
                                    {% render_field form.notes class+="form-control" rows="3" placeholder="Any additional notes about this payment..." %}
                                    {% for error in form.notes.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                            </fieldset>

                            <hr class="my-4">

                            <div class="d-flex justify-content-end gap-2">
                                {% if invoice and invoice.pk %}
                                    <a href="{% url 'fees:invoice_detail' invoice.pk %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-lg me-1"></i>Cancel
                                    </a>
                                {% elif student and student.pk %}
                                    <a href="{% url 'students:student_detail' student.pk %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-lg me-1"></i>Cancel
                                    </a>
                                {% else %}
                                    <a href="{% url 'payments:payment_list' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-lg me-1"></i>Cancel
                                    </a>
                                {% endif %}
                                <button type="submit" class="btn btn-success px-4">
                                    <i class="bi bi-check-circle-fill me-1"></i>Record Payment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Payment form loaded successfully');

    const paymentForm = document.getElementById('paymentForm');

    if (!paymentForm) {
        console.error('Payment form not found!');
        return;
    }

    // Simple form enhancement without complex interference protection
    console.log('Setting up payment form...');

    // Basic form validation and user experience improvements
    const submitButton = paymentForm.querySelector('button[type="submit"]');
    const amountField = paymentForm.querySelector('input[name="amount"]');

    // Prevent double submission
    if (submitButton) {
        paymentForm.addEventListener('submit', function() {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Processing...';
        });
    }

    // Format amount field
    if (amountField) {
        amountField.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value)) {
                this.value = value.toFixed(2);
            }
        });
    }

    console.log('Payment form setup complete');


});
</script>
{% endblock %}


 {% endcomment %}

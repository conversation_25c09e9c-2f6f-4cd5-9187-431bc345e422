{% load static humanize i18n %}

<!-- Announcements Widget -->
<div class="card h-100 shadow-sm">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-megaphone-fill me-2"></i>
            {% trans "Announcements" %}
        </h5>
        {% if show_manage_link and perms.announcements.view_announcement %}
            <a href="/portal/announcements/" class="btn btn-sm btn-outline-light">
                <i class="bi bi-gear-fill me-1"></i>{% trans "Manage" %}
            </a>
        {% endif %}
    </div>
    <div class="card-body p-0">
        {% if announcements %}
            <div class="list-group list-group-flush">
                {% for announcement in announcements %}
                <div class="list-group-item list-group-item-action border-0 {% if announcement.is_sticky %}bg-light{% endif %}">
                    <div class="d-flex w-100 justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">
                                {% if announcement.is_sticky %}
                                    <i class="bi bi-pin-angle-fill text-warning me-1" title="{% trans 'Pinned' %}"></i>
                                {% endif %}
                                <span class="text-decoration-none">
                                    {{ announcement.title|truncatechars:60 }}
                                </span>
                            </h6>
                            <p class="mb-1 text-muted small">
                                {{ announcement.content|truncatewords:15|linebreaksbr }}
                            </p>
                            <small class="text-muted">
                                <i class="bi bi-calendar3 me-1"></i>
                                {{ announcement.publish_date|naturaltime }}
                                {% if announcement.author %}
                                    <span class="mx-1">•</span>
                                    <i class="bi bi-person-fill me-1"></i>
                                    {{ announcement.author.get_full_name|default:announcement.author.email }}
                                {% endif %}
                            </small>
                        </div>
                        {% if announcement.is_sticky %}
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="bi bi-pin-angle-fill"></i>
                            </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            
            {% if show_view_all_link %}
                <div class="card-footer text-center bg-light">
                    <a href="/portal/announcements/" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-list-ul me-1"></i>{% trans "View All Announcements" %}
                    </a>
                </div>
            {% endif %}
        {% else %}
            <div class="card-body text-center py-4">
                <div class="text-muted">
                    <i class="bi bi-megaphone display-4 mb-3 text-muted"></i>
                    <p class="mb-0">{% trans "No announcements at this time." %}</p>
                    {% if show_create_link and perms.announcements.add_announcement %}
                        <a href="/portal/announcements/create/" class="btn btn-sm btn-primary mt-2">
                            <i class="bi bi-plus-circle me-1"></i>{% trans "Create First Announcement" %}
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

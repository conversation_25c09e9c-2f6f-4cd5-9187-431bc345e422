/* static/css/tenant_form_styles.css */
label,
.form-label {
    color: #008080 !important; /* Teal - !important if <PERSON><PERSON><PERSON> overrides */
    font-weight: 600;
    display: block;
    margin-bottom: 0.3rem;
}

.helptext,
form .form-text,
small.form-text {
    font-style: italic;
    font-size: 0.85em;
    color: #495057; /* Slightly darker than default muted for readability */
    display: block;
    margin-top: 0.1rem;
}

/* Ensure form elements take full width for consistent look */
.form-control,
.form-select {
    margin-bottom: 1rem; /* Consistent spacing below inputs */
}

.errorlist {
    color: #dc3545;
    font-style: italic;
    font-size: 0.85em;
    list-style-type: none;
    padding-left: 0;
    margin-top: 0.25rem;
}



/* static/css/tenant_form_styles.css or style.css */

/* Ensure labels are teal if not already by global styles */
.card-body .form-label { /* More specific if needed */
    color: #008080; /* Teal */
    font-weight: 600;
}

/* Standard input field rounding and focus - <PERSON><PERSON><PERSON> should handle this if form-control is used */
/* You can add more specific styling if Bootstrap's defaults aren't enough */
.form-control {
    border-radius: 0.375rem; /* Bootstrap's default rounding */
}

/* For help text, if any in login form (usually not) */
.form-text {
    font-style: italic;
    font-size: 0.85em;
}

/* D:\school_fees_saas_v2\static\css\tenant_form_styles.css */

/* ... (your existing form label, helptext, input styles) ... */

/* --- Dashboard Card Styling --- */
.dashboard-card {
    border: none; /* Remove default card border for a cleaner look */
    border-radius: 0.5rem; /* Slightly more rounded corners */
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    /* Consider a subtle hover effect if desired */
    /* &:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,.1);
    } */
}

.dashboard-card .card-header {
    background-color: transparent; /* Remove default header background for some card types */
    border-bottom: 1px solid rgba(0,0,0,0.08); /* Softer border */
    font-weight: 500; /* Slightly less bold than h5 default for headers */
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
}

.dashboard-card .card-header i {
    font-size: 1.1em; /* Slightly larger icons in card headers */
}

.dashboard-card .card-body {
    padding: 1.25rem;
}

.dashboard-card .card-title.display-6 { /* For the big stat numbers */
    font-weight: 600; /* Make stats prominent */
    margin-bottom: 0.75rem;
}

.dashboard-card .card-text {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8); /* Lighter text on dark cards */
}
.dashboard-card.text-dark .card-text { /* Specific for light cards with dark text */
    color: #495057;
}


.dashboard-card .btn-sm { /* Make buttons in cards slightly more prominent */
    padding: 0.3rem 0.75rem;
    font-size: 0.8rem;
}

/* Specific background card styles if needed */
.dashboard-card.bg-primary .card-header,
.dashboard-card.bg-success .card-header {
    border-bottom-color: rgba(255,255,255,0.2);
}


/* --- Quick Actions List Group Styling --- */
.quick-actions-list .list-group-item {
    border-left: 3px solid transparent; /* Placeholder for hover effect */
    transition: background-color 0.2s ease, border-left-color 0.2s ease;
    padding: 0.9rem 1.25rem; /* Slightly more padding */
}

.quick-actions-list .list-group-item:hover {
    background-color: #e9ecef; /* Bootstrap light hover */
    border-left-color: var(--bs-primary); /* Use Bootstrap primary color variable */
}

.quick-actions-list .list-group-item i.quick-action-icon {
    transition: transform 0.2s ease;
}
.quick-actions-list .list-group-item:hover i.quick-action-icon {
    transform: translateX(3px);
}


/* --- Recent Activities & Alerts List Styling --- */
.activity-list .list-group-item,
.alerts-list .list-group-item {
    border: none; /* Remove default borders */
    border-bottom: 1px solid #f0f0f0; /* Light separator */
    padding: 0.75rem 0; /* Adjust padding */
}
.activity-list .list-group-item:last-child,
.alerts-list .list-group-item:last-child {
    border-bottom: none;
}

.activity-list .fw-bold {
    color: #343a40; /* Slightly darker than default for emphasis */
}

.activity-list small.text-muted a {
    color: #007bff; /* Make links in activity details stand out a bit */
    text-decoration: none;
}
.activity-list small.text-muted a:hover {
    text-decoration: underline;
}

/* Alert specific styling */
.alerts-list .alert { /* If using alert classes directly in list items */
    box-shadow: none;
    border-left-width: 4px;
}

/* --- General Card Header for Management Sections --- */
.management-card .card-header {
    background-color: #f8f9fa; /* Consistent light header for these cards */
    border-bottom: 1px solid #dee2e6;
}
.management-card .card-header h5 {
    font-weight: 500;
    color: #343a40;
}
.management-card .list-group-item {
    border-left: 3px solid transparent;
    transition: background-color 0.15s ease-in-out, border-left-color 0.15s ease-in-out;
}
.management-card .list-group-item:hover {
    background-color: #eef3f8; /* Softer hover */
    border-left-color: var(--bs-info); /* Use info color for accent */
}

/* --- Alerts & Notifications Card Styling --- */
.alerts-list .list-group-item {
    /* border: none; /* Remove if you want default list-group-item borders back */
    /* border-bottom: 1px solid #f0f0f0; /* Re-add if border:none was too much */
    padding: 0; /* Remove padding from list-group-item itself */
    margin-bottom: 0.75rem; /* Add space BETWEEN alert items */
    background-color: transparent; /* Make list-group-item background transparent */
    border-radius: 0.375rem; /* Optional: give the list item a radius if you want */
}

.alerts-list .list-group-item:last-child {
    margin-bottom: 0; /* No margin for the last item */
    /* border-bottom: none; */ /* If re-added border-bottom above */
}

/* Style the actual alert div inside the list item */
.alerts-list .alert { /* Target the Bootstrap alert class */
    margin-bottom: 0; /* Alert itself doesn't need bottom margin if list-item has it */
    width: 100%;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075); /* Add a subtle shadow to each alert */
    display: flex; /* Keep this from your previous working style */
    align-items: center; /* Keep this */
}

.alerts-list .alert i.bi { /* Icon styling */
    margin-right: 0.75rem;
    font-size: 1.25rem; /* Adjust icon size if needed */
}


#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import psycopg
from django.conf import settings

def delete_schema_safely(cur, schema_name):
    """Safely delete a schema and all its contents"""
    try:
        print(f'  🗑️  Deleting schema: {schema_name}')
        
        # Drop the schema and all its contents
        cur.execute(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE;')
        
        print(f'  ✅ Successfully deleted schema: {schema_name}')
        return True
        
    except Exception as e:
        print(f'  ❌ Error deleting schema {schema_name}: {e}')
        return False

def delete_tenant_records(cur, schemas_to_delete):
    """Delete tenant records from the public schema"""
    try:
        print('🗑️  Deleting tenant records from public.tenants_school table...')
        
        # Set search path to public
        cur.execute('SET search_path TO public;')
        
        for schema in schemas_to_delete:
            # Delete the tenant record
            cur.execute('DELETE FROM tenants_school WHERE schema_name = %s', (schema,))
            print(f'  ✅ Deleted tenant record for: {schema}')
            
    except Exception as e:
        print(f'  ❌ Error deleting tenant records: {e}')

print("🚨 WARNING: This will permanently delete all schemas except 'alpha'!")
print("📋 Schemas that will be DELETED:")

db_settings = settings.DATABASES['default']
conn = psycopg.connect(
    host=db_settings['HOST'],
    port=db_settings['PORT'],
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD']
)
conn.autocommit = True
cur = conn.cursor()

# Get all tenant schemas except alpha
cur.execute("""
    SELECT schema_name 
    FROM information_schema.schemata 
    WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'public', 'pg_toast', 'alpha')
    ORDER BY schema_name
""")
schemas_to_delete = [row[0] for row in cur.fetchall()]

for schema in schemas_to_delete:
    print(f"  - {schema}")

if not schemas_to_delete:
    print("✅ No schemas to delete (only alpha exists)")
    conn.close()
    exit()

print(f"\n📊 Total schemas to delete: {len(schemas_to_delete)}")
print("⚠️  This action cannot be undone!")

# Confirm deletion
response = input("\nType 'DELETE ALL' to confirm deletion of all schemas except alpha: ")

if response != 'DELETE ALL':
    print("❌ Deletion cancelled.")
    conn.close()
    exit()

print("\n🚀 Starting schema deletion process...")

# Delete all schemas except alpha
deleted_count = 0
for schema in schemas_to_delete:
    if delete_schema_safely(cur, schema):
        deleted_count += 1

# Delete tenant records from public schema
delete_tenant_records(cur, schemas_to_delete)

conn.close()

print(f"\n🎉 Schema deletion completed!")
print(f"✅ Successfully deleted {deleted_count} out of {len(schemas_to_delete)} schemas")
print("✅ Only 'alpha' schema remains")
print("\nYou can now run: python manage.py migrate")

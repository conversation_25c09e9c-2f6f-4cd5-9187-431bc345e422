from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.apps import apps

class Command(BaseCommand):
    help = 'Checks if a specific tenant exists and prints its details from the TenantModel.'

    def add_arguments(self, parser):
        parser.add_argument(
            'schema_name',
            type=str,
            help='The schema_name of the tenant to check.',
            default='zenith',  # Default to 'zenith'
            nargs='?'          # Makes the argument optional, using the default if not provided
        )

    def handle(self, *args, **options):
        schema_to_check = options['schema_name']
        self.stdout.write(f"Attempting to find tenant with schema_name: '{schema_to_check}'")
        self.stdout.write(f"Using TENANT_MODEL setting: '{settings.TENANT_MODEL}'")

        try:
            TenantModel = apps.get_model(settings.TENANT_MODEL)
            self.stdout.write(f"Successfully loaded TenantModel: {TenantModel}")
        except LookupError:
            raise CommandError(f"Could not load TENANT_MODEL: '{settings.TENANT_MODEL}'. Is it correctly defined in settings and installed apps?")
        except Exception as e:
            raise CommandError(f"An unexpected error occurred loading TenantModel: {e}")

        try:
            # First, check if the field 'schema_name' actually exists on the model
            # This is a common point of failure if the field name is different.
            TenantModel._meta.get_field('schema_name')
        except Exception: # Django raises FieldDoesNotExist, but a general Exception is fine here for simplicity
            self.stdout.write(self.style.WARNING(
                f"The TenantModel '{TenantModel.__name__}' does not seem to have a field named 'schema_name'. "
                f"Please ensure your TENANT_MODEL's schema identifier field is named 'schema_name' or "
                f"that django-tenants is configured for a different field name."
            ))
            # We can still try the query, but it's likely to fail if the field name is wrong.

        try:
            tenant = TenantModel.objects.get(schema_name=schema_to_check)
            self.stdout.write(self.style.SUCCESS(
                f"Tenant '{schema_to_check}' FOUND in management command context."
            ))
            self.stdout.write(f"  Tenant PK: {tenant.pk}")
            # Attempt to print other common attributes, if they exist
            if hasattr(tenant, 'name'):
                self.stdout.write(f"  Tenant Name: {tenant.name}")
            if hasattr(tenant, 'domain_url'): # If you use django-tenants domains
                self.stdout.write(f"  Tenant Domain URL: {tenant.domain_url}")
            # Add any other relevant fields from your TenantModel
            self.stdout.write(f"  Tenant Object: {tenant}")

        except TenantModel.DoesNotExist:
            self.stdout.write(self.style.ERROR(
                f"Tenant '{schema_to_check}' NOT FOUND in management command context using schema_name."
            ))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"An error occurred while querying for the tenant: {e}"))
            self.stdout.write(self.style.WARNING(
                "This could be due to an incorrect 'schema_name' field in your TenantModel, "
                "database connectivity issues, or other model misconfigurations."
            ))
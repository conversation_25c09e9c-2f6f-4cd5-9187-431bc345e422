# apps/accounting/coa_template.py
# This structure defines the standard Chart of Accounts.
# Each dictionary represents an Account to be created.
# 'account_type_classification' refers to the AccountType.classification DB value.
# 'parent_code' refers to the 'code' of the parent account in this template.


# apps/accounting/coa_template.py

STANDARD_CHART_OF_ACCOUNTS = [
    # Assets
    {"code": "1000", "name": "Assets", "account_type_name": "Asset Control", "parent_code": None, "is_control_account": True, "description": "Resources owned by the school."},
        {"code": "1100", "name": "Current Assets", "account_type_name": "Asset Control", "parent_code": "1000", "is_control_account": True}, # Could be a more specific "Current Asset Control" if you have it
            {"code": "1110", "name": "Cash and Cash Equivalents", "account_type_name": "Cash Equivalents Control", "parent_code": "1100", "is_control_account": True},
                {"code": "1111", "name": "Petty Cash", "account_type_name": "Cash", "parent_code": "1110", "is_control_account": False},
                {"code": "1112", "name": "Main Operating Bank Account", "account_type_name": "Bank Account", "parent_code": "1110", "is_control_account": False},
                {"code": "1113", "name": "Savings Account", "account_type_name": "Bank Account", "parent_code": "1110", "is_control_account": False},
            {"code": "1200", "name": "Accounts Receivable", "account_type_name": "Accounts Receivable Control", "parent_code": "1100", "is_control_account": True},
                {"code": "1210", "name": "AR - Student Fees", "account_type_name": "Accounts Receivable", "parent_code": "1200", "is_control_account": False},
            {"code": "1300", "name": "Inventory (e.g., Uniforms, Books)", "account_type_name": "Inventory Asset", "parent_code": "1100", "is_control_account": False},
        {"code": "1500", "name": "Non-Current Assets", "account_type_name": "Asset Control", "parent_code": "1000", "is_control_account": True}, # Could be "Non-Current Asset Control"
            {"code": "1510", "name": "Furniture and Fixtures", "account_type_name": "Fixed Asset", "parent_code": "1500", "is_control_account": True},
                {"code": "1511", "name": "Accumulated Depreciation - Furniture", "account_type_name": "Accumulated Depreciation", "parent_code": "1510", "is_control_account": False},
            {"code": "1520", "name": "Equipment", "account_type_name": "Fixed Asset", "parent_code": "1500", "is_control_account": True},
                {"code": "1521", "name": "Accumulated Depreciation - Equipment", "account_type_name": "Accumulated Depreciation", "parent_code": "1520", "is_control_account": False},
            {"code": "1530", "name": "Buildings", "account_type_name": "Fixed Asset", "parent_code": "1500", "is_control_account": True},
                {"code": "1531", "name": "Accumulated Depreciation - Buildings", "account_type_name": "Accumulated Depreciation", "parent_code": "1530", "is_control_account": False},

    # Liabilities
    {"code": "2000", "name": "Liabilities", "account_type_name": "Liability Control", "parent_code": None, "is_control_account": True},
        {"code": "2100", "name": "Current Liabilities", "account_type_name": "Liability Control", "parent_code": "2000", "is_control_account": True}, # Could be "Current Liability Control"
            {"code": "2110", "name": "Accounts Payable", "account_type_name": "Accounts Payable Control", "parent_code": "2100", "is_control_account": True},
                {"code": "2111", "name": "AP - General Suppliers", "account_type_name": "Accounts Payable", "parent_code": "2110", "is_control_account": False},
            {"code": "2200", "name": "Unearned Revenue (Fees Paid in Advance)", "account_type_name": "Unearned Revenue", "parent_code": "2100", "is_control_account": False},
            {"code": "2300", "name": "Salaries Payable", "account_type_name": "Accrued Expense Payable", "parent_code": "2100", "is_control_account": False}, # Or create "Salaries Payable" AccountType
        {"code": "2500", "name": "Non-Current Liabilities", "account_type_name": "Liability Control", "parent_code": "2000", "is_control_account": True}, # Could be "Non-Current Liability Control"
            {"code": "2510", "name": "Long-Term Loans", "account_type_name": "Long-Term Debt", "parent_code": "2500", "is_control_account": False},

    # Equity
    {"code": "3000", "name": "Equity", "account_type_name": "Equity Control", "parent_code": None, "is_control_account": True},
        {"code": "3100", "name": "Owner's Capital / Contributed Capital", "account_type_name": "Owners Equity", "parent_code": "3000", "is_control_account": False},
        {"code": "3200", "name": "Retained Earnings", "account_type_name": "Retained Earnings", "parent_code": "3000", "is_control_account": False},
        {"code": "3300", "name": "Owner's Drawings", "account_type_name": "Owners Drawings", "parent_code": "3000", "is_control_account": False},
        {"code": "3900", "name": "Current Year Earnings", "account_type_name": "Current Year Earnings", "parent_code": "3000", "is_control_account": False},

    # Revenue/Income
    {"code": "4000", "name": "Operating Revenue", "account_type_name": "Revenue Control", "parent_code": None, "is_control_account": True},
        {"code": "4010", "name": "Tuition Fee Income", "account_type_name": "Tuition Revenue", "parent_code": "4000", "is_control_account": False},
        {"code": "4020", "name": "Application Fee Income", "account_type_name": "Other Fee Revenue", "parent_code": "4000", "is_control_account": False}, # Or create "Application Fee Revenue" AccountType
        {"code": "4030", "name": "Library Services Income", "account_type_name": "Other Fee Revenue", "parent_code": "4000", "is_control_account": False}, # Or create "Library Revenue" AccountType
        {"code": "4040", "name": "Sports Activities Income", "account_type_name": "Other Fee Revenue", "parent_code": "4000", "is_control_account": False}, # Or create "Sports Revenue" AccountType
        {"code": "4050", "name": "Examination Fee Income", "account_type_name": "Other Fee Revenue", "parent_code": "4000", "is_control_account": False}, # Or create "Exam Fee Revenue" AccountType
        {"code": "4060", "name": "Transportation Fee Income", "account_type_name": "Other Fee Revenue", "parent_code": "4000", "is_control_account": False}, # Or create "Transport Revenue" AccountType
        {"code": "4800", "name": "Other Operating Income", "account_type_name": "Miscellaneous Revenue", "parent_code": "4000", "is_control_account": False},
        {"code": "4900", "name": "Discounts on Fees", "account_type_name": "Contra Revenue (Discounts)", "parent_code": "4000", "is_control_account": False},

    # Cost of Goods Sold
    {"code": "5000", "name": "Cost of Goods Sold", "account_type_name": "COGS Control", "parent_code": None, "is_control_account": True},
        {"code": "5010", "name": "Cost of Uniforms Sold", "account_type_name": "Cost of Goods Sold", "parent_code": "5000", "is_control_account": False},

    # Expenses
    {"code": "6000", "name": "Operating Expenses", "account_type_name": "Expense Control", "parent_code": None, "is_control_account": True},
        {"code": "6100", "name": "Salaries and Wages", "account_type_name": "Salaries Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6200", "name": "Rent Expense", "account_type_name": "Rent Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6300", "name": "Utilities Expense", "account_type_name": "Utilities Expense", "parent_code": "6000", "is_control_account": True}, # Made this a control for sub-utilities
            {"code": "6310", "name": "Electricity", "account_type_name": "Utilities Expense", "parent_code": "6300", "is_control_account": False},
            {"code": "6320", "name": "Water", "account_type_name": "Utilities Expense", "parent_code": "6300", "is_control_account": False},
            {"code": "6330", "name": "Internet & Telephone", "account_type_name": "Utilities Expense", "parent_code": "6300", "is_control_account": False},
        {"code": "6400", "name": "Teaching Supplies & Materials", "account_type_name": "Supplies Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6500", "name": "Repairs and Maintenance", "account_type_name": "Repair and Maintenance Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6600", "name": "Bank Service Charges", "account_type_name": "Bank Charges Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6700", "name": "Insurance Expense", "account_type_name": "Insurance Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6800", "name": "Depreciation Expense", "account_type_name": "Depreciation Expense", "parent_code": "6000", "is_control_account": False},
        {"code": "6900", "name": "Miscellaneous Expenses", "account_type_name": "Miscellaneous Expense", "parent_code": "6000", "is_control_account": False},
]














# STANDARD_CHART_OF_ACCOUNTS = [
#     # Assets
#     {"code": "1000", "name": "Assets", "account_type_classification": "ASSET", "parent_code": None, "is_control_account": True, "description": "Resources owned by the school."},
#         {"code": "1100", "name": "Current Assets", "account_type_classification": "ASSET", "parent_code": "1000", "is_control_account": True},
#             {"code": "1110", "name": "Cash and Cash Equivalents", "account_type_classification": "ASSET", "parent_code": "1100", "is_control_account": True},
#                 {"code": "1111", "name": "Petty Cash", "account_type_classification": "ASSET", "parent_code": "1110", "is_control_account": False},
#                 {"code": "1112", "name": "Main Operating Bank Account", "account_type_classification": "ASSET", "parent_code": "1110", "is_control_account": False},
#                 {"code": "1113", "name": "Savings Account", "account_type_classification": "ASSET", "parent_code": "1110", "is_control_account": False},
#             {"code": "1200", "name": "Accounts Receivable", "account_type_classification": "ASSET", "parent_code": "1100", "is_control_account": True},
#                 {"code": "1210", "name": "AR - Student Fees", "account_type_classification": "ASSET", "parent_code": "1200", "is_control_account": False},
#             {"code": "1300", "name": "Inventory (e.g., Uniforms, Books)", "account_type_classification": "ASSET", "parent_code": "1100", "is_control_account": False},
#         {"code": "1500", "name": "Non-Current Assets", "account_type_classification": "ASSET", "parent_code": "1000", "is_control_account": True},
#             {"code": "1510", "name": "Furniture and Fixtures", "account_type_classification": "ASSET", "parent_code": "1500", "is_control_account": True},
#                 {"code": "1511", "name": "Accumulated Depreciation - Furniture", "account_type_classification": "ASSET", "parent_code": "1510", "is_control_account": False}, # Contra-Asset
#             {"code": "1520", "name": "Equipment", "account_type_classification": "ASSET", "parent_code": "1500", "is_control_account": True},
#                 {"code": "1521", "name": "Accumulated Depreciation - Equipment", "account_type_classification": "ASSET", "parent_code": "1520", "is_control_account": False}, # Contra-Asset
#             {"code": "1530", "name": "Buildings", "account_type_classification": "ASSET", "parent_code": "1500", "is_control_account": True},
#                 {"code": "1531", "name": "Accumulated Depreciation - Buildings", "account_type_classification": "ASSET", "parent_code": "1530", "is_control_account": False}, # Contra-Asset

#     # Liabilities
#     {"code": "2000", "name": "Liabilities", "account_type_classification": "LIABILITY", "parent_code": None, "is_control_account": True},
#         {"code": "2100", "name": "Current Liabilities", "account_type_classification": "LIABILITY", "parent_code": "2000", "is_control_account": True},
#             {"code": "2110", "name": "Accounts Payable", "account_type_classification": "LIABILITY", "parent_code": "2100", "is_control_account": True},
#                 {"code": "2111", "name": "AP - General Suppliers", "account_type_classification": "LIABILITY", "parent_code": "2110", "is_control_account": False},
#             {"code": "2200", "name": "Unearned Revenue (Fees Paid in Advance)", "account_type_classification": "LIABILITY", "parent_code": "2100", "is_control_account": False},
#             {"code": "2300", "name": "Salaries Payable", "account_type_classification": "LIABILITY", "parent_code": "2100", "is_control_account": False},
#         {"code": "2500", "name": "Non-Current Liabilities", "account_type_classification": "LIABILITY", "parent_code": "2000", "is_control_account": True},
#             {"code": "2510", "name": "Long-Term Loans", "account_type_classification": "LIABILITY", "parent_code": "2500", "is_control_account": False},

#     # Equity
#     {"code": "3000", "name": "Equity", "account_type_classification": "EQUITY", "parent_code": None, "is_control_account": True},
#         {"code": "3100", "name": "Owner's Capital / Contributed Capital", "account_type_classification": "EQUITY", "parent_code": "3000", "is_control_account": False},
#         {"code": "3200", "name": "Retained Earnings", "account_type_classification": "EQUITY", "parent_code": "3000", "is_control_account": False},
#         {"code": "3300", "name": "Owner's Drawings", "account_type_classification": "EQUITY", "parent_code": "3000", "is_control_account": False}, # Contra-Equity
#         # Current Year Earnings is often a system-calculated account not directly posted to by users
#         {"code": "3900", "name": "Current Year Earnings", "account_type_classification": "EQUITY", "parent_code": "3000", "is_control_account": False},

#     # Revenue/Income
#     {"code": "4000", "name": "Operating Revenue", "account_type_classification": "REVENUE", "parent_code": None, "is_control_account": True},
#         {"code": "4010", "name": "Tuition Fee Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4020", "name": "Application Fee Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4030", "name": "Library Services Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4040", "name": "Sports Activities Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4050", "name": "Examination Fee Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4060", "name": "Transportation Fee Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4800", "name": "Other Operating Income", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False},
#         {"code": "4900", "name": "Discounts on Fees", "account_type_classification": "REVENUE", "parent_code": "4000", "is_control_account": False}, # Contra-Revenue

#     # Cost of Goods Sold (if applicable, e.g., for selling uniforms, books)
#     {"code": "5000", "name": "Cost of Goods Sold", "account_type_classification": "COGS", "parent_code": None, "is_control_account": True},
#         {"code": "5010", "name": "Cost of Uniforms Sold", "account_type_classification": "COGS", "parent_code": "5000", "is_control_account": False},

#     # Expenses
#     {"code": "6000", "name": "Operating Expenses", "account_type_classification": "EXPENSE", "parent_code": None, "is_control_account": True}, # Changed from 5000 to avoid clash with COGS
#         {"code": "6100", "name": "Salaries and Wages", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6200", "name": "Rent Expense", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6300", "name": "Utilities Expense", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6310", "name": "Electricity", "account_type_classification": "EXPENSE", "parent_code": "6300", "is_control_account": False},
#         {"code": "6320", "name": "Water", "account_type_classification": "EXPENSE", "parent_code": "6300", "is_control_account": False},
#         {"code": "6330", "name": "Internet & Telephone", "account_type_classification": "EXPENSE", "parent_code": "6300", "is_control_account": False},
#         {"code": "6400", "name": "Teaching Supplies & Materials", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6500", "name": "Repairs and Maintenance", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6600", "name": "Bank Service Charges", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6700", "name": "Insurance Expense", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
#         {"code": "6800", "name": "Depreciation Expense", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False}, # Often posted via year-end adjustments
#         {"code": "6900", "name": "Miscellaneous Expenses", "account_type_classification": "EXPENSE", "parent_code": "6000", "is_control_account": False},
# ]

# You can add more specific account types if needed, e.g. for different types of assets
# For example, if 'Cash' and 'Bank' are distinct AccountTypes rather than just Accounts under 'Asset' type.
# If they are distinct AccountTypes, then the 'account_type_name' in STANDARD_CHART_OF_ACCOUNTS
# would need to reference those specific names (e.g., 'Cash', 'Bank') and you'd ensure those AccountTypes exist.
# For this example, we assume broader types like "Asset" and specific accounts are created under them.
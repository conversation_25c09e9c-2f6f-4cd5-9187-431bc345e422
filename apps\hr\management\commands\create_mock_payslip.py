# D:\school_fees_saas_v2\apps\hr\management\commands\create_mock_payslip.py

import logging
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django_tenants.utils import schema_context
from django.utils import timezone
from datetime import date

# Import necessary models
from apps.tenants.models import School
from apps.schools.models import StaffUser
from apps.hr.models import PayrollRun, Payslip

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Creates a sample PayrollRun and a Payslip for a specific staff member.'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name of the tenant.')
        parser.add_argument('staff_email', type=str, help='The email of the staff member to create the payslip for.')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        staff_email = options['staff_email']

        try:
            tenant = School.objects.get(schema_name=schema_name)
        except School.DoesNotExist:
            raise CommandError(f'Tenant with schema "{schema_name}" does not exist.')

        with schema_context(tenant.schema_name):
            self.stdout.write(self.style.SUCCESS(f'--- Processing tenant: {tenant.name} ---'))

            # Find the staff member
            try:
                staff_member = StaffUser.objects.get(email=staff_email)
                self.stdout.write(f"Found staff member: {staff_member.get_full_name()}")
            except StaffUser.DoesNotExist:
                raise CommandError(f"Staff member with email '{staff_email}' not found in tenant '{schema_name}'.")

            # 1. Create or get a PayrollRun for the current month
            today = timezone.now().date()
            first_day_of_month = today.replace(day=1)
            # A simple way to get the last day of the month
            next_month = first_day_of_month.replace(day=28) + timezone.timedelta(days=4)
            last_day_of_month = next_month - timezone.timedelta(days=next_month.day)

            payroll_run, created = PayrollRun.objects.get_or_create(
                pay_period_start=first_day_of_month,
                pay_period_end=last_day_of_month,
                defaults={
                    'payment_date': last_day_of_month,
                    'status': PayrollRun.Status.PROCESSED,
                    'processed_at': timezone.now()
                }
            )

            if created:
                self.stdout.write(f"CREATED new PayrollRun for {first_day_of_month.strftime('%B %Y')}.")
            else:
                self.stdout.write(f"USING existing PayrollRun for {first_day_of_month.strftime('%B %Y')}.")
            
            # 2. Create the sample Payslip object
            # This is where you would normally have complex logic to get these values
            # from the StaffSalaryStructure, attendance, etc. For now, we hardcode them.
            
            payslip_data = {
                'payroll_run': payroll_run,
                'staff_member': staff_member,
                'basic_salary': Decimal('5000.00'),
                'allowances': Decimal('750.00'),
                'bonuses': Decimal('250.00'),
                'tax_deductions': Decimal('650.00'),
                'pension_deductions': Decimal('400.00'),
                'loan_repayments': Decimal('150.00'),
                'other_deductions': Decimal('50.00'),
                'notes': "Sample payslip generated by management command."
            }
            
            # Using get_or_create to avoid duplicating payslips on re-run
            payslip, created = Payslip.objects.get_or_create(
                payroll_run=payroll_run,
                staff_member=staff_member,
                defaults=payslip_data
            )

            if created:
                # The save method on the payslip automatically calculates totals
                self.stdout.write(self.style.SUCCESS(f"SUCCESS: Created new payslip (PK: {payslip.pk}) for {staff_member.get_full_name()}."))
                self.stdout.write(f"  -> Gross Earnings: {payslip.gross_earnings}")
                self.stdout.write(f"  -> Total Deductions: {payslip.total_deductions}")
                self.stdout.write(f"  -> Net Pay: {payslip.net_pay}")
            else:
                self.stdout.write(self.style.WARNING(f"Payslip for {staff_member.get_full_name()} for this period already exists (PK: {payslip.pk}). No changes made."))

            self.stdout.write(self.style.SUCCESS("\nSample data created. You can now generate the PDF."))
            self.stdout.write(f"Test URL: http://{tenant.domains.first().domain}:8000/hr/payslips/{payslip.pk}/pdf/")
            
            

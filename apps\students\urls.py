# D:\school_fees_saas_v2\apps\students\urls.py
from django.urls import path
from . import views

app_name = 'students'

urlpatterns = [
    path('', views.StudentListView.as_view(), name='student_list'),
    path('new/', views.StudentCreateView.as_view(), name='student_create'),
    path('<int:pk>/', views.StudentDetailView.as_view(), name='student_detail'),
    path('<int:pk>/edit/', views.StudentUpdateView.as_view(), name='student_update'),
    path('<int:pk>/delete/', views.StudentDeleteView.as_view(), name='student_delete'),
    # URL for AJAX call to load sections (if using JS for cascading dropdowns)
    # This should ideally be in schools/urls.py if the view is there.
    # For now, we can add it here if the view is in students/views.py
    # path('ajax/load-sections/', views.load_sections_for_class, name='ajax_load_sections'),
    
    # POTENTIALLY MISSING OR MISNAMED URL:
    path('bulk-delete/', views.student_bulk_delete_view, name='bulk_delete'),
    path('<int:pk>/manage-parents/', views.StudentManageParentsView.as_view(), name='student_manage_parents'),
    
    path('parents/', views.ParentUserListView.as_view(), name='parentuser_list'),
    path('parents/add/', views.ParentUserCreateView.as_view(), name='parentuser_create'),
    path('parents/<int:pk>/update/', views.ParentUserUpdateView.as_view(), name='parentuser_update'),

    # Parent Import/Export URLs
    path('parents/import/', views.ParentUserImportView.as_view(), name='import_parents'),
    path('parents/import/template/', views.DownloadParentImportTemplateView.as_view(), name='download_parent_import_template'),

    # Student Import/Export URLs
    path('import/', views.StudentImportView.as_view(), name='import_students'),
    path('import/template/', views.DownloadImportTemplateView.as_view(), name='download_import_template'),
]
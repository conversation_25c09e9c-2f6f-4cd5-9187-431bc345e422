{# D:\school_fees_saas_v2\apps\schools\templates\schools\schoolclass_detail.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ view_title }}</h1>
            <p class="text-muted mb-0">{% trans "View class details and manage its sections." %}</p>
        </div>
        <div>
            <a href="{% url 'schools:class_list' %}" class="btn btn-secondary btn-sm">
                <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Back to All Classes" %}
            </a>
        </div>
    </div>

    {% include "partials/_messages.html" %}

    <div class="row">
        {# Column for Class Details #}
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Class Information" %}</h5>
                </div>
                <div class="card-body">
                    <p><strong>{% trans "Name" %}:</strong> {{ school_class.name }}</p>
                    <p><strong>{% trans "Description" %}:</strong> {{ school_class.description|default:"N/A" }}</p>
                    {# Add other SchoolClass fields here if any #}
                    <hr>
                    <a href="{% url 'schools:class_update' pk=school_class.pk %}" class="btn btn-primary btn-sm">
                        <i class="bi bi-pencil-square me-1"></i> {% trans "Edit Class Details" %}
                    </a>
                </div>
            </div>
        </div>
        
        {# Column for Sections #}
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "Sections in this Class" %}</h5>
                    <a href="{% url 'schools:section_create' class_pk=school_class.pk %}" class="btn btn-success btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> {% trans "Add New Section" %}
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if sections %}
                        <ul class="list-group list-group-flush">
                            {% for section in sections %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>
                                        <strong>{{ section.name }}</strong>
                                        {% if section.class_teacher %}
                                            <small class="text-muted ms-2"> - {% trans "Teacher" %}: {{ section.class_teacher.get_full_name }}</small>
                                        {% endif %}
                                    </span>
                                    <span>
                                        <a href="{% url 'schools:section_update' class_pk=school_class.pk pk=section.pk %}" class="btn btn-sm btn-outline-secondary py-0 px-1" title="Edit Section"><i class="bi bi-pencil"></i></a>
                                        <a href="{% url 'schools:section_delete' class_pk=school_class.pk pk=section.pk %}" class="btn btn-sm btn-outline-danger py-0 px-1" title="Delete Section"><i class="bi bi-trash"></i></a>
                                    </span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="p-4 text-center text-muted">
                            <p>{% trans "No sections have been created for this class yet." %}</p>
                            <a href="{% url 'schools:section_create' class_pk=school_class.pk %}" class="btn btn-primary">{% trans "Create the first one" %}</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



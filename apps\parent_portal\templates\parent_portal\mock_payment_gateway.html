{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\mock_payment_gateway.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Secure Payment Simulation") }}{% endblock %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .premium-payment-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .premium-payment-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border-radius: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
            max-width: 700px;
            margin: 0 auto;
        }

        .premium-payment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
        }

        .premium-payment-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .premium-payment-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .premium-payment-header h4 {
            position: relative;
            z-index: 2;
            margin: 0;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .premium-payment-body {
            padding: 3rem;
            background: white;
        }

        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-floating > .form-control[readonly] {
            background-color: #f8f9fa;
            opacity: 0.8;
            cursor: not-allowed;
        }

        .form-floating > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1rem 0.75rem;
            pointer-events: none;
            border: 2px solid transparent;
            transform-origin: 0 0;
            transition: all 0.3s ease;
            color: #6c757d;
            font-weight: 500;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            color: #667eea;
        }

        .invoice-summary {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .invoice-summary h6 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .invoice-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            font-size: 0.95rem;
        }

        .invoice-item:last-child {
            border-bottom: none;
        }

        .total-amount {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
            text-align: center;
            margin-top: 1rem;
        }

        .btn-premium-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 1rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .btn-premium-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
            color: white;
            text-decoration: none;
        }

        .btn-premium-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 1rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .btn-premium-danger:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.6);
            color: white;
            text-decoration: none;
        }

        .btn-premium-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 1rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
            color: white;
            text-decoration: none;
            display: inline-block;
        }

        .btn-premium-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.6);
            color: white;
            text-decoration: none;
        }

        .security-notice {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 2px solid #b8daff;
            border-radius: 1rem;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
            color: #0c5460;
        }

        .security-notice i {
            color: #17a2b8;
            font-size: 1.2rem;
        }

        .alert {
            border-radius: 1rem;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .card-details-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .card-details-section h5 {
            color: #667eea;
            font-weight: 600;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .simulation-buttons {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
        }

        .simulation-buttons p {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
    </style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="premium-payment-container">
    <div class="container">
        <div class="premium-payment-card">
            <div class="premium-payment-header">
                <h4><i class="fas fa-shield-alt me-3"></i>{{ view_title|default:"Secure Payment Simulation" }}</h4>
            </div>
            <div class="premium-payment-body">
                {% if payment_error %}
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "There was an issue with your payment session. Please return to select invoices again." %}
                    </div>
                    <div class="text-center">
                        <a href="{% url 'parent_portal:select_invoices_for_payment' %}" class="btn-premium-primary">
                            <i class="fas fa-file-invoice me-2"></i>{% trans "Select Invoices" %}
                        </a>
                    </div>
                {% elif invoice_details %}
                    <div class="security-notice">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "You are about to make a simulated payment for the following invoices" %}
                    </div>

                    <div class="invoice-summary">
                        <h6><i class="fas fa-file-invoice me-2"></i>{% trans "Selected Invoices:" %}</h6>
                        {% for inv in invoice_details %}
                        <div class="invoice-item">
                            <span><strong>{{ inv.number }}</strong></span>
                            <span class="text-end">{{ currency }}{{ inv.amount_due|floatformat:2|intcomma }}</span>
                        </div>
                        {% endfor %}

                        <div class="total-amount">
                            <i class="fas fa-calculator me-2"></i>
                            {% trans "Total Amount:" %} {{ currency }}{{ total_amount_to_pay|floatformat:2|intcomma }}
                        </div>
                    </div>

                    <div class="card-details-section">
                        <h5><i class="fas fa-credit-card me-2"></i>{% trans "Simulated Card Details" %}</h5>
                        <div class="security-notice">
                            <i class="fas fa-shield-alt me-2"></i>
                            {% trans "This is a mock payment form. No real card details are processed or stored." %}
                        </div>

                        <form method="post" action="{% url 'parent_portal:process_mock_payment' %}">
                            {% csrf_token %}

                            <!-- Card Number -->
                            <div class="form-floating">
                                <input type="text"
                                       class="form-control"
                                       id="mockCardNumber"
                                       value="4242 4242 4242 4242"
                                       readonly
                                       placeholder="Card Number">
                                <label for="mockCardNumber">
                                    <i class="fas fa-credit-card me-2"></i>{% trans "Card Number" %}
                                </label>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Expiry Date -->
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control"
                                               id="mockExpiry"
                                               value="12/28"
                                               readonly
                                               placeholder="MM/YY">
                                        <label for="mockExpiry">
                                            <i class="fas fa-calendar-alt me-2"></i>{% trans "Expiry (MM/YY)" %}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <!-- CVC -->
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control"
                                               id="mockCVC"
                                               value="123"
                                               readonly
                                               placeholder="CVC">
                                        <label for="mockCVC">
                                            <i class="fas fa-lock me-2"></i>{% trans "CVC" %}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Cardholder Name -->
                            <div class="form-floating">
                                <input type="text"
                                       class="form-control"
                                       id="mockCardName"
                                       value="{{ parent.get_full_name|default:parent.email }}"
                                       readonly
                                       placeholder="Name on Card">
                                <label for="mockCardName">
                                    <i class="fas fa-user me-2"></i>{% trans "Name on Card" %}
                                </label>
                            </div>

                            <div class="simulation-buttons">
                                <p><i class="fas fa-cogs me-2"></i>{% trans "For simulation purposes, choose the outcome:" %}</p>
                                <div class="d-flex flex-column flex-md-row justify-content-center align-items-center">
                                    <button type="submit" name="payment_outcome" value="success" class="btn-premium-success">
                                        <i class="fas fa-check-circle me-2"></i>{% trans "Simulate Successful Payment" %}
                                    </button>
                                    <button type="submit" name="payment_outcome" value="failure" class="btn-premium-danger">
                                        <i class="fas fa-times-circle me-2"></i>{% trans "Simulate Failed Payment" %}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                {% else %}
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "No payment details found in your session. Please select invoices to pay first." %}
                    </div>
                    <div class="text-center">
                        <a href="{% url 'parent_portal:select_invoices_for_payment' %}" class="btn-premium-primary">
                            <i class="fas fa-file-invoice me-2"></i>{% trans "Select Invoices" %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submission handling
    const form = document.querySelector('form');
    const submitButtons = document.querySelectorAll('button[type="submit"]');

    if (form && submitButtons.length > 0) {
        submitButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                // Prevent double submission
                submitButtons.forEach(function(btn) {
                    btn.disabled = true;
                });

                // Update button text based on outcome
                const outcome = this.value;
                if (outcome === 'success') {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Processing Successful Payment..." %}';
                } else {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Processing Failed Payment..." %}';
                }

                // Re-enable buttons after 10 seconds as fallback
                setTimeout(function() {
                    submitButtons.forEach(function(btn) {
                        btn.disabled = false;
                        if (btn.value === 'success') {
                            btn.innerHTML = '<i class="fas fa-check-circle me-2"></i>{% trans "Simulate Successful Payment" %}';
                        } else {
                            btn.innerHTML = '<i class="fas fa-times-circle me-2"></i>{% trans "Simulate Failed Payment" %}';
                        }
                    });
                }, 10000);
            });
        });
    }

    // Add loading animation to card
    const card = document.querySelector('.premium-payment-card');
    if (card) {
        card.style.animation = 'fadeInUp 0.6s ease-out';
    }

    // Add hover effects to invoice items
    const invoiceItems = document.querySelectorAll('.invoice-item');
    invoiceItems.forEach(function(item) {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
            this.style.transform = 'translateX(5px)';
            this.style.transition = 'all 0.3s ease';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
            this.style.transform = 'translateX(0)';
        });
    });

    // Security notice animation
    const securityNotices = document.querySelectorAll('.security-notice');
    securityNotices.forEach(function(notice, index) {
        notice.style.animationDelay = (index * 0.2) + 's';
        notice.style.animation = 'fadeInUp 0.6s ease-out forwards';
    });

    // Add pulse animation to total amount
    const totalAmount = document.querySelector('.total-amount');
    if (totalAmount) {
        totalAmount.addEventListener('mouseenter', function() {
            this.style.animation = 'pulse 1s infinite';
        });

        totalAmount.addEventListener('mouseleave', function() {
            this.style.animation = 'none';
        });
    }
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
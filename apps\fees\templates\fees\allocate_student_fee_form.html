{# D:\school_fees_saas_v2\templates\fees\allocate_student_fee_form.html #}
{% extends "tenant_base.html" %}
{% load core_tags widget_tweaks %} {# Assuming you use widget_tweaks #}

{% block title %}{{ view_title|default:"Allocate Fee Structure" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Allocate Fee Structure" }}</h1>
    
    {# Display student information #}
    {% if student %}
        <div class="alert alert-info">
            <h5 class="alert-heading">Allocating for Student: {{ student.full_name }}</h5>
            <p class="mb-1">Admission No: {{ student.admission_number }}</p>
            <p class="mb-0">Current Class: {{ student.current_class.name|default:"N/A" }} {% if student.current_section %}- {{ student.current_section.name }}{% endif %}</p>
        </div>
    {% endif %}

    {% include "partials/_messages.html" %}

    <form method="post" novalidate>
        {% csrf_token %}

        {# Render form fields. Using as_p for simplicity, or render manually #}
        {# {{ form.as_p }} #}

        {# Manual Rendering with Bootstrap for better control #}
        {% if form.student and form.student.is_hidden %}
            {{ form.student }} {# Render hidden student field if pre-filled and hidden by form #}
        {% elif form.student %} {# Only show student dropdown if not pre-filled from view #}
            <div class="mb-3">
                <label for="{{ form.student.id_for_label }}" class="form-label">{{ form.student.label }}</label>
                {% render_field form.student class+="form-select form-select-sm student-select" %}
                {% for error in form.student.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>
        {% endif %}

        <div class="mb-3">
            <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
            {% render_field form.academic_year class+="form-select form-select-sm" id="id_academic_year_alloc" %} {# Added ID for potential JS #}
            {% for error in form.academic_year.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>

        <div class="mb-3">
            <label for="{{ form.fee_structure.id_for_label }}" class="form-label">{{ form.fee_structure.label }}</label>
            {% render_field form.fee_structure class+="form-select form-select-sm" id="id_fee_structure_alloc" %} {# Added ID for potential JS #}
            <small class="form-text text-muted">{{ form.fee_structure.help_text }}</small>
            {% for error in form.fee_structure.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>

        <div class="mb-3">
            <label for="{{ form.term.id_for_label }}" class="form-label">{{ form.term.label }}</label>
            {% render_field form.term class+="form-select form-select-sm" id="id_term_alloc" %}
            <small class="form-text text-muted">{{ form.term.help_text }}</small>
            {% for error in form.term.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>
        
        {# Description/Notes field from StudentFeeAllocationForm if it exists #}
        {% if form.description %}
        <div class="mb-3">
            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
            {% render_field form.description class+="form-control form-control-sm" rows="3" %}
            {% for error in form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>
        {% endif %}


        {% if form.non_field_errors %}
            <div class="alert alert-danger mt-3">
                {% for error in form.non_field_errors %}
                    {{ error }}<br>
                {% endfor %}
            </div>
        {% endif %}

        <div class="mt-4">
            <button type="submit" class="btn btn-success">Allocate Fee Structure</button>
            {% if student %}
                <a href="{% url 'students:student_detail' student.pk %}" class="btn btn-secondary ms-2">Cancel & Back to Student</a>
            {% else %}
                <a href="#" onclick="window.history.back(); return false;" class="btn btn-secondary ms-2">Cancel</a>
            {% endif %}
        </div>
    </form>

    {# Display existing allocations for this student (passed from view context) #}
    {% if allocated_structures %}
        <hr class="my-4">
        <h4>Currently Allocated Structures for {{ student.full_name }}</h4>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead class="table-light">
                    <tr>
                        <th>Academic Year</th>
                        <th>Fee Structure</th>
                        <th>Term</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for allocation in allocated_structures %}
                    <tr>
                        <td>{{ allocation.academic_year.name }}</td>
                        <td>{{ allocation.fee_structure.name }}</td>
                        <td>{{ allocation.term.name|default:"N/A (Full Year)" }}</td>
                        <td>{{ allocation.description|default:"-" }}</td>
                        <td>
                            {# Add delete button/form for allocation later if needed #}
                            {# <form action="{% url 'fees:student_fee_allocation_delete' allocation.pk %}" method="post" style="display:inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-xs btn-danger" onclick="return confirm('Remove this allocation?');">Remove</button>
                            </form> #}
                            <small class="text-muted">Manage Later</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

</div>
{% endblock %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const academicYearSelect = document.getElementById('id_academic_year_alloc');
    const feeStructureSelect = document.getElementById('id_fee_structure_alloc');
    const termSelect = document.getElementById('id_term_alloc');

    if (academicYearSelect && feeStructureSelect) {
        // Store all fee structure options
        const allFeeStructureOptions = Array.from(feeStructureSelect.options);
        
        function filterFeeStructures() {
            const selectedYearId = academicYearSelect.value;
            let currentFeeStructureValue = feeStructureSelect.value; // Preserve selection if possible
            feeStructureSelect.innerHTML = ''; // Clear current options

            allFeeStructureOptions.forEach(function(option) {
                // Assuming option.text is like "Structure Name (Academic Year Name)"
                // Or we need a data attribute on the option for the year ID.
                // For robust filtering, the FeeStructureForm should populate options
                // with a data-academic-year-id attribute.
                // Simple text match for now (less reliable):
                if (!selectedYearId || option.text.includes(academicYearSelect.options[academicYearSelect.selectedIndex].text.split(' (')[0])) {
                     // This simple text match won't work if AcademicYear name is part of structure name.
                     // Let's assume for now the view already filters structures based on student's class if any.
                     // For this JS, we'd ideally have data-ay-id on options.
                    feeStructureSelect.appendChild(option.cloneNode(true));
                }
            });
            // Try to reselect previous value if it's still valid
            if (Array.from(feeStructureSelect.options).find(opt => opt.value === currentFeeStructureValue)) {
                feeStructureSelect.value = currentFeeStructureValue;
            }
            filterTerms(); // Also filter terms when academic year changes
        }

        function filterTerms() {
            // Similar logic for terms based on selected academic_year or fee_structure's academic_year
            // This part is more complex if terms are tied to fee_structure's specific academic year.
            // For now, let's assume terms are just filtered by the main Academic Year select.
            const selectedYearId = academicYearSelect.value;
             const allTermOptions = Array.from(termSelect.options); // Assuming termSelect has all terms initially
            let currentTermValue = termSelect.value;
            termSelect.innerHTML = '';

            allTermOptions.forEach(function(option) {
                 if (!selectedYearId || option.dataset.academicYearId === selectedYearId || option.value === "") { // Check for data-ay-id or empty
                    termSelect.appendChild(option.cloneNode(true));
                }
            });
            if (Array.from(termSelect.options).find(opt => opt.value === currentTermValue)) {
                termSelect.value = currentTermValue;
            }
        }


        if (academicYearSelect) academicYearSelect.addEventListener('change', filterFeeStructures);
        // Initial filter if needed (though server-side form __init__ is better for this)
        // filterFeeStructures();
    }
});
</script>
{% endblock %}



from django.shortcuts import redirect
from django.http import HttpRequest, HttpResponse
from django.views import View
from django.contrib import messages

class SetGlobalAcademicYearView(View):
    def post(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        academic_year_id = request.POST.get('academic_year', None)
        
        if academic_year_id and academic_year_id.isdigit():
            request.session['selected_global_academic_year_pk'] = int(academic_year_id)
            messages.info(request, "Global academic year filter has been set.")
        else:
            # If an empty value is submitted, clear the session variable
            if 'selected_global_academic_year_pk' in request.session:
                del request.session['selected_global_academic_year_pk']
                messages.info(request, "Global academic year filter has been cleared.")

        # Redirect back to the page the user came from
        # Using HTTP_REFERER is common but can be unreliable; have a fallback.
        fallback_url = reverse_lazy('schools:dashboard') # Or another sensible default
        redirect_url = request.META.get('HTTP_REFERER', fallback_url)
        
        return redirect(redirect_url)
    
    
    
{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\systemnotification_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("System Notification") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .form-check {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .form-check-input {
            width: 1.5rem;
            height: 1.5rem;
            margin-top: 0.125rem;
            border: 2px solid #667eea;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .fieldset-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            margin: 2rem 0 1.5rem 0;
            text-align: center;
        }

        .fieldset-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .notification-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 2px solid #bbdefb;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .notification-info h6 {
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .notification-info p {
            color: #424242;
            margin: 0;
            font-size: 0.9rem;
        }

        .select2-container {
            margin-bottom: 1.5rem;
        }

        .select2-container .select2-selection--multiple {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 0.5rem;
            min-height: 3.5rem;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .select2-label {
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: block;
        }
    </style>
{% endblock %}
















{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("System Notification") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:systemnotification_list' %}">{% trans "Notifications" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}{% trans "Edit Notification" %}{% else %}{% trans "New Notification" %}{% endif %}
            </li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-bell-fill me-2"></i>
                        {% if object %}{% trans "Edit System Notification" %}{% else %}{% trans "Create System Notification" %}{% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Information Section -->
                    <div class="notification-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "System Notification Information" %}</h6>
                        <p>{% trans "System notifications are displayed to platform users and administrators. Configure the target audience, timing, and content to effectively communicate important information." %}</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        <!-- Notification Content Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-chat-text me-2"></i>{% trans "Notification Content" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if form.title.errors %} is-invalid{% endif %}"
                                           id="{{ form.title.id_for_label }}"
                                           name="{{ form.title.name }}"
                                           value="{{ form.title.value|default:'' }}"
                                           placeholder="{% trans 'Enter notification title' %}"
                                           {% if form.title.field.required %}required{% endif %}>
                                    <label for="{{ form.title.id_for_label }}">
                                        <i class="bi bi-card-heading icon-input"></i>{% trans "Notification Title" %}
                                    </label>
                                    {% if form.title.help_text %}
                                        <div class="form-text">{{ form.title.help_text }}</div>
                                    {% endif %}
                                    {% if form.title.errors %}
                                        <div class="invalid-feedback">{{ form.title.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Notification Type Field -->
                                <div class="form-floating">
                                    <select class="form-select{% if form.notification_type.errors %} is-invalid{% endif %}"
                                            id="{{ form.notification_type.id_for_label }}"
                                            name="{{ form.notification_type.name }}"
                                            {% if form.notification_type.field.required %}required{% endif %}>
                                        <option value="">{% trans "Select Type" %}</option>
                                        {% for value, label in form.notification_type.field.choices %}
                                            <option value="{{ value }}" {% if form.notification_type.value == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="{{ form.notification_type.id_for_label }}">
                                        <i class="bi bi-tag-fill icon-input"></i>{% trans "Notification Type" %}
                                    </label>
                                    {% if form.notification_type.help_text %}
                                        <div class="form-text">{{ form.notification_type.help_text }}</div>
                                    {% endif %}
                                    {% if form.notification_type.errors %}
                                        <div class="invalid-feedback">{{ form.notification_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Message Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if form.message.errors %} is-invalid{% endif %}"
                                              id="{{ form.message.id_for_label }}"
                                              name="{{ form.message.name }}"
                                              placeholder="{% trans 'Enter notification message' %}"
                                              style="height: 120px;"
                                              {% if form.message.field.required %}required{% endif %}>{{ form.message.value|default:'' }}</textarea>
                                    <label for="{{ form.message.id_for_label }}">
                                        <i class="bi bi-chat-square-text icon-input"></i>{% trans "Notification Message" %}
                                    </label>
                                    {% if form.message.help_text %}
                                        <div class="form-text">{{ form.message.help_text }}</div>
                                    {% endif %}
                                    {% if form.message.errors %}
                                        <div class="invalid-feedback">{{ form.message.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Scheduling Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-calendar-event me-2"></i>{% trans "Scheduling & Timing" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Publish Date Field -->
                                <div class="form-floating">
                                    <input type="datetime-local"
                                           class="form-control{% if form.publish_date.errors %} is-invalid{% endif %}"
                                           id="{{ form.publish_date.id_for_label }}"
                                           name="{{ form.publish_date.name }}"
                                           value="{{ form.publish_date.value|default:'' }}"
                                           {% if form.publish_date.field.required %}required{% endif %}>
                                    <label for="{{ form.publish_date.id_for_label }}">
                                        <i class="bi bi-calendar-plus icon-input"></i>{% trans "Publish Date" %}
                                    </label>
                                    {% if form.publish_date.help_text %}
                                        <div class="form-text">{{ form.publish_date.help_text }}</div>
                                    {% endif %}
                                    {% if form.publish_date.errors %}
                                        <div class="invalid-feedback">{{ form.publish_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Expires At Field -->
                                <div class="form-floating">
                                    <input type="datetime-local"
                                           class="form-control{% if form.expires_at.errors %} is-invalid{% endif %}"
                                           id="{{ form.expires_at.id_for_label }}"
                                           name="{{ form.expires_at.name }}"
                                           value="{{ form.expires_at.value|default:'' }}"
                                           {% if form.expires_at.field.required %}required{% endif %}>
                                    <label for="{{ form.expires_at.id_for_label }}">
                                        <i class="bi bi-calendar-x icon-input"></i>{% trans "Expires At (Optional)" %}
                                    </label>
                                    {% if form.expires_at.help_text %}
                                        <div class="form-text">{{ form.expires_at.help_text }}</div>
                                    {% endif %}
                                    {% if form.expires_at.errors %}
                                        <div class="invalid-feedback">{{ form.expires_at.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Targeting Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-people-fill me-2"></i>{% trans "Target Audience" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <!-- Target Users Field (Select2) -->
                                <label class="select2-label" for="{{ form.target_users.id_for_label }}">
                                    <i class="bi bi-person-check icon-input"></i>{% trans "Target Specific Platform Users (Optional)" %}
                                </label>
                                <select class="form-select select2-multiple{% if form.target_users.errors %} is-invalid{% endif %}"
                                        id="{{ form.target_users.id_for_label }}"
                                        name="{{ form.target_users.name }}"
                                        multiple
                                        data-placeholder="{% trans 'Select specific users (leave empty for all users)' %}">
                                    {% for value, label in form.target_users.field.choices %}
                                        <option value="{{ value }}" {% if value in form.target_users.value %}selected{% endif %}>{{ label }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.target_users.help_text %}
                                    <div class="form-text">{{ form.target_users.help_text }}</div>
                                {% endif %}
                                {% if form.target_users.errors %}
                                    <div class="invalid-feedback">{{ form.target_users.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                <!-- Is Active Field -->
                                <div class="form-check">
                                    <input type="checkbox"
                                           class="form-check-input{% if form.is_active.errors %} is-invalid{% endif %}"
                                           id="{{ form.is_active.id_for_label }}"
                                           name="{{ form.is_active.name }}"
                                           value="1"
                                           {% if form.is_active.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="bi bi-toggle-on me-2"></i>{% trans "Active Notification" %}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <div class="form-text">{{ form.is_active.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'platform_management:systemnotification_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}{% trans "Update Notification" %}{% else %}{% trans "Create Notification" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Select2 for target users
        $('.select2-multiple').select2({
            theme: "bootstrap-5",
            width: '100%',
            placeholder: function() {
                return $(this).data('placeholder') || "{% trans 'Select options' %}";
            },
            allowClear: true,
            closeOnSelect: false
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation and enhancement
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');
        const publishDateInput = document.getElementById('{{ form.publish_date.id_for_label }}');
        const expiresAtInput = document.getElementById('{{ form.expires_at.id_for_label }}');

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Date validation - expires_at should be after publish_date
        function validateDates() {
            if (publishDateInput.value && expiresAtInput.value) {
                const publishDate = new Date(publishDateInput.value);
                const expiresDate = new Date(expiresAtInput.value);

                if (expiresDate <= publishDate) {
                    expiresAtInput.setCustomValidity('{% trans "Expiry date must be after publish date" %}');
                    expiresAtInput.classList.add('is-invalid');
                } else {
                    expiresAtInput.setCustomValidity('');
                    expiresAtInput.classList.remove('is-invalid');
                }
            }
        }

        if (publishDateInput && expiresAtInput) {
            publishDateInput.addEventListener('change', validateDates);
            expiresAtInput.addEventListener('change', validateDates);
        }

        // Set default publish date to current time if empty
        if (publishDateInput && !publishDateInput.value) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            publishDateInput.value = localDateTime;
        }

        // Notification type change handler for styling
        const notificationTypeSelect = document.getElementById('{{ form.notification_type.id_for_label }}');
        if (notificationTypeSelect) {
            notificationTypeSelect.addEventListener('change', function() {
                const infoSection = document.querySelector('.notification-info');
                const selectedType = this.value.toLowerCase();

                // Change info section styling based on notification type
                if (selectedType.includes('urgent') || selectedType.includes('critical')) {
                    infoSection.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
                    infoSection.style.borderColor = '#dc3545';
                } else if (selectedType.includes('warning')) {
                    infoSection.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
                    infoSection.style.borderColor = '#ffc107';
                } else {
                    infoSection.style.background = 'linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)';
                    infoSection.style.borderColor = '#bbdefb';
                }
            });
        }

        // Auto-focus first input
        const firstInput = form.querySelector('input[type="text"], textarea');
        if (firstInput) {
            firstInput.focus();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // Character counter for message field
        const messageField = document.getElementById('{{ form.message.id_for_label }}');
        if (messageField) {
            const maxLength = 500; // Adjust as needed
            const counter = document.createElement('div');
            counter.className = 'form-text text-end';
            counter.style.fontSize = '0.8rem';

            function updateCounter() {
                const remaining = maxLength - messageField.value.length;
                counter.textContent = `${messageField.value.length}/${maxLength} characters`;
                counter.style.color = remaining < 50 ? '#dc3545' : '#6c757d';
            }

            messageField.addEventListener('input', updateCounter);
            messageField.parentElement.appendChild(counter);
            updateCounter();
        }
    });
    </script>
{% endblock %}

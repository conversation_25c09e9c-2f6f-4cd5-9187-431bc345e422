<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Reminder - {{ school_name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .invoice-details {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .amount-due {
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
        }
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ school_name }}</h1>
        <h2>Payment Reminder</h2>
    </div>

    <p>Dear Parent/Guardian,</p>

    <p>This is a friendly reminder that payment is due for the following invoice:</p>

    <div class="invoice-details">
        <h3>Invoice Details</h3>
        <p><strong>Student:</strong> {{ student_name }}</p>
        <p><strong>Invoice Number:</strong> #{{ invoice.invoice_number_display }}</p>
        <p><strong>Issue Date:</strong> {{ invoice.issue_date|date:"F d, Y" }}</p>
        <p><strong>Due Date:</strong> {{ due_date|date:"F d, Y" }}
            {% if is_overdue %}
                <span class="overdue">(OVERDUE)</span>
            {% endif %}
        </p>
        <p><strong>Original Amount:</strong> ${{ invoice.total_amount|floatformat:2 }}</p>
        <p><strong>Amount Paid:</strong> ${{ invoice.amount_paid|floatformat:2 }}</p>
    </div>

    <div class="amount-due">
        Outstanding Balance: ${{ balance_due|floatformat:2 }}
    </div>

    {% if is_overdue %}
    <p style="color: #dc3545; font-weight: bold;">
        ⚠️ This invoice is now overdue. Please arrange payment as soon as possible to avoid any late fees or service interruptions.
    </p>
    {% else %}
    <p>
        Please arrange payment by the due date to avoid any late fees.
    </p>
    {% endif %}

    <p>You can make payment through:</p>
    <ul>
        <li>Online payment portal (if available)</li>
        <li>Bank transfer or direct deposit</li>
        <li>Cash or check payment at the school office</li>
    </ul>

    <p>If you have already made this payment, please disregard this reminder. If you have any questions about this invoice or need to discuss payment arrangements, please contact our accounts office.</p>

    <div class="footer">
        <p>Thank you for your prompt attention to this matter.</p>
        <p><strong>{{ school_name }}</strong><br>
        Accounts Department</p>
        
        <p><em>This is an automated reminder. Please do not reply to this email.</em></p>
    </div>
</body>
</html>

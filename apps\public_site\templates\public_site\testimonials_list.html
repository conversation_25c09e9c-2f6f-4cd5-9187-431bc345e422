{# D:\school_fees_saas_v2\apps\public_site\templates\public_site\testimonials_list.html #}
{% extends "public_base.html" %}
{% load static humanize i18n %}

{% block public_page_title %}{{ view_title|default:_("Client Testimonials") }}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .testimonial-card-full {
            margin-bottom: 2rem;
            border: 1px solid #e0e0e0;
            border-radius: 0.5rem;
            transition: box-shadow 0.3s ease-in-out;
        }
        .testimonial-card-full:hover {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.1);
        }
        .testimonial-card-full .card-body {
            padding: 1.5rem;
        }
        .testimonial-card-full .card-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            padding: 1rem 1.5rem;
        }
        .testimonial-quote {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
            font-style: italic;
            color: #555;
        }
        .testimonial-author {
            font-weight: bold;
            color: #333;
        }
        .testimonial-title-school {
            font-size: 0.9rem;
            color: #777;
        }
        .rating-stars .bi-star-fill {
            color: #ffc107; /* Bootstrap warning yellow */
        }
        .rating-stars .bi-star {
            color: #e0e0e0; /* Light grey for empty stars */
        }
    </style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold">{{ view_title|default:_("What Our Clients Say") }}</h1>
        <p class="lead text-muted">
            {% trans "Read experiences from schools and administrators using our platform." %}
        </p>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show mb-4" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if testimonials %}
        <div class="row">
            {% for testimonial in testimonials %}
                <div class="col-md-6 col-lg-4">
                    <div class="card testimonial-card-full">
                        <div class="card-body">
                            {% if testimonial.rating %}
                                <div class="rating-stars mb-2">
                                    {% for i in "12345"|make_list %}
                                        <i class="bi bi-star{% if testimonial.rating >= i|add:0 %}-fill{% endif %}"></i>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <blockquote class="blockquote mb-0">
                                <p class="testimonial-quote">"{{ testimonial.quote|safe|linebreaksbr }}"</p>
                                <footer class="blockquote-footer mt-3">
                                    <span class="testimonial-author">{{ testimonial.author_name }}</span><br>
                                    {% if testimonial.author_title_school %}
                                        <cite title="{{ testimonial.author_title_school }}" class="testimonial-title-school">{{ testimonial.author_title_school }}</cite>
                                    {% endif %}
                                </footer>
                            </blockquote>
                        </div>
                        {% if request.user.is_superuser or request.user.is_staff %} {# Example: Edit/Delete for admins #}
                            <div class="card-footer text-end">
                                <a href="{% url 'public_site:testimonial_update' pk=testimonial.pk %}" class="btn btn-sm btn-outline-primary me-1">{% trans "Edit" %}</a>
                                <a href="{% url 'public_site:testimonial_delete' pk=testimonial.pk %}" class="btn btn-sm btn-outline-danger">{% trans "Delete" %}</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>

        {# Add pagination if you have it #}
        {% if is_paginated %}
            <nav aria-label="Testimonial navigation" class="mt-5">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page=1">{% trans "« First" %}</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a></li>
                    {% endif %}

                    <li class="page-item disabled"><span class="page-link">{% blocktrans with current_page=page_obj.number total_pages=page_obj.paginator.num_pages %}Page {{ current_page }} of {{ total_pages }}.{% endblocktrans %}</span></li>

                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last »" %}</a></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}

    {% else %}
        <div class="alert alert-info text-center" role="alert">
            {% trans "There are currently no testimonials to display. Check back soon!" %}
        </div>
    {% endif %}

    <div class="text-center mt-5">
        <a href="{% url 'public_site:testimonial_add' %}" class="btn btn-lg btn-success">
            <i class="bi bi-chat-right-quote-fill me-2"></i> {% trans "Share Your Experience" %}
        </a>
        <a href="{% url 'public_site:home' %}" class="btn btn-lg btn-outline-secondary ms-2">
            <i class="bi bi-arrow-left me-2"></i> {% trans "Back to Home" %}
        </a>
    </div>

</div>
{% endblock content %}



from django import template
from django.utils import timezone
from ..models import Announcement

register = template.Library()


@register.inclusion_tag('announcements/widgets/announcement_widget.html', takes_context=True)
def staff_announcements(context, limit=5):
    """Display announcements for staff members"""
    request = context.get('request')
    announcements = Announcement.get_staff_announcements(limit=limit)
    
    return {
        'announcements': announcements,
        'show_manage_link': True,
        'show_view_all_link': True,
        'show_create_link': True,
        'request': request,
    }


@register.inclusion_tag('announcements/widgets/parent_announcement_widget.html', takes_context=True)
def parent_announcements(context, limit=5):
    """Display announcements for parents"""
    request = context.get('request')
    announcements = Announcement.get_parent_announcements(limit=limit)
    
    return {
        'announcements': announcements,
        'request': request,
    }


@register.simple_tag
def get_staff_announcements(limit=5):
    """Get announcements for staff members"""
    return Announcement.get_staff_announcements(limit=limit)


@register.simple_tag
def get_parent_announcements(limit=5):
    """Get announcements for parents"""
    return Announcement.get_parent_announcements(limit=limit)


@register.filter
def announcement_icon(announcement):
    """Return appropriate icon for announcement based on its properties"""
    if announcement.is_sticky:
        return "bi-star-fill"
    elif announcement.priority == 'HIGH':
        return "bi-exclamation-triangle-fill"
    elif announcement.priority == 'MEDIUM':
        return "bi-info-circle-fill"
    else:
        return "bi-megaphone-fill"


@register.filter
def announcement_badge_class(announcement):
    """Return appropriate badge class for announcement based on its properties"""
    if announcement.is_sticky:
        return "bg-warning text-dark"
    elif announcement.priority == 'HIGH':
        return "bg-danger"
    elif announcement.priority == 'MEDIUM':
        return "bg-info"
    else:
        return "bg-primary"

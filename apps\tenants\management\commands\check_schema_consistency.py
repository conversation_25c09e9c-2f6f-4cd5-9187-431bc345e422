# apps/tenants/management/commands/check_schema_consistency.py

from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import schema_context
from apps.tenants.models import School
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Check schema consistency for StaffUser table across all tenants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix missing columns by adding them'
        )

    def handle(self, *args, **options):
        fix_mode = options.get('fix', False)
        
        if fix_mode:
            self.stdout.write(self.style.WARNING('FIX MODE ENABLED - Will attempt to add missing columns'))
        else:
            self.stdout.write('ANALYSIS MODE - Checking schema consistency')
        
        # Get all tenants
        tenants = School.objects.exclude(schema_name='public')
        self.stdout.write(f'Checking {tenants.count()} tenant schemas...\n')
        
        schemas_with_middle_name = []
        schemas_without_middle_name = []
        schemas_with_errors = []
        
        for tenant in tenants:
            self.stdout.write(f'--- Checking tenant: {tenant.schema_name} ({tenant.name}) ---')
            
            try:
                with schema_context(tenant.schema_name):
                    # Check if middle_name column exists
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_name = 'schools_staffuser' 
                            AND table_schema = %s
                            AND column_name = 'middle_name'
                        """, [tenant.schema_name])
                        
                        result = cursor.fetchone()
                        
                        if result:
                            self.stdout.write(self.style.SUCCESS(f'✅ middle_name column EXISTS in {tenant.schema_name}'))
                            schemas_with_middle_name.append(tenant.schema_name)
                        else:
                            self.stdout.write(self.style.ERROR(f'❌ middle_name column MISSING in {tenant.schema_name}'))
                            schemas_without_middle_name.append(tenant.schema_name)
                            
                            if fix_mode:
                                self.stdout.write(f'🔧 Attempting to add middle_name column to {tenant.schema_name}...')
                                try:
                                    cursor.execute("""
                                        ALTER TABLE schools_staffuser 
                                        ADD COLUMN middle_name VARCHAR(100) DEFAULT '' NOT NULL
                                    """)
                                    self.stdout.write(self.style.SUCCESS(f'✅ Successfully added middle_name column to {tenant.schema_name}'))
                                    schemas_without_middle_name.remove(tenant.schema_name)
                                    schemas_with_middle_name.append(tenant.schema_name)
                                except Exception as fix_error:
                                    self.stdout.write(self.style.ERROR(f'❌ Failed to add middle_name column to {tenant.schema_name}: {fix_error}'))
                        
                        # Also check other potentially missing fields
                        cursor.execute("""
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_name = 'schools_staffuser' 
                            AND table_schema = %s
                            ORDER BY column_name
                        """, [tenant.schema_name])
                        
                        columns = [row[0] for row in cursor.fetchall()]
                        expected_columns = [
                            'id', 'email', 'first_name', 'middle_name', 'last_name', 
                            'is_staff', 'is_active', 'is_superuser', 'employee_id', 
                            'designation', 'is_owner_profile', 'date_joined', 'last_login'
                        ]
                        
                        missing_columns = [col for col in expected_columns if col not in columns]
                        if missing_columns:
                            self.stdout.write(self.style.WARNING(f'⚠️  Missing columns in {tenant.schema_name}: {missing_columns}'))
                        
                        extra_columns = [col for col in columns if col not in expected_columns]
                        if extra_columns:
                            self.stdout.write(f'ℹ️  Extra columns in {tenant.schema_name}: {extra_columns}')
                            
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ Error checking {tenant.schema_name}: {str(e)}'))
                schemas_with_errors.append((tenant.schema_name, str(e)))
                logger.exception(f'Error checking schema {tenant.schema_name}')
        
        # Summary
        self.stdout.write('\n' + '='*60)
        self.stdout.write('SUMMARY:')
        self.stdout.write('='*60)
        
        self.stdout.write(f'✅ Schemas WITH middle_name ({len(schemas_with_middle_name)}):')
        for schema in schemas_with_middle_name:
            self.stdout.write(f'   - {schema}')
        
        self.stdout.write(f'\n❌ Schemas WITHOUT middle_name ({len(schemas_without_middle_name)}):')
        for schema in schemas_without_middle_name:
            self.stdout.write(f'   - {schema}')
        
        if schemas_with_errors:
            self.stdout.write(f'\n⚠️  Schemas with ERRORS ({len(schemas_with_errors)}):')
            for schema, error in schemas_with_errors:
                self.stdout.write(f'   - {schema}: {error}')
        
        if not fix_mode and schemas_without_middle_name:
            self.stdout.write(f'\n🔧 To fix missing columns, run:')
            self.stdout.write(f'   python manage.py check_schema_consistency --fix')
        
        if fix_mode:
            if not schemas_without_middle_name and not schemas_with_errors:
                self.stdout.write(self.style.SUCCESS('\n🎉 All schemas are now consistent!'))
            else:
                self.stdout.write(self.style.WARNING(f'\n⚠️  {len(schemas_without_middle_name)} schemas still need fixing'))

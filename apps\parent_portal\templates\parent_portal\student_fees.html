{# This template should extend your parent portal base template #}
{% extends "parent_portal/parent_portal_base.html" %} 
{% load static humanize i18n fees_tags %} {# Ensure all necessary tags are loaded #}

{% block parent_portal_page_title %}{{ view_title|default:_("Student Fee Details") }}{% endblock parent_portal_page_title %}

{# The main content block name MUST match the one in parent_portal_base.html #}
{% block parent_portal_main_content %}
<div class="container mt-4">
    {# The breadcrumb is great for navigation #}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:my_children_list' %}">{% trans "My Children" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ student.get_full_name }}'s {% trans "Fee Details" %}</li>
        </ol>
    </nav>

    <h1 class="mb-3">{{ view_title|default:_("Fee Details") }}</h1>

    {% if student %}
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0">{% trans "Fee Summary for" %} {{ student.get_full_name }}</h5>
        </div>
        <div class="card-body text-center">
            <p class="card-text text-muted mt-2">{% trans "Total Outstanding Fees for this Student" %}:</p>
            <p class="card-text fs-4 fw-bold text-danger">
                {{ school_profile.currency_symbol|default:"$" }}{{ total_student_outstanding|floatformat:2|intcomma }}
            </p>
            {% if total_student_outstanding > 0 and tenant_features.ONLINE_PAYMENTS %}
                <a href="{% url 'parent_portal:select_invoices_for_payment' %}?student={{ student.pk }}" class="btn btn-success mt-2">
                    <i class="bi bi-credit-card-fill me-2"></i> {% trans "Pay All Outstanding for" %} {{ student.first_name }}
                </a>
            {% endif %}
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0">{% trans "Invoice History for" %} {{ student.get_full_name }}</h5>
        </div>
        <div class="card-body p-0">
            {% if student_invoices %}
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Invoice #" %}</th>
                            <th>{% trans "Issue Date" %}</th>
                            <th>{% trans "Due Date" %}</th>
                            <th class="text-end">{% trans "Total" %}</th>
                            <th class="text-end">{% trans "Paid" %}</th>
                            <th class="text-end">{% trans "Balance" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in student_invoices %}
                        <tr>
                            <td><strong>{{ invoice.invoice_number_display }}</strong></td>
                            <td>{{ invoice.issue_date|date:"d M, Y" }}</td>
                            <td>{{ invoice.due_date|date:"d M, Y" }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.total_amount|floatformat:2|intcomma }}</td> {# Changed from total_invoice_amount #}
                            <td class="text-end text-success">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
                            <td class="text-end fw-bold {% if invoice.balance_due > 0 %}text-danger{% endif %}">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.balance_due|floatformat:2|intcomma }}</td>
                            <td class="text-center">{% invoice_status_badge invoice.status %}</td>
                            <td class="text-center">
                                <a href="{% url 'parent_portal:invoice_detail' pk=invoice.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'View Invoice Details' %}"><i class="bi bi-eye"></i></a>
                                <a href="{% url 'parent_portal:invoice_pdf' pk=invoice.pk %}" class="btn btn-sm btn-outline-secondary ms-1" target="_blank" title="{% trans 'View Invoice PDF' %}"><i class="bi bi-file-earmark-pdf"></i></a>
                                {% if invoice.is_payable and tenant_features.ONLINE_PAYMENTS %}
                                <a href="{% url 'parent_portal:select_invoices_for_payment' %}?invoice={{ invoice.pk }}" class="btn btn-sm btn-outline-success ms-1" title="{% trans 'Pay This Invoice' %}"><i class="bi bi-credit-card"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="card-body">
                <p class="text-center text-muted p-3">{% trans "No invoices found for" %} {{ student.get_full_name }}.</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
        <div class="alert alert-danger">{% trans "Student fee details could not be loaded." %}</div>
    {% endif %}

    <div class="mt-4 text-center">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary me-2"><i class="bi bi-arrow-left-circle"></i> {% trans "Back to Dashboard" %}</a>
        <a href="{% url 'parent_portal:payment_history_all' %}" class="btn btn-info"><i class="bi bi-clock-history"></i> {% trans "View Full Payment History" %}</a>
    </div>
</div>
{% endblock parent_portal_main_content %}







{% comment %} {% extends "tenant_base.html" %} {# Or your parent_base.html #}
{% load static humanize %}

{% block title %}{{ view_title|default:"Student Fee Details" }}{% endblock title %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Parent Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:student_detail' student_pk=student.pk %}">{{ student.full_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Fee Details</li>
        </ol>
    </nav>

    <h1 class="mb-3">{{ view_title }}</h1>

    {% if student %}
    <div class="card mb-3">
        <div class="card-header bg-info text-white">
            Fee Summary for {{ student.full_name }}
        </div>
        <div class="card-body text-center">
            <p class="card-text text-muted mt-2">Total Outstanding Fees for this Student:</p>
            <p class="card-text fs-4 fw-bold text-danger">
                {{ school_profile.currency_symbol|default:"$" }}{{ total_student_outstanding|floatformat:2|intcomma }}
            </p>
            {# Placeholder for future "Pay for this student" button #}
            {% if total_student_outstanding > 0 and tenant_features.ONLINE_PAYMENTS %}
                <a href="#" class="btn btn-success mt-2 disabled">
                    <i class="bi bi-credit-card-fill"></i> Pay {{ student.first_name }}'s Fees (Coming Soon)
                </a>
            {% endif %}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            Invoice History for {{ student.full_name }}
        </div>
        <div class="card-body p-0"> {# For table to be flush #}
            {% if student_invoices %}
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th>{% trans "Invoice #" %}</th>
                            <th>{% trans "Issue Date" %}</th>
                            <th>{% trans "Due Date" %}</th>
                            <th class="text-end">{% trans "Total" %}</th>
                            <th class="text-end">{% trans "Paid" %}</th>
                            <th class="text-end">{% trans "Balance" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in student_invoices %}
                        <tr>
                            <td>{{ invoice.invoice_number }}</td>
                            <td>{{ invoice.issue_date|date:"d M Y" }}</td>
                            <td>{{ invoice.due_date|date:"d M Y" }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.total_invoice_amount|floatformat:2|intcomma }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
                            <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.balance_due|floatformat:2|intcomma }}</td>
                            <td><span class="badge bg-{{ invoice.get_status_badge_class }}">{{ invoice.get_status_display }}</span></td>
                            <td>
                                <a href="{% url 'parent_portal:invoice_detail' invoice.pk %}" class="btn btn-sm btn-outline-primary" title="View Invoice Details"><i class="bi bi-eye"></i></a>
                                <a href="{% url 'parent_portal:invoice_pdf' invoice.pk %}" class="btn btn-sm btn-outline-secondary ms-1" target="_blank" title="View Invoice PDF"><i class="bi bi-file-earmark-pdf"></i></a>
                                {% if invoice.is_payable and tenant_features.ONLINE_PAYMENTS %}
                                <a href="{% url 'parent_portal:select_invoices_for_payment' %}?invoice={{ invoice.pk }}" class="btn btn-sm btn-outline-success ms-1" title="Pay This Invoice"><i class="bi bi-credit-card"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="card-body">
                <p class="text-center text-muted p-3">No invoices found for {{ student.full_name }}.</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
        <p>Student fee details could not be loaded.</p>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary me-2"><i class="bi bi-arrow-left-circle"></i> Back to Dashboard</a>
        {% if student %}
        <a href="{% url 'parent_portal:student_detail' student_pk=student.pk %}" class="btn btn-info me-2"><i class="bi bi-person-lines-fill"></i> View {{ student.first_name }}'s Details</a>
        {# --- ADDED PAYMENT HISTORY LINK HERE --- #}
        <a href="{% url 'parent_portal:student_payment_history' student_pk=student.pk %}" class="btn btn-success">
            <i class="bi bi-cash-stack"></i> View {{ student.first_name }}'s Payment History
        </a>
        {# --- END OF ADDED LINK --- #}
        {% endif %}
    </div>
</div>
{% endblock parent_portal_main_content %}
 {% endcomment %}

from django import forms
from .models import Student # Assuming Student is in the same app's models.py
from apps.schools.models import SchoolClass, Section # Adjust import path if necessary

# apps/students/forms.py
import django_filters

from django.db.models import Q, Prefetch, ExpressionWrapper, F, DecimalField

from .models import Student, STUDENT_STATUS_CHOICES

from django import forms
from .models import Student, ParentUser # Assuming ParentUser is in students.models
from django.db.models import Q # For searching

from django.utils.translation import gettext_lazy as _

from django.urls import reverse_lazy, reverse
from django.contrib import messages



class StudentForm(forms.ModelForm):
    # Define fields here if you need to override the default ModelChoiceField widget
    # or add specific attributes not easily done in Meta.widgets.
    # Otherwise, relying on Meta.fields is fine.

    # The querysets for current_class and current_section will be dynamically set in __init__.
    # Defining them here with .none() is a good placeholder.
    current_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False, # Make optional if student can be without class initially
        widget=forms.Select(attrs={'class': 'form-select'}), # Default class
        label="Current Class/Grade"
    )
    current_section = forms.ModelChoiceField(
        queryset=Section.objects.none(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}), # Default class
        label="Current Section"
    )

    class Meta:
        model = Student
        fields = [
            'admission_number', 'first_name', 'middle_name', 'last_name',
            'date_of_birth', 'gender', 'photo', 'date_of_admission', 'status', 'is_active',
            'current_class', 'current_section', 'roll_number',
            'student_email', 'student_phone',
            'guardian1_full_name', 'guardian1_relationship', 'guardian1_phone', 'guardian1_email', 'guardian1_occupation',
            'guardian2_full_name', 'guardian2_relationship', 'guardian2_phone', 'guardian2_email',
            'address_line1', 'address_line2', 'city', 'state_province', 'postal_code', 'country',
            'blood_group', 'allergies', 'medical_conditions',
            'previous_school', 'notes',
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
            'date_of_admission': forms.DateInput(attrs={'type': 'date'}),
            'gender': forms.Select(), # Use form-select via loop
            'status': forms.Select(), # Use form-select via loop
            'blood_group': forms.Select(), # Use form-select via loop
            'photo': forms.ClearableFileInput(), # Use form-control via loop
            'allergies': forms.Textarea(attrs={'rows': 2}),
            'medical_conditions': forms.Textarea(attrs={'rows': 2}),
            'notes': forms.Textarea(attrs={'rows': 3}),
            # 'is_active' will be a checkbox, styled by Bootstrap's form-check classes in template
        }
        labels = {
            'is_active': 'Is Student Account Active?',
            'date_of_admission': 'Date of Admission', # Ensure field name matches model if different
            # Add any other custom labels
        }
        help_texts = {
            'admission_number': 'Unique admission ID for this student within the school.',
            'current_section': 'Select class first. Sections will load based on selected class (if applicable).',
        }

    def __init__(self, *args, **kwargs):
        # The view should pass 'tenant' if it intends for the form to use it explicitly.
        # However, SchoolClass/Section.objects.all() are already tenant-aware.
        self.tenant = kwargs.pop('tenant', None) # Pop tenant if passed by the view
        
        super().__init__(*args, **kwargs)

        # Set queryset for 'current_class' - it's already tenant-aware
        # if SchoolClass is a tenant-specific model.
        self.fields['current_class'].queryset = SchoolClass.objects.all().order_by('name')

        # Logic for dependent 'current_section' dropdown
        # This handles both initial load (for edit) and when form is bound with data.
        selected_class_id = None
        if self.instance and self.instance.pk and self.instance.current_class:
            selected_class_id = self.instance.current_class.pk
        elif 'current_class' in self.data: # Check bound data if form submitted
            try:
                selected_class_id = int(self.data.get('current_class'))
            except (ValueError, TypeError):
                selected_class_id = None

        if selected_class_id:
            self.fields['current_section'].queryset = Section.objects.filter(
                school_class_id=selected_class_id
            ).order_by('name')
        else:
            self.fields['current_section'].queryset = Section.objects.none()

        # Apply premium Bootstrap styling and tooltips to all fields
        for field_name, field in self.fields.items():
            existing_attrs = field.widget.attrs

            # Add premium styling attributes
            premium_attrs = {
                'data-bs-toggle': 'tooltip',
                'data-bs-placement': 'top'
            }

            if isinstance(field.widget, forms.CheckboxInput):
                # Bootstrap handles checkbox styling via label and div wrappers typically
                field.widget.attrs.update(premium_attrs)
            elif isinstance(field.widget, forms.RadioSelect):
                # Bootstrap handles radio styling differently
                field.widget.attrs.update(premium_attrs)
            elif isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = existing_attrs.get('class', '') + ' form-select'
                field.widget.attrs['class'] = field.widget.attrs['class'].strip()
                field.widget.attrs.update(premium_attrs)
            elif isinstance(field.widget, (forms.ClearableFileInput, forms.FileInput)):
                field.widget.attrs['class'] = existing_attrs.get('class', '') + ' form-control'
                field.widget.attrs['class'] = field.widget.attrs['class'].strip()
                field.widget.attrs.update(premium_attrs)
            else: # Text inputs, Textareas, etc.
                field.widget.attrs['class'] = existing_attrs.get('class', '') + ' form-control'
                field.widget.attrs['class'] = field.widget.attrs['class'].strip()
                field.widget.attrs.update(premium_attrs)

            # Add placeholder if not explicitly set and field is CharField or EmailField
            if not existing_attrs.get('placeholder') and isinstance(field, (forms.CharField)):
                field.widget.attrs['placeholder'] = f'Enter {field.label.lower()}' if field.label else field_name.replace('_', ' ').title()

            # Add specific tooltips for key fields
            if field_name == 'admission_number':
                field.widget.attrs['title'] = 'Unique student identification number for school records'
            elif field_name in ['first_name', 'last_name', 'middle_name']:
                field.widget.attrs['title'] = f'Student\'s {field.label.lower() if field.label else field_name.replace("_", " ")}'
            elif field_name == 'date_of_birth':
                field.widget.attrs['title'] = 'Student\'s date of birth (YYYY-MM-DD format)'
            elif field_name == 'current_class':
                field.widget.attrs['title'] = 'Select the student\'s current class or grade level'
            elif field_name == 'current_section':
                field.widget.attrs['title'] = 'Select the specific section within the class'
            elif 'email' in field_name:
                field.widget.attrs['title'] = f'Valid email address for {field.label.lower() if field.label else "contact"}'
            elif 'phone' in field_name:
                field.widget.attrs['title'] = f'Contact phone number for {field.label.lower() if field.label else "communication"}'
            else:
                field.widget.attrs['title'] = f'Enter {field.label.lower() if field.label else field_name.replace("_", " ")}'


    def clean_admission_number(self):
        admission_number = self.cleaned_data.get('admission_number')
        if admission_number:
            # Student.objects.filter() is automatically scoped to the current tenant's schema.
            query = Student.objects.filter(admission_number__iexact=admission_number)
            
            # If updating an existing student, exclude self from the uniqueness check
            if self.instance and self.instance.pk:
                query = query.exclude(pk=self.instance.pk)
            
            if query.exists():
                raise forms.ValidationError(
                    "A student with this admission number ('%(adm_no)s') already exists.",
                    code='duplicate_admission_number',
                    params={'adm_no': admission_number}
                )
        return admission_number

    def clean_student_email(self):
        student_email = self.cleaned_data.get('student_email')
        if student_email:
            query = Student.objects.filter(student_email__iexact=student_email)
            if self.instance and self.instance.pk:
                query = query.exclude(pk=self.instance.pk)
            if query.exists():
                raise forms.ValidationError(
                    "A student with this email ('%(email)s') already exists.",
                    code='duplicate_student_email',
                    params={'email': student_email}
                )
        return student_email

    # Example: Ensure date of admission is not before date of birth
    def clean(self):
        cleaned_data = super().clean()
        date_of_birth = cleaned_data.get("date_of_birth")
        date_of_admission = cleaned_data.get("date_of_admission")

        if date_of_birth and date_of_admission:
            if date_of_admission < date_of_birth:
                self.add_error('date_of_admission', "Date of admission cannot be before date of birth.")
                # Or raise forms.ValidationError for non-field errors
        
        # If current_class is selected, current_section might become required
        # (This depends on your school's policy - can be complex if sections are optional for some classes)
        # current_class = cleaned_data.get("current_class")
        # current_section = cleaned_data.get("current_section")
        # if current_class and not current_section:
        #     # Check if the selected class actually HAS sections.
        #     if Section.objects.filter(school_class=current_class).exists():
        #         self.add_error('current_section', "Please select a section for the chosen class.")

        return cleaned_data



# apps/students/forms.py
import django_filters
from .models import Student, SchoolClass, Section # Assuming these are relevant
from django import forms # For widgets

class StudentFilterForm(django_filters.FilterSet):
    first_name = django_filters.CharFilter(lookup_expr='icontains', label='First Name')
    last_name = django_filters.CharFilter(lookup_expr='icontains', label='Last Name')
    admission_number = django_filters.CharFilter(lookup_expr='iexact', label='Admission No.')
    current_class = django_filters.ModelChoiceFilter(
        queryset=SchoolClass.objects.all(), # You might want to scope this to tenant in __init__
        label='Class'
    )
    current_section = django_filters.ModelChoiceFilter(
        queryset=Section.objects.all(), # Scope this too
        label='Section'
    )
    # Add is_active if you want to filter by it
    # is_active = django_filters.BooleanFilter(label='Is Active')


    class Meta:
        model = Student
        fields = ['first_name', 'last_name', 'admission_number', 'current_class', 'current_section'] #, 'is_active']

    def __init__(self, *args, **kwargs):
        request = kwargs.pop('request', None) # If you pass request from view
        super().__init__(*args, **kwargs)
        # Example: Scoping choices if SchoolClass/Section are tenant-specific
        # if request and hasattr(request, 'tenant') and request.tenant:
        #     self.filters['current_class'].queryset = SchoolClass.objects.filter(tenant=request.tenant) # Assuming FK
        #     self.filters['current_section'].queryset = Section.objects.filter(school_class__tenant=request.tenant) # Example
        
        # Add form-control class to widgets for Bootstrap styling
        for field_name, field in self.filters.items(): # django-filter uses self.filters
            if hasattr(field.field.widget, 'attrs'): # field.field is the actual Django form field
                current_class = field.field.widget.attrs.get('class', '')
                field.field.widget.attrs['class'] = f'{current_class} form-control form-control-sm'.strip()
            else:
                field.field.widget.attrs = {'class': 'form-control form-control-sm'}


import logging # Add this
from django.db.models import Q # Ensure Q is imported

logger = logging.getLogger(__name__) # Add this

class StudentParentLinkForm(forms.ModelForm):
    search_query = forms.CharField(
        label=_("Search Parent (by Email or Name)"),
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control mb-2', 'placeholder': 'Type to search...'})
    )
    parents = forms.ModelMultipleChoiceField(
        queryset=ParentUser.objects.none(), 
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label=_("Available/Linked Parents") 
    )

    class Meta:
        model = Student
        fields = ['parents'] # Field name must match M2M field on Student model
        widgets = {
            'parents': forms.CheckboxSelectMultiple,
        }
        labels = {
            'parents': _("Available/Linked Parents (Check to link, uncheck to unlink)")
        }

    def __init__(self, *args, **kwargs):
        self.student_instance = kwargs.get('instance') 
        request_get = kwargs.pop('request_get', None) # Pop before super if you don't want it passed up
        
        super().__init__(*args, **kwargs) # self.instance is available after super if passed

        search_val = None
        if request_get:
            search_val = request_get.get('search_query')
            logger.debug(f"StudentParentLinkForm __init__: Search query from request_get: '{search_val}'")
        else:
            logger.debug("StudentParentLinkForm __init__: No request_get or search_query in request_get.")

        # Initial queryset: all active parents (scoped by current tenant schema)
        parent_qs = ParentUser.objects.filter(is_active=True)
        logger.debug(f"StudentParentLinkForm __init__: Initial active parent_qs count: {parent_qs.count()}")

        if search_val:
            logger.debug(f"StudentParentLinkForm __init__: Applying search filter for '{search_val}'")
            parent_qs = parent_qs.filter(
                Q(email__icontains=search_val) |
                Q(first_name__icontains=search_val) |
                Q(last_name__icontains=search_val)
            ).distinct()
            logger.debug(f"StudentParentLinkForm __init__: parent_qs count AFTER search: {parent_qs.count()}")
        
        final_parent_qs = parent_qs.order_by('last_name', 'first_name')
        self.fields['parents'].queryset = final_parent_qs
        logger.debug(f"StudentParentLinkForm __init__: Final queryset for 'parents' field assigned. Count: {final_parent_qs.count()}")
        
        if self.student_instance and self.student_instance.pk:
            self.fields['parents'].initial = self.student_instance.parents.all()
            logger.debug(f"StudentParentLinkForm __init__: Student PK {self.student_instance.pk}. Initial linked parents for checkbox: {self.fields['parents'].initial.count()}")
        
        if search_val:
            self.fields['search_query'].initial = search_val

# # apps/students/forms.py

# class StudentParentLinkForm(forms.ModelForm):
    search_query = forms.CharField(
        label=_("Search Parent (by Email or Name)"),
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control mb-2', 'placeholder': 'Type to search...'})
    )
    parents = forms.ModelMultipleChoiceField( # Field name matches M2M field on Student
        queryset=ParentUser.objects.none(), 
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label=_("Available/Linked Parents") 
    )

    class Meta:
        model = Student
        fields = ['parents'] # This tells ModelForm to handle this M2M

    def __init__(self, *args, **kwargs):
        self.student_instance = kwargs.get('instance') # Get instance (student)
        # tenant = kwargs.pop('tenant', None) # Pop if you explicitly pass tenant
        
        # Get search query from GET data if provided by the view
        request_get = kwargs.pop('request_get', None) 
        search_val = None
        if request_get:
            search_val = request_get.get('search_query')

        super().__init__(*args, **kwargs)

        # Initial queryset: all active parents
        parent_qs = ParentUser.objects.filter(is_active=True)

        if search_val:
            parent_qs = parent_qs.filter(
                Q(email__icontains=search_val) |
                Q(first_name__icontains=search_val) |
                Q(last_name__icontains=search_val)
            ).distinct()
        
        self.fields['parents'].queryset = parent_qs.order_by('last_name', 'first_name')
        
        if self.student_instance and self.student_instance.pk:
            self.fields['parents'].initial = self.student_instance.parents.all()
        
        if search_val: # Re-set the search query in the form field
            self.fields['search_query'].initial = search_val

# class StudentFilterForm(django_filters.FilterSet):
#     name = django_filters.CharFilter(method='filter_by_name', label="Name or Admission No.",
#                                     widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Search...'}))
#     current_class = django_filters.ModelChoiceFilter(
#         queryset=SchoolClass.objects.all().order_by('name'), # Base queryset for choices
#         widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
#         label="Class/Grade",
#         empty_label="All Classes" # Add an empty label
#     )

#     STATUS_CHOICES = [
#         ('', 'All Active'), # Default
#         ('ACTIVE', 'Active Only'),
#         ('INACTIVE', 'Inactive Only'),
#         ('ALL_RECORDS', 'All (Active & Inactive)'), # Renamed for clarity
#     ]
#     status = django_filters.ChoiceFilter(
#         choices=STATUS_CHOICES,
#         widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
#         label="Status",
#         method='filter_by_status'
#     )

#     class Meta:
#         model = Student
#         fields = ['name', 'current_class', 'status']

#     def __init__(self, *args, **kwargs):
#         self.request = kwargs.pop('request', None) # Store request if needed by custom methods
#         # self.tenant = kwargs.pop('tenant', None) # Remove this, not used by FilterSet base
#         super().__init__(*args, **kwargs)
#         # Querysets for choices are already tenant-scoped by django-tenants
#         # No need to explicitly filter self.filters['current_class'].queryset by tenant here
#         # It will use SchoolClass.objects.all() from the current tenant's schema.

#     def filter_by_name(self, queryset, name_attr, value):
#         if value:
#             return queryset.filter(
#                 Q(first_name__icontains=value) |
#                 Q(last_name__icontains=value) |
#                 Q(middle_name__icontains=value) |
#                 Q(admission_number__iexact=value)
#             ).distinct() # Add distinct if Q objects can cause duplicates
#         return queryset

#     def filter_by_status(self, queryset, name_attr, value):
#         if value == 'ACTIVE':
#             return queryset.filter(is_active=True)
#         elif value == 'INACTIVE':
#             return queryset.filter(is_active=False)
#         elif value == 'ALL_RECORDS': # Show all students regardless of active status
#             return queryset # No additional is_active filter
#         # Default: if value is empty string (from '-- All Active --') or invalid
#         return queryset.filter(is_active=True) # Default to active
    


# class StudentParentLinkForm(forms.ModelForm):
#     # This field allows selecting existing parents.
#     # We use a ModelMultipleChoiceField to allow linking multiple parents.
#     parents_to_link = forms.ModelMultipleChoiceField(
#         queryset=ParentUser.objects.none(), # Initial empty queryset
#         widget=forms.CheckboxSelectMultiple, # Or forms.SelectMultiple, or a Select2 widget
#         required=False,
#         label="Select Parents to Link/Unlink"
#     )
    
#     # Optional: Field to search for parents to add to the queryset above
#     search_parent_email = forms.CharField(
#         required=False, 
#         label="Search Parent by Email",
#         widget=forms.TextInput(attrs={'placeholder': 'Enter email to find parent'})
#     )

#     class Meta:
#         model = Student 
#         fields = [] # We are not directly editing student fields, only the M2M 'parents'

#     def __init__(self, *args, **kwargs):
#         self.student_instance = kwargs.pop('instance', None) # Student instance passed from view
#         # tenant = kwargs.pop('tenant', None) # If ParentUser needs tenant scoping for queryset
#         super().__init__(*args, **kwargs)

#         if self.student_instance:
#             # Populate with currently linked parents
#             self.fields['parents_to_link'].initial = self.student_instance.parents.all()
            
#             # Queryset for the selection field - show all active parents in the current tenant
#             # This assumes ParentUser is tenant-specific (no FK to tenant, but queried within tenant schema)
#             self.fields['parents_to_link'].queryset = ParentUser.objects.filter(is_active=True).order_by('last_name', 'first_name')
#         else:
#             self.fields['parents_to_link'].queryset = ParentUser.objects.none()

#     def save(self, commit=True):
#         # The super().save() won't handle M2M by default if it's not in fields.
#         # We handle the M2M relationship manually.
#         if self.student_instance:
#             linked_parents_data = self.cleaned_data.get('parents_to_link')
#             if linked_parents_data is not None: # Check if the field was present and cleaned
#                 self.student_instance.parents.set(linked_parents_data) # .set() handles add/remove
#         return self.student_instance # Return the student instance



# D:\school_fees_saas_v2\apps\students\forms.py

from django import forms
from django.forms import PasswordInput # For better password widget if not default
from django.utils.translation import gettext_lazy as _
from .models import ParentUser # Or from wherever ParentUser is defined (e.g., apps.users.models)

# ... (Your existing forms: StudentForm, StudentFilterForm, StudentParentLinkForm) ...

class ParentUserCreationForm(forms.ModelForm):
    email = forms.EmailField(
        label=_("Email Address"), 
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': '<EMAIL>'})
    )
    first_name = forms.CharField(
        label=_("First Name"), 
        max_length=150, 
        required=True, # Typically required
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    last_name = forms.CharField(
        label=_("Last Name"), 
        max_length=150, 
        required=True, # Typically required
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    phone_number = forms.CharField(
        label=_("Phone Number"), 
        max_length=30, 
        required=False, # Make this optional unless business rule says otherwise
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., ******-123-4567'})
    )
    password = forms.CharField(
        label=_("Password"), 
        widget=forms.PasswordInput(attrs={'class': 'form-control'}),
        help_text=_("Enter a secure password for the parent.")
    )
    password_confirm = forms.CharField(
        label=_("Confirm Password"), 
        widget=forms.PasswordInput(attrs={'class': 'form-control'}),
        help_text=_("Enter the same password again for verification.")
    )

    class Meta:
        model = ParentUser
        fields = ['email', 'first_name', 'last_name', 'phone_number'] 
        # Password fields are handled separately, not directly from model fields in Meta for creation

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if ParentUser.objects.filter(email__iexact=email).exists():
            raise forms.ValidationError(_("A parent account with this email address already exists."))
        return email

    def clean_password_confirm(self):
        password = self.cleaned_data.get("password")
        password_confirm = self.cleaned_data.get("password_confirm")
        if password and password_confirm and password != password_confirm:
            raise forms.ValidationError(_("The two password fields didn't match."))
        return password_confirm # Return the cleaned data, even if it's just for validation

    def save(self, commit=True):
        user = super().save(commit=False) # Get model instance without saving to DB yet
        user.set_password(self.cleaned_data["password"]) # Hash the password
        # is_active is True by default in your ParentUser model example
        # is_parent_user flag is True by default in your ParentUser model example
        if commit:
            user.save()
        return user



class ParentUserChangeForm(forms.ModelForm):
    email = forms.EmailField(label=_("Email Address"), required=True,
                            widget=forms.EmailInput(attrs={'class': 'form-control'}))
    first_name = forms.CharField(label=_("First Name"), max_length=150, required=True,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(label=_("Last Name"), max_length=150, required=True,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    phone_number = forms.CharField(label=_("Phone Number"), max_length=30, required=False,
                                    widget=forms.TextInput(attrs={'class': 'form-control'}))
    is_active = forms.BooleanField(label=_("Active"), required=False,
                                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}))

    class Meta:
        model = ParentUser
        fields = ['email', 'first_name', 'last_name', 'phone_number', 'is_active']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Make email read-only for existing users if that's your policy
            # self.fields['email'].disabled = True
            # self.fields['email'].help_text = _("Email address cannot be changed.")
            pass # No changes for now, but this is where you'd put such logic
        
            
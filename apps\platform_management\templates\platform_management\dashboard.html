{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\dashboard.html #}
{% extends "platform_management/platform_admin_base.html" %}

{% load static i18n humanize %}

{% block platform_admin_page_title %}{% trans "Dashboard" %}{% endblock %}

{% block extra_platform_admin_css %}
    {# If dashboard_styles.css is specific to only this page, link it here #}
    {# Otherwise, it's better in platform_admin_base.html if shared by other admin pages #}
{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{% trans "Platform Dashboard" %}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Home" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Dashboard" %}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section dashboard">
    <div class="row">
        {# Main Column - Info Cards & Recent Activity #}
        <div class="col-lg-8">
            <div class="row">
                {# Platform Settings Card #}
                <div class="col-md-6 mb-4"> {# Added mb-4 for spacing below this card's column #}
                    <div class="card info-card sales-card h-100"> {# Added h-100 for consistent card height in a row #}
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Platform Settings" %}</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-sliders"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>{{ total_settings|default:0 }}</h6>
                                    <span class="text-muted small pt-2 ps-1">{% trans "Active Modules/Configs" %}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {# Active Announcements Card #}
                <div class="col-md-6 mb-4"> {# Added mb-4 #}
                    <div class="card info-card revenue-card h-100"> {# Added h-100 #}
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Active Announcements" %}</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-megaphone-fill"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>{{ active_platform_announcements|default:0 }}</h6>
                                    <span class="text-muted small pt-2 ps-1">{% trans "Platform-wide" %}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {# Active System Notifications Card #}
                <div class="col-md-6 mb-4"> {# Added mb-4, removed <br/> #}
                    <div class="card info-card customers-card h-100"> {# Added h-100 #}
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Active System Notifications" %}</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-bell-fill"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>{{ active_system_notifications|default:0 }}</h6>
                                    <span class="text-muted small pt-2 ps-1">{% trans "Targeted Alerts" %}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {# Maintenance Mode Card #}
                <div class="col-md-6 mb-4"> {# Added mb-4, removed <br/> #}
                    <div class="card info-card h-100"> {# Added h-100 #}
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Maintenance Mode" %}</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center 
                                    {% if maintenance_mode_status.is_enabled %}bg-warning text-dark{% else %}bg-success-subtle text-success-emphasis{% endif %}">
                                    <i class="bi {% if maintenance_mode_status.is_enabled %}bi-cone-striped{% else %}bi-shield-check{% endif %}"></i>
                                </div>
                                <div class="ps-3">
                                    <h6 class="{% if maintenance_mode_status.is_enabled %}text-warning{% else %}text-success{% endif %}">
                                        {% if maintenance_mode_status.is_enabled %}{% trans "ENABLED" %}{% else %}{% trans "DISABLED" %}{% endif %}
                                    </h6>
                                    <a href="{% url 'platform_management:maintenance_mode_toggle' %}" class="btn btn-sm {% if maintenance_mode_status.is_enabled %}btn-outline-secondary{% else %}btn-outline-warning{% endif %} small mt-1">
                                        {% if maintenance_mode_status.is_enabled %}{% trans "Disable" %}{% else %}{% trans "Enable" %}{% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Audit Logs -->
                <div class="col-12 mb-4"> {# Added mb-4 #}
                    <div class="card recent-sales overflow-auto">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "Recent Platform Audit Logs" %} <span>| {% trans "Last 5" %}</span></h5>
                            {% if recent_audit_logs %}
                            <table class="table table-borderless table-hover table-sm">
                                <thead>
                                    <tr>
                                        <th scope="col">{% trans "Timestamp" %}</th>
                                        <th scope="col">{% trans "User" %}</th>
                                        <th scope="col">{% trans "Action" %}</th>
                                        <th scope="col">{% trans "Target" %}</th>
                                        <th scope="col">{% trans "Description" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in recent_audit_logs %}
                                    <tr>
                                        <td><small class="text-muted">{{ log.timestamp|naturaltime }}</small></td>
                                        <td><small>{{ log.user.get_full_name|default:log.user.email|default:"System" }}</small></td>
                                        <td><span class="badge bg-light text-dark border">{{ log.get_action_type_display|default:log.action_type }}</span></td>
                                        <td><small>{{ log.target_repr|truncatechars:30|default:"N/A" }}</small></td>
                                        <td><small>{{ log.description|truncatechars:40 }}</small></td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            <div class="text-end mt-2">
                                <a href="{% url 'platform_management:auditlog_list' %}" class="btn btn-sm btn-link">{% trans "View All Logs" %} <i class="bi bi-arrow-right-short"></i></a>
                            </div>
                            {% else %}
                                <p class="text-center text-muted p-3">{% trans "No recent platform audit logs." %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div><!-- End Recent Audit Logs -->
            </div>
        </div>

        {# Right Column - Quick Actions & Other Info #}
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4"> {# Added mb-4 #}
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0 py-1">{% trans "Quick Actions" %}</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'platform_management:platform_announcement_create' %}" class="list-group-item list-group-item-action"><i class="bi bi-megaphone-fill me-2 text-primary"></i>{% trans "New Platform Announcement" %}</a>
                    <a href="{% url 'platform_management:systemnotification_create' %}" class="list-group-item list-group-item-action"><i class="bi bi-bell-fill me-2 text-info"></i>{% trans "Send System Notification" %}</a>
                    <a href="{% url 'platform_management:platformsetting_list' %}" class="list-group-item list-group-item-action"><i class="bi bi-sliders me-2 text-secondary"></i>{% trans "Manage Platform Settings" %}</a>
                    <a href="{% url 'platform_management:maintenance_mode_toggle' %}" class="list-group-item list-group-item-action"><i class="bi bi-cone-striped me-2 text-warning"></i>{% trans "Toggle Maintenance Mode" %}</a>
                    <a href="{% url 'admin:index' %}" target="_blank" class="list-group-item list-group-item-action"><i class="bi bi-gear-wide-connected me-2 text-dark"></i>{% trans "Full Django Admin" %}</a>
                </div>
            </div>

            <div class="card shadow-sm mb-4"> {# Added mb-4 (or mb-0 if it's the last element in the column) #}
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0 py-1">{% trans "System Overview" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mt-3">
                        <li class="d-flex justify-content-between"><span>Total Tenants:</span> <strong>{{ total_tenants|default:"N/A" }}</strong></li>
                        <li class="d-flex justify-content-between"><span>Active Subscriptions:</span> <strong>{{ active_subscriptions|default:"N/A" }}</strong></li>
                        <li class="d-flex justify-content-between"><span>Django Version:</span> <strong>{{ django_version|default:"N/A" }}</strong></li> 
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}




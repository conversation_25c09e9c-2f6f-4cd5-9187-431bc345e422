# D:\school_fees_saas_v2\apps\platform_management\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    PlatformSetting, 
    SystemNotification, 
    AuditLog, 
    MaintenanceMode,
    PlatformAnnouncement # Import the new model
)

from django.contrib import admin
from .models import PlatformAnnouncement 

@admin.register(PlatformAnnouncement)
class PlatformAnnouncementAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_published', 'publish_date', 'expiry_date', 'author', 'updated_at')
    list_filter = ('is_published', 'author', 'publish_date')
    search_fields = ('title', 'content', 'author__email')
    readonly_fields = ('created_at', 'updated_at', 'author') # Make author readonly if set automatically

    # Example: Making 'is_published' and 'publish_date' editable in the list
    list_editable = ('is_published', 'publish_date') # Must be a list or tuple
    # Note: 'title' cannot be here if it's the link_display field (usually the first in list_display)

    fieldsets = (
        (None, {'fields': ('title', 'content')}),
        ('Visibility & Timing', {'fields': ('is_published', 'publish_date', 'expiry_date')}),
        # ('Auditing', {'fields': ('author',)}), # Author is now readonly or set in save_model
    )

    def save_model(self, request, obj, form, change):
        if not obj.author_id: 
            obj.author = request.user
        super().save_model(request, obj, form, change)

# @admin.register(PlatformAnnouncement)
# class PlatformAnnouncementAdmin(admin.ModelAdmin):
#     list_display = ('title', 'author_display', 'publish_date', 'expiry_date', 'is_published', 'created_at')
#     list_filter = ('is_published', 'publish_date', 'author')
#     search_fields = ('title', 'content', 'author__username', 'author__email') # Adjust based on your AUTH_USER_MODEL fields
#     list_editable = ('is_published')
    
#     fieldsets = (
#         (None, {'fields': ('title', 'content')}),
#         (_('Publication Details'), {'fields': ('author', 'publish_date', 'expiry_date', 'is_published')}),
#     )
#     readonly_fields = ('created_at', 'updated_at')

#     def author_display(self, obj):
#         if obj.author:
#             return obj.author.get_full_name() or getattr(obj.author, obj.author.USERNAME_FIELD, str(obj.author))
#         return "-"
#     author_display.short_description = _('Author')
#     author_display.admin_order_field = 'author' # Enable sorting by author

#     def save_model(self, request, obj, form, change):
#         if not obj.author_id and not change: # If creating new and author not set by form
#             obj.author = request.user 
#         super().save_model(request, obj, form, change)

#     def get_form(self, request, obj=None, **kwargs):
#         # Pre-fill author for new announcements if not already set
#         form = super().get_form(request, obj, **kwargs)
#         if obj is None and 'author' in form.base_fields: # For new objects
#             form.base_fields['author'].initial = request.user
#         return form

@admin.register(PlatformSetting) # Renamed from PlatformSettings to PlatformSettingAdmin
class PlatformSettingAdmin(admin.ModelAdmin):
    list_display = ('setting_name', 'setting_value_snippet', 'is_active', 'updated_at', 'created_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('setting_name', 'description', 'setting_value')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('is_active',) # Example

    def setting_value_snippet(self, obj):
        return (obj.setting_value[:75] + '...') if len(obj.setting_value) > 75 else obj.setting_value
    setting_value_snippet.short_description = _('Value Snippet')

@admin.register(SystemNotification)
class SystemNotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'notification_type', 'is_active', 'publish_date', 'expires_at', 'created_by_display', 'created_at')
    list_filter = ('notification_type', 'is_active', 'publish_date', 'created_at')
    search_fields = ('title', 'message', 'created_by__username', 'created_by__email')
    readonly_fields = ('created_at',)
    list_editable = ('is_active', 'notification_type')
    filter_horizontal = ('target_users',) # Good for ManyToManyField

    def created_by_display(self, obj):
        if obj.created_by:
            return obj.created_by.get_full_name() or getattr(obj.created_by, obj.created_by.USERNAME_FIELD, str(obj.created_by))
        return "-"
    created_by_display.short_description = _('Created By')
    created_by_display.admin_order_field = 'created_by'


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user_display', 'action_type', 'model_name', 'object_pk', 'description_snippet', 'ip_address')
    list_filter = ('action_type', 'model_name', 'timestamp', 'user')
    search_fields = ('user__username', 'user__email', 'description', 'model_name', 'object_pk', 'ip_address')
    readonly_fields = ('timestamp', 'user', 'action_type', 'model_name', 'object_pk', 'description', 'details', 'ip_address', 'user_agent') # Make all fields readonly
    date_hierarchy = 'timestamp'

    def user_display(self, obj):
        if obj.user:
            return obj.user.get_full_name() or getattr(obj.user, obj.user.USERNAME_FIELD, str(obj.user))
        return _("System")
    user_display.short_description = _('User')
    user_display.admin_order_field = 'user'
    
    def description_snippet(self, obj):
        return (obj.description[:75] + '...') if len(obj.description) > 75 else obj.description
    description_snippet.short_description = _('Description')

    def has_add_permission(self, request): # Audit logs are typically created by the system
        return False

    def has_change_permission(self, request, obj=None): # And not changed
        return False

    # def has_delete_permission(self, request, obj=None): # Allow deletion for cleanup if needed by superusers
    #     return request.user.is_superuser


@admin.register(MaintenanceMode)
class MaintenanceModeAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'is_enabled', 'enabled_by_display', 'enabled_at', 'updated_at')
    readonly_fields = ('enabled_at', 'updated_at', 'enabled_by') # enabled_by is set in save method
    
    # Since it's a singleton (pk=1), prevent adding new ones and limit deletion.
    def has_add_permission(self, request):
        # Prevent adding if one already exists
        return not MaintenanceMode.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Typically, you don't delete this; you toggle it.
        # Allow deletion only if absolutely necessary and by superuser, perhaps.
        return False # Or request.user.is_superuser

    def enabled_by_display(self, obj):
        if obj.enabled_by:
            return obj.enabled_by.get_full_name() or getattr(obj.enabled_by, obj.enabled_by.USERNAME_FIELD, str(obj.enabled_by))
        return "-"
    enabled_by_display.short_description = _('Toggled By')
    enabled_by_display.admin_order_field = 'enabled_by'
    
    def save_model(self, request, obj, form, change):
        # Logic to set enabled_by when is_enabled is changed
        if 'is_enabled' in form.changed_data:
            if obj.is_enabled:
                obj.enabled_by = request.user
                # obj.enabled_at is handled by model's save()
            else:
                obj.enabled_by = request.user # Log who disabled it
                # obj.enabled_at = None (handled by model's save())
        super().save_model(request, obj, form, change)


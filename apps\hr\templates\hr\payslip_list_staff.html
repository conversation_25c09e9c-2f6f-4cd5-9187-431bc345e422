{% extends "tenant_base.html" %}
{% load humanize %}
{% block title %}My Payslips{% endblock %}
{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>My Payslips</h1>
    <p>Employee: {{ request.user.full_name }}</p>
    {% include "partials/_messages.html" %}
    {% if payslips %}
        <table class="table table-sm table-hover">
            <thead><tr><th>Pay Period</th><th>Issue Date</th><th class="text-end">Net Pay</th><th>Actions</th></tr></thead>
            <tbody>
            {% for payslip in payslips %}
                <tr>
                    <td>{{ payslip.pay_period_start|date:"M Y" }}</td>
                    <td>{{ payslip.issue_date|date:"d M Y" }}</td>
                    <td class="text-end">{{ payslip.net_pay|floatformat:2|intcomma }}</td>
                    <td>
                        <a href="{% url 'hr:payslip_detail' payslip.pk %}" class="btn btn-sm btn-info">View</a>
                        <a href="{% url 'hr:payslip_pdf' payslip.pk %}" class="btn btn-sm btn-secondary" target="_blank">PDF</a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        {% include "partials/_pagination.html" %}
    {% else %}
        <p>No payslips available for you yet.</p>
    {% endif %}
    <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary mt-3">Back to Dashboard</a>
</div>
{% endblock %}
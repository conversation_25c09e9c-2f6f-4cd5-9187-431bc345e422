# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# Import models from this app (hr)
from .models import LeaveType, LeaveRequest, EmployeeProfile

# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from .models import LeaveType, LeaveRequest, LeaveBalance, LeaveBalanceLog # Import all models

@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    # --- THIS IS THE CORRECTED 'list_display' ---
    list_display = (
        'name', 
        'is_paid', 
        'is_active', 
        'accrual_frequency', 
        'accrual_rate', 
        'max_accrual_balance',
        'max_days_per_year_grant'
    )
    list_filter = ('is_paid', 'is_active', 'accrual_frequency')
    search_fields = ('name',)
    
    # Use fieldsets to organize the form in the admin for better usability
    fieldsets = (
        ('General Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Leave Policy', {
            'fields': ('is_paid', 'requires_approval')
        }),
        ('Leave Granting (for non-accruing types)', {
            'description': "Use this for leave types like 'Sick Leave' where a set number of days is granted annually.",
            'fields': ('max_days_per_year_grant',)
        }),
        ('Automated Accrual System', {
            'description': "Use these settings for leave types like 'Annual Leave' that are earned over time.",
            'fields': ('accrual_frequency', 'accrual_rate', 'max_accrual_balance', 'prorate_accrual')
        }),
    )



# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from .models import LeaveRequest # And other models...

@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    """
    Admin interface for managing staff leave requests.
    """
    # --- CORE DISPLAY AND FILTERING ---

    list_display = (
        'get_employee_display', # Use the custom method for a rich display
        'leave_type',
        'start_date',
        'end_date',
        'days_requested_display', # Use a display method for the calculated value
        'status',
        'get_approver_name', # Use the custom method
    )
    list_filter = (
        'status',
        'leave_type',
        'start_date',
        'employee__user__designation', # Filter by the staff member's designation
    )
    search_fields = (
        'employee__user__first_name',
        'employee__user__last_name',
        'employee__user__email',
        'employee__user__employee_id',
        'leave_type__name',
    )
    list_select_related = ('employee__user', 'leave_type', 'approved_by') # Correctly fetches related objects
    date_hierarchy = 'start_date' # Adds a date drill-down navigation bar

    # --- FORM LAYOUT AND FIELDS ---

    # For ForeignKey dropdowns with many items, 'autocomplete' is better than 'raw_id_fields'.
    # This requires the related admin (e.g., EmployeeProfileAdmin) to have `search_fields` defined.
    autocomplete_fields = ['employee', 'approved_by']
    
    # Organize the form into logical sections
    fieldsets = (
        ("Request Details", {
            'fields': ('employee', 'leave_type', 'status', 'reason', 'attachment')
        }),
        ("Leave Period", {
            'fields': (
                ('start_date', 'end_date'), 
                ('half_day_start', 'half_day_end'),
                'days_requested_display' # Display the calculated value as a readonly field
            )
        }),
        ("Approval Information", {
            'fields': ('approved_by', 'approval_date', 'admin_notes')
        }),
        ("Timestamps", {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',) # Makes this section collapsible
        }),
    )
    
    # These fields will be displayed but not editable.
    # We include our custom display method 'days_requested_display' here.
    readonly_fields = ('days_requested_display', 'created_at', 'updated_at', 'approval_date')

    # --- CUSTOM ADMIN METHODS ---

    @admin.display(description='Employee', ordering='employee__user__last_name')
    def get_employee_display(self, obj):
        """
        Creates a readable string for the employee, including their ID.
        """
        if obj.employee and obj.employee.user:
            user = obj.employee.user
            emp_id_str = f"({user.employee_id})" if hasattr(user, 'employee_id') and user.employee_id else ""
            return f"{user.get_full_name() or user.email} {emp_id_str}"
        return "N/A"

    @admin.display(description='Approver', ordering='approved_by__user__last_name')
    def get_approver_name(self, obj):
        """
        Safely gets the name of the approving staff member.
        """
        if obj.approved_by and obj.approved_by.user:
            return obj.approved_by.user.get_full_name()
        return "—" # A dash is clearer than "N/A" for an empty optional field

    @admin.display(description='Days Requested', ordering='days_requested')
    def days_requested_display(self, obj):
        """
        Displays the calculated number of days from the model's property or method.
        This assumes your LeaveRequest model has a property/method `days_requested`.
        """
        # This assumes your LeaveRequest model has a method or property called 'days_requested'.
        # For example, in models.py: @property def days_requested(self): ...
        if hasattr(obj, 'days_requested'):
            return obj.days_requested
        return "Calculation Error" 
    


from django.contrib import admin
from .models import LeaveBalance # and your other HR models

@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    """
    Admin interface for managing staff leave balances.
    """
    # Use the correct field names and custom display methods
    list_display = (
        'get_employee_name',  # Custom method to display the employee's name
        'leave_type',
        'year_or_period_info',
        'days_accrued',
        'days_taken',
        'get_days_remaining', # Custom method for the calculated property
        'updated_at',
    )
    
    list_filter = ('leave_type', 'year_or_period_info')
    
    # Update search fields to use the correct relationship path
    search_fields = (
        'employee__user__first_name',
        'employee__user__last_name',
        'employee__user__email',
    )
    
    # Use 'autocomplete_fields' for a better user experience than raw_id_fields
    # This requires EmployeeProfileAdmin to have search_fields defined.
    autocomplete_fields = ('employee',)
    
    # Make calculated fields readonly in the detail view
    readonly_fields = ('get_days_remaining', 'created_at', 'updated_at')
    
    # For a better form layout in the admin
    fieldsets = (
        (None, {
            'fields': ('employee', 'leave_type', 'year_or_period_info')
        }),
        ('Balance Details', {
            'fields': ('days_accrued', 'days_taken', 'get_days_remaining')
        }),
        ('Timestamps', {
            'fields': ('last_accrual_date', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    @admin.display(description='Employee', ordering='employee__user__last_name')
    def get_employee_name(self, obj):
        """
        Creates a readable string for the employee from the related EmployeeProfile.
        """
        if obj.employee and hasattr(obj.employee, 'user') and obj.employee.user:
            return obj.employee.user.get_full_name() or obj.employee.user.email
        return "N/A"

    @admin.display(description='Days Remaining', ordering='days_accrued') # Can't sort on property directly, so sort by a real field
    def get_days_remaining(self, obj):
        """
        Displays the result of the `days_remaining` model property.
        """
        return obj.days_remaining



@admin.register(LeaveBalanceLog)
class LeaveBalanceLogAdmin(admin.ModelAdmin):
    list_display = ('leave_balance', 'action', 'change_amount', 'created_at')
    list_filter = ('action',)
    search_fields = ('leave_balance__staff_user__first_name', 'leave_balance__leave_type__name')
    raw_id_fields = ('leave_balance',)
    

# @admin.register(LeaveType)
# class LeaveTypeAdmin(admin.ModelAdmin):
#     list_display = (
#         'name', 
#         'description_snippet', 
#         'max_annual_days',
#         'is_paid',
#         'requires_approval',
#         'is_active'  # Assumes 'is_active' field is ADDED to LeaveType model and migrated
#     )
#     list_filter = (
#         'is_active', # Assumes 'is_active' field is ADDED to LeaveType model
#         'is_paid',
#         'requires_approval',
#     )
#     search_fields = ('name', 'description')
#     ordering = ('name',)

#     @admin.display(description='Description')
#     def description_snippet(self, obj):
#         if obj.description:
#             return (obj.description[:75] + '...') if len(obj.description) > 75 else obj.description
#         return ""



@admin.register(EmployeeProfile)
class EmployeeProfileAdmin(admin.ModelAdmin):
    list_display = (
        'get_user_full_name', 
        'get_user_email', 
        'get_user_employee_id', # Method to display from StaffUser
        'get_user_designation', # Method to display from StaffUser
        'employment_type', 
        'updated_at'
    )
    search_fields = ( # Fields used for autocomplete when EmployeeProfile is a ForeignKey
        'user__first_name', 
        'user__last_name', 
        'user__email',
        'user__employee_id', # Assuming StaffUser has employee_id
    )
    list_filter = ('employment_type', 'user__is_active', 'user__designation') # Filter by StaffUser's designation
    list_select_related = ('user',) # Crucial for performance of display methods
    ordering = ('user__last_name', 'user__first_name')
    raw_id_fields = ('user',) # Often better than dropdown for OneToOne to a large User table

    fieldsets = (
        # 'user' is the PK, so it's not typically in fieldsets unless you want to make it selectable
        # when creating a new EmployeeProfile (if not primary_key=True).
        # Since it's primary_key=True linked to StaffUser, it's implicitly handled.
        # If you need to select the StaffUser when creating an EmployeeProfile (if not pk):
        # (None, {'fields': ('user',)}), 
        ('Personal Details', {'fields': ('middle_name', 'gender', 'date_of_birth', 'marital_status', 'photo')}),
        ('Contact & Address', {'fields': ('phone_number_alternate', 'address_line1', 'address_line2', 'city', 'state_province', 'postal_code', 'country')}),
        ('Employment Details', {'fields': ('employment_type', 'date_left')}),
        ('Internal Notes', {'fields': ('notes',)}),
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    readonly_fields = ('created_at', 'updated_at')

    @admin.display(description='Employee Name', ordering='user__last_name')
    def get_user_full_name(self, obj):
        return obj.user.get_full_name() if obj.user else "N/A"

    @admin.display(description='Email', ordering='user__email')
    def get_user_email(self, obj):
        return obj.user.email if obj.user else "N/A"

    @admin.display(description='Employee ID', ordering='user__employee_id') # Assuming StaffUser.employee_id
    def get_user_employee_id(self, obj):
        return obj.user.employee_id if obj.user and hasattr(obj.user, 'employee_id') else "N/A"

    @admin.display(description='Designation', ordering='user__designation') # Assuming StaffUser.designation
    def get_user_designation(self, obj):
        return obj.user.designation if obj.user and hasattr(obj.user, 'designation') else "N/A"




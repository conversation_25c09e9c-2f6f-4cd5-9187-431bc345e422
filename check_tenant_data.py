#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_tenants.utils import schema_context
from apps.schools.models import School
from apps.payments.models import Payment
from apps.students.models import Student

def check_tenant_data():
    # Check available tenants
    tenants = School.objects.all()
    print(f"Available tenants: {tenants.count()}")
    
    for tenant in tenants:
        print(f"\n=== Checking tenant: {tenant.name} (schema: {tenant.schema_name}) ===")
        
        try:
            with schema_context(tenant.schema_name):
                payment_count = Payment.objects.count()
                student_count = Student.objects.count()
                
                print(f"Payment count: {payment_count}")
                print(f"Student count: {student_count}")
                
                # Check for Pascal and Sekai
                pascal = Student.objects.filter(first_name__icontains='Pascal').first()
                sekai = Student.objects.filter(first_name__icontains='Sekai').first()
                
                if pascal:
                    print(f"Pascal found: {pascal.get_full_name()} (ID: {pascal.pk})")
                    pascal_payments = Payment.objects.filter(student=pascal)
                    print(f"Pascal payments: {pascal_payments.count()}")
                    for p in pascal_payments[:3]:  # Show first 3
                        print(f"  - Payment {p.pk}: {p.amount} on {p.payment_date}")
                        
                if sekai:
                    print(f"Sekai found: {sekai.get_full_name()} (ID: {sekai.pk})")
                    sekai_payments = Payment.objects.filter(student=sekai)
                    print(f"Sekai payments: {sekai_payments.count()}")
                    for p in sekai_payments[:3]:  # Show first 3
                        print(f"  - Payment {p.pk}: {p.amount} on {p.payment_date}")
                
                # Show all payments in this tenant
                all_payments = Payment.objects.all()[:5]
                if all_payments:
                    print(f"Sample payments in {tenant.schema_name}:")
                    for p in all_payments:
                        student_name = p.student.get_full_name() if p.student else "No student"
                        print(f"  - Payment {p.pk}: {p.amount} on {p.payment_date} for {student_name}")
                        
        except Exception as e:
            print(f"Error checking tenant {tenant.schema_name}: {e}")

if __name__ == "__main__":
    check_tenant_data()

{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .summary-card .card-title {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .summary-card .card-text {
            color: var(--bs-primary);
        }
        .table-payslips th, .table-payslips td {
            vertical-align: middle;
        }
        /* Style for the expand/collapse button */
        .btn-toggle-details {
            width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        /* Style for the hidden details row */
        .payslip-details-row {
            background-color: #f8f9fa; /* A light background to distinguish it */
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3 gap-2">
        <div>
            <h1 class="h3 mb-0">{{ view_title }}</h1>
            <p class="text-muted mb-0">Review the details and generated payslips for this payroll period.</p>
        </div>
        <div class="text-end">
            {# --- "MARK AS PAID" BUTTON LOGIC --- #}
            {% if payroll_run.status == 'PROCESSED' and perms.hr.manage_payroll %} {# Use your actual permission #}
                <form action="{% url 'hr:payroll_run_mark_paid' pk=payroll_run.pk %}" method="post" class="d-inline"
                    onsubmit="return confirm('Are you sure you have completed the bank transfers and want to mark this payroll as PAID? This action cannot be easily undone.')">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle-fill me-1"></i> Mark as Paid
                    </button>
                </form>
            {% endif %}

            <a href="{% url 'hr:payroll_run_list' %}" class="btn btn-secondary ms-2">
                <i class="bi bi-arrow-left-circle me-1"></i> Back to All Payrolls
            </a>
        </div>
    </div>
    
    {% include "partials/_messages.html" %}

    {# Summary Cards - Kept exactly as you had them #}
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-primary border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-primary fw-bold">Status</div>
                            <div class="h5 mb-0 fw-bold">{{ payroll_run.get_status_display }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-flag-fill fs-2 text-muted"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-success border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-success fw-bold">Total Net Pay</div>
                            <div class="h5 mb-0 fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ total_net_pay|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-wallet2 fs-2 text-muted"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-info border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-info fw-bold">Total Gross Earnings</div>
                            <div class="h5 mb-0 fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ total_gross_earnings|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-graph-up fs-2 text-muted"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-warning border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-warning fw-bold">Payslips Generated</div>
                            <div class="h5 mb-0 fw-bold">{{ payslip_count }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-people-fill fs-2 text-muted"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# List of Payslips in this Run - NOW WITH COLLAPSIBLE DETAILS #}
    <div class="card shadow-sm">
        <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Payslips in this Run" %}</h6>
            {# Add an export button here later? #}
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0 table-payslips">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 5%;"></th> {# Column for the expand/collapse button #}
                            <th>{% trans "Staff Member" %}</th>
                            <th class="text-end">{% trans "Gross Salary" %}</th>
                            <th class="text-end">{% trans "Total Deductions" %}</th>
                            <th class="text-end">{% trans "Net Pay" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payslip in payslips %}
                            {# Main Summary Row #}
                            <tr class="align-middle">
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary btn-toggle-details" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ payslip.pk }}" aria-expanded="false" aria-controls="collapse-{{ payslip.pk }}">
                                        <i class="bi bi-plus-lg"></i>
                                    </button>
                                </td>
                                <td>
                                    <strong>{{ payslip.staff.full_name }}</strong><br>
                                    <small class="text-muted">{{ payslip.staff.employee_id|default:"No ID" }}</small>
                                </td>
                                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.gross_salary|floatformat:2|intcomma }}</td>
                                <td class="text-end text-danger">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.total_deductions|floatformat:2|intcomma }}</td>
                                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.net_pay|floatformat:2|intcomma }}</td>
                                <td class="text-center">
                                    <a href="{% url 'hr:payslip_pdf' pk=payslip.pk %}" target="_blank" class="btn btn-sm btn-outline-danger" title="View PDF Payslip">
                                        <i class="bi bi-file-earmark-pdf-fill"></i>
                                    </a>
                                </td>
                            </tr>
                            {# Collapsible Detail Row #}
                            <tr>
                                <td colspan="6" class="p-0 border-0"> {# Colspan must match number of columns #}
                                    <div class="collapse" id="collapse-{{ payslip.pk }}">
                                        <div class="p-3 payslip-details-row">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>Earnings & Adjustments</h6>
                                                    <dl class="row mb-0">
                                                        <dt class="col-sm-6">Base Gross Salary</dt>
                                                        <dd class="col-sm-6 text-end">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.gross_salary|floatformat:2|intcomma }}</dd>
                                                        
                                                        {% if payslip.adjustments != 0 %}
                                                            <dt class="col-sm-6 mt-2">
                                                                {% if payslip.adjustments > 0 %}Bonus / Other Earnings{% else %}Other Deductions{% endif %}
                                                            </dt>
                                                            <dd class="col-sm-6 text-end mt-2 {% if payslip.adjustments > 0 %}text-success{% else %}text-danger{% endif %}">
                                                                {{ school_profile.currency_symbol|default:'$' }}{{ payslip.adjustments|floatformat:2|intcomma }}
                                                            </dd>
                                                        {% endif %}
                                                    </dl>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Deductions Breakdown</h6>
                                                    <dl class="row mb-0">
                                                        <dt class="col-sm-6">Income Tax</dt>
                                                        <dd class="col-sm-6 text-end text-danger">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.tax_deducted|floatformat:2|intcomma }}</dd>
                                                        
                                                        {# Loop through the JSON field for other deductions #}
                                                        {% for name, amount in payslip.other_deductions.items %}
                                                            <dt class="col-sm-6">{{ name }}</dt>
                                                            <dd class="col-sm-6 text-end text-danger">{{ school_profile.currency_symbol|default:'$' }}{{ amount|floatformat:2|intcomma }}</dd>
                                                        {% endfor %}
                                                        
                                                        <hr class="my-2">
                                                        
                                                        <dt class="col-sm-6 fw-bold">Total Deductions</dt>
                                                        <dd class="col-sm-6 text-end text-danger fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.total_deductions|floatformat:2|intcomma }}</dd>
                                                    </dl>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center p-4 text-muted">{% trans "No individual payslips were found for this payroll run." %}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



































{% comment %} {% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .summary-card .card-title {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .summary-card .card-text {
            color: var(--bs-primary);
        }
        .table-payslips th, .table-payslips td {
            vertical-align: middle;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3 gap-2">
        <div>
            <h1 class="h3 mb-0">{{ view_title }}</h1>
            <p class="text-muted mb-0">Review the details and generated payslips for this payroll period.</p>
        </div>
        <div class="text-end">
            {# --- "MARK AS PAID" BUTTON LOGIC --- #}
            {# This button only appears if the run has been processed but not yet paid. #}
            {% if payroll_run.status == 'PROCESSED' and perms.hr.manage_payroll %}
                <form action="{% url 'hr:payroll_run_mark_paid' pk=payroll_run.pk %}" method="post" class="d-inline"
                    onsubmit="return confirm('Are you sure you have completed the bank transfers and want to mark this payroll as PAID? This action cannot be easily undone.')">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle-fill me-1"></i> Mark as Paid
                    </button>
                </form>
            {% endif %}

            <a href="{% url 'hr:payroll_run_list' %}" class="btn btn-secondary ms-2">
                <i class="bi bi-arrow-left-circle me-1"></i> Back to All Payrolls
            </a>
        </div>
    </div>
    
    {% include "partials/_messages.html" %}

    {# Summary Cards #}
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-primary border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-primary fw-bold">Status</div>
                            <div class="h5 mb-0 fw-bold">{{ payroll_run.get_status_display }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-flag-fill fs-2 text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-success border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-success fw-bold">Total Net Pay</div>
                            <div class="h5 mb-0 fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ total_net_pay|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-wallet2 fs-2 text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-info border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-info fw-bold">Total Gross Earnings</div>
                            <div class="h5 mb-0 fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ total_gross_earnings|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-graph-up fs-2 text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card summary-card border-start border-warning border-4 h-100 shadow-sm">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="card-title text-warning fw-bold">Payslips Generated</div>
                            <div class="h5 mb-0 fw-bold">{{ payslip_count }}</div>
                        </div>
                        <div class="col-auto"><i class="bi bi-people-fill fs-2 text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# List of Payslips in this Run #}
    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Payslips in this Run" %}</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0 table-payslips">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Staff Member" %}</th>
                            <th class="text-end">{% trans "Gross Earnings" %}</th>
                            <th class="text-end">{% trans "Total Deductions" %}</th>
                            <th class="text-end">{% trans "Net Pay" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payslip in payslips %}
                            <tr>
                                <td>{{ payslip.staff_member.get_full_name }}</td>
                                <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.gross_earnings|floatformat:2|intcomma }}</td>
                                <td class="text-end">({{ school_profile.currency_symbol|default:'$' }}{{ payslip.total_deductions|floatformat:2|intcomma }})</td>
                                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ payslip.net_pay|floatformat:2|intcomma }}</td>
                                <td class="text-center">
                                    <a href="{% url 'hr:payslip_pdf' pk=payslip.pk %}" target="_blank" class="btn btn-sm btn-outline-danger" title="View PDF Payslip">
                                        <i class="bi bi-file-earmark-pdf-fill"></i>
                                    </a>
                                    {# You could add a link to a future HTML detail view of the payslip here #}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="5" class="text-center p-4 text-muted">{% trans "No individual payslips were found for this payroll run." %}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}


 {% endcomment %}





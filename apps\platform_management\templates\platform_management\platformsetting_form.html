{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformsetting_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Platform Setting") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .form-check {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .form-check-input {
            width: 1.5rem;
            height: 1.5rem;
            margin-top: 0.125rem;
            border: 2px solid #667eea;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .fieldset-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            margin: 2rem 0 1.5rem 0;
            text-align: center;
        }

        .fieldset-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .setting-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 2px solid #bbdefb;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .setting-info h6 {
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .setting-info p {
            color: #424242;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}
{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Platform Setting") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:platformsetting_list' %}">{% trans "Settings" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}{% trans "Edit Setting" %}{% else %}{% trans "New Setting" %}{% endif %}
            </li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-gear-fill me-2"></i>
                        {% if object %}{% trans "Edit Platform Setting" %}{% else %}{% trans "Create Platform Setting" %}{% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Information Section -->
                    <div class="setting-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "Platform Setting Information" %}</h6>
                        <p>{% trans "Platform settings control system-wide behavior and configuration. Changes to these settings affect all tenants and users on the platform." %}</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        <!-- Setting Configuration Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-sliders me-2"></i>{% trans "Setting Configuration" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Setting Name Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if form.setting_name.errors %} is-invalid{% endif %}"
                                           id="{{ form.setting_name.id_for_label }}"
                                           name="{{ form.setting_name.name }}"
                                           value="{{ form.setting_name.value|default:'' }}"
                                           placeholder="{% trans 'e.g., SITE_REGISTRATION_OPEN' %}"
                                           {% if form.setting_name.field.required %}required{% endif %}>
                                    <label for="{{ form.setting_name.id_for_label }}">
                                        <i class="bi bi-tag-fill icon-input"></i>{% trans "Setting Name" %}
                                    </label>
                                    {% if form.setting_name.help_text %}
                                        <div class="form-text">{{ form.setting_name.help_text }}</div>
                                    {% endif %}
                                    {% if form.setting_name.errors %}
                                        <div class="invalid-feedback">{{ form.setting_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Is Active Field -->
                                <div class="form-check">
                                    <input type="checkbox"
                                           class="form-check-input{% if form.is_active.errors %} is-invalid{% endif %}"
                                           id="{{ form.is_active.id_for_label }}"
                                           name="{{ form.is_active.name }}"
                                           value="1"
                                           {% if form.is_active.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="bi bi-toggle-on me-2"></i>{% trans "Active Setting" %}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <div class="form-text">{{ form.is_active.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Setting Value Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if form.setting_value.errors %} is-invalid{% endif %}"
                                              id="{{ form.setting_value.id_for_label }}"
                                              name="{{ form.setting_value.name }}"
                                              placeholder="{% trans 'e.g., True, path/to/logo.png, #FFFFFF' %}"
                                              style="height: 100px;"
                                              {% if form.setting_value.field.required %}required{% endif %}>{{ form.setting_value.value|default:'' }}</textarea>
                                    <label for="{{ form.setting_value.id_for_label }}">
                                        <i class="bi bi-code-square icon-input"></i>{% trans "Setting Value" %}
                                    </label>
                                    {% if form.setting_value.help_text %}
                                        <div class="form-text">{{ form.setting_value.help_text }}</div>
                                    {% endif %}
                                    {% if form.setting_value.errors %}
                                        <div class="invalid-feedback">{{ form.setting_value.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Description Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if form.description.errors %} is-invalid{% endif %}"
                                              id="{{ form.description.id_for_label }}"
                                              name="{{ form.description.name }}"
                                              placeholder="{% trans 'Brief explanation of this setting' %}"
                                              style="height: 80px;"
                                              {% if form.description.field.required %}required{% endif %}>{{ form.description.value|default:'' }}</textarea>
                                    <label for="{{ form.description.id_for_label }}">
                                        <i class="bi bi-file-text icon-input"></i>{% trans "Description" %}
                                    </label>
                                    {% if form.description.help_text %}
                                        <div class="form-text">{{ form.description.help_text }}</div>
                                    {% endif %}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'platform_management:platformsetting_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}{% trans "Update Setting" %}{% else %}{% trans "Create Setting" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation and enhancement
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Setting name validation - convert to uppercase and validate format
        const settingNameInput = document.getElementById('{{ form.setting_name.id_for_label }}');
        if (settingNameInput) {
            settingNameInput.addEventListener('input', function() {
                // Convert to uppercase
                this.value = this.value.toUpperCase();

                // Validate format (letters, numbers, underscores only)
                const validPattern = /^[A-Z0-9_]*$/;
                if (this.value && !validPattern.test(this.value)) {
                    this.setCustomValidity('{% trans "Setting name can only contain uppercase letters, numbers, and underscores" %}');
                    this.classList.add('is-invalid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                }
            });
        }

        // Auto-focus first input
        const firstInput = form.querySelector('input[type="text"], textarea');
        if (firstInput) {
            firstInput.focus();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // Setting value format hints based on common patterns
        const settingValueInput = document.getElementById('{{ form.setting_value.id_for_label }}');
        if (settingValueInput && settingNameInput) {
            settingNameInput.addEventListener('input', function() {
                const settingName = this.value.toLowerCase();
                let placeholder = '{% trans "Enter setting value" %}';

                if (settingName.includes('color')) {
                    placeholder = 'e.g., #FFFFFF, rgb(255,255,255)';
                } else if (settingName.includes('url') || settingName.includes('path')) {
                    placeholder = 'e.g., https://example.com, /path/to/file';
                } else if (settingName.includes('enable') || settingName.includes('active')) {
                    placeholder = 'e.g., True, False';
                } else if (settingName.includes('count') || settingName.includes('limit')) {
                    placeholder = 'e.g., 100, 50';
                }

                settingValueInput.setAttribute('placeholder', placeholder);
            });
        }
    });
    </script>
{% endblock %}
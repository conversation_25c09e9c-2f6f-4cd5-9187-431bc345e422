{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\auditlog_list.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{% trans "Audit Logs" %}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{% trans "Platform Audit Logs" %}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Audit Logs" %}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">{% trans "Filter Logs" %}</h5>
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="id_action_type" class="form-label">{% trans "Action Type" %}</label>
                    <select name="action_type" id="id_action_type" class="form-select">
                        <option value="">{% trans "All Actions" %}</option>
                        {% for code, name in action_types %}
                            <option value="{{ code }}" {% if selected_action == code %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="id_user" class="form-label">{% trans "User" %}</label>
                    <select name="user" id="id_user" class="form-select">
                        <option value="">{% trans "All Users" %}</option>
                        {% for u in users_for_filter %}
                            <option value="{{ u.pk }}" {% if selected_user == u.pk|stringformat:"s" %}selected{% endif %}>{{ u.username }} ({{ u.get_full_name|default:"N/A" }})</option>
                        {% endfor %}
                    </select>
                </div>
                {# Add date range filters later if needed #}
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">{% trans "Filter" %}</button>
                </div>
                 <div class="col-md-2">
                    <a href="{% url 'platform_management:auditlog_list' %}" class="btn btn-outline-secondary w-100">{% trans "Clear" %}</a>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">{% trans "Log Entries" %}</h5>
            {% if logs %}
            <div class="table-responsive">
                <table class="table table-sm table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "Timestamp" %}</th>
                            <th>{% trans "User" %}</th>
                            <th>{% trans "Action Type" %}</th>
                            <th>{% trans "Model" %}</th>
                            <th>{% trans "Object PK" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "IP Address" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log_entry in logs %}
                        <tr>
                            <td><small>{{ log_entry.timestamp|date:"Y-m-d P" }}</small></td>
                            <td><small>{{ log_entry.user.username|default:"System" }}</small></td>
                            <td><span class="badge bg-info text-dark">{{ log_entry.get_action_type_display }}</span></td>
                            <td><small>{{ log_entry.model_name|default:"N/A" }}</small></td>
                            <td><small>{{ log_entry.object_pk|default:"N/A" }}</small></td>
                            <td>{{ log_entry.description|truncatewords:20 }}</td>
                            <td><small>{{ log_entry.ip_address|default:"N/A" }}</small></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include "partials/_pagination.html" with page_obj=logs %} {# Assuming logs is your page_obj #}
            {% else %}
            <p class="text-muted">{% trans "No audit logs found matching your criteria." %}</p>
            {% endif %}
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}



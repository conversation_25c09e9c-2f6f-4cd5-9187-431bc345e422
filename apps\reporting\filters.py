# D:\school_fees_saas_v2\apps\reporting\filters.py
import django_filters
from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta

# Assuming these models are correctly located
from apps.schools.models import SchoolClass, Term, AcademicYear 

class DateRangeClassTermForm(django_filters.FilterSet):
    # This form can be used by multiple reports that need these common filters
    
    # If reports need a single "as_of_date" instead of a range:
    report_date = django_filters.DateFilter(
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        # initial=timezone.localdate # Initial can also be set in view's get_initial_filter_data
    )

    start_date = django_filters.DateFilter(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'})
    )
    end_date = django_filters.DateFilter(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'})
    )
    
    academic_year = django_filters.ModelChoiceFilter(
        queryset=AcademicYear.objects.all().order_by('-start_date'), # Assuming AcademicYear is shared or tenant-scoped
        label=_("Academic Year"),
        empty_label=_("-- All Academic Years --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    term = django_filters.ModelChoiceFilter(
        queryset=Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'name'),
        label=_("Term"),
        empty_label=_("-- All Terms --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    school_class = django_filters.ModelChoiceFilter(
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'), # Assuming SchoolClass is tenant-scoped
        label=_("Class"),
        empty_label=_("-- All Classes --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    
    class Meta:
        # This FilterSet might not be directly filtering a single model
        # if it's just used to collect parameters for various reports.
        # If it were filtering, e.g., an 'ActivityLog' model:
        # model = ActivityLog
        # fields = ['report_date', 'start_date', 'end_date', 'academic_year', 'term', 'school_class']
        # For now, let's assume it's a parameter collection form for reports.
        # If used with ListView that has a model, specify model & fields it CAN filter.
        # from apps.students.models import Student # Example if filtering students
        # model = Student
        # fields = ['school_class'] # Only school_class from above would auto-filter Student.current_class

        # If it's just a parameter form, Meta can be empty or just specify form if needed
        fields = {} # No direct model filtering by default from this FilterSet's Meta

    def __init__(self, *args, **kwargs):
        self.tenant = kwargs.pop('tenant', None) # If passed from view's get_filter_form_kwargs
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

        # Dynamically filter querysets if needed, e.g., based on tenant, if not auto-scoped by django-tenants
        if self.tenant:
            if 'academic_year' in self.filters and hasattr(AcademicYear, 'tenant'): # If AY is explicitly tenant-linked
                self.filters['academic_year'].queryset = AcademicYear.objects.filter(tenant=self.tenant).order_by('-start_date')
            if 'term' in self.filters and hasattr(Term, 'tenant'):
                self.filters['term'].queryset = Term.objects.filter(tenant=self.tenant).select_related('academic_year').order_by('-academic_year__start_date', 'name')
            # SchoolClass is likely already tenant-scoped if it's in a TENANT_APP like 'schools'
            # self.filters['school_class'].queryset = SchoolClass.objects.filter(tenant=self.tenant, is_active=True)...

# You might also have other specific filter forms here:
# class TrialBalanceReportForm(forms.Form): ...
# class IncomeExpenseReportForm(forms.Form): ...
# OR, if these are simple, they can just use relevant fields from DateRangeClassTermForm
# by specifying `fields` in the view's `filter_form_class.Meta` or directly in the view.
# For consistency, it's often good to have specific forms even if they reuse fields.

# Example: If TrialBalanceReportForm only needs 'report_date'
class ReportPeriodForm(forms.Form): # For Trial Balance
    report_date = forms.DateField(
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.localdate, # Default to today
        required=True
    )
    def __init__(self, *args, **kwargs):
        kwargs.pop('tenant', None) # Remove tenant if not used
        kwargs.pop('request', None)
        super().__init__(*args, **kwargs)


# IncomeExpenseReportForm might be the same as DateRangeClassTermForm or simpler
class IncomeExpenseReportForm(forms.Form): # For P&L and Cash Flow
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    # You could add academic_year or term here if these reports can be filtered by them
    # academic_year = forms.ModelChoiceField(queryset=AcademicYear.objects.all().order_by('-start_date'), required=False, ...)

    def __init__(self, *args, **kwargs):
        kwargs.pop('tenant', None)
        kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        if start_date and end_date and end_date < start_date:
            raise forms.ValidationError(_("End date cannot be before start date."))
        return cleaned_data

# Ensure BudgetVarianceReportForm is also defined if used by BudgetVarianceReportView
class BudgetVarianceReportForm(forms.Form): # For Budget Variance
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        label=_("Academic Year"),
        empty_label=_("-- Select Academic Year --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        required=True
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'name'),
        label=_("Term (Optional)"),
        empty_label=_("-- Whole Academic Year --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        required=False
    )

    def __init__(self, *args, **kwargs):
        self.tenant = kwargs.pop('tenant', None)
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        if self.tenant: # Or if using django-tenants auto-scoping
            self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date') # Already scoped
            self.fields['term'].queryset = Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'name') # Already scoped
        # Dynamic filtering of term based on academic_year selection would need JS or a form refresh
        
        

# D:\school_fees_saas_v2\apps\reporting\forms.py
from django import forms
from apps.students.models import Student

class StudentLedgerFilterForm(forms.Form):
    """
    A form to capture user input for the Student Ledger report.
    This is a standard Django Form, not a django-filter FilterSet.
    """
    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(is_active=True).order_by('first_name', 'last_name'),
        required=True, # A student must be selected to run the ledger
        label="Select Student",
        widget=forms.Select(attrs={'class': 'form-select select2-field'}) # Added class for Select2 JS
    )
    
    start_date = forms.DateField(
        required=False, # Make it optional
        label='Start Date',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = forms.DateField(
        required=False, # Make it optional
        label='End Date',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    # --- THIS IS THE FIX ---
    def __init__(self, *args, **kwargs):
        # Pop the custom 'tenant' or 'request' kwarg before calling super()
        # Your mixin might be passing either, so we can handle both.
        tenant = kwargs.pop('tenant', None)
        request = kwargs.pop('request', None)
        
        # Now call the parent __init__ with the remaining standard kwargs
        super().__init__(*args, **kwargs)
        
        # Now you can use the tenant or request to dynamically set the queryset.
        # This is good practice as it scopes the student dropdown to the current tenant.
        # Since queries on tenant-aware models are automatically scoped by the
        # middleware, this is often sufficient. We add .all() to be explicit.
        self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('first_name', 'last_name')
        
        

{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block title %}{{ view_title|default:"School Profile" }} - {{ tenant_name|default:request.tenant.name }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        /* Premium Form Design - School Profile */
        .premium-card {
            border: none;
            border-radius: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            max-width: 1000px;
            margin: 2rem auto;
        }

        .premium-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .premium-header {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .premium-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .premium-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 2rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .premium-header p {
            margin: 0;
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .premium-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .form-floating {
            position: relative;
            margin-bottom: 2rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem 0.75rem 0.5rem 0.75rem;
            background-color: #fff;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #20c997;
            box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
            transform: translateY(-2px);
        }

        .form-floating > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1.25rem 0.75rem;
            pointer-events: none;
            border: 2px solid transparent;
            transform-origin: 0 0;
            transition: all 0.3s ease;
            color: #20c997;
            font-weight: 500;
            z-index: 2;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label,
        .form-floating > .form-select ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
            color: #20c997;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }

        .icon-input {
            color: #20c997;
            margin-right: 0.5rem;
        }

        .fieldset-header {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border: 2px solid #e9ecef;
            border-radius: 1.2rem;
            padding: 2rem;
            margin-bottom: 2.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(32, 201, 151, 0.1);
        }

        .fieldset-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .fieldset-header:hover::before {
            left: 100%;
        }

        .fieldset-header h4 {
            color: #20c997;
            font-weight: 700;
            margin: 0;
            font-size: 1.3rem;
            text-shadow: 0 1px 3px rgba(32, 201, 151, 0.2);
            position: relative;
            z-index: 2;
        }

        .fieldset-header i {
            font-size: 1.5rem;
            margin-right: 0.8rem;
            color: #20c997;
            text-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
        }

        .btn-premium {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            border: none;
            border-radius: 1rem;
            padding: 1.2rem 3rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(32, 201, 151, 0.4);
            position: relative;
            overflow: hidden;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 200px;
            font-size: 0.95rem;
        }

        .btn-premium:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(32, 201, 151, 0.6);
            color: white;
            text-decoration: none;
            background: linear-gradient(135deg, #1ea085 0%, #138496 100%);
        }

        .btn-secondary-premium {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 1rem;
            padding: 1.2rem 3rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 200px;
            font-size: 0.95rem;
        }

        .btn-secondary-premium:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.6);
            color: white;
            text-decoration: none;
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
        }

        .form-body {
            padding: 3rem;
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545;
        }

        .form-control.is-invalid:focus,
        .form-select.is-invalid:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="premium-card">
        <!-- Premium Header -->
        <div class="premium-header">
            <i class="fas fa-school"></i>
            <h3 class="mb-2 fw-bold">{% trans "School Profile & Settings" %}</h3>
            <p class="mb-0 opacity-75">
                Configure your school's information, financial settings, and system preferences
            </p>
        </div>

        <!-- Form Body -->
        <div class="form-body">
            {% include "partials/_messages.html" %}

            <form method="post" enctype="multipart/form-data" novalidate id="schoolProfileForm">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- General Information Section -->
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-info-circle"></i>{% trans "General Information" %}
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.school_name_override.errors %} is-invalid{% endif %}"
                                   id="{{ form.school_name_override.id_for_label }}"
                                   name="{{ form.school_name_override.name }}"
                                   value="{{ form.school_name_override.value|default:'' }}"
                                   placeholder="{% trans 'School Name Override' %}">
                            <label for="{{ form.school_name_override.id_for_label }}">
                                <i class="fas fa-school icon-input"></i>{% trans "School Name (Display Override)" %}
                            </label>
                            {% if form.school_name_override.errors %}
                                <div class="invalid-feedback">{{ form.school_name_override.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.school_motto.errors %} is-invalid{% endif %}"
                                   id="{{ form.school_motto.id_for_label }}"
                                   name="{{ form.school_motto.name }}"
                                   value="{{ form.school_motto.value|default:'' }}"
                                   placeholder="{% trans 'School Motto' %}">
                            <label for="{{ form.school_motto.id_for_label }}">
                                <i class="fas fa-quote-left icon-input"></i>{% trans "School Motto" %}
                            </label>
                            {% if form.school_motto.errors %}
                                <div class="invalid-feedback">{{ form.school_motto.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="email"
                                   class="form-control{% if form.school_email.errors %} is-invalid{% endif %}"
                                   id="{{ form.school_email.id_for_label }}"
                                   name="{{ form.school_email.name }}"
                                   value="{{ form.school_email.value|default:'' }}"
                                   placeholder="{% trans 'School Email' %}">
                            <label for="{{ form.school_email.id_for_label }}">
                                <i class="fas fa-envelope icon-input"></i>{% trans "School Contact Email" %}
                            </label>
                            {% if form.school_email.errors %}
                                <div class="invalid-feedback">{{ form.school_email.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="tel"
                                   class="form-control{% if form.phone_number.errors %} is-invalid{% endif %}"
                                   id="{{ form.phone_number.id_for_label }}"
                                   name="{{ form.phone_number.name }}"
                                   value="{{ form.phone_number.value|default:'' }}"
                                   placeholder="{% trans 'Phone Number' %}">
                            <label for="{{ form.phone_number.id_for_label }}">
                                <i class="fas fa-phone icon-input"></i>{% trans "School Contact Phone" %}
                            </label>
                            {% if form.phone_number.errors %}
                                <div class="invalid-feedback">{{ form.phone_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Address Information Section -->
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-map-marker-alt"></i>{% trans "Address Information" %}
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.address_line1.errors %} is-invalid{% endif %}"
                                   id="{{ form.address_line1.id_for_label }}"
                                   name="{{ form.address_line1.name }}"
                                   value="{{ form.address_line1.value|default:'' }}"
                                   placeholder="{% trans 'Address Line 1' %}">
                            <label for="{{ form.address_line1.id_for_label }}">
                                <i class="fas fa-home icon-input"></i>{% trans "Address Line 1" %}
                            </label>
                            {% if form.address_line1.errors %}
                                <div class="invalid-feedback">{{ form.address_line1.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.address_line2.errors %} is-invalid{% endif %}"
                                   id="{{ form.address_line2.id_for_label }}"
                                   name="{{ form.address_line2.name }}"
                                   value="{{ form.address_line2.value|default:'' }}"
                                   placeholder="{% trans 'Address Line 2' %}">
                            <label for="{{ form.address_line2.id_for_label }}">
                                <i class="fas fa-building icon-input"></i>{% trans "Address Line 2" %}
                            </label>
                            {% if form.address_line2.errors %}
                                <div class="invalid-feedback">{{ form.address_line2.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.city.errors %} is-invalid{% endif %}"
                                   id="{{ form.city.id_for_label }}"
                                   name="{{ form.city.name }}"
                                   value="{{ form.city.value|default:'' }}"
                                   placeholder="{% trans 'City' %}">
                            <label for="{{ form.city.id_for_label }}">
                                <i class="fas fa-city icon-input"></i>{% trans "City" %}
                            </label>
                            {% if form.city.errors %}
                                <div class="invalid-feedback">{{ form.city.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.state_province.errors %} is-invalid{% endif %}"
                                   id="{{ form.state_province.id_for_label }}"
                                   name="{{ form.state_province.name }}"
                                   value="{{ form.state_province.value|default:'' }}"
                                   placeholder="{% trans 'State/Province' %}">
                            <label for="{{ form.state_province.id_for_label }}">
                                <i class="fas fa-map icon-input"></i>{% trans "State/Province" %}
                            </label>
                            {% if form.state_province.errors %}
                                <div class="invalid-feedback">{{ form.state_province.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.postal_code.errors %} is-invalid{% endif %}"
                                   id="{{ form.postal_code.id_for_label }}"
                                   name="{{ form.postal_code.name }}"
                                   value="{{ form.postal_code.value|default:'' }}"
                                   placeholder="{% trans 'Postal Code' %}">
                            <label for="{{ form.postal_code.id_for_label }}">
                                <i class="fas fa-mail-bulk icon-input"></i>{% trans "Postal Code" %}
                            </label>
                            {% if form.postal_code.errors %}
                                <div class="invalid-feedback">{{ form.postal_code.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.country_name.errors %} is-invalid{% endif %}"
                                   id="{{ form.country_name.id_for_label }}"
                                   name="{{ form.country_name.name }}"
                                   value="{{ form.country_name.value|default:'' }}"
                                   placeholder="{% trans 'Country' %}">
                            <label for="{{ form.country_name.id_for_label }}">
                                <i class="fas fa-globe icon-input"></i>{% trans "Country" %}
                            </label>
                            {% if form.country_name.errors %}
                                <div class="invalid-feedback">{{ form.country_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="file"
                                   class="form-control{% if form.logo.errors %} is-invalid{% endif %}"
                                   id="{{ form.logo.id_for_label }}"
                                   name="{{ form.logo.name }}"
                                   accept="image/*">
                            <label for="{{ form.logo.id_for_label }}">
                                <i class="fas fa-image icon-input"></i>{% trans "School Logo" %}
                            </label>
                            {% if form.logo.errors %}
                                <div class="invalid-feedback">{{ form.logo.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Financial Settings Section -->
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-dollar-sign"></i>{% trans "Financial Settings" %}
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.school_name_on_reports.errors %} is-invalid{% endif %}"
                                   id="{{ form.school_name_on_reports.id_for_label }}"
                                   name="{{ form.school_name_on_reports.name }}"
                                   value="{{ form.school_name_on_reports.value|default:'' }}"
                                   placeholder="{% trans 'School Name on Reports' %}">
                            <label for="{{ form.school_name_on_reports.id_for_label }}">
                                <i class="fas fa-file-alt icon-input"></i>{% trans "School Name (for Reports)" %}
                            </label>
                            {% if form.school_name_on_reports.errors %}
                                <div class="invalid-feedback">{{ form.school_name_on_reports.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.currency_symbol.errors %} is-invalid{% endif %}"
                                   id="{{ form.currency_symbol.id_for_label }}"
                                   name="{{ form.currency_symbol.name }}"
                                   value="{{ form.currency_symbol.value|default:'' }}"
                                   maxlength="5"
                                   placeholder="{% trans 'Currency Symbol' %}">
                            <label for="{{ form.currency_symbol.id_for_label }}">
                                <i class="fas fa-coins icon-input"></i>{% trans "Currency Symbol" %}
                            </label>
                            {% if form.currency_symbol.errors %}
                                <div class="invalid-feedback">{{ form.currency_symbol.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.financial_year_start_month.errors %} is-invalid{% endif %}"
                                    id="{{ form.financial_year_start_month.id_for_label }}"
                                    name="{{ form.financial_year_start_month.name }}">
                                <option value="">{% trans "Select Month" %}</option>
                                {% for value, label in form.financial_year_start_month.field.choices %}
                                    <option value="{{ value }}" {% if form.financial_year_start_month.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.financial_year_start_month.id_for_label }}">
                                <i class="fas fa-calendar-alt icon-input"></i>{% trans "Financial Year Start Month" %}
                            </label>
                            {% if form.financial_year_start_month.errors %}
                                <div class="invalid-feedback">{{ form.financial_year_start_month.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="number"
                                   class="form-control{% if form.default_due_days.errors %} is-invalid{% endif %}"
                                   id="{{ form.default_due_days.id_for_label }}"
                                   name="{{ form.default_due_days.name }}"
                                   value="{{ form.default_due_days.value|default:'' }}"
                                   min="0"
                                   placeholder="{% trans 'Default Due Days' %}">
                            <label for="{{ form.default_due_days.id_for_label }}">
                                <i class="fas fa-clock icon-input"></i>{% trans "Default Invoice Due Days" %}
                            </label>
                            {% if form.default_due_days.errors %}
                                <div class="invalid-feedback">{{ form.default_due_days.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-floating">
                            <select class="form-select{% if form.current_academic_year.errors %} is-invalid{% endif %}"
                                    id="{{ form.current_academic_year.id_for_label }}"
                                    name="{{ form.current_academic_year.name }}">
                                <option value="">{% trans "Select Academic Year" %}</option>
                                {% for value, label in form.current_academic_year.field.choices %}
                                    <option value="{{ value }}" {% if form.current_academic_year.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.current_academic_year.id_for_label }}">
                                <i class="fas fa-graduation-cap icon-input"></i>{% trans "Current Academic Year" %}
                            </label>
                            {% if form.current_academic_year.errors %}
                                <div class="invalid-feedback">{{ form.current_academic_year.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Chart of Accounts Settings Section -->
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-chart-line"></i>{% trans "Default Chart of Accounts" %}
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_accounts_receivable_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_accounts_receivable_coa.id_for_label }}"
                                    name="{{ form.default_accounts_receivable_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_accounts_receivable_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_accounts_receivable_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_accounts_receivable_coa.id_for_label }}">
                                <i class="fas fa-money-bill-wave icon-input"></i>{% trans "Default Accounts Receivable (CoA)" %}
                            </label>
                            {% if form.default_accounts_receivable_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_accounts_receivable_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_cash_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_cash_coa.id_for_label }}"
                                    name="{{ form.default_cash_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_cash_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_cash_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_cash_coa.id_for_label }}">
                                <i class="fas fa-cash-register icon-input"></i>{% trans "Default Cash (CoA)" %}
                            </label>
                            {% if form.default_cash_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_cash_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_bank_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_bank_coa.id_for_label }}"
                                    name="{{ form.default_bank_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_bank_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_bank_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_bank_coa.id_for_label }}">
                                <i class="fas fa-university icon-input"></i>{% trans "Default Bank (CoA)" %}
                            </label>
                            {% if form.default_bank_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_bank_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_fee_income_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_fee_income_coa.id_for_label }}"
                                    name="{{ form.default_fee_income_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_fee_income_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_fee_income_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_fee_income_coa.id_for_label }}">
                                <i class="fas fa-hand-holding-usd icon-input"></i>{% trans "Default Fee Income (CoA)" %}
                            </label>
                            {% if form.default_fee_income_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_fee_income_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_discount_given_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_discount_given_coa.id_for_label }}"
                                    name="{{ form.default_discount_given_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_discount_given_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_discount_given_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_discount_given_coa.id_for_label }}">
                                <i class="fas fa-percentage icon-input"></i>{% trans "Default Discount Given (CoA)" %}
                            </label>
                            {% if form.default_discount_given_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_discount_given_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select class="form-select{% if form.default_expense_coa.errors %} is-invalid{% endif %}"
                                    id="{{ form.default_expense_coa.id_for_label }}"
                                    name="{{ form.default_expense_coa.name }}">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for value, label in form.default_expense_coa.field.choices %}
                                    <option value="{{ value }}" {% if form.default_expense_coa.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <label for="{{ form.default_expense_coa.id_for_label }}">
                                <i class="fas fa-receipt icon-input"></i>{% trans "Default General Expense (CoA)" %}
                            </label>
                            {% if form.default_expense_coa.errors %}
                                <div class="invalid-feedback">{{ form.default_expense_coa.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                    <button type="submit" class="btn-premium">
                        <i class="fas fa-save me-2"></i>{% trans "Save Profile & Settings" %}
                    </button>
                    <a href="{% url 'schools:dashboard' %}" class="btn-secondary-premium">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                    </a>
                </div>

            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission handling
        const form = document.getElementById('schoolProfileForm');
        const submitButton = form.querySelector('button[type="submit"]');

        form.addEventListener('submit', function(e) {
            // Prevent double submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Saving..." %}';
            submitButton.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>{% trans "Save Profile & Settings" %}';
                submitButton.style.background = 'linear-gradient(135deg, #20c997 0%, #17a2b8 100%)';
            }, 10000);
        });

        // File input enhancement
        const fileInput = document.getElementById('{{ form.logo.id_for_label }}');
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                const fileName = e.target.files[0]?.name;
                if (fileName) {
                    const label = fileInput.nextElementSibling;
                    label.innerHTML = '<i class="fas fa-image icon-input"></i>{% trans "Selected: " %}' + fileName;
                }
            });
        }
    });
</script>
{% endblock %}

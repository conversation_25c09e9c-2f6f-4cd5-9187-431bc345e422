{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block title %}{{ view_title|default:"School Profile" }} - {{ tenant_name|default:request.tenant.name }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .form-section h5 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="bi bi-building me-2"></i>{% trans "School Profile & Settings" %}
            </h4>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                {{ form.as_p }}

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>{% trans "Save Profile & Settings" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

# D:\school_fees_saas_v2\apps\hr\payroll.py

import logging
from decimal import Decimal
from .models import StaffSalary, TaxBracket, SalaryComponent, StaffUser

logger = logging.getLogger(__name__)

class PayrollProcessor:
    """
    A class dedicated to calculating a single payslip for a given staff member.
    It gathers all earnings, calculates statutory and regular deductions,
    and determines the final net pay.
    """

    def __init__(self, staff_user: StaffUser, pay_period_start, pay_period_end):
        """
        Initializes the processor with the staff member and pay period.
        """
        self.staff_user = staff_user
        self.pay_period_start = pay_period_start
        self.pay_period_end = pay_period_end
        
        # These lists will hold the breakdown of the payslip
        self.earnings = []      # e.g., {'name': 'Basic Salary', 'amount': 5000}
        self.deductions = []    # e.g., {'name': 'Income Tax', 'amount': 250}
        
        # These will hold the final calculated totals
        self.gross_earnings = Decimal('0.00')
        self.total_deductions = Decimal('0.00')
        self.net_pay = Decimal('0.00')
        self.taxable_income = Decimal('0.00')

    def run(self):
        """
        This is the main public method to execute the payroll calculation.
        It runs all the steps in the correct order and returns the final results.
        """
        logger.info(f"Starting payroll processing for '{self.staff_user.email}' for period {self.pay_period_start} to {self.pay_period_end}.")
        
        # Step 0: Get the staff member's salary structure
        try:
            # The OneToOneField gives us direct access via 'salary_structure'
            salary_structure = self.staff_user.salary_structure
        except StaffSalary.DoesNotExist:
            logger.error(f"SKIPPING PAYROLL: No salary structure found for staff: {self.staff_user.email}.")
            return None # Return None to indicate failure

        # --- CALCULATION STEPS ---
        self._gather_earnings(salary_structure)
        self._gather_deductions(salary_structure)
        self._calculate_statutory_deductions()
        self._calculate_final_totals()

        # --- RETURN RESULTS ---
        # Return a clean dictionary of the calculated data.
        # The view that called this will use this data to create the Payslip models.
        return {
            'gross_earnings': self.gross_earnings,
            'total_deductions': self.total_deductions,
            'net_pay': self.net_pay,
            'earnings_lines': self.earnings,
            'deductions_lines': self.deductions,
        }

    def _gather_earnings(self, salary_structure: StaffSalary):
        """
        Step 1: Find all recurring earnings for the staff member.
        This includes their basic salary and any components marked as 'EARNING'.
        """
        # Start with the basic salary
        self.earnings.append({
            'name': 'Basic Salary', 
            'amount': salary_structure.basic_salary,
            'source_component': None # Basic salary doesn't have a component source
        })

        # Add all other recurring earning components
        earning_components = salary_structure.components.filter(component__type=SalaryComponent.ComponentType.EARNING)
        for item in earning_components:
            self.earnings.append({
                'name': item.component.name, 
                'amount': item.amount,
                'source_component': item.component
            })

        # Calculate the Gross Earnings
        self.gross_earnings = sum(item['amount'] for item in self.earnings)
        
        # Initially, assume all earnings are taxable.
        # A more advanced system could have a flag on SalaryComponent for taxability.
        self.taxable_income = self.gross_earnings
        
        logger.debug(f"  (1) Gross earnings calculated: {self.gross_earnings}")

    def _gather_deductions(self, salary_structure: StaffSalary):
        """
        Step 2: Find all *non-statutory*, recurring deductions.
        These are things like loan repayments, voluntary savings, etc.
        Statutory deductions (like tax) are calculated separately.
        """
        deduction_components = salary_structure.components.filter(
            component__type=SalaryComponent.ComponentType.DEDUCTION,
            component__is_statutory=False # We only want non-statutory deductions here
        )
        for item in deduction_components:
            self.deductions.append({
                'name': item.component.name, 
                'amount': item.amount,
                'source_component': item.component
            })
        logger.debug(f"  (2) Gathered {len(deduction_components)} non-statutory deductions.")

    def _calculate_statutory_deductions(self):
        """
        Step 3: Calculate mandatory, government-regulated deductions.
        This is where tax calculation happens.
        """
        # --- Income Tax Calculation ---
        income_tax = self._calculate_progressive_tax(self.taxable_income)
        if income_tax > 0:
            # We add the calculated tax to our list of deductions
            self.deductions.append({
                'name': 'Income Tax (PAYE)', # A standard name for this deduction
                'amount': income_tax,
                'source_component': None # It's calculated, not a fixed component
            })
        logger.debug(f"  (3a) Calculated Income Tax: {income_tax}")

        # --- Other Statutory Deductions ---
        # You would add other calculations here, e.g., Social Security
        # social_security = self.gross_earnings * Decimal('0.05') # Example
        # self.deductions.append({'name': 'Social Security', 'amount': social_security, ...})

    def _calculate_final_totals(self):
        """
        Step 4: Calculate the final Total Deductions and Net Pay.
        """
        self.total_deductions = sum(item['amount'] for item in self.deductions)
        self.net_pay = self.gross_earnings - self.total_deductions
        logger.debug(f"  (4) Final totals: Deductions={self.total_deductions}, Net Pay={self.net_pay}")

    def _calculate_progressive_tax(self, taxable_income: Decimal) -> Decimal:
        """
        Calculates income tax based on the tenant's defined TaxBracket model.
        This handles a progressive tax system where different parts of income
        are taxed at different rates.
        """
        if taxable_income <= 0:
            return Decimal('0.00')

        total_tax = Decimal('0.00')
        
        # Get all active tax brackets for the tenant, ordered by income level
        brackets = TaxBracket.objects.filter(is_active=True).order_by('from_amount')
        if not brackets.exists():
            logger.warning(f"No active tax brackets found for tenant. Tax will be calculated as 0.")
            return Decimal('0.00')

        # This logic correctly calculates tax for a progressive system
        for bracket in brackets:
            # If taxable income is below this bracket's start, we're done
            if taxable_income < bracket.from_amount:
                break
            
            # The upper limit of this bracket. If it's the top bracket, it has no upper limit.
            bracket_upper_bound = bracket.to_amount if bracket.to_amount is not None else taxable_income

            # Calculate how much of the person's income falls *within* this specific bracket
            taxable_in_this_bracket = min(taxable_income, bracket_upper_bound) - bracket.from_amount
            
            if taxable_in_this_bracket > 0:
                tax_for_this_bracket = (taxable_in_this_bracket * bracket.rate_percent) / 100
                total_tax += tax_for_this_bracket
        
        # A simpler way to handle deductions if they are a flat amount off the final calculated tax
        # This part of your logic may need to be adjusted based on specific government rules.
        final_bracket = brackets.filter(from_amount__lte=taxable_income).last()
        if final_bracket and final_bracket.deduction_amount > 0:
            total_tax -= final_bracket.deduction_amount

        return max(total_tax, Decimal('0.00')) # Ensure tax is never negative
    
    
    
    
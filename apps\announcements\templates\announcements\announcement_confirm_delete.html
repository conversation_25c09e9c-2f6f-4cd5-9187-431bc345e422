{# D:\school_fees_saas_v2\apps\announcements\templates\announcements\announcement_confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'tenant_announcements:announcement_list' %}">{% trans "Announcements" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Confirm Delete" %}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">{% trans "Confirm Deletion" %}</h5>
                </div>
                <div class="card-body">
                    <p class="card-text mt-3">
                        {% blocktrans with title=object.title trimmed %}
                        Are you sure you want to delete the announcement titled "<strong>{{ title }}</strong>"?
                        {% endblocktrans %}
                    </p>
                    <p class="text-danger"><strong>{% trans "This action cannot be undone." %}</strong></p>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="text-end mt-4">
                            <a href="{% url 'announcements:announcement_detail' pk=object.pk %}" class="btn btn-secondary me-2">{% trans "Cancel" %}</a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, Delete" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}




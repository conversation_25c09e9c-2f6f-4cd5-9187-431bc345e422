# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.conf import settings # For settings.AUTH_USER_MODEL or settings.STAFF_USER_MODEL
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from apps.accounting.models import Account

import logging
logger = logging.getLogger(__name__)

# Import StaffUser from the schools app
# This creates a dependency: hr app depends on schools app.
# Ensure 'apps.schools' appears before 'apps.hr' in INSTALLED_APPS if this becomes an issue,
# or use string references like 'schools.StaffUser'.
from apps.schools.models import StaffUser

# --- Constants for Choices ---
GENDER_CHOICES = [
    ('MALE', 'Male'),
    ('FEMALE', 'Female'),
    ('OTHER', 'Other'),
    ('UNSPECIFIED', 'Prefer not to say'),
]

EMPLOYMENT_TYPE_CHOICES = [
    ('FULL_TIME', 'Full-Time'),
    ('PART_TIME', 'Part-Time'),
    ('CONTRACT', 'Contract'),
    ('INTERN', 'Intern'),
    ('TEMPORARY', 'Temporary'),
]

MARITAL_STATUS_CHOICES = [
    ('SINGLE', 'Single'),
    ('MARRIED', 'Married'),
    ('DIVORCED', 'Divorced'),
    ('WIDOWED', 'Widowed'),
    ('OTHER', 'Other'),
]

# --- Employee Profile Model ---
class EmployeeProfile(models.Model):
    """
    Stores HR-specific details for a StaffUser.
    Linked one-to-one with the StaffUser model from the 'schools' app.
    """
    user = models.OneToOneField(
        StaffUser, # Link to the StaffUser in apps.schools
        on_delete=models.CASCADE, # If StaffUser is deleted, delete profile
        primary_key=True, # Use StaffUser's PK as this model's PK
        related_name='hr_profile' # Access via staff_user.hr_profile
    )
    # employee_id was moved to StaffUser model in the consolidated approach
    # designation was moved to StaffUser model
    # department was moved to StaffUser model
    # date_hired (renamed to date_joined on StaffUser) was moved

    # Additional HR-specific fields:
    middle_name = models.CharField(_("middle name(s)"), max_length=100, blank=True)
    gender = models.CharField(
        _("gender"), max_length=15, choices=GENDER_CHOICES, blank=True, null=True
    )
    date_of_birth = models.DateField(_("date of birth"), null=True, blank=True)
    marital_status = models.CharField(
        _("marital status"), max_length=15, choices=MARITAL_STATUS_CHOICES, blank=True, null=True
    )
    # phone_number (primary) was moved to StaffUser
    phone_number_alternate = models.CharField(
        _("alternate phone"), max_length=30, blank=True,
        help_text=_("Optional secondary contact number.")
    )
    address_line1 = models.CharField(_("address line 1"), max_length=255, blank=True)
    address_line2 = models.CharField(_("address line 2"), max_length=255, blank=True)
    city = models.CharField(_("city"), max_length=100, blank=True)
    state_province = models.CharField(_("state/province"), max_length=100, blank=True)
    postal_code = models.CharField(_("postal/zip code"), max_length=20, blank=True)
    country = models.CharField(_("country"), max_length=100, blank=True) # Consider django-countries

    employment_type = models.CharField(
        _("employment type"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, blank=True, null=True
    )
    date_left = models.DateField(_("date left employment"), null=True, blank=True)
    photo = models.ImageField(
        _("profile photo"),
        upload_to='staff_photos/', # Will be under MEDIA_ROOT/tenants/<schema>/staff_photos/
        null=True, blank=True
    )
    # Add other HR-specific fields: emergency contact, bank details (encrypt!), qualifications etc.

    notes = models.TextField(_("internal notes"), blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("employee HR profile")
        verbose_name_plural = _("employee HR profiles")
        ordering = ['user__last_name', 'user__first_name']
        
    def __str__(self):
        if self.user:
            # Call the method get_full_name()
            user_display_name = self.user.get_full_name() 
            # It's also good to provide a fallback if get_full_name() returns empty
            # or if first_name/last_name are blank.
            if not user_display_name: # If get_full_name() returned an empty string
                user_display_name = self.user.email # Fallback to email
            return f"HR Profile for {user_display_name}"
        return "HR Profile for Unknown User"



# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

class LeaveType(models.Model):
    """
    Defines different types of leave available in the school (e.g., Annual, Sick),
    including rules for how leave days are accrued over time.
    """
    # --- Existing Fields (They are good) ---
    name = models.CharField(
        max_length=100,
        unique=True, # Unique within the tenant
        help_text=_("Name of the leave type (e.g., Annual Leave, Sick Leave).")
    )
    description = models.TextField(blank=True, null=True)
    is_paid = models.BooleanField(default=True, help_text=_("Is this leave type paid?"))
    is_active = models.BooleanField(
        default=True, 
        help_text=_("Is this leave type available for new requests?")
    )
    
    # --- CONSOLIDATED & REFINED ACCRUAL FIELDS ---

    # We can rename max_annual_days for clarity if it's confusing, but it's fine.
    # Let's keep it but also add our new, more specific accrual fields.
    max_days_per_year_grant = models.DecimalField(
        _("Max Days Granted Annually"), 
        max_digits=5, decimal_places=2, null=True, blank=True,
        help_text=_("For non-accruing leave types, how many days are granted per year (e.g., 15 sick days).")
    )

    class AccrualFrequency(models.TextChoices):
        NONE = 'NONE', _('No Accrual (Manually Granted)')
        MONTHLY = 'MONTHLY', _('Monthly')
        ANNUALLY = 'ANNUALLY', _('Annually')

    accrual_frequency = models.CharField(
        _("Accrual Frequency"),
        max_length=20, choices=AccrualFrequency.choices, default=AccrualFrequency.NONE,
        help_text=_("Choose 'No Accrual' for leave that is granted manually or all at once per year.")
    )
    
    accrual_rate = models.DecimalField(
        _("Accrual Rate"),
        max_digits=5, decimal_places=2, default=Decimal('0.00'),
        help_text=_("If accruing, number of days added per period (e.g., 1.75 for monthly).")
    )
    
    max_accrual_balance = models.PositiveIntegerField(
        _("Maximum Balance"),
        null=True, blank=True,
        help_text=_("The maximum number of leave days a staff member can accumulate. Leave blank for no limit.")
    )
    
    prorate_accrual = models.BooleanField(
        _("Prorate for New Staff"),
        default=True,
        help_text=_("If accruing, new staff hired mid-period will receive a prorated amount for their first accrual.")
    )
    
    requires_approval = models.BooleanField(
        default=True, 
        help_text=_("Does this leave type require manager/admin approval?")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = _("leave type")
        verbose_name_plural = _("leave types")
        permissions = [
            ("view_hr_module", "Can view the main HR module and navbar link"),
            ("approve_leave_requests", "Can approve or reject leave requests for other staff"),
        ]

    def __str__(self):
        return self.name



class LeaveBalance(models.Model):
    """
    Tracks the leave balance for an employee for a specific leave type.
    Could be for a specific year/period if your school policy requires.
    """
    employee = models.ForeignKey(
        EmployeeProfile,
        on_delete=models.CASCADE,
        related_name='leave_balances'
    )
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE, # If leave type deleted, balances for it are gone
        related_name='leave_balances'
    )
    # Optional: Link to AcademicYear if balances are per academic year
    # academic_year = models.ForeignKey(
    #     'fees.AcademicYear', # Assumes fees app has AcademicYear
    #     on_delete=models.SET_NULL, null=True, blank=True
    # )
    year_or_period_info = models.CharField( # Simple text field for period context
        max_length=50, blank=True, null=True,
        help_text="E.g., '2024', '2024-2025 Academic Year', 'Q1 2025'"
    )

    days_accrued = models.DecimalField( # Total days entitled/accrued
        _("days accrued/entitled"), max_digits=5, decimal_places=1, default=Decimal('0.0')
    )
    days_taken = models.DecimalField( # Total days already taken
        _("days taken"), max_digits=5, decimal_places=1, default=Decimal('0.0')
    )
    # Last updated by an accrual process or manual adjustment
    last_accrual_date = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("leave balance")
        verbose_name_plural = _("leave balances")
        # Ensure only one balance record per employee, per leave_type, per period
        unique_together = ('employee', 'leave_type', 'year_or_period_info')
        ordering = ['employee__user__last_name', 'leave_type__name']

    def __str__(self):
        return f"{self.employee.user.full_name} - {self.leave_type.name}: {self.days_remaining} days left"

    @property
    def days_remaining(self):
        return self.days_accrued - self.days_taken



class LeaveBalanceLog(models.Model):
    """
    An audit trail for every change to a LeaveBalance.
    """
    class Action(models.TextChoices):
        ACCRUAL = 'ACCRUAL', _('Automatic Accrual')
        ADJUSTMENT = 'ADJUSTMENT', _('Manual Adjustment')
        APPLICATION = 'APPLICATION', _('Leave Application')
        RESET = 'RESET', _('Yearly Reset')
        INITIAL = 'INITIAL', _('Initial Balance')

    leave_balance = models.ForeignKey(LeaveBalance, on_delete=models.CASCADE, related_name='logs')
    action = models.CharField(_("Action"), max_length=20, choices=Action.choices)
    change_amount = models.DecimalField(
        _("Change Amount"),
        max_digits=5, decimal_places=2,
        help_text=_("The number of days added (positive) or removed (negative).")
    )
    notes = models.CharField(_("Notes"), max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        'schools.StaffUser', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, # You had this
        related_name='leave_log_actions', # <<< CORRECTION: Add a related_name
        help_text=_("The admin who made a manual adjustment, or null for system actions.")
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Leave Balance Log" # <<< CORRECTION: Add verbose names
        verbose_name_plural = "Leave Balance Logs"

    def __str__(self):
        # This __str__ method is excellent.
        return f"{self.get_action_display()} of {self.change_amount} for {self.leave_balance}"


from django.db import models


class LeaveRequestStatusChoices(models.TextChoices):
    PENDING = 'PENDING', _('Pending')
    APPROVED = 'APPROVED', _('Approved')
    REJECTED = 'REJECTED', _('Rejected')
    CANCELLED_BY_STAFF = 'CANCELLED_BY_STAFF', _('Cancelled by Staff')
    # Add more as needed

LEAVE_REQUEST_STATUS_CHOICES = LeaveRequestStatusChoices.choices

class LeaveRequest(models.Model):
    """
    Represents a leave request submitted by an employee.
    """
    employee = models.ForeignKey(
        EmployeeProfile,
        on_delete=models.CASCADE,
        related_name='leave_requests'
    )
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.PROTECT, # Don't delete leave type if requests use it
        related_name='leave_requests'
    )
    start_date = models.DateField(_("start date"))
    end_date = models.DateField(_("end date"))
    half_day_start = models.BooleanField(_("Half Day (Start)"), default=False)
    half_day_end = models.BooleanField(_("Half Day (End)"), default=False)
    reason = models.TextField(_("reason for leave"))
    
    # --- ADD ATTACHMENT FIELD HERE ---
    attachment = models.FileField(
        _("attachment"),
        upload_to='leave_attachments/%Y/%m/', # Store in year/month subdirectories
        blank=True, 
        null=True,
        help_text=_("Optional: Attach any supporting document (e.g., medical certificate).")
    )
    # --- END ATTACHMENT FIELD ---
    
    status = models.CharField(
        _("status"), max_length=20, # Increased max_length slightly for longer status keys
        choices=LEAVE_REQUEST_STATUS_CHOICES, # Use the defined choices
        default=LeaveRequestStatusChoices.PENDING # Use the enum/choices class for default
    )
    request_date = models.DateTimeField(_("request submitted on"), default=timezone.now)
    approved_by = models.ForeignKey(
        EmployeeProfile, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='approved_leave_requests'
    )
    approval_date = models.DateTimeField(_("approval date"), null=True, blank=True)
    admin_notes = models.TextField(_("admin/manager notes"), blank=True, null=True)

    number_of_days_requested_calc = models.DecimalField( # Renamed to avoid clash with property
        _("calculated number of days"), max_digits=5, decimal_places=1, default=Decimal('0.0'),
        help_text=_("Calculated number of leave days by the system.")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-request_date', 'employee']
        verbose_name = _("leave request")
        verbose_name_plural = _("leave requests")

    def __str__(self):
        # Ensure employee.user exists or handle AttributeError if employee might not have a user
        employee_name = "N/A"
        if self.employee and hasattr(self.employee, 'user') and self.employee.user:
            employee_name = self.employee.user.get_full_name() or self.employee.user.email
        elif self.employee:
            employee_name = str(self.employee) # Fallback to employee's __str__

        return f"Leave for {employee_name} ({self.leave_type.name}) from {self.start_date} to {self.end_date}"

    # Keep your calculate_number_of_days method as is (it's good)
    def calculate_number_of_days(self, exclude_weekends=True, holidays=None):
        if not self.start_date or not self.end_date or self.end_date < self.start_date:
            return Decimal('0.0') # Return Decimal

        current_date = self.start_date
        days_count = Decimal('0.0') # Use Decimal for precision
        
        if holidays is None:
            holidays = set()
        else:
            holidays = set(holidays)

        while current_date <= self.end_date:
            is_weekend = current_date.weekday() >= 5 
            is_holiday = current_date in holidays
            day_contribution = Decimal('1.0')

            if exclude_weekends and is_weekend:
                day_contribution = Decimal('0.0')
            elif is_holiday:
                day_contribution = Decimal('0.0')
            
            if day_contribution > 0:
                is_single_day_request = (self.start_date == self.end_date)
                
                if is_single_day_request:
                    if self.half_day_start and self.half_day_end: # Both half-days on a single day means full day
                        day_contribution = Decimal('1.0')
                    elif self.half_day_start or self.half_day_end: # One half-day on a single day
                        day_contribution = Decimal('0.5')
                else: # Multi-day request
                    if current_date == self.start_date and self.half_day_start:
                        day_contribution -= Decimal('0.5')
                    if current_date == self.end_date and self.half_day_end:
                        day_contribution -= Decimal('0.5')
            
            if day_contribution < 0: day_contribution = Decimal('0.0') # Safety
            days_count += day_contribution
            current_date += timedelta(days=1)
        return days_count

    # This property will use the calculation. The form can populate number_of_days_requested_calc
    @property
    def effective_number_of_days(self):
        # You might want to store the result of calculate_number_of_days in number_of_days_requested_calc
        # upon saving the form, and then this property can just return that stored value.
        # Or recalculate each time if needed (can be intensive if holidays are complex)
        return self.calculate_number_of_days(exclude_weekends=True, holidays=None) # Pass actual holidays if available

    # Property for simple duration, might be different from effective_number_of_days
    @property
    def duration_in_calendar_days(self): # Renamed for clarity
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0
    
    @property
    def is_half_day(self): # This indicates if ANY half-day flag is set
        return self.half_day_start or self.half_day_end
    
    def get_status_badge_class(self):
        if self.status == LeaveRequestStatusChoices.PENDING:
            return "warning text-dark"
        elif self.status == LeaveRequestStatusChoices.APPROVED:
            return "success"
        elif self.status == LeaveRequestStatusChoices.REJECTED:
            return "danger"
        elif self.status == LeaveRequestStatusChoices.CANCELLED_BY_STAFF:
            return "secondary"
        return "light text-dark"
    
    def save(self, *args, **kwargs):
        # Automatically update the calculated number of days before saving
        # if the relevant fields have changed or it's a new instance.
        self.number_of_days_requested_calc = self.calculate_number_of_days()
        super().save(*args, **kwargs)
    

class HRPermissions(models.Model):
    class Meta:
        managed = False  # No database table created
        default_permissions = ()
        permissions = [
            ('view_hr_module', _('Can view the main HR module and navbar link')),
            ('manage_staff_users', _('Can create, edit, and manage staff accounts')),
            ('approve_leave_requests', _('Can approve or reject leave requests')),
            ('manage_leave_types', _('Can configure leave types')),
        ]
        




# D:\school_fees_saas_v2\apps/hr/models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
# ... other imports and your existing HR models ...

# ==============================================================================
# PAYROLL COMPONENT MODELS (Configurable by Tenant Admin)
# ==============================================================================

class SalaryComponent(models.Model):
    """
    A template for any kind of earning or deduction (e.g., "Housing Allowance", "Pension Fund").
    This is configured once by the tenant admin.
    """
    class ComponentType(models.TextChoices):
        EARNING = 'EARNING', _('Earning')
        DEDUCTION = 'DEDUCTION', _('Deduction')

    name = models.CharField(_("Component Name"), max_length=100)
    type = models.CharField(_("Component Type"), max_length=10, choices=ComponentType.choices)
    is_statutory = models.BooleanField(
        _("Is Statutory"), default=False,
        help_text=_("Check if this is a mandatory government/statutory deduction (e.g., Income Tax, Social Security).")
    )
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('name', 'type') # A tenant can't have two earning components with the same name
        ordering = ['type', 'name']
        verbose_name = _("Salary Component")
        verbose_name_plural = _("Salary Components")

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"


class TaxBracket(models.Model):
    """
    Defines income tax brackets. A tenant can have multiple brackets to form a progressive tax system.
    """
    name = models.CharField(_("Bracket Name/Description"), max_length=100)
    # The lower bound of the income range for this tax bracket
    from_amount = models.DecimalField(_("From Amount"), max_digits=12, decimal_places=2)
    # The upper bound. Can be null for the highest bracket (meaning "and above").
    to_amount = models.DecimalField(_("To Amount"), max_digits=12, decimal_places=2, null=True, blank=True)
    # The percentage rate for this bracket
    rate_percent = models.DecimalField(_("Tax Rate (%)"), max_digits=5, decimal_places=2, help_text=_("e.g., 25 for 25%"))
    # A flat amount to be deducted in addition to the percentage (less common, but exists in some systems)
    deduction_amount = models.DecimalField(_("Deduction Amount"), max_digits=12, decimal_places=2, default=Decimal('0.00'))
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['from_amount']
        verbose_name = _("Tax Bracket")
        verbose_name_plural = _("Tax Brackets")

    def __str__(self):
        to_str = f"to {self.to_amount}" if self.to_amount else "and above"
        return f"{self.name}: {self.from_amount} {to_str} @ {self.rate_percent}%"


# ==============================================================================
# STAFF-SPECIFIC SALARY STRUCTURE
# ==============================================================================
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

class StaffSalary(models.Model):
    """
    Defines the salary structure for a single staff member.
    """
    staff_user = models.OneToOneField(
        'schools.StaffUser', 
        on_delete=models.CASCADE, 
        related_name='salary_structure'
    )
    # --- ENSURE THESE TWO FIELDS ARE PRESENT ---
    basic_salary = models.DecimalField(_("Basic Salary"), max_digits=12, decimal_places=2)
    effective_date = models.DateField(_("Effective Date"), default=timezone.now)
    # ---
    
    is_active = models.BooleanField(default=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-effective_date', 'staff_user__last_name']
        verbose_name = _("Staff Salary Structure")
        verbose_name_plural = _("Staff Salary Structures")

    def __str__(self):
        if self.staff_user:
            return f"Salary for {self.staff_user.get_full_name()}"
        return f"Unassigned Salary Structure (PK: {self.pk})"


    
class StaffSalaryComponent(models.Model):
    """
    Links a specific, recurring earning or deduction to a staff member's salary.
    e.g., Jane Doe gets a "Housing Allowance" of 500 every month.
    """
    staff_salary = models.ForeignKey(StaffSalary, on_delete=models.CASCADE, related_name='components')
    component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT)
    # The value of this component for this staff member
    amount = models.DecimalField(_("Amount"), max_digits=12, decimal_places=2)
    
    class Meta:
        unique_together = ('staff_salary', 'component')
        verbose_name = _("Staff Salary Component")
        verbose_name_plural = _("Staff Salary Components")

    def __str__(self):
        return f"{self.component.name} for {self.staff_salary.staff_user.get_full_name()}"


# ==============================================================================
# PAYSLIP MODELS
# ==============================================================================

class Payslip(models.Model):
    """
    A generated payslip for a specific staff member forExcellent! Building a robust payroll system is a major step towards making your application an all-in-one solution for schools. This is a complex module, so we'll break it down into logical, manageable parts.

**High-Level Goal:**
Create a system that allows an administrator to define salary structures, assign them to staff, generate monthly payslips with earnings and deductions (including automated tax calculations), and record these transactions in the accounting ledger a specific period.
    This model stores the *results* of the payroll calculation.
    """
    class PayslipStatus(models.TextChoices):
        DRAFT = 'DRAFT', _('Draft')
        GENERATED = 'GENERATED', _('Generated')
        PAID = 'PAID', _('Paid')
        CANCELLED = 'CANCELLED', _('Cancelled')

    staff_user = models.ForeignKey('schools.StaffUser', on_delete=models.PROTECT, related_name='payslips')
    pay_period_start = models.DateField()
    pay_period_end = models.DateField()
    
    # --- Calculated & Stored Values ---
    gross_earnings = models.DecimalField(max_digits=12, decimal_places=2)
    total_deductions = models.DecimalField(max_digits=12, decimal_places=2)
    net_pay = models.DecimalField(max_digits=12, decimal_places=2) # gross_earnings - total_deductions
    
    status = models.CharField(max_length=20, choices=PayslipStatus.choices, default=PayslipStatus.DRAFT)
    generated_on = models.DateTimeField(auto_now_add=True)
    paid_on = models.DateField(null=True, blank=True)
    
    class Meta:
        unique_together = ('staff_user', 'pay_period_start', 'pay_period_end')
        ordering = ['-pay_period_start', 'staff_user__last_name']
        verbose_name = _("Payslip")
        verbose_name_plural = _("Payslips")

    def __str__(self):
        return f"Payslip for {self.staff_user.get_full_name()} ({self.pay_period_start})"



class PayslipLineItem(models.Model):
    """
    A single line item on a generated payslip, representing either
    an earning or a deduction for that specific period.
    """
    # This inherits the 'type' from the source component but stores it here
    # to protect the historical record if the source component changes.
    class ComponentType(models.TextChoices):
        EARNING = 'EARNING', _('Earning')
        DEDUCTION = 'DEDUCTION', _('Deduction')
        
    payslip = models.ForeignKey(Payslip, on_delete=models.CASCADE, related_name='line_items')
    # The name is stored directly on the line item for historical accuracy
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=10, choices=ComponentType.choices)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Optional: Link to the source component for reference, but don't rely on it for historical data
    source_component = models.ForeignKey(SalaryComponent, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        ordering = ['type', 'name']
        verbose_name = _("Payslip Line Item")
        verbose_name_plural = _("Payslip Line Items")

    def __str__(self):
        return f"{self.name}: {self.amount}"
    
    

# ==============================================================================
# PAYROLL MODELS
# ==============================================================================

class SalaryComponent(models.Model):
    """
    Defines a single type of earning or deduction (e.g., Basic Salary, Housing Allowance, PAYE Tax).
    This is a global definition available to all tenants.
    """
    class ComponentType(models.TextChoices):
        EARNING = 'EARNING', _('Earning')
        DEDUCTION = 'DEDUCTION', _('Deduction')
    
    name = models.CharField(_("Component Name"), max_length=100)
    code = models.CharField(_("Short Code"), max_length=20, unique=True, help_text=_("A unique code, e.g., BASIC, HOUSING, TAX_PAYE"))
    component_type = models.CharField(_("Component Type"), max_length=10, choices=ComponentType.choices)
    is_statutory = models.BooleanField(
        _("Is Statutory"), default=False, 
        help_text=_("Check if this is a government-mandated deduction like tax or social security.")
    )
    # Link to GL accounts for automated journal entries
    expense_account = models.ForeignKey(
        Account, verbose_name=_("Expense/Liability Account"),
        on_delete=models.PROTECT, related_name='salary_components',
        help_text=_("The GL account to post this component to (e.g., 'Salaries Expense', 'Tax Payable')."),
        null=True, blank=True # Allow setting it up later
    )
    
    def __str__(self):
        return f"{self.name} ({self.get_component_type_display()})"

class SalaryGrade(models.Model):
    """
    A template for a salary structure (e.g., "Senior Teacher", "Admin Grade 1").
    A tenant defines their own salary grades.
    """
    name = models.CharField(_("Grade Name"), max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('name',) # Name should be unique per tenant
        ordering = ['name']
        
    def __str__(self):
        return self.name

class SalaryGradeComponent(models.Model):
    """
    Links a SalaryComponent to a SalaryGrade and defines its amount or formula.
    """
    grade = models.ForeignKey(SalaryGrade, on_delete=models.CASCADE, related_name='components')
    component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT, related_name='grade_links')
    # For now, we'll use a fixed amount. Later, this could be a formula (e.g., % of basic).
    amount = models.DecimalField(_("Amount"), max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    class Meta:
        unique_together = ('grade', 'component')
        ordering = ['component__component_type', 'component__name']
        
    def __str__(self):
        return f"{self.grade.name} - {self.component.name}: {self.amount}"

class StaffSalary(models.Model):
    """
    Assigns a SalaryGrade to a specific staff member.
    This is where individual variations can be made.
    """
    staff = models.OneToOneField(StaffUser, on_delete=models.CASCADE, related_name='salary_structure')
    grade = models.ForeignKey(
        SalaryGrade, on_delete=models.PROTECT, null=True, blank=True,
        help_text=_("The base salary grade for this staff member.")
    )
    # This allows for individual allowances/deductions not in the grade
    # (To be implemented later with a separate model if needed)
    
    def __str__(self):
        return f"Salary for {self.staff.get_full_name()}"

class Payslip(models.Model):
    """
    A record of a salary payment for a specific period for one staff member.
    """
    class PayslipStatus(models.TextChoices):
        DRAFT = 'DRAFT', _('Draft')
        GENERATED = 'GENERATED', _('Generated')
        PAID = 'PAID', _('Paid')
        CANCELLED = 'CANCELLED', _('Cancelled')
        
    staff = models.ForeignKey(StaffUser, on_delete=models.PROTECT, related_name='payslips')
    
    pay_period_start = models.DateField()
    pay_period_end = models.DateField()
    
    gross_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_deductions = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    net_pay = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))


    def _calculate_tax(self, taxable_income):
        """
        Calculates income tax based on the defined TaxBracket model.
        This is a progressive tax calculation.
        """
        if taxable_income <= 0:
            return Decimal('0.00')

        total_tax = Decimal('0.00')
        remaining_income = taxable_income
        
        brackets = TaxBracket.objects.filter(is_active=True).order_by('from_amount')
        if not brackets.exists():
            logger.warning("No active tax brackets found. Tax will be calculated as 0.")
            return Decimal('0.00')
            
        for bracket in brackets:
            if remaining_income <= 0:
                break
            
            bracket_min = bracket.from_amount
            # Handle the top bracket (no upper limit)
            bracket_max = bracket.to_amount if bracket.to_amount is not None else remaining_income + 1
            
            # Amount of income that falls into this bracket
            income_in_bracket = min(remaining_income, bracket_max - bracket_min)
            
            tax_for_bracket = (income_in_bracket * bracket.rate_percent) / 100
            total_tax += tax_for_bracket
            
            remaining_income -= income_in_bracket
            
        # Apply any flat deduction from the bracket the income falls into
        # This is a simplified approach; real tax systems can be more complex
        final_bracket = brackets.filter(from_amount__lte=taxable_income).last()
        if final_bracket:
            total_tax -= final_bracket.deduction_amount

        return max(total_tax, Decimal('0.00')) # Ensure tax is not negative

    def run(self):
        """
        Executes the payroll calculation process.
        """
        try:
            salary_structure = self.staff_user.salary_structure
        except StaffSalary.DoesNotExist:
            logger.error(f"No salary structure found for staff: {self.staff_user.email}. Skipping payroll.")
            return None # Indicate failure

        # 1. Gather all Earnings
        self.earnings.append({'name': 'Basic Salary', 'amount': salary_structure.basic_salary, 'source': None})
        for component in salary_structure.components.filter(component__type='EARNING'):
            self.earnings.append({'name': component.component.name, 'amount': component.amount, 'source': component.component})

        self.gross_earnings = sum(item['amount'] for item in self.earnings)
        
        # For now, let's assume taxable income is the same as gross earnings.
        # In a real system, some earnings might be non-taxable.
        self.taxable_income = self.gross_earnings

        # 2. Gather Pre-Tax Deductions
        for component in salary_structure.components.filter(component__type='DEDUCTION', component__is_statutory=False):
            self.deductions.append({'name': component.component.name, 'amount': component.amount, 'source': component.component})
            # Some pre-tax deductions might reduce taxable income
            # self.taxable_income -= component.amount
        
        # 3. Calculate Statutory Deductions (like Tax)
        income_tax = self._calculate_tax(self.taxable_income)
        if income_tax > 0:
            tax_component, _ = SalaryComponent.objects.get_or_create(
                name="Income Tax (PAYE)", type='DEDUCTION', is_statutory=True
            )
            self.deductions.append({'name': "Income Tax (PAYE)", 'amount': income_tax, 'source': tax_component})
            
        # Add other statutory calculations here (e.g., Social Security based on a percentage of gross)

        # 4. Final Calculations
        self.total_deductions = sum(item['amount'] for item in self.deductions)
        self.net_pay = self.gross_earnings - self.total_deductions
        
        # Return a dictionary of the calculated results
        return {
            'gross_earnings': self.gross_earnings,
            'total_deductions': self.total_deductions,
            'net_pay': self.net_pay,
            'earnings_lines': self.earnings,
            'deductions_lines': self.deductions,
        }

# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.conf import settings # For settings.AUTH_USER_MODEL or settings.STAFF_USER_MODEL
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

# Import StaffUser from the schools app
# This creates a dependency: hr app depends on schools app.
# Ensure 'apps.schools' appears before 'apps.hr' in INSTALLED_APPS if this becomes an issue,
# or use string references like 'schools.StaffUser'.
from apps.schools.models import StaffUser

# --- Constants for Choices ---
GENDER_CHOICES = [
    ('MALE', 'Male'),
    ('FEMALE', 'Female'),
    ('OTHER', 'Other'),
    ('UNSPECIFIED', 'Prefer not to say'),
]

EMPLOYMENT_TYPE_CHOICES = [
    ('FULL_TIME', 'Full-Time'),
    ('PART_TIME', 'Part-Time'),
    ('CONTRACT', 'Contract'),
    ('INTERN', 'Intern'),
    ('TEMPORARY', 'Temporary'),
]

MARITAL_STATUS_CHOICES = [
    ('SINGLE', 'Single'),
    ('MARRIED', 'Married'),
    ('DIVORCED', 'Divorced'),
    ('WIDOWED', 'Widowed'),
    ('OTHER', 'Other'),
]

# --- Employee Profile Model ---
class EmployeeProfile(models.Model):
    """
    Stores HR-specific details for a StaffUser.
    Linked one-to-one with the StaffUser model from the 'schools' app.
    """
    user = models.OneToOneField(
        StaffUser, # Link to the StaffUser in apps.schools
        on_delete=models.CASCADE, # If StaffUser is deleted, delete profile
        primary_key=True, # Use StaffUser's PK as this model's PK
        related_name='hr_profile' # Access via staff_user.hr_profile
    )
    # employee_id was moved to StaffUser model in the consolidated approach
    # designation was moved to StaffUser model
    # department was moved to StaffUser model
    # date_hired (renamed to date_joined on StaffUser) was moved

    # Additional HR-specific fields:
    middle_name = models.CharField(_("middle name(s)"), max_length=100, blank=True)
    gender = models.CharField(
        _("gender"), max_length=15, choices=GENDER_CHOICES, blank=True, null=True
    )
    date_of_birth = models.DateField(_("date of birth"), null=True, blank=True)
    marital_status = models.CharField(
        _("marital status"), max_length=15, choices=MARITAL_STATUS_CHOICES, blank=True, null=True
    )
    # phone_number (primary) was moved to StaffUser
    phone_number_alternate = models.CharField(
        _("alternate phone"), max_length=30, blank=True,
        help_text=_("Optional secondary contact number.")
    )
    address_line1 = models.CharField(_("address line 1"), max_length=255, blank=True)
    address_line2 = models.CharField(_("address line 2"), max_length=255, blank=True)
    city = models.CharField(_("city"), max_length=100, blank=True)
    state_province = models.CharField(_("state/province"), max_length=100, blank=True)
    postal_code = models.CharField(_("postal/zip code"), max_length=20, blank=True)
    country = models.CharField(_("country"), max_length=100, blank=True) # Consider django-countries

    employment_type = models.CharField(
        _("employment type"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, blank=True, null=True
    )
    date_left = models.DateField(_("date left employment"), null=True, blank=True)
    photo = models.ImageField(
        _("profile photo"),
        upload_to='staff_photos/', # Will be under MEDIA_ROOT/tenants/<schema>/staff_photos/
        null=True, blank=True
    )
    # Add other HR-specific fields: emergency contact, bank details (encrypt!), qualifications etc.

    notes = models.TextField(_("internal notes"), blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("employee HR profile")
        verbose_name_plural = _("employee HR profiles")
        ordering = ['user__last_name', 'user__first_name']
        
    def __str__(self):
        if self.user:
            # Call the method get_full_name()
            user_display_name = self.user.get_full_name() 
            # It's also good to provide a fallback if get_full_name() returns empty
            # or if first_name/last_name are blank.
            if not user_display_name: # If get_full_name() returned an empty string
                user_display_name = self.user.email # Fallback to email
            return f"HR Profile for {user_display_name}"
        return "HR Profile for Unknown User"




# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from decimal import Decimal # Ensure Decimal is imported if not already

# ... (Your GENDER_CHOICES, EMPLOYMENT_TYPE_CHOICES, MARITAL_STATUS_CHOICES if they are in this file) ...
# ... (Your EmployeeProfile model definition) ...
# ... (Your LEAVE_REQUEST_STATUS_CHOICES if they are in this file) ...
# ... (Your LeaveRequest model definition) ...


# --- Leave Type Model ---
class LeaveType(models.Model):
    """
    Defines different types of leave available in the school (e.g., Vacation, Sick).
    """
    name = models.CharField(
        max_length=100,
        unique=True, # Unique within the tenant
        help_text=_("Name of the leave type (e.g., Annual Leave, Sick Leave, Maternity Leave).")
    )
    description = models.TextField(blank=True, null=True)
    
    max_annual_days = models.DecimalField(
        _("max annual days"), max_digits=5, decimal_places=1,
        null=True, blank=True, default=Decimal('0.0'),
        help_text=_("Maximum days that can be typically accrued or granted per year for this leave type. Use 0 or None for no limit/manual assignment.")
    )
    is_paid = models.BooleanField(default=True, help_text=_("Is this leave type paid?"))
    requires_approval = models.BooleanField(default=True, help_text=_("Does this leave type require manager/admin approval?"))
    
    # --- ADDED FIELD ---
    is_active = models.BooleanField(
        default=True, 
        help_text=_("Is this leave type currently available for new requests and active in the system?")
    )
    # --------------------

    # Min notice period in days (optional)
    # min_notice_days = models.PositiveIntegerField(null=True, blank=True)
    # Can carry forward?
    # allow_carry_forward = models.BooleanField(default=False)
    # max_carry_forward_days = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = _("leave type")
        verbose_name_plural = _("leave types")

    def __str__(self):
        return self.name



class LeaveBalance(models.Model):
    """
    Tracks the leave balance for an employee for a specific leave type.
    Could be for a specific year/period if your school policy requires.
    """
    employee = models.ForeignKey(
        EmployeeProfile,
        on_delete=models.CASCADE,
        related_name='leave_balances'
    )
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE, # If leave type deleted, balances for it are gone
        related_name='leave_balances'
    )
    # Optional: Link to AcademicYear if balances are per academic year
    # academic_year = models.ForeignKey(
    #     'fees.AcademicYear', # Assumes fees app has AcademicYear
    #     on_delete=models.SET_NULL, null=True, blank=True
    # )
    year_or_period_info = models.CharField( # Simple text field for period context
        max_length=50, blank=True, null=True,
        help_text="E.g., '2024', '2024-2025 Academic Year', 'Q1 2025'"
    )

    days_accrued = models.DecimalField( # Total days entitled/accrued
        _("days accrued/entitled"), max_digits=5, decimal_places=1, default=Decimal('0.0')
    )
    days_taken = models.DecimalField( # Total days already taken
        _("days taken"), max_digits=5, decimal_places=1, default=Decimal('0.0')
    )
    # Last updated by an accrual process or manual adjustment
    last_accrual_date = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("leave balance")
        verbose_name_plural = _("leave balances")
        # Ensure only one balance record per employee, per leave_type, per period
        unique_together = ('employee', 'leave_type', 'year_or_period_info')
        ordering = ['employee__user__last_name', 'leave_type__name']

    def __str__(self):
        return f"{self.employee.user.full_name} - {self.leave_type.name}: {self.days_remaining} days left"

    @property
    def days_remaining(self):
        return self.days_accrued - self.days_taken


from django.db import models


class LeaveRequestStatusChoices(models.TextChoices):
    PENDING = 'PENDING', _('Pending')
    APPROVED = 'APPROVED', _('Approved')
    REJECTED = 'REJECTED', _('Rejected')
    CANCELLED_BY_STAFF = 'CANCELLED_BY_STAFF', _('Cancelled by Staff')
    # Add more as needed

LEAVE_REQUEST_STATUS_CHOICES = LeaveRequestStatusChoices.choices

class LeaveRequest(models.Model):
    """
    Represents a leave request submitted by an employee.
    """
    employee = models.ForeignKey(
        EmployeeProfile,
        on_delete=models.CASCADE,
        related_name='leave_requests'
    )
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.PROTECT, # Don't delete leave type if requests use it
        related_name='leave_requests'
    )
    start_date = models.DateField(_("start date"))
    end_date = models.DateField(_("end date"))
    half_day_start = models.BooleanField(_("Half Day (Start)"), default=False)
    half_day_end = models.BooleanField(_("Half Day (End)"), default=False)
    reason = models.TextField(_("reason for leave"))
    
    # --- ADD ATTACHMENT FIELD HERE ---
    attachment = models.FileField(
        _("attachment"),
        upload_to='leave_attachments/%Y/%m/', # Store in year/month subdirectories
        blank=True, 
        null=True,
        help_text=_("Optional: Attach any supporting document (e.g., medical certificate).")
    )
    # --- END ATTACHMENT FIELD ---
    
    status = models.CharField(
        _("status"), max_length=20, # Increased max_length slightly for longer status keys
        choices=LEAVE_REQUEST_STATUS_CHOICES, # Use the defined choices
        default=LeaveRequestStatusChoices.PENDING # Use the enum/choices class for default
    )
    request_date = models.DateTimeField(_("request submitted on"), default=timezone.now)
    approved_by = models.ForeignKey(
        EmployeeProfile, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='approved_leave_requests'
    )
    approval_date = models.DateTimeField(_("approval date"), null=True, blank=True)
    admin_notes = models.TextField(_("admin/manager notes"), blank=True, null=True)

    number_of_days_requested_calc = models.DecimalField( # Renamed to avoid clash with property
        _("calculated number of days"), max_digits=5, decimal_places=1, default=Decimal('0.0'),
        help_text=_("Calculated number of leave days by the system.")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-request_date', 'employee']
        verbose_name = _("leave request")
        verbose_name_plural = _("leave requests")

    def __str__(self):
        # Ensure employee.user exists or handle AttributeError if employee might not have a user
        employee_name = "N/A"
        if self.employee and hasattr(self.employee, 'user') and self.employee.user:
            employee_name = self.employee.user.get_full_name() or self.employee.user.email
        elif self.employee:
            employee_name = str(self.employee) # Fallback to employee's __str__

        return f"Leave for {employee_name} ({self.leave_type.name}) from {self.start_date} to {self.end_date}"

    # Keep your calculate_number_of_days method as is (it's good)
    def calculate_number_of_days(self, exclude_weekends=True, holidays=None):
        if not self.start_date or not self.end_date or self.end_date < self.start_date:
            return Decimal('0.0') # Return Decimal

        current_date = self.start_date
        days_count = Decimal('0.0') # Use Decimal for precision
        
        if holidays is None:
            holidays = set()
        else:
            holidays = set(holidays)

        while current_date <= self.end_date:
            is_weekend = current_date.weekday() >= 5 
            is_holiday = current_date in holidays
            day_contribution = Decimal('1.0')

            if exclude_weekends and is_weekend:
                day_contribution = Decimal('0.0')
            elif is_holiday:
                day_contribution = Decimal('0.0')
            
            if day_contribution > 0:
                is_single_day_request = (self.start_date == self.end_date)
                
                if is_single_day_request:
                    if self.half_day_start and self.half_day_end: # Both half-days on a single day means full day
                        day_contribution = Decimal('1.0')
                    elif self.half_day_start or self.half_day_end: # One half-day on a single day
                        day_contribution = Decimal('0.5')
                else: # Multi-day request
                    if current_date == self.start_date and self.half_day_start:
                        day_contribution -= Decimal('0.5')
                    if current_date == self.end_date and self.half_day_end:
                        day_contribution -= Decimal('0.5')
            
            if day_contribution < 0: day_contribution = Decimal('0.0') # Safety
            days_count += day_contribution
            current_date += timedelta(days=1)
        return days_count

    # This property will use the calculation. The form can populate number_of_days_requested_calc
    @property
    def effective_number_of_days(self):
        # You might want to store the result of calculate_number_of_days in number_of_days_requested_calc
        # upon saving the form, and then this property can just return that stored value.
        # Or recalculate each time if needed (can be intensive if holidays are complex)
        return self.calculate_number_of_days(exclude_weekends=True, holidays=None) # Pass actual holidays if available

    # Property for simple duration, might be different from effective_number_of_days
    @property
    def duration_in_calendar_days(self): # Renamed for clarity
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0
    
    @property
    def is_half_day(self): # This indicates if ANY half-day flag is set
        return self.half_day_start or self.half_day_end
    
    def get_status_badge_class(self):
        if self.status == LeaveRequestStatusChoices.PENDING:
            return "warning text-dark"
        elif self.status == LeaveRequestStatusChoices.APPROVED:
            return "success"
        elif self.status == LeaveRequestStatusChoices.REJECTED:
            return "danger"
        elif self.status == LeaveRequestStatusChoices.CANCELLED_BY_STAFF:
            return "secondary"
        return "light text-dark"
    
    def save(self, *args, **kwargs):
        # Automatically update the calculated number of days before saving
        # if the relevant fields have changed or it's a new instance.
        self.number_of_days_requested_calc = self.calculate_number_of_days()
        super().save(*args, **kwargs)
    

class HRPermissions(models.Model):
    class Meta:
        managed = False  # No database table created
        default_permissions = ()
        permissions = [
            ('view_hr_module', _('Can view the main HR module and navbar link')),
            ('manage_staff_users', _('Can create, edit, and manage staff accounts')),
            ('approve_leave_requests', _('Can approve or reject leave requests')),
            ('manage_leave_types', _('Can configure leave types')),
        ]
        
        
# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommunicationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('APP_NOTIFICATION', 'In-App Notification')], max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('FAILED', 'Failed'), ('DELIVERED', 'Delivered'), ('READ', 'Read')], default='PENDING', max_length=20)),
                ('recipient_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('recipient_phone', models.CharField(blank=True, max_length=30, null=True)),
                ('subject', models.CharField(blank=True, max_length=255, null=True)),
                ('body_preview', models.TextField(blank=True, help_text='A short preview or summary of the message.', null=True)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype')),
            ],
            options={
                'verbose_name': 'Communication Log',
                'verbose_name_plural': 'Communication Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]

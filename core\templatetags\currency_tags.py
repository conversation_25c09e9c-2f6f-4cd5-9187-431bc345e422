from django import template
# import locale # You can uncomment and configure this for more advanced locale-aware formatting

register = template.Library()

@register.filter(name='currency')
def currency(value, symbol="USD"): # Default symbol is USD, can be overridden in template
    """
    Formats a numeric value as currency.
    Usage: {{ value|currency }} or {{ value|currency:"EUR" }}
    """
    try:
        # Ensure value is a float for formatting, handle potential None
        if value is None:
            return "" # Or "N/A", or 0.00 formatted, depending on preference
        
        val = float(value)
        
        # Basic f-string formatting for thousands separator and 2 decimal places
        formatted_value = f"{val:,.2f}"
        
        return f"{symbol} {formatted_value}"
    
    except (ValueError, TypeError):
        # If value can't be converted to float, return it as is or an error indicator
        return value 

# Example of a more locale-aware version (requires locale setup on your server):
# @register.filter(name='locale_currency')
# def locale_currency(value):
#     try:
#         # Make sure to set the locale appropriately, e.g., in middleware or settings
#         # This is just an example, actual locale might need to be dynamic
#         # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8') # Example: US English
#         # For school_fees_saas, you might want to get locale from school settings
#         return locale.currency(float(value), grouping=True)
#     except (ValueError, TypeError, locale.Error):
#         return value


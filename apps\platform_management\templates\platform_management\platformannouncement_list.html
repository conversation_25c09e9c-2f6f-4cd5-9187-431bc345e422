{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformannouncement_list.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n humanize %}

{% block platform_admin_page_title %}{{ view_title|default:_("Platform Announcements") }}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Platform Announcements" %}</li>
        </ol>
    </nav>
</div>

<div class="mb-3">
    <a href="{% url 'platform_management:platform_announcement_create' %}" class="btn btn-primary">
        <i class="bi bi-plus-circle-fill me-1"></i> {% trans "Create New Platform Announcement" %}
    </a>
</div>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">{% trans "All Platform Announcements" %}</h5>
        {% if announcements %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Title" %}</th>
                        <th>{% trans "Severity" %}</th>
                        <th>{% trans "Author" %}</th>
                        <th>{% trans "Published" %}</th>
                        <th>{% trans "Expires" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for announcement in announcements %}
                    <tr>
                        <td><a href="#">{{ announcement.title }}</a></td> {# Link to detail/update later #}
                        <td><span class="badge bg-{{ announcement.severity|lower|default:'light text-dark' }}">{{ announcement.get_severity_display }}</span></td>
                        <td>{{ announcement.author.username|default:"N/A" }}</td>
                        <td>{{ announcement.publish_date|date:"d M Y, P" }}</td>
                        <td>{{ announcement.expiry_date|date:"d M Y, P"|default:"Never" }}</td>
                        <td>
                            {% if announcement.is_published and announcement.is_currently_visible %}
                                <span class="badge bg-success">{% trans "Visible" %}</span>
                            {% elif announcement.is_published %}
                                <span class="badge bg-secondary">{% trans "Scheduled/Expired" %}</span>
                            {% else %}
                                <span class="badge bg-warning text-dark">{% trans "Draft" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'platform_management:platform_announcement_update' pk=announcement.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}"><i class="bi bi-pencil-square"></i></a>
                            <a href="{% url 'platform_management:platform_announcement_delete' pk=announcement.pk %}" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}"><i class="bi bi-trash"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% include "partials/_pagination.html" with page_obj=announcements %} {# Assuming announcements is page_obj #}
        {% else %}
        <p class="text-muted">{% trans "No platform announcements found." %}</p>
        {% endif %}
    </div>
</div>
{% endblock platform_admin_page_content %}



{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\payment_cancel.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Subscription Cancelled") }}{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card text-center shadow-sm border-warning">
                <div class="card-body py-5">
                    <i class="bi bi-exclamation-circle-fill text-warning display-1 mb-3"></i>
                    <h4 class="card-title">{% trans "Subscription Process Cancelled" %}</h4>
                    <p class="card-text">
                        {% trans "Your attempt to subscribe or update your subscription was cancelled." %}
                    </p>
                    <p>{% trans "Your previous subscription status (if any) remains unchanged. You can try again or choose a different plan." %}</p>
                    <div class="mt-4">
                        <a href="{% url 'subscriptions:select_plan' %}" class="btn btn-primary mx-2">{% trans "Choose a Plan" %}</a>
                        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary mx-2">{% trans "Back to Dashboard" %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}
{# apps/reporting/templates/reporting/student_ledger_report.html #}
{% extends "tenant_base.html" %}
{% load static humanize %}

{% block tenant_page_title %}Student Ledger: {{ student.full_name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .report-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
        }
        .report-header h1 {
            font-weight: 600;
            color: #343a40;
        }
        .student-info-box {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
        }
        .student-info-box strong {
            display: inline-block;
            min-width: 120px; /* Adjust as needed */
        }
        .transaction-table th {
            background-color: #e9ecef;
            font-weight: 600;
            white-space: nowrap;
        }
        .transaction-table td, .transaction-table th {
            vertical-align: middle;
        }
        .debit-amount {
            color: #dc3545; /* Bootstrap danger color for debits */
        }
        .credit-amount {
            color: #198754; /* Bootstrap success color for credits */
        }
        .balance-row td {
            font-weight: bold;
            border-top: 2px solid #adb5bd;
        }
        .report-summary-box {
            background-color: #e7f3ff; /* Light blue */
            border: 1px solid #b8daff;
            color: #004085; /* Dark blue */
            padding: 1rem;
            border-radius: 0.375rem;
            margin-top: 1.5rem;
        }
        .report-summary-box .summary-value {
            font-size: 1.25rem;
            font-weight: 600;
        }
        .print-button-area {
            margin-bottom: 1rem;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="report-header d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="bi bi-person-lines-fill me-2"></i>Student Ledger</h1>
            
            <p class="text-muted mb-0">Financial transaction history for {{ student.full_name }}</p>
        </div>
        <div class="print-button-area">
            <button class="btn btn-outline-secondary btn-sm" onclick="window.print();">
                <i class="bi bi-printer me-1"></i> Print Ledger
            </button>
            {# Add Export to PDF/CSV buttons here later #}
        </div>
    </div>

    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Student Ledger Filters" %}
    {% include "partials/_messages.html" %}

    <div class="student-info-box">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-1"><strong>Student Name:</strong> {{ student.full_name }}</p>
                <p class="mb-1"><strong>Admission No:</strong> {{ student.admission_number }}</p>
                <p class="mb-0"><strong>Class:</strong> {{ student.current_class_and_section|default:"N/A" }}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-1"><strong>Report Period:</strong> {{ report_period_start|date:"j M Y"|default:"Beginning" }} - {{ report_period_end|date:"j M Y"|default:"Today" }}</p>
                <p class="mb-0"><strong>Generated On:</strong> {% now "jS F Y, P" %}</p>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body p-0"> {# p-0 to make table flush if desired #}
            <div class="table-responsive">
                <table class="table table-hover transaction-table mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" style="width: 12%;">Date</th>
                            <th scope="col">Transaction Details</th>
                            <th scope="col" class="text-end" style="width: 12%;">Invoice #</th>
                            <th scope="col" class="text-end" style="width: 12%;">Payment #</th>
                            <th scope="col" class="text-end" style="width: 12%;">Debit ({{ request.tenant.schoolprofile.currency_symbol|default:'$' }})</th>
                            <th scope="col" class="text-end" style="width: 12%;">Credit ({{ request.tenant.schoolprofile.currency_symbol|default:'$' }})</th>
                            <th scope="col" class="text-end" style="width: 15%;">Balance ({{ request.tenant.schoolprofile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if opening_balance is not None %}
                        <tr class="balance-row">
                            <td colspan="6" class="text-end">Opening Balance as of {{ report_period_start|date:"j M Y"|default:"Beginning" }}</td>
                            <td class="text-end">{{ opening_balance|floatformat:2|intcomma }}</td>
                        </tr>
                        {% endif %}

                        {% for tx in transactions %}
                        <tr>
                            <td>{{ tx.date|date:"Y-m-d" }}</td>
                            <td>
                                {{ tx.description }}
                                {% if tx.type == "Invoice" %}<span class="badge bg-primary-subtle text-primary-emphasis rounded-pill ms-1">Invoice</span>
                                {% elif tx.type == "Payment" %}<span class="badge bg-success-subtle text-success-emphasis rounded-pill ms-1">Payment</span>
                                {% elif tx.type == "Concession" %}<span class="badge bg-warning-subtle text-warning-emphasis rounded-pill ms-1">Concession</span>
                                {% elif tx.type == "Adjustment" %}<span class="badge bg-secondary-subtle text-secondary-emphasis rounded-pill ms-1">Adjustment</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if tx.type == "Invoice" and tx.reference_link %}<a href="{{ tx.reference_link }}">{{ tx.reference_number|default:"N/A" }}</a>
                                {% elif tx.type == "Invoice" %}{{ tx.reference_number|default:"N/A" }}
                                {% endif %}
                            </td>
                             <td class="text-end">
                                {% if tx.type == "Payment" and tx.reference_link %}<a href="{{ tx.reference_link }}">{{ tx.reference_number|default:"N/A" }}</a>
                                {% elif tx.type == "Payment" %}{{ tx.reference_number|default:"N/A" }}
                                {% endif %}
                            </td>
                            <td class="text-end debit-amount">{% if tx.debit %}{{ tx.debit|floatformat:2|intcomma }}{% endif %}</td>
                            <td class="text-end credit-amount">{% if tx.credit %}{{ tx.credit|floatformat:2|intcomma }}{% endif %}</td>
                            <td class="text-end"><strong>{{ tx.balance|floatformat:2|intcomma }}</strong></td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted py-4">No transactions found for this period.</td>
                        </tr>
                        {% endfor %}

                        <tr class="balance-row table-light">
                            <td colspan="6" class="text-end"><strong>Closing Balance as of {{ report_period_end|date:"j M Y"|default:"Today" }}</strong></td>
                            <td class="text-end"><strong>{{ closing_balance|floatformat:2|intcomma }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="report-summary-box mt-4">
        <h5 class="mb-3">Ledger Summary</h5>
        <div class="row">
            <div class="col-md-4">
                Total Debits: <span class="summary-value">{{ total_debits|floatformat:2|intcomma|default:"0.00" }}</span>
            </div>
            <div class="col-md-4">
                Total Credits: <span class="summary-value">{{ total_credits|floatformat:2|intcomma|default:"0.00" }}</span>
            </div>
            <div class="col-md-4">
                Net Balance: <span class="summary-value">{{ closing_balance|floatformat:2|intcomma|default:"0.00" }}</span>
            </div>
        </div>
    </div>

</div>
{% endblock tenant_specific_content %}
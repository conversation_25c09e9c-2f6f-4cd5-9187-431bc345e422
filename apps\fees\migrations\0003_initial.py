# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fees', '0002_initial'),
        ('schools', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='students.student'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='schools.term'),
        ),
        migrations.AddField(
            model_name='invoicedetail',
            name='concession_type',
            field=models.ForeignKey(blank=True, help_text='Link to ConcessionType if this is a discount line.', null=True, on_delete=django.db.models.deletion.PROTECT, to='fees.concessiontype'),
        ),
        migrations.AddField(
            model_name='invoicedetail',
            name='fee_head',
            field=models.ForeignKey(blank=True, help_text='Link to FeeHead if this is a standard charge/fee.', null=True, on_delete=django.db.models.deletion.PROTECT, to='fees.feehead'),
        ),
        migrations.AddField(
            model_name='invoicedetail',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='fees.invoice'),
        ),
        migrations.AddField(
            model_name='studentconcession',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='studentconcession',
            name='concession_type',
            field=models.ForeignKey(limit_choices_to={'is_active': True}, on_delete=django.db.models.deletion.PROTECT, to='fees.concessiontype'),
        ),
        migrations.AddField(
            model_name='studentconcession',
            name='granted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_concessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentconcession',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applied_concessions', to='students.student'),
        ),
        migrations.AddField(
            model_name='studentconcession',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='schools.term'),
        ),
        migrations.AddField(
            model_name='studentfeeallocation',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='student_fee_allocations', to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='studentfeeallocation',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_fee_allocations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentfeeallocation',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='student_allocations', to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='studentfeeallocation',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_allocations', to='students.student'),
        ),
        migrations.AddField(
            model_name='studentfeeallocation',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='student_fee_allocations_term', to='schools.term'),
        ),
        migrations.AlterUniqueTogether(
            name='feestructure',
            unique_together={('name', 'academic_year', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='feestructureitem',
            unique_together={('fee_structure', 'fee_head')},
        ),
        migrations.AlterUniqueTogether(
            name='studentconcession',
            unique_together={('student', 'concession_type', 'academic_year', 'term')},
        ),
        migrations.AddConstraint(
            model_name='studentfeeallocation',
            constraint=models.UniqueConstraint(condition=models.Q(('term__isnull', False)), fields=('student', 'fee_structure', 'term'), name='unique_student_structure_term_allocation'),
        ),
        migrations.AddConstraint(
            model_name='studentfeeallocation',
            constraint=models.UniqueConstraint(condition=models.Q(('term__isnull', True)), fields=('student', 'fee_structure'), name='unique_student_structure_allocation_year'),
        ),
    ]

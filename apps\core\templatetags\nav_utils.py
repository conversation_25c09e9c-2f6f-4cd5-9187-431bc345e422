# D:\school_fees_saas_v2\apps\core\templatetags\nav_utils.py
from django import template

register = template.Library()

@register.simple_tag(takes_context=True)
def is_active_nav(context, view_names="", app_names="", return_value="active"):
    """
    Checks if the current view is in the provided list of view names or app names.
    
    Args:
        context: The template context (automatically passed).
        view_names (str): A comma-separated string of full view names (e.g., 'schools:staff_list,schools:staff_create').
        app_names (str): An optional comma-separated string of app names (e.g., 'hr,payroll').
        return_value (str): The string to return if active (defaults to "active").
        
    Returns:
        str: The return_value if active, otherwise an empty string.
    """
    request = context.get('request')
    if not request or not hasattr(request, 'resolver_match') or not request.resolver_match:
        return ""

    current_view_name = request.resolver_match.view_name
    current_app_name = request.resolver_match.app_name

    # Check against specific view names
    if view_names:
        # Create a set for faster lookups
        view_name_set = {name.strip() for name in view_names.split(',')}
        if current_view_name in view_name_set:
            return return_value

    # Check against app names
    if app_names:
        app_name_set = {name.strip() for name in app_names.split(',')}
        if current_app_name in app_name_set:
            return return_value
            
    return ""



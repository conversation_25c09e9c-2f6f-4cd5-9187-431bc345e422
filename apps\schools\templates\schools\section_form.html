{% extends "tenant_base.html" %}
{% load static core_tags widget_tweaks %}

{% block title %}{{ view_title|default:"Section Form" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">{{ view_title|default:"Section Form" }}</h4>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            <form method="post" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                {{ form.as_p }}

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="{% url 'schools:class_detail' pk=school_class.pk %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if object %}Update{% else %}Create{% endif %} Section
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}




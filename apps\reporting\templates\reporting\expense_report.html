{# D:\school_fees_saas_v2\apps\reporting\templates\reporting\expense_report.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .report-summary-card { background-color: #f8f9fa; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">

    {% include "partials/_report_header.html" with report_icon_class="bi bi-wallet-fill text-danger" %}
    {# Assumes _report_header.html exists and uses view_title, report_tenant_name from context #}
    {% comment %} {# Alternatively, manually create the header structure here:
    <div class="pagetitle mb-3">
        <div class="d-flex align-items-center">
            <i class="bi bi-wallet-fill me-3" style="font-size: 2.5rem; color: var(--bs-danger);"></i>
            <div>
                <h1>{{ view_title }}</h1>
                {% if report_tenant_name %}<h2 class="h5 text-muted">{{ report_tenant_name }}</h2>{% endif %}
            </div>
        </div>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item">{% trans "Reports" %}</li>
                <li class="breadcrumb-item active">{{ view_title|truncatechars:30 }}</li>
            </ol>
        </nav>
    </div>
    #} {% endcomment %}

    {% include "partials/_messages.html" %}

    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}
    
    {% comment %} {% if filter_form %}
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="mb-0 card-title-sm py-1 d-flex justify-content-between align-items-center">
                <span><i class="bi bi-funnel-fill me-2"></i>{% trans "Filter Expenses" %}</span>
                <a class="btn btn-link btn-sm p-0" data-bs-toggle="collapse" href="#filterCollapse" role="button" aria-expanded="{% if request.GET %}true{% else %}false{% endif %}" aria-controls="filterCollapse">
                    Toggle Filters
                </a>
            </h5>
        </div>
        <div class="collapse {% if request.GET %}show{% endif %}" id="filterCollapse">
            <div class="card-body pt-3 pb-2">
                <form method="get" class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="{{ filter_form.start_date.id_for_label }}" class="form-label form-label-sm">{{ filter_form.start_date.label }}</label>
                        {% render_field filter_form.start_date class+="form-control-sm" %}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ filter_form.end_date.id_for_label }}" class="form-label form-label-sm">{{ filter_form.end_date.label }}</label>
                        {% render_field filter_form.end_date class+="form-control-sm" %}
                    </div>
                    {# Example if you add category filter #}
                    <div class="col-md-3">
                        <label for="{{ filter_form.category.id_for_label }}" class="form-label form-label-sm">{{ filter_form.category.label }}</label>
                        {% render_field filter_form.category class+="form-select-sm" %}
                    </div>
                    <div class="col-md-3">
                        <label class="form-label form-label-sm"> </label> {# Spacer for alignment #}
                        <button type="submit" class="btn btn-primary btn-sm w-100"><i class="bi bi-search me-1"></i> {% trans "Apply Filters" %}</button>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label form-label-sm"> </label> {# Spacer for alignment #}
                        <a href="{{ request.path }}" class="btn btn-outline-secondary btn-sm w-100"><i class="bi bi-arrow-clockwise me-1"></i> {% trans "Reset Filters" %}</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %} {% endcomment %}

    {# Summary Section - uses data from get_report_data #}
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card report-summary-card shadow-sm">
                <div class="card-body text-center p-3">
                    <h6 class="text-muted mb-1">{% trans "Total Expenses in Period" %}</h6>
                    <h3 class="fw-bold mb-0">{{ school_profile.currency_symbol|default:"$" }}{{ summary_total_expenses|intcomma|default:"0.00" }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card report-summary-card shadow-sm">
                <div class="card-body text-center p-3">
                    <h6 class="text-muted mb-1">{% trans "Number of Expense Transactions" %}</h6>
                    <h3 class="fw-bold mb-0">{{ summary_expense_count|intcomma|default:"0" }}</h3>
                </div>
            </div>
        </div>
        {# You can add expenses_by_category summary here if you implement it #}
    </div>


    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0 card-title-sm py-1">{% trans "Expense Details" %}</h5>
            <div>
                <a href="?{{ filter_params }}&export=csv" class="btn btn-sm btn-outline-secondary"><i class="bi bi-file-earmark-ruled me-1"></i>CSV</a>
                <a href="?{{ filter_params }}&export=excel" class="btn btn-sm btn-outline-success"><i class="bi bi-file-earmark-excel me-1"></i>Excel</a>
                <a href="?{{ filter_params }}&export=pdf" class="btn btn-sm btn-outline-danger" target="_blank"><i class="bi bi-file-earmark-pdf me-1"></i>PDF</a>
            </div>
        </div>
        <div class="card-body p-0">
            {% if expenses_on_page %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm mb-0 report-table">
                        <thead>
                            <tr>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Description" %}</th>
                                <th>{% trans "Category" %}</th>
                                <th class="text-end">{% trans "Amount" %}</th>
                                <th>{% trans "Payment Method" %}</th>
                                <th>{% trans "Recorded By" %}</th>
                                <th>{% trans "Vendor/Payee" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses_on_page %}
                            <tr>
                                <td>{{ expense.expense_date|date:"Y-m-d" }}</td>
                                <td>{{ expense.description|truncatechars:50 }}</td>
                                <td>{{ expense.category.name|default:"N/A" }}</td>
                                <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ expense.amount|intcomma }}</td>
                                <td>{{ expense.payment_method.name|default:"N/A" }}</td>
                                <td>{{ expense.recorded_by.get_full_name|default:expense.recorded_by.email|default:"N/A" }}</td>
                                <td>{{ expense.vendor_name|default:"N/A" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-light m-3 text-center">{% trans "No expense records found for the selected criteria." %}</div>
            {% endif %}
        </div>
        {% if is_paginated %}
        <div class="card-footer">
            {% include "partials/_pagination.html" with page_obj=page_obj query_params=filter_params %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}




{# D:\school_fees_saas_v2\templates\fees\invoice_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize fees_tags widget_tweaks %}

{% block title %}{{ view_title|default:"Manage Invoices" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .table th, .table td { vertical-align: middle; }
        .payment-breakdown { 
            font-size: 0.8rem; 
            line-height: 1.3; 
            white-space: nowrap; /* Prevent method names from wrapping */
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800 me-3">{{ view_title|default:"Manage Invoices" }}</h1>
        <div class="d-flex align-items-center gap-2 mt-2 mt-md-0">
            {% if perms.fees.add_invoice %}
                <a href="{% url 'fees:invoice_create' %}" class="btn btn-primary btn-sm"><i class="bi bi-plus-circle-fill me-1"></i> Create Invoice</a>
                <a href="{% url 'fees:generate_structure_invoices' %}" class="btn btn-success btn-sm"><i class="bi bi-receipt-cutoff me-1"></i> Generate by Structure</a>
            {% endif %}
        </div>
    </div>

    {% include "partials/_messages.html" %}

    {% include "fees/_invoice_filter_toggle.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">Invoice Records</h6>
        </div>
        <div class="card-body">
            {% if invoices %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered align-middle table-sm">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 8%;">Invoice #</th>
                                <th style="width: 18%;">Student</th>
                                <th style="width: 10%;">Issue Date</th>
                                <th class="text-end" style="width: 10%;">Total</th>
                                <th class="text-end" style="width: 8%;">Discount</th>
                                <th class="text-end" style="width: 15%;">Paid</th> {# <-- SINGLE 'Paid' Header #}
                                <th class="text-end" style="width: 10%;">Balance Due</th>
                                <th class="text-center" style="width: 8%;">Status</th>
                                <th class="text-center" style="width: 13%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td><a href="{{ invoice.get_absolute_url }}"><strong>{{ invoice.invoice_number_display|default:invoice.pk }}</strong></a></td>
                                <td>
                                    {% if invoice.student %}
                                        <a href="{% url 'students:student_detail' invoice.student.pk %}">{{ invoice.student.full_name }}</a>
                                        <small class="d-block text-muted">{{ invoice.student.admission_number }}</small>
                                    {% else %} N/A {% endif %}
                                </td>
                                <td>{{ invoice.issue_date|date:"d M Y" }}</td>
                                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.total_amount|intcomma }}</td>
                                <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ invoice.discount_applied|intcomma }}</td>
                                
                                <!-- === REPLACED "PAID" COLUMN === -->
                                <td class="text-end">
                                    {% if invoice.amount_paid > 0 %}
                                        <strong class="text-success">{{ school_profile.currency_symbol|default:'$' }}{{ invoice.amount_paid|intcomma }}</strong>
                                        {# Loop through the prefetched allocations/payments #}
                                        {% for allocation in invoice.allocations.all %}
                                            <div class="payment-breakdown text-muted" title="Paid on {{ allocation.payment.payment_date|date:'d M, Y' }}">
                                                <i class="bi bi-dot"></i>{{ allocation.payment.payment_method.name|default:"N/A" }}
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">{{ school_profile.currency_symbol|default:'$' }}0</span>
                                    {% endif %}
                                </td>
                                <!-- === END OF REPLACED "PAID" COLUMN === -->

                                <td class="text-end {% if invoice.balance_due > 0 %}text-danger fw-bold{% else %}text-muted{% endif %}">
                                    {{ school_profile.currency_symbol|default:"$" }}{{ invoice.balance_due|intcomma }}
                                </td>
                                <td class="text-center">
                                    {% invoice_status_badge invoice.status %}
                                </td>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionsMenu{{invoice.pk}}" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionsMenu{{invoice.pk}}">
                                            <li><a class="dropdown-item" href="{{ invoice.get_absolute_url }}"><i class="bi bi-eye me-2"></i>View Details</a></li>
                                            <li><a class="dropdown-item" href="{% url 'fees:invoice_pdf_view' pk=invoice.pk %}" target="_blank"><i class="bi bi-file-earmark-pdf me-2"></i>View/Print PDF</a></li>
                                            
                                            {% if invoice.is_editable and perms.fees.change_invoice %}
                                                <li><a class="dropdown-item" href="{% url 'fees:invoice_update' pk=invoice.pk %}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                                            {% endif %}
                                            
                                            {% if invoice.is_payable %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{% url 'payments:record_manual_payment' invoice_pk=invoice.pk %}"><i class="bi bi-cash-coin me-2"></i>Record Payment</a></li>
                                            {% endif %}
                                            
                                            {% if invoice.status == 'DRAFT' and perms.fees.delete_invoice %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="{% url 'fees:invoice_delete' pk=invoice.pk %}"><i class="bi bi-trash3 me-2"></i>Delete Draft</a>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" with page_obj=page_obj %}
            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    No invoices found matching your criteria.
                    {% if perms.fees.add_invoice and not request.GET.urlencode %}
                        <a href="{% url 'fees:invoice_create' %}" class="alert-link">Create one manually?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a>
    </div>

</div>
{% endblock tenant_specific_content %}


{% block page_specific_js %}
    {{ block.super }}
    {# Initialize Select2 if you choose to use it for better dropdowns #}
    {# You would need to include Select2 CSS in base.html or tenant_base.html #}
    {# and Select2 JS here or in base.html #}
    {% comment %} {#
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        // Target specific select elements if they are rendered by django-filter
        $('#{{ filter.form.student.id_for_label|default_if_none:filter_form.student.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Student",
            allowClear: true
        });
        $('#{{ filter.form.academic_year.id_for_label|default_if_none:filter_form.academic_year.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Year",
            allowClear: true
        });
        $('#{{ filter.form.status.id_for_label|default_if_none:filter_form.status.id_for_label }}').select2({
            theme: "bootstrap-5",
            placeholder: "Select Status",
            allowClear: true
        });
    });
    </script>
    #} {% endcomment %}
{% endblock %}













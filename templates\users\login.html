{# D:\school_fees_saas_v2\templates\users\login.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title|default:"Platform Administrator Login" }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .admin-login-card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .admin-login-card .card-header {
            border-top-left-radius: 1.5rem;
            border-top-right-radius: 1.5rem;
            padding: 3rem 2rem 2rem;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border: none;
        }
        .form-floating > .form-control {
            border-radius: 1rem !important;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            height: 3.5rem;
            padding: 1rem 0.75rem 0.25rem 0.75rem;
        }
        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            transform-origin: 0 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        }
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }
        .btn-primary {
            border-radius: 0.75rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.75rem 2rem rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .admin-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            padding: 1.5rem;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .forgot-password-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .forgot-password-link:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }
        .register-link {
            color: #764ba2;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .register-link:hover {
            color: #6a4190;
            text-decoration: underline;
        }
        .field-icon {
            color: #667eea;
            margin-right: 0.5rem;
        }
        .form-floating.focused > .form-control {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > .form-control::placeholder {
            color: transparent;
        }
        .form-floating > .form-control:focus::placeholder {
            color: transparent;
        }
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 0.75rem;
            color: white;
        }
        @media (max-width: 768px) {
            .admin-login-card .card-header {
                padding: 2rem 1rem 1.5rem;
            }
            .admin-login-card .card-body {
                padding: 1.5rem 1rem;
            }
            .admin-icon {
                padding: 1rem;
                margin-bottom: 0.5rem;
            }
            .security-badge {
                position: static;
                display: inline-block;
                margin-top: 1rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6 col-xl-5">
            <div class="card admin-login-card">
                <div class="card-header text-white text-center position-relative">
                    <div class="security-badge">
                        <i class="bi bi-shield-check me-1"></i>SSL Secured
                    </div>
                    <div class="admin-icon">
                        <i class="bi bi-shield-lock-fill" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 class="mb-2 fw-bold">{{ view_title|default:"Platform Administrator" }}</h2>
                    <p class="mb-0 opacity-75">Secure access to platform management</p>
                </div>

                <div class="card-body p-4 p-md-5">
                    {# Display messages (success, error, info) #}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {# Display non-field errors (e.g., "Invalid login credentials") #}
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2 mb-4">
                            {% for error in form.non_field_errors %}
                                {{ error }}{% if not forloop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Administrator Email Field #}
                        <div class="mb-4">
                            <div class="form-floating">
                                <input type="email"
                                       class="form-control"
                                       id="floatingAdminEmail"
                                       name="{{ form.username.name }}"
                                       placeholder="Enter your administrator email address"
                                       autofocus
                                       autocomplete="email"
                                       data-bs-toggle="tooltip"
                                       data-bs-placement="top"
                                       title="Enter the email address associated with your administrator account"
                                       {% if form.username.value %}value="{{ form.username.value }}"{% endif %}
                                       required>
                                <label for="floatingAdminEmail">
                                    <i class="bi bi-envelope-fill field-icon"></i>{{ form.username.label }}
                                </label>
                            </div>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Password Field #}
                        <div class="mb-4">
                            <div class="form-floating">
                                <input type="password"
                                       class="form-control"
                                       id="floatingAdminPassword"
                                       name="{{ form.password.name }}"
                                       placeholder="Enter your secure password"
                                       autocomplete="current-password"
                                       data-bs-toggle="tooltip"
                                       data-bs-placement="top"
                                       title="Enter your administrator account password"
                                       required>
                                <label for="floatingAdminPassword">
                                    <i class="bi bi-key-fill field-icon"></i>{{ form.password.label }}
                                </label>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Remember Me Checkbox #}
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                                <label class="form-check-label" for="rememberMe">
                                    Keep me signed in for 30 days
                                </label>
                            </div>
                        </div>
                        {# Hidden input for 'next' parameter (for redirection after successful login) #}
                        {% if request.GET.next %}
                            <input type="hidden" name="next" value="{{ request.GET.next }}">
                        {% endif %}

                        {# Login Button #}
                        <button type="submit" class="btn btn-primary w-100 btn-lg mt-4">
                            <i class="bi bi-shield-lock-fill me-2"></i>
                            Access Platform
                        </button>
                    </form>

                    {# Footer Links #}
                    <hr class="my-5">
                    <div class="text-center">
                        <p class="mb-3">
                            <a href="{% url 'password_reset' %}" class="forgot-password-link">
                                <i class="bi bi-question-circle me-1"></i>Forgot your password?
                            </a>
                        </p>
                        <p class="mb-0">
                            <small class="text-muted">New school?</small><br>
                            <a href="{% url 'tenants:register_school' %}" class="register-link">
                                <i class="bi bi-building-add me-1"></i>Register your institution
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{# Initialize tooltips and add security features #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add focus effects for better UX and handle form-floating behavior
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(function(control) {
        // Check if field has value on page load and add focused class
        if (control.value && control.value.trim() !== '') {
            control.parentElement.classList.add('focused');
        }

        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            if (!this.value || this.value.trim() === '') {
                this.parentElement.classList.remove('focused');
            }
        });

        control.addEventListener('input', function() {
            if (this.value && this.value.trim() !== '') {
                this.parentElement.classList.add('focused');
            } else {
                this.parentElement.classList.remove('focused');
            }
        });
    });

    // Add loading state to submit button
    const loginForm = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');

    if (loginForm && submitBtn) {
        loginForm.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Authenticating...';
            submitBtn.disabled = true;
        });
    }
});
</script>
{% endblock content %}


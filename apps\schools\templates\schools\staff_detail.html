{# D:\school_fees_saas_v2\apps\schools\templates\schools\staff_detail.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize %} {# Load humanize for intcomma if displaying numbers #}

{% block title %}{{ view_title|default:"Staff Details" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Staff Details" }}</h1>
        <div>
            {% if perms.schools.change_staffuser %}
            <a href="{% url 'schools:staff_update' staff_member.pk %}" class="btn btn-primary btn-sm">
                <i class="bi bi-pencil-square"></i> Edit Details
            </a>
            {% endif %}

            {# NEW SALARY BUTTON GOES HERE #}
            {# We'll assume a permission like 'hr.change_staffsalarystructure' is needed #}
            {% if perms.hr.change_staffsalarystructure %}
            <a href="{% url 'hr:staff_salary_update' staff_pk=staff_member.pk %}" class="btn btn-success btn-sm ms-2">
                <i class="bi bi-currency-dollar"></i> Manage Salary Structure
            </a>
            {% endif %}

            {% if perms.auth.change_group and perms.auth.view_group %} {# Or more specific permission for assigning roles #}
            <a href="{% url 'schools:staff_assign_roles' staff_member.pk %}" class="btn btn-info btn-sm ms-2">
                <i class="bi bi-person-check-fill"></i> Assign Roles
            </a>
            {% endif %}
        </div>
    </div>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:staff_list' %}">Manage Staff</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ staff_member.full_name|default:staff_member.email }}</li>
        </ol>
    </nav>

    {% include "partials/_messages.html" %}


    {# Use cards for better organization #}
    <div class="row">
        {# --- Login & Access Details --- #}
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-key-fill me-2"></i>Login & Access Details</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-5">Login Email:</dt>
                        <dd class="col-sm-7">{{ staff_member.email }}</dd>

                        <dt class="col-sm-5">First Name:</dt>
                        <dd class="col-sm-7">{{ staff_member.first_name|default:"-" }}</dd>

                        <dt class="col-sm-5">Last Name:</dt>
                        <dd class="col-sm-7">{{ staff_member.last_name|default:"-" }}</dd>

                        <dt class="col-sm-5">Account Active:</dt>
                        <dd class="col-sm-7">
                            {% if staff_member.is_active %}
                                <span class="badge bg-success">Yes</span>
                            {% else %}
                                <span class="badge bg-danger">No</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-5">Tenant Admin Access:</dt>
                        <dd class="col-sm-7">
                            {% if staff_member.is_staff %}
                                <span class="badge bg-success">Yes</span>
                            {% else %}
                                <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-5">System Superuser:</dt>
                        <dd class="col-sm-7">
                            {% if staff_member.is_superuser %}
                                <span class="badge bg-warning text-dark">Yes</span>
                            {% else %}
                                <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-5">Assigned Roles/Groups:</dt>
                        <dd class="col-sm-7">
                            {% for group in assigned_groups %} {# assigned_groups passed from view #}
                                <span class="badge bg-info me-1">{{ group.name }}</span>
                            {% empty %}
                                <span class="text-muted">None Assigned</span>
                            {% endfor %}
                        </dd>

                        <dt class="col-sm-5">Date Joined System:</dt>
                        <dd class="col-sm-7">{{ staff_member.date_joined|date:"Y-m-d H:i" }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        {# --- HR Profile Details --- #}
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2"></i>HR & Employment Details</h5>
                </div>
                <div class="card-body">
                    {% if staff_member.photo %}
                        <div class="text-center mb-3">
                            <img src="{{ staff_member.photo.url }}" alt="Photo of {{ staff_member.full_name }}" class="img-thumbnail" style="max-height: 150px; max-width: 150px; object-fit: cover;">
                        </div>
                    {% endif %}
                    <dl class="row mb-0">
                        <dt class="col-sm-5">Employee ID:</dt>
                        <dd class="col-sm-7">{{ staff_member.employee_id|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Designation:</dt>
                        <dd class="col-sm-7">{{ staff_member.designation|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Department:</dt>
                        <dd class="col-sm-7">{{ staff_member.department|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Employment Type:</dt>
                        <dd class="col-sm-7">{{ staff_member.get_employment_type_display|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Date Hired:</dt>
                        <dd class="col-sm-7">{{ staff_member.date_hired|date:"Y-m-d"|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Middle Name:</dt>
                        <dd class="col-sm-7">{{ staff_member.middle_name|default:"-" }}</dd>

                        <dt class="col-sm-5">Gender:</dt>
                        <dd class="col-sm-7">{{ staff_member.get_gender_display|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Date of Birth:</dt>
                        <dd class="col-sm-7">{{ staff_member.date_of_birth|date:"Y-m-d"|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Marital Status:</dt>
                        <dd class="col-sm-7">{{ staff_member.get_marital_status_display|default:"N/A" }}</dd>

                        <dt class="col-sm-5">Primary Phone:</dt>
                        <dd class="col-sm-7">{{ staff_member.phone_number_primary|default:"-" }}</dd>

                        <dt class="col-sm-5">Alternate Phone:</dt>
                        <dd class="col-sm-7">{{ staff_member.phone_number_alternate|default:"-" }}</dd>

                        <dt class="col-sm-5">Address:</dt>
                        <dd class="col-sm-7">
                            {{ staff_member.address_line1|default:"" }}
                            {% if staff_member.address_line2 %}<br>{{ staff_member.address_line2 }}{% endif %}
                            {% if staff_member.city or staff_member.state_province or staff_member.postal_code %}
                                <br>{{ staff_member.city|default:"" }}{% if staff_member.city and staff_member.state_province %},{% endif %} {{ staff_member.state_province|default:"" }} {{ staff_member.postal_code|default:"" }}
                            {% endif %}
                            {% if staff_member.country %}<br>{{ staff_member.country|default:"" }}{% endif %}
                        </dd>

                        <dt class="col-sm-5">Date Left School:</dt>
                        <dd class="col-sm-7">{{ staff_member.date_left|date:"Y-m-d"|default:"N/A" }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div> {# End row #}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:staff_list' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left-circle me-2"></i>Back to Staff List</a>
        {% if perms.schools.delete_staffuser and request.user != staff_member %}
            <form action="{% url 'schools:staff_delete' staff_member.pk %}" method="post" style="display: inline; margin-left: 5px;">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete staff member \'{{ staff_member.full_name|escapejs }}\'? This will permanently remove their login account and profile.');"><i class="bi bi-trash-fill me-2"></i>Delete Staff Member</button>
            </form>
        {% endif %}
    </div>

</div> {# End container #}
{% endblock %}
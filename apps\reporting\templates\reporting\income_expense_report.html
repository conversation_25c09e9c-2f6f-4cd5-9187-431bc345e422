{% extends "tenant_base.html" %}
{% load humanize static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
{% comment %} <div class="container-fluid">
    <h1 class="h3 mb-3 text-gray-800">{{ view_title }}</h1> {% endcomment %}


    {% include "partials/_messages.html" %}
    {% comment %} {% include "./_report_filter_export_card.html" %} {% endcomment %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}
    
    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Income Statement for Period: {{ report_data.start_date_filter|date:"d M Y" }} to {{ report_data.end_date_filter|date:"d M Y" }}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-8 offset-lg-2">
                    {# Revenue Section #}
                    <h5 class="mt-3">Revenue</h5>
                    <table class="table table-sm">
                        {% for item in report_data.revenues %}
                        <tr>
                            <td>    {{ item.account_name }}</td>
                            <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="2"><em>No revenue accounts found for this period.</em></td>
                        </tr>
                        {% endfor %}
                        <tr class="fw-bold table-light">
                            <td>Total Revenue</td>
                            <td class="text-end">{{ report_data.total_revenue|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>

                    {# Cost of Sales Section #}
                    <h5 class="mt-3">Cost of Sales</h5>
                    <table class="table table-sm">
                        {% for item in report_data.cost_of_sales %}
                        <tr>
                            <td>    {{ item.account_name }}</td>
                            <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="2"><em>No cost of sales accounts found for this period.</em></td>
                        </tr>
                        {% endfor %}
                        <tr class="fw-bold table-light">
                            <td>Total Cost of Sales</td>
                            <td class="text-end">{{ report_data.total_cost_of_sales|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                    <table class="table table-sm mt-1">
                        <tr class="fw-bolder fs-5 table-secondary">
                            <td>GROSS PROFIT</td>
                            <td class="text-end">{{ report_data.gross_profit|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>

                    {# Operating Expenses Section #}
                    <h5 class="mt-3">Operating Expenses</h5>
                    <table class="table table-sm">
                        {% for item in report_data.operating_expenses %}
                        <tr>
                            <td>    {{ item.account_name }}</td>
                            <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="2"><em>No operating expense accounts found for this period.</em></td>
                        </tr>
                        {% endfor %}
                        <tr class="fw-bold table-light">
                            <td>Total Operating Expenses</td>
                            <td class="text-end">{{ report_data.total_operating_expenses|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                    <table class="table table-sm mt-1">
                        <tr class="fw-bolder fs-5 table-secondary">
                            <td>OPERATING INCOME / (LOSS)</td>
                            <td class="text-end">{{ report_data.operating_income|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                    
                    {# ... Other Income / Other Expenses ... #}

                    <table class="table table-sm mt-3">
                        <tr class="fw-bolder fs-4 table-dark text-white">
                            <td>NET PROFIT / (LOSS)</td>
                            <td class="text-end">{{ report_data.net_profit|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}





{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Income Expense Report" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Income Expense Report" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %} {% endcomment %}
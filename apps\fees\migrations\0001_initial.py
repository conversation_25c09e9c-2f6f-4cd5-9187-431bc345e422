# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeePermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': [('view_fees_module', 'Can view the main Fees Management module link')],
                'managed': False,
                'default_permissions': (),
            },
        ),
        migrations.CreateModel(
            name='ConcessionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Name of the concession (e.g., 'Sibling Discount', 'Staff Child Waiver').", max_length=150, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('PERCENTAGE', 'Percentage'), ('FIXED_AMOUNT', 'Fixed Amount')], default='PERCENTAGE', max_length=20)),
                ('value', models.DecimalField(decimal_places=2, help_text='Percentage (e.g., 10 for 10%) or Fixed Amount.', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('is_active', models.BooleanField(default=True, help_text='Is this concession type currently available?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Concession Type',
                'verbose_name_plural': 'Concession Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="e.g., 'Grade 1 Fees 2024-25 Term 1'", max_length=150)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total amount calculated from fee structure items', max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Fee Structure',
                'verbose_name_plural': 'Fee Structures',
                'ordering': ['-academic_year__start_date', 'term__start_date', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructureItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('is_optional', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Fee Structure Item',
                'verbose_name_plural': 'Fee Structure Items',
                'ordering': ['fee_head__name'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(blank=True, db_index=True, max_length=50, unique=True)),
                ('issue_date', models.DateField(default=django.utils.timezone.now)),
                ('due_date', models.DateField()),
                ('subtotal_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_concession_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SENT', 'Sent'), ('PARTIALLY_PAID', 'Partially Paid'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue'), ('VOID', 'Void'), ('CANCELLED', 'Cancelled')], db_index=True, default='DRAFT', max_length=20)),
                ('notes_to_parent', models.TextField(blank=True, null=True)),
                ('internal_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'ordering': ['-issue_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('line_type', models.CharField(choices=[('FEE_ITEM', 'Fee Item / Charge'), ('CONCESSION_ITEM', 'Concession / Discount Applied')], default='FEE_ITEM', max_length=20)),
                ('description', models.CharField(help_text='Description of the charge or concession.', max_length=255)),
                ('quantity', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_price', models.DecimalField(decimal_places=2, help_text='Price per unit. For concessions, this might be the discount amount per unit if quantity > 1.', max_digits=10)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Total for this line (Quantity * Unit Price). Negative for concessions.', max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Invoice Line Item',
                'verbose_name_plural': 'Invoice Line Items',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='StudentConcession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, null=True)),
                ('granted_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Student Specific Concession',
                'verbose_name_plural': 'Student Specific Concessions',
                'ordering': ['student__last_name', '-academic_year__start_date', 'concession_type__name'],
            },
        ),
        migrations.CreateModel(
            name='StudentFeeAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, help_text='Is this allocation currently active for billing?')),
                ('notes', models.TextField(blank=True, help_text="Any specific notes for this student's fee structure allocation.", null=True, verbose_name='Notes/Reason for Allocation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Student Fee Structure Allocation',
                'verbose_name_plural': 'Student Fee Structure Allocations',
                'ordering': ['student__last_name', '-academic_year__start_date', 'term__start_date'],
            },
        ),
        migrations.CreateModel(
            name='FeeHead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="e.g., 'Tuition Fee', 'Bus Fee'", max_length=150, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('income_account_link', models.ForeignKey(blank=True, limit_choices_to={'account_type__classification': 'REVENUE'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='fee_heads', to='accounting.account', verbose_name='Default Income Account')),
            ],
            options={
                'verbose_name': 'Fee Head',
                'verbose_name_plural': 'Fee Heads',
                'ordering': ['name'],
            },
        ),
    ]

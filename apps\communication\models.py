# D:\school_fees_saas_v2\apps\communication\models.py
from django.db import models
from django.conf import settings # For settings.AUTH_USER_MODEL
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import <PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType

class CommunicationLog(models.Model):
    class MessageType(models.TextChoices):
        # Email types (can be more granular)
        EMAIL = 'EMAIL', _('Generic Email') # Default for the generic task
        WELCOME = 'WELCOME', _('Welcome Email')
        FEE_REMINDER = 'FEE_REMINDER', _('<PERSON><PERSON>minder')
        PAYMENT_CONFIRM = 'PAYMENT_CONFIRM', _('Payment Confirmation')
        INVOICE_ISSUED = 'INVOICE_ISSUED', _('Invoice Issued')
        LEAVE_STATUS = 'LEAVE_STATUS', _('Leave Status Update')
        TENANT_GENERAL = 'TENANT_GENERAL', _('General Tenant Notification')
        PLATFORM_ANNOUNCEMENT_EMAIL = 'PLATFORM_ANNOUNCEMENT_EMAIL', _('Platform Announcement (Email)') # If platform announcements can be emailed
        
        # Other types
        SMS = 'SMS', _('SMS') 
        APP_NOTIFICATION = 'APP_NOTIFICATION', _('In-App Notification')
        OTHER = 'OTHER', _('Other Communication')

    class Status(models.TextChoices):
        PENDING = 'PENDING', _('Pending')
        PROCESSING = 'PROCESSING', _('Processing') # Task picked it up
        SENT = 'SENT', _('Sent')                   # Dispatch successful
        FAILED = 'FAILED', _('Failed')                 # Dispatch failed after retries
        DELIVERED = 'DELIVERED', _('Delivered')         # Requires webhook from email provider
        OPENED = 'OPENED', _('Opened')                 # Requires tracking pixel / webhook
        CLICKED = 'CLICKED', _('Clicked')               # Requires link tracking / webhook
        INVALID_RECIPIENT = 'INVALID_RECIPIENT', _('Invalid Recipient') # If email/phone is bad
        UNSUBSCRIBED = 'UNSUBSCRIBED', _('Unsubscribed') # If user opted out

    # Core communication details
    message_type = models.CharField(
        _("Message Type"),
        max_length=30,  # Increased length for more descriptive choices
        choices=MessageType.choices, 
        default=MessageType.EMAIL # Default to generic email
    )
    status = models.CharField(
        _("Status"),
        max_length=20, 
        choices=Status.choices, 
        default=Status.PENDING, 
        db_index=True
    )
    
    # Sender (Usually the system or a platform admin for automated emails)
    # For tenant-initiated emails by staff, this might be the StaffUser.
    # Using AUTH_USER_MODEL is good for platform-level, but consider how tenant staff initiated emails are logged.
    sent_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='initiated_communications',
        verbose_name=_("Initiated By (User)")
    )
    
    # Recipient Information
    recipient_email = models.EmailField(_("Recipient Email"), blank=True, null=True, db_index=True)
    recipient_phone = models.CharField(_("Recipient Phone"), max_length=30, blank=True, null=True, db_index=True)
    # Optional: Direct links to user models if you want to navigate directly from the log
    # recipient_public_user = models.ForeignKey(settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL, related_name='communication_logs_as_public_recipient')
    # recipient_staff_user = models.ForeignKey('schools.StaffUser', null=True, blank=True, on_delete=models.SET_NULL, related_name='communication_logs_as_staff_recipient')
    # recipient_parent_user = models.ForeignKey('students.ParentUser', null=True, blank=True, on_delete=models.SET_NULL, related_name='communication_logs_as_parent_recipient')

    subject = models.CharField(_("Subject"), max_length=255, blank=True, null=True) 
    body_preview = models.TextField(_("Body Preview"), blank=True, null=True, help_text=_("A short preview or summary of the message content."))
    
    # Related object (e.g., link to the Invoice, Payment, LeaveRequest that triggered it)
    content_type = models.ForeignKey(
        ContentType, 
        on_delete=models.SET_NULL, # Keep log if related model type is deleted (rare)
        null=True, blank=True,
        verbose_name=_("Related Object Type")
    )
    object_id = models.CharField(_("Related Object ID"), max_length=100, null=True, blank=True, db_index=True) # CharField for UUIDs too
    related_object = GenericForeignKey('content_type', 'object_id')

    # Timestamps and Errors
    sent_at = models.DateTimeField(_("Sent At"), null=True, blank=True, help_text=_("Timestamp when the communication was successfully dispatched."))
    created_at = models.DateTimeField(_("Logged At"), auto_now_add=True) # When the log entry was created
    updated_at = models.DateTimeField(_("Last Updated"), auto_now=True) # When the log entry was last modified
    
    error_message = models.TextField(_("Error Message (if any)"), blank=True, null=True)
    
    # Celery Task ID (if dispatched via Celery)
    task_id = models.CharField(_("Celery Task ID"), max_length=255, blank=True, null=True, db_index=True, unique=True) # Make unique if one log per task run

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Communication Log")
        verbose_name_plural = _("Communication Logs")
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self):
        recipient_info = self.recipient_email or self.recipient_phone or _("N/A")
        return f"{self.get_message_type_display()} to {recipient_info} - Status: {self.get_status_display()}"

    def save(self, *args, **kwargs):
        # Automatically update sent_at when status becomes SENT, if not already set
        if self.status == self.Status.SENT and not self.sent_at:
            self.sent_at = timezone.now()
        # Clear error_message if status is SENT
        if self.status == self.Status.SENT:
            self.error_message = None
        super().save(*args, **kwargs)
        
        
        
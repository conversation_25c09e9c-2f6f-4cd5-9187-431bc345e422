# Generated by Django 5.1.9 on 2025-06-20 06:09

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='subscriptionplan',
            options={'ordering': ['display_order', 'price_monthly', 'name'], 'verbose_name': 'Subscription Plan', 'verbose_name_plural': 'Subscription Plans'},
        ),
        migrations.AddField(
            model_name='subscription',
            name='ended_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when subscription truly ended (e.g., after trial, or after cancelled period end).', null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='notes',
            field=models.TextField(blank=True, help_text='Internal notes about this subscription.'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='price_at_subscription',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Actual price this school subscribed at for the current cycle.', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='trial_start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='display_order',
            field=models.PositiveIntegerField(default=0, help_text='Order in which plans are displayed on pricing page.'),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly version of the name, auto-generated if blank.', max_length=120, unique=True),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='trial_period_days',
            field=models.PositiveIntegerField(default=0, help_text='Number of trial days offered for this plan (0 for no trial).'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='cancel_at_period_end',
            field=models.BooleanField(default=False, help_text='If true, subscription will be cancelled at period end, not renewed.'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='cancelled_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when cancellation was processed.', null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='current_period_end',
            field=models.DateTimeField(blank=True, db_index=True, help_text="End of current paid billing period (or trial). Access might be restricted after this if status isn't Active/Trialing.", null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='current_period_start',
            field=models.DateTimeField(blank=True, help_text='Start of the current paid billing period.', null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='pg_customer_id',
            field=models.CharField(blank=True, db_index=True, help_text='Customer ID from the payment gateway.', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='pg_subscription_id',
            field=models.CharField(blank=True, db_index=True, help_text='Subscription ID from the payment gateway.', max_length=100, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions_to_plan', to='subscriptions.subscriptionplan'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending Payment/Setup'), ('TRIALING', 'Trialing'), ('ACTIVE', 'Active'), ('PAST_DUE', 'Past Due'), ('CANCELLED', 'Cancelled'), ('UNPAID', 'Unpaid'), ('INCOMPLETE', 'Incomplete'), ('INCOMPLETE_EXPIRED', 'Incomplete Expired'), ('ENDED', 'Ended'), ('SUSPENDED', 'Suspended')], db_index=True, default='PENDING', max_length=25),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='features',
            field=models.ManyToManyField(blank=True, related_name='plans_offering_feature', to='subscriptions.feature'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Is this plan currently offered for new subscriptions?'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='is_public',
            field=models.BooleanField(default=True, help_text='Is this plan visible on the public pricing page?'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='max_staff',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of active staff users allowed. Null for unlimited.', null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='max_students',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of active students allowed. Null for unlimited.', null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='pg_price_id_annually',
            field=models.CharField(blank=True, help_text='Price ID from payment gateway for annual billing.', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='pg_price_id_monthly',
            field=models.CharField(blank=True, help_text='Price ID from payment gateway for monthly billing.', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='price_annually',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Price per year if billed annually (often discounted).', max_digits=10),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='price_monthly',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Price per month if billed monthly.', max_digits=10),
        ),
    ]

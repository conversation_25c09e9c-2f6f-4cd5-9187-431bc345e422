# apps/fees/tasks.py

# apps/fees/tasks.py
from celery import shared_task
from django.db import transaction, IntegrityError, models
from django.shortcuts import get_object_or_404
import time # For potential debug sleep


# apps/fees/tasks.py
from celery import shared_task
from django_tenants.utils import tenant_context
from apps.tenants.models import School # Import School (Tenant) model

# Import other models needed for invoice generation
from .models import FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail
from django.utils import timezone
from decimal import Decimal
from .utils import calculate_due_date # Assuming this helper exists


# Import models needed by the task
# Use full paths if necessary, though relative should work if structure is standard
from .models import FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail, Term, AcademicYear
# Import helpers
from .utils import calculate_due_date 


# Import models carefully to avoid circular imports if possible
# It's often safer to pass IDs and query within the task
from .models import AcademicYear, FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail, FeeStructureDetail
from apps.students.models import Student # Import student model


# Import models (use full path if needed, but relative should work if apps registered)
from .models import FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail, StudentConcession
# Import helpers
from .utils import get_billing_periods, calculate_due_date


# Import necessary models (use full paths if needed, but direct should work in task)
from apps.tenants.models import School # Need School to get tenant object
from .models import (
    FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail,
    StudentConcession, Concession, AcademicYear, Term, FeeHead
)

# Import context manager and helper
from django_tenants.utils import tenant_context


from django.db import transaction, IntegrityError
from django.db.models import Sum, F, DecimalField, Q # Import Q for filtering


# Import necessary models (use full paths)
from apps.fees.models import (
    FeeStructure, StudentFeeAllocation, Invoice, InvoiceDetail,
    Concession, StudentConcession, FeeHead, AcademicYear, Term # Assume StudentConcession exists
)
# Import helpers
from apps.fees.utils import calculate_due_date # If this helper exists


@shared_task(bind=True) # bind=True allows accessing task instance (self)
def generate_invoices_for_structure_task(self, academic_year_id, fee_structure_id, period_description, issue_date_str, due_date_str, requested_by_user_id=None):
    """
    Celery task to generate invoices in the background.
    Receives IDs and serializable data.
    """
    task_id = self.request.id
    print(f"Task {task_id}: Starting invoice generation for Structure ID {fee_structure_id}, Year ID {academic_year_id}, Period '{period_description}'")

    processed_count = 0
    skipped_count = 0
    error_count = 0
    total_students = 0

    try:
        # Fetch objects using IDs passed to the task
        academic_year = AcademicYear.objects.get(pk=academic_year_id)
        fee_structure = FeeStructure.objects.select_related('academic_year').get(pk=fee_structure_id)

        # Get structure details once
        structure_details = list(fee_structure.details.select_related('fee_head').all())
        if not structure_details:
             print(f"Task {task_id}: ERROR - Fee Structure '{fee_structure.name}' has no details defined. Aborting.")
             # Update state to failure
             self.update_state(state='FAILURE', meta={'exc_type': 'ValueError', 'exc_message': 'Fee structure has no details.'})
             return {'status': 'Failed', 'reason': 'No structure details'}


        # Find relevant student allocations
        allocations = StudentFeeAllocation.objects.filter(
            academic_year=academic_year,
            fee_structure=fee_structure
        ).select_related('student') # Select student

        total_students = allocations.count()
        print(f"Task {task_id}: Found {total_students} students allocated.")
        # Optional: Update initial progress state
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': total_students, 'skipped': 0})


        # --- Loop through allocations and generate invoices ---
        # Use smaller transactions or handle errors per student?
        # For now, one big transaction. Consider breaking down for large sets.
        with transaction.atomic():
            for i, allocation in enumerate(allocations):
                student = allocation.student
                current_progress = i + 1

                try:
                    # --- Duplicate Check ---
                    existing_invoice = Invoice.objects.filter(
                        student=student,
                        academic_year=academic_year,
                        period_description__iexact=period_description
                    ).first()

                    if existing_invoice:
                        skipped_count += 1
                        print(f"Task {task_id}: Skipped duplicate for {student}, Period '{period_description}'")
                        # Optional: Update progress state
                        self.update_state(state='PROGRESS', meta={'current': current_progress, 'total': total_students, 'skipped': skipped_count})
                        continue

                    # --- Create Invoice ---
                    new_invoice = Invoice(
                        student=student,
                        academic_year=academic_year,
                        issue_date=issue_date_str, # Use date strings passed
                        due_date=due_date_str,     # Use date strings passed
                        period_description=period_description,
                        status='DUE',
                        total_amount=0
                    )
                    new_invoice.save() # Save to get PK and generate number

                    # --- Create Invoice Details ---
                    invoice_total = Decimal(0.00)
                    for detail_item in structure_details:
                        # TODO: Add concession logic here!
                        line_amount = detail_item.amount
                        invoice_detail = InvoiceDetail(
                            invoice=new_invoice,
                            fee_head=detail_item.fee_head,
                            description=f"{detail_item.fee_head.name} ({period_description})",
                            amount=line_amount
                        )
                        invoice_detail.save()
                        invoice_total += line_amount

                    # Update Invoice Total and Save Again
                    new_invoice.total_amount = invoice_total
                    new_invoice.save()

                    processed_count += 1
                    print(f"Task {task_id}: Created Invoice {new_invoice.invoice_number or new_invoice.pk} for {student}")

                    # Optional: Update progress state more frequently
                    if current_progress % 10 == 0: # Update every 10 students
                         self.update_state(state='PROGRESS', meta={'current': current_progress, 'total': total_students, 'skipped': skipped_count})
                    # time.sleep(0.1) # Add small sleep for debugging long tasks

                except Exception as student_error:
                    # Log error for specific student but continue task if possible?
                    print(f"Task {task_id}: ERROR processing student {student.pk} ({student}): {student_error}")
                    error_count += 1
                    # Decide whether to abort entire task or just skip student
                    # For now, continue

        print(f"Task {task_id}: Generation finished. Processed: {processed_count}, Skipped: {skipped_count}, Errors: {error_count}")
        return {'status': 'Completed', 'processed': processed_count, 'skipped': skipped_count, 'errors': error_count}

    except AcademicYear.DoesNotExist:
         print(f"Task {task_id}: ERROR - AcademicYear ID {academic_year_id} not found.")
         self.update_state(state='FAILURE', meta={'exc_type': 'DoesNotExist', 'exc_message': 'AcademicYear not found.'})
         return {'status': 'Failed', 'reason': 'AcademicYear not found'}
    except FeeStructure.DoesNotExist:
         print(f"Task {task_id}: ERROR - FeeStructure ID {fee_structure_id} not found.")
         self.update_state(state='FAILURE', meta={'exc_type': 'DoesNotExist', 'exc_message': 'FeeStructure not found.'})
         return {'status': 'Failed', 'reason': 'FeeStructure not found'}
    except Exception as e:
        # Catch unexpected errors during setup or loop
        print(f"Task {task_id}: UNEXPECTED ERROR - {e}")
        import traceback
        traceback.print_exc()
        # Update state to failure
        self.update_state(state='FAILURE', meta={'exc_type': type(e).__name__, 'exc_message': str(e)})
        return {'status': 'Failed', 'reason': str(e)}
    


@shared_task(bind=True, max_retries=2, default_retry_delay=180)
def generate_invoices_task(self, fee_structure_pk, tenant_id):
    """
    Celery task to generate invoices for a fee structure within a specific tenant's context.
    Handles Term linking, Concessions (as line items), and Duplicate Checking.
    """
    task_id = self.request.id or 'NO_TASK_ID'
    print(f"TASK {task_id}: Starting generation - Tenant ID: {tenant_id}, Structure PK: {fee_structure_pk}")

    try:
        tenant = School.objects.get(pk=tenant_id)
    except School.DoesNotExist:
        print(f"TASK {task_id}: CRITICAL ERROR - Tenant {tenant_id} not found. Aborting task.")
        self.update_state(state='FAILURE', meta={'exc_type': 'TenantNotFound', 'exc_message': f'Tenant {tenant_id} not found'})
        raise ValueError(f"Tenant {tenant_id} not found.")
    except Exception as e:
        print(f"TASK {task_id}: ERROR retrieving tenant {tenant_id}: {e}. Retrying...")
        raise self.retry(exc=e, countdown=60)

    # --- Execute database logic within the correct tenant schema ---
    invoices_created_count = 0
    students_skipped_count = 0
    try:
        with tenant_context(tenant):
            print(f"TASK {task_id}: Switched context to schema '{tenant.schema_name}'")

            # --- Fetch data within tenant context ---
            try:
                fee_structure = FeeStructure.objects.select_related(
                    'academic_year', 'term'
                ).prefetch_related('items__fee_head').get(pk=fee_structure_pk) # Prefetch items/heads
            except FeeStructure.DoesNotExist:
                 print(f"TASK {task_id}: ERROR - FeeStructure PK {fee_structure_pk} not found in schema '{tenant.schema_name}'.")
                 return f"Failed: Fee Structure {fee_structure_pk} not found."

            academic_year = fee_structure.academic_year
            target_term = fee_structure.term
            structure_items = list(fee_structure.items.all()) # Already prefetched

            if not structure_items:
                 print(f"TASK {task_id}: ERROR - Structure has no items.")
                 return f"Failed: Structure {fee_structure_pk} has no items."

            # 1. Determine Billing Period Identifier and Dates
            if target_term:
                period_start, period_end = target_term.start_date, target_term.end_date
                # Use a combination that's likely unique for duplicate checking
                period_identifier = f"FY{academic_year.pk}-T{target_term.pk}-FS{fee_structure.pk}"
                period_display_name = f"{target_term.name} ({academic_year.name})"
            else:
                period_start, period_end = academic_year.start_date, academic_year.end_date
                period_identifier = f"FY{academic_year.pk}-FS{fee_structure.pk}" # For full year structures
                period_display_name = f"{academic_year.name} (Full Year)"
            print(f"TASK {task_id}: Determined Period ID: '{period_identifier}', Display: '{period_display_name}'")

            # 2. Get Allocations for ACTIVE students
            allocations = StudentFeeAllocation.objects.filter(
                fee_structure=fee_structure, academic_year=academic_year, student__is_active=True
            ).select_related('student') # Fetch student data

            if not allocations.exists():
                print(f"TASK {task_id}: No active students allocated. Task finished.")
                return "Success: No students needed invoices for this structure/period."

            # 3. Duplicate Check based on allocation and period identifier
            existing_invoice_alloc_ids = set(Invoice.objects.filter(
                allocation__in=allocations,
                period_description=period_identifier, # Use the unique identifier
            ).exclude(status='CANCELLED').values_list('allocation_id', flat=True))
            students_skipped_count = len(existing_invoice_alloc_ids)
            print(f"TASK {task_id}: Found {students_skipped_count} existing invoices for period ID '{period_identifier}'.")

            # 4. Prepare Data for Students Needing Invoice
            students_needing_invoice = allocations.exclude(id__in=existing_invoice_alloc_ids)
            if not students_needing_invoice:
                 print(f"TASK {task_id}: No new invoices to generate after duplicate check.")
                 return f"Completed: No new invoices generated. Skipped {students_skipped_count}."

            # Fetch Concessions for relevant students (if StudentConcession model exists)
            # This assumes a StudentConcession model like:
            # class StudentConcession(models.Model):
            #    student = models.ForeignKey('students.Student', ...)
            #    concession = models.ForeignKey(Concession, ...)
            #    academic_year = models.ForeignKey(AcademicYear, ...)
            student_ids_needing_invoice = students_needing_invoice.values_list('student_id', flat=True)
            concessions_by_student = {}
            
            # --- Fetch Discount Fee Head (MUST EXIST) ---
            try:
                discount_fee_head = FeeHead.objects.get(name__iexact='Discount Adjustment') # Or your chosen name
                print(f"TASK {task_id}: Found Discount Fee Head: {discount_fee_head.pk}")
            except FeeHead.DoesNotExist:
                print(f"TASK CRITICAL ERROR: FeeHead 'Discount Adjustment' not found in schema {tenant.schema_name}. Cannot apply concessions as line items.")
                raise ValueError("Required 'Discount Adjustment' FeeHead is missing.")

            try:
                active_concessions = StudentConcession.objects.filter(
                    student_id__in=student_ids_needing_invoice,
                    academic_year=academic_year,
                    concession__is_active=True # Only active concession types
                ).select_related('concession') # Fetch related concession details

                for sc in active_concessions:
                    if sc.student_id not in concessions_by_student: concessions_by_student[sc.student_id] = []
                    concessions_by_student[sc.student_id].append(sc.concession) # Store Concession objects
            except NameError: # If StudentConcession model doesn't exist yet
                 print(f"TASK INFO: StudentConcession model not found or implemented. Skipping concession calculation.")
                 active_concessions = [] # Empty list


            # 5. Prepare Invoice and Detail Objects
            invoices_to_create = []
            details_to_create = [] # Flat list

            for allocation in students_needing_invoice:
                student = allocation.student
                details_for_this_invoice = [] # Details specific to this student/invoice
                current_invoice_subtotal = Decimal('0.00')

                # Add structure items
                for item in structure_items:
                    details_for_this_invoice.append(
                        InvoiceDetail(
                            fee_head=item.fee_head, # Assign FeeHead from structure item
                            description=f"{item.fee_head.name} ({period_display_name})",
                            amount=item.amount
                        )
                    )
                    current_invoice_subtotal += item.amount

                # Calculate and add concession items
                total_concession_amount = Decimal('0.00')
                student_concessions = concessions_by_student.get(student.id, [])
                for concession in student_concessions:
                    discount = concession.calculate_discount(current_invoice_subtotal) # Assumes calculate_discount exists on Concession model
                    if discount > 0:
                        total_concession_amount += discount
                        # --- Create Concession Detail - Assign the Discount Fee Head ---
                        details_for_this_invoice.append(
                            InvoiceDetail(
                                fee_head=discount_fee_head, # <<< ASSIGN THE FETCHED DISCOUNT FEE HEAD HERE
                                description=f"Concession: {concession.name}",
                                amount=-discount # Negative amount
                            )
                        )

                # Calculate final total and prepare Invoice object
                final_invoice_total = max(current_invoice_subtotal - total_concession_amount, Decimal('0.00'))
                initial_status = 'DUE' if final_invoice_total > 0 else 'PAID'
                due_date = calculate_due_date(period_start, 'ONE_TIME') # Use appropriate frequency later

                invoice_obj = Invoice(
                    student=student, academic_year=academic_year, allocation=allocation,
                    issue_date=timezone.now().date(), due_date=due_date,
                    period_description=period_identifier,
                    period_start_date=period_start, period_end_date=period_end,
                    total_amount=final_invoice_total,
                    amount_paid=Decimal('0.00'), status=initial_status
                )
                # Append tuple of prepared invoice and its prepared details
                invoices_to_create.append((invoice_obj, details_for_this_invoice))

            # 6. Perform Bulk Creation in Transaction
            if invoices_to_create:
                with transaction.atomic():
                    created_invoice_objects = Invoice.objects.bulk_create([inv for inv, details in invoices_to_create])
                    print(f"TASK {task_id}: Bulk created {len(created_invoice_objects)} invoices.")

                    created_invoice_map = {inv.allocation_id: inv for inv in created_invoice_objects}
                    bulk_detail_list = [] # Reset/use new list for details

                    for inv_tpl, detail_list in invoices_to_create:
                        created_inv = created_invoice_map.get(inv_tpl.allocation_id)
                        if created_inv:
                            for detail_obj in detail_list:
                                detail_obj.invoice = created_inv # Set FK *after* invoice is saved
                                bulk_detail_list.append(detail_obj)
                        else:
                            print(f"TASK WARNING: Could not map created invoice back for allocation {inv_tpl.allocation_id}")

                    if bulk_detail_list:
                        InvoiceDetail.objects.bulk_create(bulk_detail_list)
                        print(f"TASK {task_id}: Bulk created {len(bulk_detail_list)} details.")

                    # Optional: Verify totals after creation if needed (less critical now)
                    # for inv in created_invoice_objects: inv.save() # Re-saving recalculates status

            # --- Task Completion ---
            result_msg = f"Completed generation for '{period_display_name}'. Created: {invoices_created_count}, Skipped: {students_skipped_count}."
            print(f"TASK {task_id}: {result_msg}")
            return result_msg

    except Exception as e:
         # Catch errors happening *outside* or *within* the tenant_context
        error_msg = f"FAILED - Error during task execution for Tenant {tenant_id}, Structure {fee_structure_pk}: {e}"
        print(f"TASK {task_id}: {error_msg}")
        self.update_state(state='FAILURE', meta={'exc_type': type(e).__name__, 'exc_message': str(e), 'structure_pk': fee_structure_pk, 'tenant_id': tenant_id})
        raise self.retry(exc=e, countdown=120, max_retries=2) # Retry on generic errors


# Helper to get period (copied or imported from utils) - Task needs self-contained logic
# Or ensure utils.py is importable by celery worker
def get_period_for_structure(fee_structure: FeeStructure):
    """ Determines the period details for a given structure """
    academic_year = fee_structure.academic_year
    target_term = fee_structure.term
    if target_term:
        period_start = target_term.start_date
        period_end = target_term.end_date
        period_desc = f"{fee_structure.name} - {target_term.name} ({academic_year.name})"
    else:
        period_start = academic_year.start_date
        period_end = academic_year.end_date
        period_desc = f"{fee_structure.name} ({academic_year.name})"
    return period_start, period_end, period_desc

@shared_task(bind=True, max_retries=2, default_retry_delay=180) # Allow retry
def generate_invoices_task(self, fee_structure_pk, tenant_id): # <<< Added tenant_id parameter
    """
    Celery task to generate invoices for a specific FeeStructure within a specific tenant.
    """
    print(f"TASK: Received job - Generate Invoices for Structure PK={fee_structure_pk}, Tenant ID={tenant_id}")

    try:
        # 1. Get the Tenant object from the public schema
        tenant = School.objects.get(pk=tenant_id)
    except School.DoesNotExist:
        print(f"TASK ERROR: Tenant with ID {tenant_id} not found. Aborting task.")
        # No retry if tenant doesn't exist
        return f"Tenant ID {tenant_id} not found."
    except Exception as e:
        print(f"TASK ERROR: Could not retrieve tenant {tenant_id}: {e}. Retrying...")
        raise self.retry(exc=e, countdown=60) # Retry if tenant fetch fails unexpectedly

    # 2. Execute generation logic within the tenant's context
    invoices_created_count = 0
    students_skipped_count = 0
    try:
        with tenant_context(tenant):
            print(f"TASK: Switched to schema '{tenant.schema_name}'")
            # --- Paste the Invoice Generation Logic HERE ---
            # (This is the logic previously inside the generate_invoices_for_structure view)

            fee_structure = get_object_or_404(
                FeeStructure.objects.select_related('academic_year', 'term'),
                pk=fee_structure_pk
            )
            academic_year = fee_structure.academic_year
            target_term = fee_structure.term

            # Determine billing period
            if target_term:
                period_start = target_term.start_date
                period_end = target_term.end_date
                period_desc = f"{fee_structure.name} - {target_term.name} ({academic_year.name})"
            else:
                period_start = academic_year.start_date
                period_end = academic_year.end_date
                period_desc = f"{fee_structure.name} ({academic_year.name})"

            # Find allocations
            allocations = StudentFeeAllocation.objects.filter(
                fee_structure=fee_structure,
                academic_year=academic_year
            ).select_related('student')

            if not allocations.exists():
                print(f"TASK INFO: No students allocated '{fee_structure.name}' for {academic_year.name}. Task finished.")
                return "No students allocated."

            structure_items = list(fee_structure.items.select_related('fee_head').all())
            structure_total = fee_structure.get_total_amount()

            if not structure_items or structure_total <= 0:
                 print(f"TASK ERROR: Structure '{fee_structure.name}' has no items or total is zero.")
                 return f"Structure {fee_structure_pk} has no items/zero total."

            # Duplicate Check
            existing_invoices = Invoice.objects.filter(
                allocation__in=allocations,
                period_description=period_desc,
            ).exclude(status='CANCELLED').values_list('allocation_id', flat=True)

            bulk_invoice_list = []
            bulk_detail_list = []
            allocation_student_map = {alloc.id: alloc.student for alloc in allocations}

            for alloc_id, student in allocation_student_map.items():
                if alloc_id in existing_invoices:
                    students_skipped_count += 1
                    continue

                due_date = calculate_due_date(period_start, fee_structure.frequency if hasattr(fee_structure, 'frequency') else 'ONE_TIME')

                invoice_obj = Invoice(
                    student=student, academic_year=academic_year, allocation_id=alloc_id,
                    issue_date=timezone.now().date(), due_date=due_date,
                    period_description=period_desc, period_start_date=period_start, period_end_date=period_end,
                    total_amount=structure_total, amount_paid=Decimal('0.00'), status='DUE'
                )
                bulk_invoice_list.append(invoice_obj)

            # Bulk create (still inside transaction atomic block)
            if bulk_invoice_list:
                created_invoices = Invoice.objects.bulk_create(bulk_invoice_list)
                invoices_created_count = len(created_invoices)
                print(f"TASK: Bulk created {invoices_created_count} invoices in schema '{tenant.schema_name}'")

                invoice_map_by_alloc_id = {inv.allocation_id: inv for inv in created_invoices}

                for alloc_id, invoice in invoice_map_by_alloc_id.items():
                     for item in structure_items:
                          bulk_detail_list.append(
                              InvoiceDetail(
                                  invoice=invoice, fee_head=item.fee_head,
                                  description=f"{item.fee_head.name} ({period_desc})",
                                  amount=item.amount
                              )
                          )

                if bulk_detail_list:
                    InvoiceDetail.objects.bulk_create(bulk_detail_list)
                    print(f"TASK: Bulk created {len(bulk_detail_list)} invoice details in schema '{tenant.schema_name}'")
            # --- End Invoice Generation Logic ---

        # End of tenant_context block
        print(f"TASK INFO: Finished processing for {tenant.schema_name}. Created: {invoices_created_count}, Skipped: {students_skipped_count}")
        return f"Processed Structure {structure_pk} for {tenant.schema_name}. Created: {invoices_created_count}, Skipped: {students_skipped_count}"

    except Exception as e:
        # Catch errors happening inside the tenant context
        print(f"TASK ERROR: Exception within tenant_context for {tenant.schema_name}, Structure {structure_pk}: {e}")
        # Retry the task based on decorator settings
        raise self.retry(exc=e, countdown=120, max_retries=2) # Example retry with longer delay
    



{# D:\school_fees_saas_v2\templates\schools\staff_login.html #}
{% extends "tenant_base.html" %}

{% load static %}

{% block title %}{{ view_title|default:"Staff Portal Login" }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
        }
        .staff-login-card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .staff-login-card .card-header {
            border-top-left-radius: 1.5rem;
            border-top-right-radius: 1.5rem;
            padding: 3rem 2rem 2rem;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
        }
        .form-floating > .form-control {
            border-radius: 1rem !important;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            height: 3.5rem;
            padding: 1rem 0.75rem 0.25rem 0.75rem;
        }
        .form-floating > .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            transform-origin: 0 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        }
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }
        .btn-success {
            border-radius: 0.75rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.75rem 2rem rgba(40, 167, 69, 0.4);
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }
        .staff-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            padding: 1.5rem;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .forgot-password-link {
            color: #28a745;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .forgot-password-link:hover {
            color: #218838;
            text-decoration: underline;
        }
        .field-icon {
            color: #28a745;
            margin-right: 0.5rem;
        }
        .form-floating.focused > .form-control {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > .form-control::placeholder {
            color: transparent;
        }
        .form-floating > .form-control:focus::placeholder {
            color: transparent;
        }
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
            padding: 0.5rem;
            font-size: 0.75rem;
            color: white;
        }
        @media (max-width: 768px) {
            .staff-login-card .card-header {
                padding: 2rem 1rem 1.5rem;
            }
            .staff-login-card .card-body {
                padding: 1.5rem 1rem;
            }
            .staff-icon {
                padding: 1rem;
                margin-bottom: 0.5rem;
            }
            .security-badge {
                position: static;
                display: inline-block;
                margin-top: 1rem;
            }
        }
    </style>
{% endblock %}

{% block content %}

<div class="container mt-5 mb-5"> {# Added mb-5 for bottom margin #}
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-5 col-xl-4"> {# Slightly adjusted column for better centering on larger screens #}
            <div class="card shadow-lg login-card"> {# Added shadow-lg #}
                <div class="card-header bg-success text-white text-center"> {# Centered header text #}
                    <i class="bi bi-person-workspace" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i> {# Added an icon #}
                    <h4 class="mb-0">{{ view_title }}</h4>
                    {% if tenant_name and tenant_name != "Unknown Tenant" %}
                        <p class="mb-0 mt-1 opacity-75"><small>School: {{ tenant_name }}</small></p> {# Smaller, slightly muted school name #}
                    {% endif %}
                </div>
                
                <div class="card-body p-4 p-md-5"> {# Increased padding on larger screens #}
                    {% if messages %}
                        {% for message in messages %}
                            {# Use standard message display partial if you have one #}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2"> {# Adjusted padding #}
                            {% for error in form.non_field_errors %}
                                {{ error }}{% if not forloop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Username/Email Field with Icon (Optional) #}
                        <div class="mb-3 form-floating"> {# Using form-floating for a modern look #}
                            {{ form.username }} {# Widget attrs set in form (placeholder, class) #}
                            <label for="{{ form.username.id_for_label }}"><i class="bi bi-envelope-fill me-2"></i>{{ form.username.label }}</label>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block mt-1"> {# Ensure error shows below #}
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Password Field with Icon (Optional) #}
                        <div class="mb-4 form-floating"> {# Using form-floating #}
                            {{ form.password }} {# Widget attrs set in form (placeholder, class) #}
                            <label for="{{ form.password.id_for_label }}"><i class="bi bi-key-fill me-2"></i>{{ form.password.label }}</label>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <input type="hidden" name="next" value="{{ request.GET.next }}">
                        <button type="submit" class="btn btn-success w-100 btn-lg mt-3">Login as Staff</button>
                    </form>
                    <hr class="my-4">
                    <div class="text-center">
                        <p class="mb-0"><a href="#" class="text-muted"><small>Forgot password?</small></a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

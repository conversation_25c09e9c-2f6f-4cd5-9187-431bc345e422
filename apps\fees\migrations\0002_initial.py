# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fees', '0001_initial'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='feestructure',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='fee_structures', to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='feestructure',
            name='applicable_classes',
            field=models.ManyToManyField(blank=True, related_name='fee_structures', to='schools.schoolclass'),
        ),
        migrations.AddField(
            model_name='feestructure',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='fee_structures', to='schools.term'),
        ),
        migrations.AddField(
            model_name='feestructureitem',
            name='fee_head',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='structure_items', to='fees.feehead'),
        ),
        migrations.AddField(
            model_name='feestructureitem',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invoices', to='schools.staffuser'),
        ),
    ]

-- Fix migration dependency issue between payments.0003_initial and fees.0003_initial
-- This script updates the applied timestamp of fees.0003_initial to be earlier than payments.0003_initial

-- For alpha schema
SET search_path TO alpha;

-- Check current migration order
SELECT app, name, applied
FROM django_migrations
WHERE (app = 'fees' AND name = '0003_initial') 
   OR (app = 'payments' AND name = '0003_initial')
ORDER BY applied;

-- Update fees.0003_initial to have an earlier timestamp
UPDATE django_migrations 
SET applied = (
    SELECT applied - INTERVAL '1 minute'
    FROM django_migrations 
    WHERE app = 'payments' AND name = '0003_initial'
)
WHERE app = 'fees' AND name = '0003_initial';

-- Verify the fix
SELECT app, name, applied
FROM django_migrations
WHERE (app = 'fees' AND name = '0003_initial') 
   OR (app = 'payments' AND name = '0003_initial')
ORDER BY applied;

-- If you have other tenant schemas, repeat for each one:
-- SET search_path TO other_schema_name;
-- UPDATE django_migrations ... (same as above)

# D:\school_fees_saas_v2\apps\platform_management\models.py
from django.db import models
from django.conf import settings # To use settings.AUTH_USER_MODEL
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)

# PlatformAnnouncement model (as discussed for system-wide announcements)
class PlatformAnnouncement(models.Model):
    title = models.CharField(_("Title"), max_length=255)
    content = models.TextField(_("Content")) 

    author = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL,
        null=True,
        blank=False, 
        related_name='authored_platform_announcements',
        verbose_name=_("Platform Author")
    )
    
    publish_date = models.DateTimeField(
        _("Publish Date"), 
        default=timezone.now,
        help_text=_("Announcement becomes visible from this date/time.")
    )
    expiry_date = models.DateTimeField(
        _("Expiry Date"), 
        null=True, 
        blank=True,
        help_text=_("Optional: Announcement will be hidden/archived after this date/time.")
    )
    
    is_published = models.BooleanField(
        _("Is Published"), 
        default=True,
        db_index=True,
        help_text=_("Uncheck to keep as a draft.")
    )
    
    SEVERITY_CHOICES = [
        ('INFO', _('Informational')),
        ('SUCCESS', _('Success')),
        ('WARNING', _('Warning')),
        ('DANGER', _('Critical/Maintenance')), 
    ]
    severity = models.CharField(
        max_length=10,
        choices=SEVERITY_CHOICES,
        default='INFO',
        verbose_name=_("Severity Level")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-publish_date'] 
        verbose_name = _("Platform Announcement")
        verbose_name_plural = _("Platform Announcements")

    def __str__(self):
        return self.title

    @property
    def is_currently_visible(self):
        now = timezone.now()
        if not self.is_published: return False
        if self.publish_date > now: return False
        if self.expiry_date and self.expiry_date < now: return False
        return True




class PlatformSetting(models.Model):
    setting_name = models.CharField(_("Setting Name"), max_length=100, unique=True)
    setting_value = models.TextField(_("Setting Value"))
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _("Platform Setting")
        verbose_name_plural = _("Platform Settings")
        ordering = ['setting_name']
    
    def __str__(self):
        return self.setting_name




class SystemNotification(models.Model):
    NOTIFICATION_TYPES = [
        ('INFO', _('Information')),
        ('WARNING', _('Warning')),
        ('ERROR', _('Error')), # Consider DANGER if mapping to Bootstrap alerts
        ('SUCCESS', _('Success')),
    ]
    
    title = models.CharField(_("Title"), max_length=200)
    message = models.TextField(_("Message"))
    notification_type = models.CharField(
        _("Notification Type"),
        max_length=20, 
        choices=NOTIFICATION_TYPES, 
        default='INFO'
    )
    is_active = models.BooleanField(_("Is Active"), default=True, db_index=True)
    
    target_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL, 
        blank=True, 
        help_text=_("Select specific platform users. Leave empty for a general platform announcement."),
        related_name="platform_notifications_received"
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_system_notifications'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    publish_date = models.DateTimeField(
        _("Publish Date"),
        default=timezone.now,
        help_text=_("Notification becomes visible from this date/time.")
    )
    expires_at = models.DateTimeField(
        _("Expires At"),
        null=True, 
        blank=True,
        help_text=_("Notification will be hidden after this date/time.")
    )
    
    class Meta:
        ordering = ['-publish_date', '-created_at']
        verbose_name = _("System Notification")
        verbose_name_plural = _("System Notifications")
    
    def __str__(self):
        return self.title
    
    @property
    def is_currently_visible(self):
        now = timezone.now()
        if not self.is_active: return False
        if self.publish_date > now: return False
        if self.expires_at and self.expires_at < now: return False
        return True

class AuditLog(models.Model):
    ACTION_TYPES = [
        ('CREATE', _('Create')),
        ('UPDATE', _('Update')),
        ('DELETE', _('Delete')),
        ('LOGIN', _('Login')),
        ('LOGOUT', _('Logout')),
        ('PAYMENT_GATEWAY', _('Payment Gateway Action')),
        ('CONFIG_CHANGE', _('Configuration Change')),
        ('SECURITY', _('Security Event')),
        ('SYSTEM', _('System Action')),
        ('MAINTENANCE_MODE_TOGGLE', _('Maintenance Mode Toggle')),
        ('PLATFORM_SETTING_CHANGE', _('Platform Setting Change')),
        ('TENANT_CREATE', _('Tenant Created')),
        ('TENANT_STATUS_CHANGE', _('Tenant Status Change')),
        ('OTHER', _('Other Platform Action')),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name="audit_log_entries"
    )
    action_type = models.CharField(_("Action Type"), max_length=30, choices=ACTION_TYPES, db_index=True)
    model_name = models.CharField(_("Model Name"), max_length=100, blank=True, null=True)
    object_pk = models.CharField(_("Object PK"), max_length=100, blank=True, null=True)
    description = models.TextField(_("Description"))
    details = models.JSONField(_("Additional Details"), null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=500, blank=True)
    timestamp = models.DateTimeField(_("Timestamp"), auto_now_add=True, db_index=True)
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = _("Platform Audit Log")
        verbose_name_plural = _("Platform Audit Logs")
    
    def __str__(self):
        user_str = str(self.user) if self.user else "System"
        return f"{self.timestamp:%Y-%m-%d %H:%M} - {user_str} - {self.get_action_type_display()} - {self.description[:50]}"

class MaintenanceMode(models.Model):
    site_name_override = models.CharField(max_length=100, blank=True, null=True, help_text=_("Name to display during maintenance mode (e.g., 'Our Platform')"))
    is_enabled = models.BooleanField(_("Maintenance Mode Enabled"), default=False, db_index=True)
    title = models.CharField(max_length=200, default=_("System Maintenance"), blank=True)
    message = models.TextField(
        _("Maintenance Message"),
        default=_("The platform is currently under maintenance. We expect to be back shortly. Thank you for your patience.")
    )
    allowed_ips = models.TextField(
        _("Allowed IPs"),
        blank=True, 
        help_text=_("Comma-separated or newline-separated IP addresses that can bypass maintenance mode.")
    )
    bypass_key = models.CharField(
        _("Bypass Key"),
        max_length=50,
        blank=True, null=True,
        help_text=_("A secret key in URL query (e.g., ?maintenance_bypass=secretkey) to bypass mode.")
    )
    bypass_staff = models.BooleanField(default=True, help_text=_("Allow platform superusers/staff to bypass maintenance mode."))
    
    enabled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='maintenance_mode_activations'
    )
    enabled_at = models.DateTimeField(_("Enabled At"), null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Maintenance Mode Setting")
        verbose_name_plural = _("Maintenance Mode Settings")
    
    def __str__(self):
        status = _("Enabled") if self.is_enabled else _("Disabled")
        return f"Platform Maintenance Mode: {status}"

    def save(self, *args, **kwargs):
        self.pk = 1 # Enforce singleton
        if self.is_enabled and not self.enabled_at:
            self.enabled_at = timezone.now()
        elif not self.is_enabled:
            self.enabled_at = None
        super().save(*args, **kwargs)

    @classmethod
    def get_status(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
    
    





# D:\school_fees_saas_v2\apps\announcements\models.py
from django.db import models
from django.utils import timezone
from django.urls import reverse
from django.conf import settings

class PlatformAnnouncement(models.Model):
    title = models.CharField(max_length=255)
    content = models.TextField()
    is_published = models.BooleanField(default=False, help_text="Check to make this announcement visible.")
    publish_date = models.DateTimeField(default=timezone.now, help_text="Date and time when the announcement should become visible.")
    expiry_date = models.DateTimeField(null=True, blank=True, help_text="Optional: Date and time when the announcement should no longer be visible.")
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, blank=True,
        related_name='platform_announcements_authored',
        limit_choices_to={'is_staff': True}
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-publish_date', '-created_at']
        verbose_name = "Platform Announcement"
        verbose_name_plural = "Platform Announcements"

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('announcements:platform_announcement_detail', kwargs={'pk': self.pk}) # Make sure this URL name matches your urls.py

    @property
    def is_currently_visible(self):
        now = timezone.now()
        if not self.is_published or self.publish_date > now:
            return False
        if self.expiry_date and self.expiry_date < now:
            return False
        return True




   

{% extends "tenant_base.html" %}
{% load humanize core_tags %}
{% block title %}{{ view_title }}{% endblock %}
{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title }}</h1>
        <a href="{% url 'payments:record_payment' %}" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Record New Payment</a>
    </div>

    <form method="get" class="filter-form card card-body bg-light mb-3">
        <div class="row g-2 align-items-end">
            <div class="col-md-3">{{ filter_form.student.label_tag }} {{ filter_form.student }}</div>
            <div class="col-md-3">{{ filter_form.payment_method.label_tag }} {{ filter_form.payment_method }}</div>
            <div class="col-md-2">{{ filter_form.start_date.label_tag }} {{ filter_form.start_date }}</div>
            <div class="col-md-2">{{ filter_form.end_date.label_tag }} {{ filter_form.end_date }}</div>
            <div class="col-md-2">{{ filter_form.payment_type.label_tag }} {{ filter_form.payment_type }}</div>
            <div class="col-md-2"><button type="submit" class="btn btn-primary btn-sm w-100">Filter</button></div>
            {% if request.GET %}<div class="col-md-2"><a href="{% url 'payments:payment_list' %}" class="btn btn-secondary btn-sm w-100">Clear</a></div>{% endif %}
        </div>
    </form>

    {% include "partials/_messages.html" %}
    {% if payments %}
        <div class="card shadow-sm"><div class="card-body p-0"><div class="table-responsive">
        <table class="table table-striped table-hover table-sm mb-0">
            <thead class="table-light"><tr>
                <th>ID</th><th>Date</th><th>Student</th><th>Amount</th><th>Method</th><th>Invoice#</th><th>Type</th><th>Reference</th><th>Recorded By</th><th>Actions</th>
            </tr></thead>
            <tbody>
            {% for payment in payments %}
                <tr>
                    <td>{{ payment.pk }}</td>
                    <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                    <td>{{ payment.student.full_name|default:"N/A" }}</td>
                    <td class="text-end">{{ payment.amount|intcomma }}</td>
                    <td>{{ payment.payment_method.name }}</td>
                    <td>
                        {% if payment.allocations.exists %}
                            {% for allocation in payment.allocations.all %}
                                <a href="{% url 'fees:invoice_detail' allocation.invoice.pk %}">{{ allocation.invoice.invoice_number|default:allocation.invoice.pk }}</a>{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        {% else %}-{% endif %}
                    </td>
                    <td>{{ payment.get_payment_type_display }}</td>
                    <td>{{ payment.reference_number|default:"-" }}</td>
                    <td>{{ payment.processed_by_staff.email|default:"System" }}</td>
                    <td class="actions-column">
                        <a href="{% url 'payments:payment_receipt_pdf' payment.pk %}" class="btn btn-sm btn-outline-secondary" target="_blank" title="View Receipt PDF"><i class="bi bi-file-earmark-pdf"></i></a>
                        {# Add Edit/Delete for payments later if needed, consider accounting implications #}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        </div></div></div>
    {% else %}
        <div class="alert alert-info">No payments found matching your criteria.</div>
    {% endif %}
    {% include "partials/_pagination.html" %}
    <div class="footer-actions mt-3"><a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a></div>
</div>
{% endblock %}


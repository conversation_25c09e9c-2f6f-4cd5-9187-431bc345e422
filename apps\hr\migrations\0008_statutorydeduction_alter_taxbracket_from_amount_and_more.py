# Generated by Django 5.1.9 on 2025-07-07 06:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0007_alter_salarygradecomponent_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StatutoryDeduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='e.g., Pension Fund, National Housing Fund', max_length=100, unique=True, verbose_name='Deduction Name')),
                ('employee_contribution_rate', models.DecimalField(decimal_places=2, help_text="Percentage of a relevant salary component (e.g., basic) deducted from the employee's pay.", max_digits=5, verbose_name='Employee Contribution (%)')),
                ('employer_contribution_rate', models.DecimalField(decimal_places=2, default=0.0, help_text='Percentage contributed by the employer (a company cost).', max_digits=5, verbose_name='Employer Contribution (%)')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Statutory Deduction',
                'verbose_name_plural': 'Statutory Deductions',
            },
        ),
        migrations.AlterField(
            model_name='taxbracket',
            name='from_amount',
            field=models.DecimalField(decimal_places=2, max_digits=12, verbose_name='From Annual Amount'),
        ),
        migrations.AlterField(
            model_name='taxbracket',
            name='to_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Leave blank for the highest bracket', max_digits=12, null=True, verbose_name='To Annual Amount'),
        ),
    ]

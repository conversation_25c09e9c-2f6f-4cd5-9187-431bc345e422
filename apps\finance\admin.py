from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# Import models from this app (finance)
from .models import ExpenseCategory, Vendor, Expense, BudgetItem, BudgetAmount

# Import related models from other apps (for filters, autocomplete, etc.)
# Assuming ChartOfAccount is from accounting and AcademicYear/Term from fees
# from apps.accounting.models import Account as ChartOfAccount 
# from apps.fees.models import AcademicYear, Term
# from apps.payments.models import PaymentMethod
# from django.conf import settings # If using settings.AUTH_USER_MODEL

# Note: For ForeignKey filters to work best, the related ModelAdmin should also be registered.

@admin.register(ExpenseCategory)
class ExpenseCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'expense_account_display', 'is_active', 'description_snippet')
    search_fields = ('name', 'description', 'expense_account__name')
    list_filter = (
        'is_active',
        'expense_account__account_type', # Filter by the type of linked expense account
        'expense_account',              # Filter by specific expense account
    )
    autocomplete_fields = ['expense_account']
    ordering = ('name',)

    @admin.display(description='Expense Account (CoA)', ordering='expense_account__name')
    def expense_account_display(self, obj):
        return obj.expense_account.name if obj.expense_account else None

    @admin.display(description='Description')
    def description_snippet(self, obj):
        return (obj.description[:75] + '...') if obj.description and len(obj.description) > 75 else obj.description

@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ('name', 'contact_person', 'email', 'phone_number', 'city', 'is_active')
    search_fields = ('name', 'contact_person', 'email', 'phone_number', 'city')
    list_filter = (
        'is_active',
        'city', # Can be useful if you have vendors in many cities
        'state_province',
    )
    ordering = ('name',)

@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    list_display = ('expense_date', 'category_name', 'vendor_name', 'amount_display', 'payment_method_name', 'recorded_by_user', 'reference_number')
    search_fields = ('description', 'reference_number', 'category__name', 'vendor__name', 'recorded_by__email') # Use email if recorded_by is User
    list_filter = (
        'expense_date', # Date hierarchy filter will be provided
        'category',     # Filter by ExpenseCategory
        'vendor',       # Filter by Vendor
        'payment_method',# Filter by PaymentMethod
        'recorded_by',  # Filter by who recorded it
    )
    date_hierarchy = 'expense_date' # Adds quick date navigation at the top
    list_select_related = ('category', 'vendor', 'payment_method', 'recorded_by') # Performance
    autocomplete_fields = ['category', 'vendor', 'payment_method', 'recorded_by']
    ordering = ('-expense_date', '-created_at')
    # raw_id_fields = ('recorded_by',) # Alternative to autocomplete if many users

    fieldsets = (
        (None, {'fields': ('expense_date', 'category', 'vendor', 'amount', 'description')}),
        ('Payment & Reference', {'fields': ('payment_method', 'reference_number')}),
        ('Audit', {'fields': ('recorded_by', 'created_at', 'updated_at')}),
    )
    readonly_fields = ('created_at', 'updated_at')

    @admin.display(description='Category', ordering='category__name')
    def category_name(self, obj):
        return obj.category.name if obj.category else None

    @admin.display(description='Vendor', ordering='vendor__name')
    def vendor_name(self, obj):
        return obj.vendor.name if obj.vendor else None

    @admin.display(description='Amount') # Add currency formatting if needed
    def amount_display(self, obj):
        # You might want to fetch currency symbol from SchoolProfile here if relevant
        return obj.amount 

    @admin.display(description='Payment Method', ordering='payment_method__name')
    def payment_method_name(self, obj):
        return obj.payment_method.name if obj.payment_method else "N/A"

    @admin.display(description='Recorded By', ordering='recorded_by__email') # Adjust if recorded_by is not User model
    def recorded_by_user(self, obj):
        if obj.recorded_by:
            return getattr(obj.recorded_by, obj.recorded_by.USERNAME_FIELD, obj.recorded_by.pk)
        return None


# Admin for the second set of Budget models (BudgetItem, BudgetAmount)
@admin.register(BudgetItem) # This is your BudgetItem that links to CoA
class BudgetItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'linked_coa_account_display', 'budget_item_type', 'description_snippet')
    search_fields = ('name', 'description', 'linked_coa_account__name', 'linked_coa_account__code')
    list_filter = (
        'budget_item_type',
        'linked_coa_account__account_type', # Filter by CoA account type (Income/Expense)
        'linked_coa_account',              # Filter by specific CoA account
    )
    autocomplete_fields = ['linked_coa_account']
    ordering = ('budget_item_type', 'name',)

    @admin.display(description='Linked CoA Account', ordering='linked_coa_account__name')
    def linked_coa_account_display(self, obj):
        return obj.linked_coa_account.name if obj.linked_coa_account else None
    
    @admin.display(description='Description')
    def description_snippet(self, obj):
        return (obj.description[:75] + '...') if obj.description and len(obj.description) > 75 else obj.description

@admin.register(BudgetAmount)
class BudgetAmountAdmin(admin.ModelAdmin):
    list_display = ('budget_item_name', 'academic_year_display', 'term_display', 'budgeted_amount_display', 'notes_snippet')
    search_fields = ('budget_item__name', 'academic_year__name', 'term__name', 'notes')
    list_filter = (
        'academic_year',    # Filter by AcademicYear
        'term',             # Filter by Term
        'budget_item__budget_item_type', # Filter by Income/Expense budget items
        'budget_item',      # Filter by specific BudgetItem
    )
    list_select_related = ('budget_item', 'academic_year', 'term') # Performance
    autocomplete_fields = ['budget_item', 'academic_year', 'term']
    ordering = ('academic_year__start_date', 'term__start_date', 'budget_item__name')

    @admin.display(description='Budget Item', ordering='budget_item__name')
    def budget_item_name(self, obj):
        return obj.budget_item.name if obj.budget_item else None

    @admin.display(description='Academic Year', ordering='academic_year__name')
    def academic_year_display(self, obj):
        return obj.academic_year.name if obj.academic_year else None

    @admin.display(description='Term', ordering='term__name')
    def term_display(self, obj):
        return obj.term.name if obj.term else "N/A (Annual)"

    @admin.display(description='Budgeted Amount') # Add currency formatting if needed
    def budgeted_amount_display(self, obj):
        return obj.budgeted_amount

    @admin.display(description='Notes')
    def notes_snippet(self, obj):
        return (obj.notes[:75] + '...') if obj.notes and len(obj.notes) > 75 else obj.notes
    
    
    
{% load i18n %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% blocktrans with school_name=school_name %}Leave Request Status Update - {{ school_name }}{% endblocktrans %}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .header img { max-height: 80px; margin-bottom: 10px; }
        .header h1 { color: {{ school_primary_color|default:'#333' }}; margin:0; font-size: 24px;}
        .content p { margin-bottom: 1em; }
        .content strong { color: {{ school_primary_color|default:'#333' }}; }
        .status-approved { color: green; font-weight: bold; }
        .status-rejected { color: red; font-weight: bold; }
        .status-pending { color: orange; font-weight: bold; }
        .button-container { text-align: center; margin-top: 25px; }
        .button { display: inline-block; padding: 12px 25px; background-color: {{ school_secondary_color|default:'#007bff' }}; color: white !important; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .footer { text-align: center; margin-top: 25px; font-size: 0.85em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {% if school_logo_url %}<img src="{{ school_logo_url }}" alt="{{ school_name }} Logo">{% endif %}
            <h1>{{ school_name }}</h1>
        </div>
        <div class="content">
            <p>{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}</p>
            
            <p>{% blocktrans %}This email is to inform you about an update to your leave request.{% endblocktrans %}</p>

            <h4>{% trans "Leave Request Details:" %}</h4>
            <p><strong>{% trans "Leave Type:" %}</strong> {{ leave_request.leave_type }}</p>
            <p><strong>{% trans "Start Date:" %}</strong> {{ leave_request.start_date|date:"d M Y" }}</p>
            <p><strong>{% trans "End Date:" %}</strong> {{ leave_request.end_date|date:"d M Y" }}</p>
            <p><strong>{% trans "Reason:" %}</strong> {{ leave_request.reason|default:"N/A" }}</p>
            <p><strong>{% trans "New Status:" %}</strong> 
                <span class="
                    {% if leave_request.current_status == 'Approved' %}status-approved{% endif %}
                    {% if leave_request.current_status == 'Rejected' %}status-rejected{% endif %}
                    {% if leave_request.current_status == 'Pending' %}status-pending{% endif %}
                ">{{ leave_request.current_status_display|default:leave_request.current_status }}</span>
            </p>
            {% if leave_request.status_reason %}
                <p><strong>{% trans "Reason for Status:" %}</strong> {{ leave_request.status_reason }}</p>
            {% endif %}
            
            {% if leave_portal_url %}
                <p>{% trans "You can view your leave requests and details in the staff portal:" %}</p>
                <div class="button-container">
                    <a href="{{ leave_portal_url }}" class="button">{% trans "Go to Leave Portal" %}</a>
                </div>
            {% endif %}

            <p>{% blocktrans %}If you have any questions, please contact the HR department or your manager.{% endblocktrans %}</p>
            
            <p>{% trans "Regards," %}<br>
            {% blocktrans with school_name=school_name %}The {{ school_name }} HR Department{% endblocktrans %}</p>
        </div>
        <div class="footer">
            <p>{{ school_name }}<br>
            {% if school_address %}{{ school_address }}<br>{% endif %}
            {% if school_contact_info %}{{ school_contact_info }}{% endif %}</p>
            <p>© {% now "Y" %} {{ school_name }}.</p>
        </div>
    </div>
</body>
</html>


# apps/students/models.py
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError # For clean method
from datetime import date
from django.db import models
# Assuming SchoolClass and Section are imported from apps.schools.models
from apps.schools.models import SchoolClass, Section
from django.conf import settings


from decimal import Decimal
from django.db.models import Sum, F, Q
from django.db.models.functions import Coalesce


from apps.schools.models import StaffUser 

def student_photo_upload_path(instance, filename):
    # Consider tenant isolation for media later if needed.
    # Path: student_photos/<student_admission_number>/<filename>
    return f'student_photos/{instance.admission_number}/{filename}'



# apps/students/models.py
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

# Import SchoolClass and Section from apps.schools.models
# Make sure this path is correct for your project structure
try:
    from apps.schools.models import SchoolClass, Section
except ImportError:
    # Handle cases where these might not be defined or app not ready,
    # though for a working system, they should be.
    # For M2M string references, direct import at module level isn't strictly needed
    # if the app label is clear, but direct import is good for ForeignKey.
    SchoolClass = None 
    Section = None

# Import ParentUser from its correct location within the SAME APP (apps.students)
# from .models import ParentUser # Use relative import if ParentUser is in the same models.py
                                # OR: from apps.students.models import ParentUser (if it's in a separate file but same app)
                                # OR: from the actual app if ParentUser is elsewhere

# Assume STUDENT_GENDER_CHOICES, STUDENT_STATUS_CHOICES, BLOOD_GROUP_CHOICES are defined
# in this file or imported correctly.
STUDENT_GENDER_CHOICES = [('MALE', 'Male'), ('FEMALE', 'Female'), ('OTHER', 'Other'), ('NOT_SPECIFIED', 'Not Specified')]
STUDENT_STATUS_CHOICES = [('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('SUSPENDED', 'Suspended'), ('GRADUATED', 'Graduated'), ('LEFT_SCHOOL', 'Left School')]
BLOOD_GROUP_CHOICES = [('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-'), ('UNKNOWN', 'Unknown')]

def student_photo_upload_path(instance, filename):
    # file will be uploaded to MEDIA_ROOT/student_photos/<tenant_schema>/<student_id>/<filename>
    # Requires request.tenant to be available if using tenant schema in path, or pass tenant info
    # For simplicity here, let's assume a generic path or one based on student ID
    return f'student_photos/{instance.admission_number}/{filename}'


class Student(models.Model):
    
    class Gender(models.TextChoices):
        MALE = 'MALE', _('Male')
        FEMALE = 'FEMALE', _('Female')
        OTHER = 'OTHER', _('Other')
        NOT_SPECIFIED = 'NOT_SPECIFIED', _('Not Specified')

    class StudentStatus(models.TextChoices): # <<< USING TextChoices
        ACTIVE = 'ACTIVE', _('Active')
        INACTIVE = 'INACTIVE', _('Inactive')
        SUSPENDED = 'SUSPENDED', _('Suspended')
        GRADUATED = 'GRADUATED', _('Graduated')
        LEFT_SCHOOL = 'LEFT_SCHOOL', _('Left School')

    class BloodGroup(models.TextChoices):
        A_POSITIVE = 'A+', _('A+')
        A_NEGATIVE = 'A-', _('A-')
        B_POSITIVE = 'B+', _('B+')
        B_NEGATIVE = 'B-', _('B-')
        AB_POSITIVE = 'AB+', _('AB+')
        AB_NEGATIVE = 'AB-', _('AB-')
        O_POSITIVE = 'O+', _('O+')
        O_NEGATIVE = 'O-', _('O-')
        UNKNOWN = 'UNKNOWN', _('Unknown')
        
    # --- Core Student Information ---
    # tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='students')
    
    admission_number = models.CharField(
        max_length=50,
        unique=True, # This needs to be unique across the tenant schema
        help_text=_("Unique admission number for the student.")
    )
    first_name = models.CharField(_('first name'), max_length=100)
    middle_name = models.CharField(_('middle name'), max_length=100, blank=True)
    last_name = models.CharField(_('last name'), max_length=100)
    date_of_birth = models.DateField(_('date of birth'), null=True, blank=True)
    gender = models.CharField(
        _('gender'),
        max_length=15,
        choices=STUDENT_GENDER_CHOICES, 
        blank=True
    )
    photo = models.ImageField(
        upload_to=student_photo_upload_path,
        blank=True, null=True,
        verbose_name=_("student photo")
    )
    date_of_admission = models.DateField(_('date of admission'), default=timezone.now)
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=STUDENT_STATUS_CHOICES,
        default='ACTIVE',
        help_text=_("Current status of the student.")
    )
    is_active = models.BooleanField(
        _('is active'), 
        default=True, 
        help_text=_("Is the student currently considered active in the school?")
    )

    # --- Academic Information ---
    current_class = models.ForeignKey(
        SchoolClass, # Corrected: Assumes SchoolClass is imported
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='students_in_class', 
        verbose_name=_("current class/grade")
    )
    current_section = models.ForeignKey(
        Section, # Corrected: Assumes Section is imported
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='students_in_section', 
        verbose_name=_("current section")
    )
    roll_number = models.CharField(
        _('roll number'), 
        max_length=20, blank=True, 
        help_text=_("Optional roll number within the class/section.")
    )

    # ... (student_email, student_phone, guardian info, address, medical, other - these are fine) ...
    student_email = models.EmailField(_("student's email (optional)"), blank=True)
    student_phone = models.CharField(_("student's phone (optional)"), max_length=30, blank=True)
    guardian1_full_name = models.CharField(_("guardian 1 full name"), max_length=150, blank=True)
    guardian1_relationship = models.CharField(_("guardian 1 relationship"), max_length=50, blank=True, help_text=_("e.g., Father, Mother"))
    guardian1_phone = models.CharField(_("guardian 1 phone"), max_length=30, blank=True)
    guardian1_email = models.EmailField(_("guardian 1 email"), blank=True)
    guardian1_occupation = models.CharField(_("guardian 1 occupation"), max_length=100, blank=True)
    guardian2_full_name = models.CharField(_("guardian 2 full name (optional)"), max_length=150, blank=True)
    guardian2_relationship = models.CharField(_("guardian 2 relationship"), max_length=50, blank=True)
    guardian2_phone = models.CharField(_("guardian 2 phone"), max_length=30, blank=True)
    guardian2_email = models.EmailField(_("guardian 2 email"), blank=True)
    address_line1 = models.CharField(_('address line 1'), max_length=255, blank=True)
    address_line2 = models.CharField(_('address line 2'), max_length=255, blank=True)
    city = models.CharField(_('city'), max_length=100, blank=True)
    state_province = models.CharField(_("state/province"), max_length=100, blank=True)
    postal_code = models.CharField(_("postal code"), max_length=20, blank=True)
    country = models.CharField(_('country'), max_length=100, blank=True)
    
    blood_group = models.CharField(
        _('blood group'),
        max_length=10, 
        choices=BLOOD_GROUP_CHOICES, 
        blank=True, default='UNKNOWN'
    )
    allergies = models.TextField(_('allergies'), blank=True, help_text=_("List any known allergies."))
    medical_conditions = models.TextField(_('medical conditions'), blank=True, help_text=_("List any pre-existing medical conditions."))
    previous_school = models.CharField(_('previous school'), max_length=200, blank=True)
    notes = models.TextField(_('notes'), blank=True, help_text=_("General notes about the student."))

    # --- Link to ParentUser accounts for portal access ---
    parents = models.ManyToManyField(
        # Corrected to use app_label.ModelName format for models in other apps,
        # or direct import if preferred and no circularity.
        # If ParentUser is in 'apps.students', use 'students.ParentUser'
        'students.ParentUser',    # <<<<< CHANGE THIS to 'students.ParentUser'
        related_name='children',  # This is correct
        blank=True,              
        verbose_name=_('linked parent portal accounts')
    )

    created_by = models.ForeignKey(
        StaffUser, # Or StaffUser if staff always create students
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_students'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['current_class__name', 'current_section__name', 'last_name', 'first_name']
        verbose_name = _("student")
        verbose_name_plural = _("students")
        # unique_together = (('admission_number', 'tenant_context_field_if_any'),) # If admission_number is not globally unique across all tenants

    def __str__(self):
        return f"{self.full_name} ({self.admission_number})"
    
    
    def get_outstanding_balance(self):
        """
        Calculates the total outstanding balance for this specific student
        by using the actual database fields from the Invoice model.
        """
        # Local import to prevent potential circular dependency issues
        from apps.fees.models import Invoice

        outstanding_statuses = [
            Invoice.InvoiceStatus.SENT, 
            Invoice.InvoiceStatus.PARTIALLY_PAID, 
            Invoice.InvoiceStatus.OVERDUE
        ]
        
        # This is the corrected aggregation. It performs the math using
        # the real database columns: subtotal_amount, total_concession_amount, and amount_paid.
        aggregation = self.invoices.filter(status__in=outstanding_statuses).aggregate(
            total_due=Coalesce(
                Sum(
                    F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')
                ), 
                Decimal('0.00'),
                output_field=models.DecimalField()
            )
        )
        
        return aggregation.get('total_due', Decimal('0.00'))
    
    

    # --- CORRECTED/ADDED METHODS ---
    def get_full_name(self):
        """
        Returns the student's full name.
        Filters out None or empty parts before joining.
        """
        parts = [self.first_name, self.middle_name, self.last_name]
        return " ".join(p for p in parts if p and p.strip())
    
    get_full_name.short_description = 'Full Name'
    # get_full_name.admin_order_field = 'last_name'

    def get_short_name(self):
        """
        Returns the student's first name.
        """
        return self.first_name if self.first_name else ''

    def __str__(self):
        # Use the get_full_name() method here
        full_name_val = self.get_full_name()  # <<< CORRECTED LINE
        if self.admission_number:
            return f"{full_name_val} ({self.admission_number})"
        return full_name_val # Fallback if no admission number or if you prefer just the name

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def age(self):
        """Calculate student's age"""
        if not self.date_of_birth:
            return None
        
        today = date.today()
        age = today.year - self.date_of_birth.year
        
        # Check if birthday has occurred this year
        if today.month < self.date_of_birth.month or (
            today.month == self.date_of_birth.month and today.day < self.date_of_birth.day
        ):
            age -= 1
        
        return age
    
    @property
    def age_display(self):
        """Display age with proper formatting"""
        if self.age is None:
            return "N/A"

        if self.age == 1:
            return "1 year old"
        else:
            return f"{self.age} years old"

    @property
    def current_class_and_section(self):
        """Display current class and section in a formatted way"""
        if self.current_class:
            class_name = self.current_class.name
            if self.current_section:
                return f"{class_name} - {self.current_section.name}"
            return class_name
        return "N/A"
        
        
    def clean(self):
        super().clean()
        # Ensure SchoolClass and Section are imported or handled if None
        if SchoolClass and Section: # Add this check if imports might fail
            if self.current_section and self.current_class:
                if self.current_section.school_class != self.current_class:
                    raise ValidationError({
                        'current_section': _('Selected section does not belong to the selected class/grade.')
                    })
            elif self.current_section and not self.current_class:
                raise ValidationError({
                    'current_class': _('A class/grade must be selected if a section is chosen.')
                })

    def save(self, *args, **kwargs):
        # Automatically update is_active based on status
        if self.status == self.StudentStatus.ACTIVE:
            self.is_active = True
        elif self.status in [self.StudentStatus.INACTIVE, self.StudentStatus.SUSPENDED, self.StudentStatus.GRADUATED, self.StudentStatus.LEFT_SCHOOL]:
            self.is_active = False
        # else: # What if status is None or an unexpected value? Default to True or False?
            # self.is_active = True # Or based on some other logic
        
        # Consider making names title case
        if self.first_name: self.first_name = self.first_name.strip().title()
        if self.middle_name: self.middle_name = self.middle_name.strip().title()
        if self.last_name: self.last_name = self.last_name.strip().title()

        super().save(*args, **kwargs)

    def tenant_id_if_any(self): # This is for the photo path
        # This needs to reliably get the current tenant's identifier for the path.
        # If Student model has a direct FK 'tenant', use self.tenant.schema_name or self.tenant.pk
        # If not, and it relies on django-tenants schema routing:
        from django.db import connection
        return connection.schema_name if connection.schema_name != 'public' else 'shared_assets'
    


# D:\school_fees_saas_v2\apps\students\models.py

from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin, Group, Permission
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

class ParentUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        """
        Creates and saves a ParentUser with the given email and password.
        The 'username' field on ParentUser is optional and can be passed in extra_fields.
        """
        if not email:
            raise ValueError(_('Parent Users must have an email address'))
        
        email = self.normalize_email(email)
        
        # Remove is_staff and is_superuser from extra_fields if passed,
        # as parents should not have these by default.
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        extra_fields.setdefault('is_active', True) # Default to active

        # If 'username' is in extra_fields, it will be used.
        # If not, the 'username' field on ParentUser will use its model default (blank=True, null=True).
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Creates and saves a "superuser" ParentUser.
        For ParentUser, this typically means an active user, but not Django staff/superuser.
        This method is required by Django's user management commands but might
        not be practically used to create "admin" parents.
        """
        extra_fields.setdefault('is_staff', False)    # Parents are not Django staff
        extra_fields.setdefault('is_superuser', False) # Parents are not Django superusers
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not False:
            raise ValueError(_('ParentUser superuser must have is_staff=False.'))
        if extra_fields.get('is_superuser') is not False:
            raise ValueError(_('ParentUser superuser must have is_superuser=False.'))
        
        # Ensure required fields (like first_name, last_name if they are not blank=True on model
        # and are in ParentUser.REQUIRED_FIELDS) are provided or handled.
        # For example:
        # if not extra_fields.get('first_name'):
        #     raise ValueError(_('Superuser parent must have a first name.'))
        # if not extra_fields.get('last_name'):
        #     raise ValueError(_('Superuser parent must have a last name.'))

        return self.create_user(email, password, **extra_fields)



class ParentUser(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(_('email address'), unique=True)
    username = models.CharField(
        _('username'), max_length=150, unique=False, blank=True, null=True,
        help_text=_('Optional. Used for display or internal reference if needed.'),
    )
    first_name = models.CharField(_('first name'), max_length=150, blank=False)
    last_name = models.CharField(_('last name'), max_length=150, blank=False)
    
    phone_number = models.CharField(_('phone number'), max_length=20, blank=True)
    
    # --- ADD/MODIFY ADDRESS FIELDS & PROFILE PICTURE ---
    address_line1 = models.CharField(_('address line 1'), max_length=255, blank=True, null=True)
    address_line2 = models.CharField(_('address line 2'), max_length=255, blank=True, null=True)
    city = models.CharField(_('city'), max_length=100, blank=True, null=True)
    state_province = models.CharField(_('state/province'), max_length=100, blank=True, null=True) # You might need this too
    postal_code = models.CharField(_('postal/zip code'), max_length=20, blank=True, null=True)  # And this
    country = models.CharField(_('country'), max_length=100, blank=True, null=True)           # And this

    profile_picture = models.ImageField(
        _('profile picture'), 
        upload_to='parent_profiles/', # Ensure MEDIA_ROOT and MEDIA_URL are set up
        blank=True, 
        null=True
    )
    # Remove the old generic 'address' TextField if you replace it with structured fields
    # address = models.TextField(_('address'), blank=True, null=True) # REMOVE IF USING STRUCTURED ADDRESS

    is_active = models.BooleanField(_('active'), default=True)
    is_staff = models.BooleanField(_('staff status'), default=False)
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    groups = models.ManyToManyField(
        Group, verbose_name=_('groups'), blank=True,
        help_text=_('The groups this parent user belongs to...'),
        related_name="parentuser_groups_set", related_query_name="parentuser_group",
    )
    user_permissions = models.ManyToManyField(
        Permission, verbose_name=_('parent user permissions'), blank=True,
        help_text=_('Specific permissions for this parent user.'),
        related_name="parentuser_permissions_set", related_query_name="parentuser_permission",
    )
    
    objects = ParentUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        verbose_name = _('parent user')
        verbose_name_plural = _('parent users')

    def __str__(self):
        return self.email

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        return self.first_name
    
    
class StudentPermissions(models.Model):
    class Meta:
        managed = False
        default_permissions = ()
        permissions = [
            ('view_students_module', _('Can view the main Students & Parents module link')),
        ]
        
            
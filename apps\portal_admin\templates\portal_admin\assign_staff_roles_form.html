{# D:\school_fees_saas_v2\apps\portal_admin\templates\portal_admin\assign_staff_roles_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Your premium card and role checkbox styles are great and can remain here #}
    <style>
        .premium-card { background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border: none; }
        .premium-card-header { background: rgba(0, 0, 0, 0.05); border-bottom: 1px solid rgba(0, 0, 0, 0.1); padding: 1.5rem; text-align: center; }
        .premium-card-header h3 { color: #333; font-weight: 600; }
        .premium-card-body { background: white; padding: 2.5rem; }
        #{{ form.staff_member.id_for_label }} { border-radius: 0.5rem; }
        .roles-section { background: #f8f9fa; border-radius: 1rem; padding: 2rem; margin-top: 2rem; }
        .roles-section h5 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; }
        .roles-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; }
        .role-checkbox-card { background: white; border: 1px solid #e9ecef; border-radius: 0.5rem; padding: 1rem; transition: all 0.2s ease; cursor: pointer; }
        .role-checkbox-card:hover { border-color: #667eea; transform: translateY(-2px); }
        .role-checkbox-card.checked { border-color: #28a745; background: #e8f5e9; }
        .form-check-input { margin-top: 0.2em; }
        .form-check-label { font-weight: 500; }
        .no-selection-alert { padding: 2rem; text-align: center; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container my-4">
    <div class="pagetitle">
        <h1>{{ view_title|default:_("Staff Role Assignment") }}</h1>
        <nav><ol class="breadcrumb"><li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li><li class="breadcrumb-item active">Assign Roles</li></ol></nav>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3><i class="bi bi-person-badge me-2"></i>{% trans "Staff Role Assignment" %}</h3>
                </div>

                <div class="premium-card-body">
                    <p class="text-center text-muted mb-4">Select a staff member from the dropdown to view and update their assigned roles.</p>
                    {% include "partials/_messages.html" %}

                    <form method="post" id="staffRoleAssignmentForm" novalidate>
                        {% csrf_token %}
                        {% if form.non_field_errors %}<div class="alert alert-danger">{{ form.non_field_errors }}</div>{% endif %}

                        <!-- Staff Member Selection -->
                        <div class="mb-4">
                            <label for="{{ form.staff_member.id_for_label }}" class="form-label fs-5"><strong>Step 1:</strong> Select Staff Member</label>
                            {% render_field form.staff_member class="form-select form-select-lg" %}
                            {% for error in form.staff_member.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>

                        <!-- Roles Section - Only show if a staff member has been selected -->
                        {% if form.initial.staff_member or selected_staff_member_obj %}
                            <div class="roles-section">
                                <h5><i class="bi bi-shield-check me-2"></i>{% trans "Step 2: Assign Roles for" %} <strong>{{ selected_staff_member_obj.get_full_name|default:selected_staff_member_obj.email }}</strong></h5>
                                
                                <div class="roles-grid">
                                    {# Django renders CheckboxSelectMultiple as a list of checkboxes. We loop through them. #}
                                    {% for checkbox in form.roles %}
                                        <div class="role-checkbox-card" onclick="this.querySelector('input').click()">
                                            <div class="form-check">
                                                {{ checkbox.tag }} {# Renders the <input> tag #}
                                                <label class="form-check-label" for="{{ checkbox.id_for_label }}">
                                                    {{ checkbox.choice_label }}
                                                </label>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                {% if form.roles.errors %}<div class="alert alert-danger mt-3">{{ form.roles.errors }}</div>{% endif %}
                            </div>

                            <div class="d-flex justify-content-end mt-4">
                                <button type="submit" class="btn btn-success btn-lg"><i class="bi bi-check-circle me-2"></i>Update Role Assignments</button>
                            </div>
                        {% else %}
                            <div class="alert alert-info no-selection-alert">
                                <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "No Staff Member Selected" %}</h6>
                                <p>{% trans "Please select a staff member from the dropdown above to manage their roles." %}</p>
                            </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block page_specific_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const staffSelect = document.getElementById('{{ form.staff_member.id_for_label }}');

        // This JS reloads the page with the selected staff member's ID
        if (staffSelect) {
            staffSelect.addEventListener('change', function() {
                const selectedValue = this.value;
                if (selectedValue) {
                    window.location.href = `{% url 'portal_admin:assign_staff_roles' %}?staff_member_id=${selectedValue}`;
                } else {
                    window.location.href = `{% url 'portal_admin:assign_staff_roles' %}`;
                }
            });
        }

        // This JS makes the styled cards act like checkboxes
        const roleCards = document.querySelectorAll('.role-checkbox-card');
        roleCards.forEach(function(card) {
            const checkbox = card.querySelector('.form-check-input');
            function updateCardState() {
                if (checkbox.checked) {
                    card.classList.add('checked');
                } else {
                    card.classList.remove('checked');
                }
            }
            updateCardState(); // Initial state on page load
            checkbox.addEventListener('change', updateCardState);
        });
    });
    </script>
{% endblock %}





















{% comment %} {% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .staff-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border: 2px solid #28a745;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .staff-info h6 {
            color: #155724;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .staff-info p {
            color: #155724;
            margin: 0;
            font-size: 0.9rem;
        }

        .roles-section {
            background: #f8f9fa;
            border-radius: 1rem;
            padding: 2rem;
            margin-top: 2rem;
        }

        .roles-section h5 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .role-checkbox-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .role-checkbox-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .role-checkbox-card.checked {
            border-color: #28a745;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
        }

        .form-check {
            margin: 0;
        }

        .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
            border: 2px solid #667eea;
            border-radius: 0.5rem;
        }

        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .no-selection-alert {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
        }

        .no-selection-alert h6 {
            color: #856404;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .no-selection-alert p {
            color: #856404;
            margin: 0;
        }
    </style>
{% endblock %}


{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Staff Role Assignment") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'portal_admin:group_list' %}">{% trans "Roles" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Assign Staff Roles" %}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-person-badge me-2"></i>
                        {% trans "Staff Role Assignment" %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Information Section -->
                    <div class="staff-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "Role Assignment Process" %}</h6>
                        <p>{% trans "Select a staff member from the dropdown below to view and update their assigned roles. The roles list will dynamically update when you select a staff member." %}</p>
                    </div>

                    {% include "partials/_messages.html" %}

                    <form method="post" id="staffRoleAssignmentForm" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        <!-- Staff Member Selection -->
                        <div class="form-floating">
                            <select class="form-select{% if form.staff_member.errors %} is-invalid{% endif %}"
                                    id="{{ form.staff_member.id_for_label }}"
                                    name="{{ form.staff_member.name }}"
                                    required>
                                <option value="">{% trans "--- Select a Staff Member ---" %}</option>
                                {% for choice in form.staff_member.field.choices %}
                                    {% if choice.0 %}
                                        <option value="{{ choice.0 }}"
                                                {% if choice.0|stringformat:"s" == form.staff_member.value|stringformat:"s" %}selected{% endif %}>
                                            {{ choice.1 }}
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                            <label for="{{ form.staff_member.id_for_label }}">
                                <i class="bi bi-person-fill icon-input"></i>{% trans "Select Staff Member" %}
                            </label>
                            {% if form.staff_member.help_text %}
                                <div class="form-text">{{ form.staff_member.help_text }}</div>
                            {% endif %}
                            {% if form.staff_member.errors %}
                                <div class="invalid-feedback">{{ form.staff_member.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Roles Section - Only show if a staff member is selected -->
                        {% if form.initial.staff_member or selected_staff_member_obj %}
                            <div class="roles-section">
                                <h5>
                                    <i class="bi bi-shield-check me-2"></i>
                                    {% trans "Roles for" %}: <strong>{{ selected_staff_member_obj.get_full_name|default:selected_staff_member_obj.email|default:"Selected Staff" }}</strong>
                                </h5>

                                <div class="roles-grid">
                                    {% for checkbox in form.roles %}
                                        <div class="role-checkbox-card" data-role-id="{{ checkbox.data.value }}">
                                            <div class="form-check">
                                                <input type="checkbox"
                                                        class="form-check-input"
                                                        name="{{ checkbox.data.name }}"
                                                        value="{{ checkbox.data.value }}"
                                                        id="{{ checkbox.id_for_label }}"
                                                        {% if checkbox.data.selected %}checked{% endif %}>
                                                <label class="form-check-label" for="{{ checkbox.id_for_label }}">
                                                    <i class="bi bi-people-fill me-2"></i>
                                                    {{ checkbox.choice_label }}
                                                </label>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                {% if form.roles.errors %}
                                    <div class="alert alert-danger" role="alert">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        {% for error in form.roles.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                <!-- Action Buttons -->
                                <div class="d-flex justify-content-end gap-3 mt-4">
                                    <a href="{% url 'portal_admin:group_list' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>{% trans "Back to Roles" %}
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-check-circle me-2"></i>{% trans "Update Role Assignments" %}
                                    </button>
                                </div>
                            </div>
                        {% else %}
                            <div class="no-selection-alert">
                                <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "No Staff Member Selected" %}</h6>
                                <p>{% trans "Please select a staff member from the dropdown above to view and assign roles." %}</p>
                            </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form elements
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');
        const staffSelect = document.getElementById('{{ form.staff_member.id_for_label }}');

        // Staff member selection handling
        if (staffSelect) {
            staffSelect.addEventListener('change', function() {
                const selectedValue = this.value;

                if (selectedValue) {
                    // Show loading state
                    const originalText = this.innerHTML;
                    this.disabled = true;

                    // Add loading indicator
                    const loadingOption = document.createElement('option');
                    loadingOption.value = '';
                    loadingOption.textContent = '{% trans "Loading roles..." %}';
                    loadingOption.selected = true;
                    this.appendChild(loadingOption);

                    // Reload the page with the selected staff member's ID as a GET parameter
                    window.location.href = `{% url 'portal_admin:assign_staff_roles' %}?staff_member_id=${selectedValue}`;
                } else {
                    // If "--- Select ---" is chosen, reload without the parameter
                    window.location.href = `{% url 'portal_admin:assign_staff_roles' %}`;
                }
            });
        }

        // Role checkbox card interactions
        const roleCards = document.querySelectorAll('.role-checkbox-card');
        roleCards.forEach(function(card) {
            const checkbox = card.querySelector('.form-check-input');

            // Update card appearance based on checkbox state
            function updateCardState() {
                if (checkbox.checked) {
                    card.classList.add('checked');
                } else {
                    card.classList.remove('checked');
                }
            }

            // Initial state
            updateCardState();

            // Card click handling
            card.addEventListener('click', function(e) {
                if (e.target !== checkbox) {
                    checkbox.checked = !checkbox.checked;
                    updateCardState();
                }
            });

            // Checkbox change handling
            checkbox.addEventListener('change', updateCardState);
        });

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Enhanced select interactions
        if (staffSelect) {
            staffSelect.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            staffSelect.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        }

        // Role selection counter
        function updateRoleCounter() {
            const checkedRoles = document.querySelectorAll('.role-checkbox-card .form-check-input:checked');
            const counter = document.getElementById('role-counter');

            if (!counter) {
                const counterElement = document.createElement('div');
                counterElement.id = 'role-counter';
                counterElement.className = 'text-center mt-3 text-muted';
                counterElement.style.fontSize = '0.9rem';

                const rolesSection = document.querySelector('.roles-section');
                if (rolesSection) {
                    rolesSection.insertBefore(counterElement, rolesSection.querySelector('.d-flex'));
                }
            }

            const roleCounter = document.getElementById('role-counter');
            if (roleCounter) {
                const count = checkedRoles.length;
                roleCounter.innerHTML = `<i class="bi bi-check-circle-fill me-1"></i>${count} {% trans "role(s) selected" %}`;
                roleCounter.style.color = count > 0 ? '#28a745' : '#6c757d';
            }
        }

        // Update counter on checkbox changes
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('form-check-input')) {
                updateRoleCounter();
            }
        });

        // Initial counter update
        updateRoleCounter();
    });
    </script>
{% endblock %}

 {% endcomment %}

{% extends "tenant_base.html" %}
{% load static widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:term_list' %}">Terms</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ view_title }}</li>
        </ol>
    </nav>

    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">{{ view_title|default:"Term Form" }}</h4>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            <form method="post" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}{% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.name class+="form-control" %}
                        {% if form.name.help_text %}
                            <small class="form-text text-muted">{{ form.name.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.name.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}{% if form.academic_year.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.academic_year class+="form-select" %}
                        {% if form.academic_year.help_text %}
                            <small class="form-text text-muted">{{ form.academic_year.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.academic_year.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}{% if form.start_date.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.start_date class+="form-control" %}
                        {% if form.start_date.help_text %}
                            <small class="form-text text-muted">{{ form.start_date.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.start_date.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}{% if form.end_date.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.end_date class+="form-control" %}
                        {% if form.end_date.help_text %}
                            <small class="form-text text-muted">{{ form.end_date.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.end_date.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            {% render_field form.is_active class+="form-check-input" %}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                            {% if form.is_active.help_text %}
                                <small class="form-text text-muted d-block">{{ form.is_active.help_text|safe }}</small>
                            {% endif %}
                            {% for error in form.is_active.errors %}
                                <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="{% url 'schools:term_list' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if object %}Update{% else %}Create{% endif %} Term
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

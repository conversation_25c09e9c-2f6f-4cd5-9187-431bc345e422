{% extends "tenant_base.html" %}
{% load static widget_tweaks i18n %}

{% block title %}
    {% if form.instance.pk %}
        {% blocktrans with staff_name=form.instance.get_full_name|default:form.instance.email %}Edit Staff: {{ staff_name }}{% endblocktrans %}
    {% else %}
        {% translate "Create New Staff Member" %}
    {% endif %}
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:staff_list' %}">Staff</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                {% if form.instance.pk %}Edit Staff{% else %}Add Staff{% endif %}
            </li>
        </ol>
    </nav>

    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="bi bi-person-plus me-2"></i>
                {% if form.instance.pk %}
                    {% translate "Edit Staff Member" %}
                {% else %}
                    {% translate "Add New Staff Member" %}
                {% endif %}
            </h4>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            <form method="post" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}{% if form.first_name.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.first_name class+="form-control" %}
                        {% if form.first_name.help_text %}
                            <small class="form-text text-muted">{{ form.first_name.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.first_name.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}{% if form.last_name.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.last_name class+="form-control" %}
                        {% if form.last_name.help_text %}
                            <small class="form-text text-muted">{{ form.last_name.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.last_name.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}{% if form.email.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.email class+="form-control" %}
                        {% if form.email.help_text %}
                            <small class="form-text text-muted">{{ form.email.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.email.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }}{% if form.phone_number.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.phone_number class+="form-control" %}
                        {% if form.phone_number.help_text %}
                            <small class="form-text text-muted">{{ form.phone_number.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.phone_number.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>

                {% if form.password1 %}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">{{ form.password1.label }}{% if form.password1.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.password1 class+="form-control" %}
                        {% if form.password1.help_text %}
                            <small class="form-text text-muted">{{ form.password1.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.password1.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">{{ form.password2.label }}{% if form.password2.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.password2 class+="form-control" %}
                        {% if form.password2.help_text %}
                            <small class="form-text text-muted">{{ form.password2.help_text|safe }}</small>
                        {% endif %}
                        {% for error in form.password2.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            {% render_field form.is_active class+="form-check-input" %}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                            {% if form.is_active.help_text %}
                                <small class="form-text text-muted d-block">{{ form.is_active.help_text|safe }}</small>
                            {% endif %}
                            {% for error in form.is_active.errors %}
                                <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="{% url 'schools:staff_list' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>{% translate "Cancel" %}
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if form.instance.pk %}
                            {% translate "Update Staff" %}
                        {% else %}
                            {% translate "Create Staff" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

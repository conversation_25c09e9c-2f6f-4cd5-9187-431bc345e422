# Chart of Accounts & Fee Head Integration

## 🎯 Overview

This system provides automatic Chart of Accounts (COA) seeding and Fee Head income account integration for the School Fees SaaS platform.

## ✅ Features Implemented

### 1. **Automatic COA Seeding**
- **When**: Every time a new tenant (school) is created
- **What**: A comprehensive Chart of Accounts with 53+ accounts is automatically seeded
- **Structure**: Hierarchical account structure with proper parent-child relationships
- **Categories**: Assets, Liabilities, Equity, Revenue, and Expenses

### 2. **Fee Head Income Account Auto-Creation**
- **When**: Every time a new Fee Head is created
- **What**: An income account is automatically created and linked
- **Naming**: `{Fee Head Name} Income` (e.g., "Tuition Fee Income")
- **Account Codes**: Auto-generated in the 4100-4999 range
- **Account Type**: Automatically selects appropriate revenue account type

### 3. **Account Management**
- **Web Interface**: Full Chart of Accounts viewing and management
- **Account Hierarchy**: Visual tree structure with parent-child relationships
- **Account Details**: Code, name, type, balance, and status information

## 🔧 Technical Implementation

### Signal-Based Automation

**File**: `apps/fees/signals.py`

```python
@receiver(post_save, sender=FeeHead)
def create_or_update_income_account_for_fee_head(sender, instance, created, **kwargs):
    # Automatically creates income account when Fee Head is created
    # Updates account name when Fee Head name changes
```

**Key Features**:
- ✅ Automatic account creation on Fee Head creation
- ✅ Account name synchronization on Fee Head updates
- ✅ Proper error handling and logging
- ✅ Prevents duplicate account creation
- ✅ Uses appropriate revenue account types

### COA Seeding System

**Files**:
- `apps/accounting/coa_template.py` - Standard Chart of Accounts template
- `apps/accounting/management/commands/seed_standard_coa.py` - Seeding command
- `apps/tenants/signals.py` - Automatic seeding on tenant creation

**Account Structure**:
```
1000 - Assets
  1100 - Current Assets
    1110 - Cash and Cash Equivalents
    1120 - Accounts Receivable
    1130 - Inventory
  1200 - Non-Current Assets
    1210 - Property, Plant & Equipment
    1220 - Intangible Assets

2000 - Liabilities
  2100 - Current Liabilities
  2200 - Non-Current Liabilities

3000 - Equity
  3100 - Share Capital
  3200 - Retained Earnings

4000 - Revenue
  4010 - Tuition Fee Income
  4020 - Other Fee Income
  4030 - Library Services Income
  4100+ - Auto-generated Fee Head Income Accounts

5000 - Expenses
  5100 - Operating Expenses
  5200 - Administrative Expenses
```

## 🚀 Usage

### Creating a New Fee Head

1. **Via Web Interface**:
   - Navigate to `/fees/fee-heads/create/`
   - Fill in Fee Head details
   - Save - Income account is automatically created and linked

2. **Via Code**:
   ```python
   fee_head = FeeHead.objects.create(
       name="Sports Fee",
       description="Annual sports activities fee"
   )
   # Income account automatically created and linked
   print(fee_head.income_account_link)  # Shows linked account
   ```

### Viewing Chart of Accounts

- **URL**: `/tenant-accounting/chart-of-accounts/`
- **Features**: Hierarchical view, account balances, search functionality

### Managing Accounts

- **Account List**: `/tenant-accounting/accounts/`
- **Account Details**: Click on any account for detailed information
- **Account Creation**: Manual account creation available for custom needs

## 🛠️ Management Commands

### Seed COA for Tenant
```bash
python manage.py seed_standard_coa <tenant_schema> [--overwrite]
```

### Fix Fee Head Accounts
```bash
python manage.py fix_fee_head_accounts [--tenant=<schema>] [--dry-run]
```

## 📊 Account Code Ranges

| Range | Purpose | Example |
|-------|---------|---------|
| 1000-1999 | Assets | 1110 - Cash in Bank |
| 2000-2999 | Liabilities | 2100 - Accounts Payable |
| 3000-3999 | Equity | 3100 - Share Capital |
| 4000-4099 | Core Revenue | 4010 - Tuition Fee Income |
| 4100-4999 | Fee Head Revenue | 4100 - Sports Fee Income |
| 5000-5999 | Expenses | 5100 - Salaries Expense |

## 🔍 Troubleshooting

### Fee Head Not Getting Income Account

1. **Check Signals**: Ensure `apps/fees/signals.py` is imported in `apps/fees/apps.py`
2. **Check Account Types**: Verify revenue account types exist in public schema
3. **Run Fix Command**: Use `fix_fee_head_accounts` command to fix existing Fee Heads
4. **Check Logs**: Look for signal execution logs in console output

### COA Not Seeded for New Tenant

1. **Check Tenant Signals**: Verify `apps/tenants/signals.py` is working
2. **Manual Seeding**: Run `seed_standard_coa` command manually
3. **Check Account Types**: Ensure AccountTypes are seeded in public schema

## 📈 Benefits

1. **Automated Accounting**: No manual account creation needed
2. **Consistent Structure**: Standardized COA across all tenants
3. **Proper Integration**: Fee Heads automatically linked to accounting
4. **Audit Trail**: All account creation is logged
5. **Scalable**: Works for unlimited tenants and fee heads
6. **Maintainable**: Easy to modify COA template for all future tenants

## 🔐 Security & Permissions

- **Tenant Isolation**: Each tenant has its own COA
- **User Permissions**: Account management requires appropriate permissions
- **Data Integrity**: Foreign key constraints ensure data consistency
- **Audit Logging**: All changes are tracked and logged

---

**Status**: ✅ **FULLY IMPLEMENTED AND TESTED**

The Chart of Accounts system is now fully operational with automatic seeding and Fee Head integration working correctly across all tenants.

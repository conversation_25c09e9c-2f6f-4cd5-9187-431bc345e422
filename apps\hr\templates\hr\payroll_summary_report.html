<!-- apps/hr/templates/hr/payroll_summary_report.html -->

{% extends "base.html" %} {# Or your main base template #}
{% load humanize %} {# We use intcomma for nice formatting #}

{% block title %}{{ view_title }}{% endblock title %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">{{ view_title }}</h1>

    <div class="row">
        <!-- Sidebar: List of available payroll runs -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Select a Payroll Run</h5>
                </div>
                <div class="list-group list-group-flush">
                    {% for run in payroll_runs %}
                        <a href="?run_pk={{ run.pk }}" 
                           class="list-group-item list-group-item-action {% if selected_run.pk == run.pk %}active{% endif %}">
                            <strong>{{ run.start_date|date:"F Y" }}</strong>
                            <small class="d-block text-muted">
                                {{ run.start_date|date:"M d" }} - {{ run.end_date|date:"M d, Y" }}
                            </small>
                        </a>
                    {% empty %}
                        <div class="list-group-item">No processed payroll runs found.</div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Main Content: The report itself -->
        <div class="col-lg-8">
            {% if selected_run and summary_data %}
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Summary for {{ selected_run.start_date|date:"F Y" }}</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="card-subtitle mb-3 text-muted">
                            Period: {{ selected_run.start_date|date:"M d, Y" }} to {{ selected_run.end_date|date:"M d, Y" }} <br>
                            Status: <span class="badge bg-{% if selected_run.status == 'PAID' %}success{% else %}warning text-dark{% endif %}">{{ selected_run.get_status_display }}</span>
                        </h6>
                        
                        <p>This report summarizes the payroll for <strong>{{ summary_data.payslip_count }}</strong> staff members.</p>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td>Total Gross Earnings</td>
                                        <td class="text-end fw-bold fs-5 text-success">
                                            ${{ summary_data.total_gross|default:0|floatformat:2|intcomma }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Total Allowances (included in Gross)</td>
                                        <td class="text-end">
                                            ${{ summary_data.total_allowances|default:0|floatformat:2|intcomma }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Total Deductions</td>
                                        <td class="text-end fw-bold fs-5 text-danger">
                                            ${{ summary_data.total_deductions|default:0|floatformat:2|intcomma }}
                                        </td>
                                    </tr>
                                    <tr class="table-primary">
                                        <td class="fw-bold">Total Net Pay (Amount to be Paid)</td>
                                        <td class="text-end fw-bolder fs-4">
                                            ${{ summary_data.total_net|default:0|floatformat:2|intcomma }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <a href="{% url 'hr:payroll_run_detail' pk=selected_run.pk %}" class="btn btn-info">
                                <i class="bi bi-list-ul"></i> View Detailed Run
                            </a>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="bi bi-arrow-left-circle fs-1"></i>
                        <p class="mt-3">Please select a payroll run from the list to view its summary report.</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock content %}




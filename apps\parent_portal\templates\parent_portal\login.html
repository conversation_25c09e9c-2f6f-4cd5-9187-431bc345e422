{# templates/parent_portal/login.html #}
{% extends "tenant_base.html" %}

{% load static %}

{% block title %}{{ view_title|default:"Parent Portal Login" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .parent-login-card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .parent-login-card .card-header {
            border-top-left-radius: 1.5rem;
            border-top-right-radius: 1.5rem;
            padding: 2.5rem 2rem 2rem;
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            border: none;
            position: relative;
        }
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .parent-icon {
            margin-bottom: 1rem;
        }
        .form-floating > .form-control {
            border-radius: 1rem !important;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            height: 3.5rem;
            padding: 1rem 0.75rem 0.25rem 0.75rem;
        }
        .form-floating > .form-control:focus {
            border-color: #17a2b8;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            transform-origin: 0 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        }
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }
        .field-icon {
            margin-right: 0.5rem;
            color: #17a2b8;
        }
        .form-floating.focused > .form-control {
            border-color: #17a2b8;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
            background: rgba(255, 255, 255, 1);
        }
        .form-floating > .form-control::placeholder {
            color: transparent;
        }
        .form-floating > .form-control:focus::placeholder {
            color: transparent;
        }
        .btn-info {
            border-radius: 1rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            border: none;
        }
        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(23, 162, 184, 0.3);
            background: linear-gradient(135deg, #6f42c1 0%, #17a2b8 100%);
        }
        .btn-loading {
            position: relative;
            color: transparent;
        }
        .btn-loading::after {
            content: "";
            position: absolute;
            width: 1rem;
            height: 1rem;
            top: 50%;
            left: 50%;
            margin-left: -0.5rem;
            margin-top: -0.5rem;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .footer-links a {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .footer-links a:hover {
            color: #17a2b8;
        }
        @media (max-width: 768px) {
            .parent-login-card .card-header {
                padding: 2rem 1.5rem 1.5rem;
            }
            .parent-icon {
                font-size: 2.5rem !important;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6 col-xl-5">
            <div class="card parent-login-card">
                <div class="card-header text-white text-center position-relative">
                    <div class="security-badge">
                        <i class="bi bi-shield-check me-1"></i>SSL Secured
                    </div>
                    <div class="parent-icon">
                        <i class="bi bi-people-fill" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 class="mb-2 fw-bold">{{ view_title|default:"Parent Portal" }}</h2>
                    <p class="mb-0 opacity-75">
                        {% if tenant_name and tenant_name != "Unknown Tenant" and tenant_name != "Your School" %}
                            School: {{ tenant_name }}
                        {% else %}
                            Secure access to parent portal
                        {% endif %}
                    </p>
                </div>

                <div class="card-body p-4 p-md-5">
                    {% include "partials/_messages.html" %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2 mb-4">
                            {% for error in form.non_field_errors %}
                                {{ error }}{% if not forloop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Parent Email Field #}
                        <div class="mb-4">
                            <div class="form-floating">
                                <input type="email"
                                       class="form-control"
                                       id="floatingParentEmail"
                                       name="{{ form.username.name }}"
                                       placeholder="Enter your parent email address"
                                       autofocus
                                       autocomplete="email"
                                       data-bs-toggle="tooltip"
                                       data-bs-placement="top"
                                       title="Enter the email address associated with your parent account"
                                       {% if form.username.value %}value="{{ form.username.value }}"{% endif %}
                                       required>
                                <label for="floatingParentEmail">
                                    <i class="bi bi-envelope-fill field-icon"></i>{{ form.username.label }}
                                </label>
                            </div>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Password Field #}
                        <div class="mb-4">
                            <div class="form-floating">
                                <input type="password"
                                       class="form-control"
                                       id="floatingParentPassword"
                                       name="{{ form.password.name }}"
                                       placeholder="Enter your secure password"
                                       autocomplete="current-password"
                                       data-bs-toggle="tooltip"
                                       data-bs-placement="top"
                                       title="Enter your parent account password"
                                       required>
                                <label for="floatingParentPassword">
                                    <i class="bi bi-key-fill field-icon"></i>{{ form.password.label }}
                                </label>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <input type="hidden" name="next" value="{{ next|default:'' }}">
                        <button type="submit" class="btn btn-info w-100 btn-lg mt-3" id="loginBtn">
                            <span class="btn-text">Login to Parent Portal</span>
                        </button>
                    </form>

                    <hr class="my-4">
                    <div class="text-center footer-links">
                        <p class="mb-2">
                            <a href="#" class="text-decoration-none">
                                <i class="bi bi-question-circle me-1"></i>Forgot password?
                            </a>
                        </p>
                        <p class="mb-0">
                            <small class="text-muted">
                                <i class="bi bi-shield-lock me-1"></i>Your connection is secure and encrypted
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add focus effects for better UX and handle form-floating behavior
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(function(control) {
        // Check if field has value on page load and add focused class
        if (control.value && control.value.trim() !== '') {
            control.parentElement.classList.add('focused');
        }

        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            if (!this.value || this.value.trim() === '') {
                this.parentElement.classList.remove('focused');
            }
        });

        control.addEventListener('input', function() {
            if (this.value && this.value.trim() !== '') {
                this.parentElement.classList.add('focused');
            } else {
                this.parentElement.classList.remove('focused');
            }
        });
    });

    // Add loading animation to login button
    const loginForm = document.querySelector('form');
    const loginBtn = document.getElementById('loginBtn');

    if (loginForm && loginBtn) {
        loginForm.addEventListener('submit', function() {
            loginBtn.classList.add('btn-loading');
            loginBtn.disabled = true;
        });
    }
});
</script>
{% endblock content %}

# Potentially in apps.fees.services.py or a utility function
from apps.fees.models import FeeStructure, Invoice, InvoiceDetail, InvoiceStatus
from apps.students.models import Student
from apps.schools.models import AcademicYear, Term # Assuming these are in schools app
# from apps.accounting.services import create_journal_entry_for_invoice # We'll define this later
from django.utils import timezone
from django.db import transaction
import logging

logger = logging.getLogger(__name__)

def get_applicable_fee_structure(student, academic_year, term):
    """
    Finds the fee structure for the student's class, academic year, and term.
    """
    if not student.current_class:
        logger.warning(f"Student {student.pk} has no current_class assigned.")
        return None
    try:
        fee_structure = FeeStructure.objects.get(
            school_class=student.current_class,
            academic_year=academic_year,
            term=term,
            is_active=True # Assuming an is_active flag on FeeStructure
        )
        return fee_structure
    except FeeStructure.DoesNotExist:
        logger.warning(f"No active FeeStructure found for Class {student.current_class}, Year {academic_year}, Term {term}.")
        return None
    except FeeStructure.MultipleObjectsReturned:
        logger.error(f"Multiple active FeeStructures found for Class {student.current_class}, Year {academic_year}, Term {term}. Data inconsistency.")
        # Optionally, return the first one or raise an error
        return FeeStructure.objects.filter(
            school_class=student.current_class,
            academic_year=academic_year,
            term=term,
            is_active=True
        ).first()

def generate_invoice_for_student(student, academic_year, term, due_date, issue_date=None, created_by=None):
    """
    Generates an invoice for a single student for a given academic year and term.
    'created_by' should be the StaffUser who initiated this.
    """
    if not isinstance(student, Student):
        raise ValueError("Invalid student object provided.")
    if not isinstance(academic_year, AcademicYear):
        raise ValueError("Invalid academic_year object provided.")
    if not isinstance(term, Term):
        raise ValueError("Invalid term object provided.")

    issue_date = issue_date or timezone.now().date()

    fee_structure = get_applicable_fee_structure(student, academic_year, term)
    if not fee_structure:
        logger.error(f"Cannot generate invoice for Student {student.pk}: No applicable fee structure found.")
        return None, "No applicable fee structure found."

    # Check if an invoice for this student, year, term, and fee structure already exists (to prevent duplicates)
    # This check might need to be more sophisticated if fee structures can change and re-invoicing is allowed.
    if Invoice.objects.filter(
        student=student, 
        academic_year=academic_year, 
        term=term,
        # fee_structure=fee_structure, # If you add a direct link from Invoice to FeeStructure
        status__in=[Invoice.InvoiceStatus.DRAFT, Invoice.InvoiceStatus.UNPAID, Invoice.InvoiceStatus.PARTIALLY_PAID]
    ).exists():
        logger.warning(f"An existing non-cancelled/non-paid invoice already exists for Student {student.pk}, Year {academic_year}, Term {term}.")
        return None, "Existing unpaid/partially_paid invoice found."

    total_invoice_amount = 0
    fee_structure_items = fee_structure.items.all() # Assuming related_name 'items' from FeeStructureItem to FeeStructure

    if not fee_structure_items.exists():
        logger.error(f"Cannot generate invoice for Student {student.pk}: Fee structure {fee_structure.pk} has no items.")
        return None, "Fee structure has no items."

    for item in fee_structure_items:
        total_invoice_amount += item.amount
    
    if total_invoice_amount <= 0:
        logger.warning(f"Total invoice amount is zero or negative for Student {student.pk} with FeeStructure {fee_structure.pk}. Invoice not generated.")
        return None, "Total invoice amount is zero or negative."

    try:
        with transaction.atomic():
            invoice = Invoice.objects.create(
                student=student,
                academic_year=academic_year,
                term=term,
                # fee_structure=fee_structure, # Optional: Link invoice to the fee structure used
                invoice_number=Invoice.generate_invoice_number(), # Assumes a method on Invoice model
                issue_date=issue_date,
                due_date=due_date,
                total_amount=total_invoice_amount,
                created_by=created_by, # StaffUser instance
                status=InvoiceStatus.SENT # Initial status
            )
            logger.info(f"Created Invoice {invoice.invoice_number} for student {student.pk}, amount {total_invoice_amount}")

            invoice_details_to_create = []
            for item in fee_structure_items:
                invoice_details_to_create.append(
                    InvoiceDetail(
                        invoice=invoice,
                        fee_head=item.fee_head,
                        description=f"{item.fee_head.name} for {term.name}, {academic_year.name}",
                        amount=item.amount
                    )
                )
            InvoiceDetail.objects.bulk_create(invoice_details_to_create)
            logger.info(f"Created {len(invoice_details_to_create)} InvoiceDetail items for Invoice {invoice.invoice_number}")

            # --- Accounting Integration: Create Journal Entry ---
            # This is a critical step and needs its own robust service.
            # For now, a placeholder call.
            # Assumes student's AR account, and FeeHead's income account.
            # je_created, je_message = create_journal_entry_for_invoice(invoice, created_by=created_by)
            # if not je_created:
            #     logger.error(f"Failed to create Journal Entry for Invoice {invoice.invoice_number}: {je_message}")
            #     # Decide on rollback strategy: raise Exception to trigger transaction.atomic rollback
            #     raise Exception(f"Journal Entry creation failed: {je_message}") 
            # logger.info(f"Journal Entry created for Invoice {invoice.invoice_number}")
            # --- End Accounting Integration ---


        # Optionally, trigger a signal or Celery task to send invoice notification
        # send_invoice_notification_task.delay(invoice.id)

        return invoice, "Invoice generated successfully."

    except Exception as e:
        logger.exception(f"Error generating invoice for student {student.pk}: {e}")
        return None, f"An error occurred: {e}"
    
    
    
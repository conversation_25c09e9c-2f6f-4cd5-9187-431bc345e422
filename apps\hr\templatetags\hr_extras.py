from django import template
import collections.abc

register = template.Library()

@register.filter(name='abs')
def absolute_value(value):
    """Returns the absolute value of a number."""
    return abs(value)

# --- ADD THIS NEW FILTER ---
@register.filter(name='is_iterable')
def is_iterable(value):
    """Checks if a value is iterable (but not a string)."""
    return isinstance(value, collections.abc.Iterable) and not isinstance(value, str)






# # apps/hr/templatetags/hr_extras.py
# from django import template

# register = template.Library()

# @register.filter(name='abs')
# def absolute_value(value):
#     """Returns the absolute value of a number."""
#     return abs(value)



{# D:\school_fees_saas_v2\apps\hr\templates\hr\salarygrade_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
{{ block.super }}
<style>
    .component-form {
        padding-top: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    .component-form:last-of-type {
        border-bottom: none;
    }
</style>
{% endblock %}


{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <h1 class="h3 mb-4">{{ view_title }}</h1>
    
            <form method="post" id="salary-grade-form">
                {% csrf_token %}
                {{ form.media }} {# For any custom widgets that need media #}
        
                <div class="card shadow-sm mb-4">
                    <div class="card-header"><h5 class="mb-0">Grade Details</h5></div>
                    <div class="card-body p-4">
                        {% if form.non_field_errors %}<div class="alert alert-danger">{{ form.non_field_errors }}</div>{% endif %}
                        
                        <div class="mb-3">
                            <label class="form-label required" for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                            {% render_field form.name class="form-control" %}
                            {% for error in form.name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="mb-3">
                            <label class="form-label" for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                            {% render_field form.description class="form-control" %}
                        </div>
                        <div class="form-check form-switch mb-3">
                            {% render_field form.is_active class="form-check-input" role="switch" %}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">{{ form.is_active.label }}</label>
                        </div>
                    </div>
                </div>
        
                <div class="card shadow-sm">
                    <div class="card-header"><h5 class="mb-0">Recurring Salary Components for this Grade</h5></div>
                    <div class="card-body p-4">
                        {{ components_formset.management_form }}
                        {% if components_formset.non_form_errors %}<div class="alert alert-danger">{{ components_formset.non_form_errors }}</div>{% endif %}
                        
                        <div id="components-form-container">
                            {% for component_form in components_formset %}
                                <div class="row component-form align-items-center mb-2">
                                    {{ component_form.id }} {# Hidden ID field for the form #}
                                    <div class="col-md-6 mb-2">
                                        {% if forloop.first %}<label class="form-label d-none d-md-block">Component</label>{% endif %}
                                        {% render_field component_form.component class="form-select form-select-sm" %}
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        {% if forloop.first %}<label class="form-label d-none d-md-block">Amount</label>{% endif %}
                                        {% render_field component_form.amount class="form-control form-control-sm" %}
                                    </div>
                                    <div class="col-md-2 mb-2 pt-md-3">
                                        {% if component_form.instance.pk and components_formset.can_delete %}
                                        <div class="form-check">
                                            {{ component_form.DELETE|add_class:"form-check-input" }}
                                            <label for="{{ component_form.DELETE.id_for_label }}" class="form-check-label small text-danger">Delete</label>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% for error in component_form.errors.values %}<div class="text-danger small col-12">{{ error }}</div>{% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                        
                        <div id="empty-form-template" style="display: none;">
                            <div class="row component-form align-items-center mb-2">
                                {{ components_formset.empty_form.id }}
                                <div class="col-md-6 mb-2">{{ components_formset.empty_form.component|add_class:"form-select form-select-sm" }}</div>
                                <div class="col-md-4 mb-2">{{ components_formset.empty_form.amount|add_class:"form-control form-control-sm" }}</div>
                                <div class="col-md-2 mb-2 pt-md-3"></div>
                            </div>
                        </div>
        
                        <button type="button" id="add-item-button" class="btn btn-outline-success mt-3 btn-sm">
                            <i class="bi bi-plus-circle me-1"></i> Add Component
                        </button>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Save Salary Grade</button>
                    <a href="{% url 'hr:salarygrade_list' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addButton = document.getElementById('add-item-button');
    const formContainer = document.getElementById('components-form-container');
    const emptyFormTemplate = document.getElementById('empty-form-template').innerHTML;
    // The prefix is 'components' as set in the view's get_context_data
    const totalFormsInput = document.querySelector('input[name="components-TOTAL_FORMS"]'); 

    addButton.addEventListener('click', function() {
        if (!totalFormsInput) {
            console.error("Management form 'TOTAL_FORMS' input not found!");
            return;
        }
        let currentFormCount = parseInt(totalFormsInput.value, 10);
        const newFormHtml = emptyFormTemplate.replace(/__prefix__/g, currentFormCount);
        
        const newDiv = document.createElement('div');
        newDiv.innerHTML = newFormHtml;
        // Append the actual row, not the div wrapper we created
        formContainer.appendChild(newDiv.querySelector('.component-form').parentNode); 
        
        totalFormsInput.value = currentFormCount + 1;
    });
});
</script>
{% endblock %}




# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.core.validators
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='payment amount')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Date and time the payment was received/confirmed.')),
                ('transaction_reference', models.CharField(blank=True, help_text='E.g., Gateway Transaction ID, Cheque number, bank slip ID.', max_length=150, null=True, verbose_name='transaction reference')),
                ('receipt_number', models.CharField(blank=True, help_text='Unique 13-digit receipt number (e.g., RCT-*********).', max_length=20, null=True, unique=True, verbose_name='receipt number')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='notes')),
                ('payment_type', models.CharField(choices=[('FEE', 'Fee Payment'), ('OTHER_INCOME', 'Other Income'), ('REFUND_OUT', 'Refund Issued')], default='FEE', max_length=20, verbose_name='type of payment')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Confirmation'), ('COMPLETED', 'Completed Successfully'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('REVERSED', 'Reversed/Chargeback'), ('REFUNDED', 'Fully Refunded'), ('PARTIALLY_REFUNDED', 'Partially Refunded')], default='PENDING', help_text='The current status of this payment transaction.', max_length=20, verbose_name='payment status')),
                ('unallocated_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount of this payment not yet allocated to any invoice (e.g., overpayment, advance).', max_digits=12, verbose_name='unallocated amount')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_allocated', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='amount allocated')),
                ('allocation_date', models.DateField(default=django.utils.timezone.now, verbose_name='allocation date')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Payment Allocation',
                'verbose_name_plural': 'Payment Allocations',
                'ordering': ['allocation_date', 'pk'],
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(help_text='Name of the payment method (e.g., Cash, Online Portal Payment, Bank Transfer - Zenith).', max_length=100, unique=True)),
                ('type', models.CharField(choices=[('BANK_TRANSFER', 'Bank Transfer'), ('CASH', 'Cash'), ('MOBILE_MONEY', 'Mobile Money'), ('CARD_PAYMENT', 'Card Payment (Generic)'), ('ONLINE_MOCK', 'Online Mock Payment'), ('CHEQUE', 'Cheque'), ('INTERNAL_TRANSFER', 'Internal/Wallet Transfer'), ('SCHOLARSHIP_WAIVER', 'Scholarship/Waiver Application'), ('OTHER', 'Other')], default='OTHER', help_text='The general type of this payment method.', max_length=30, verbose_name='method type')),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Payment Method',
                'verbose_name_plural': 'Payment Methods',
                'ordering': ['name'],
            },
        ),
    ]

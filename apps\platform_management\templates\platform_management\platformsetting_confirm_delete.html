{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformsetting_confirm_delete.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Confirm Delete Setting") }}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:platformsetting_list' %}">{% trans "Platform Settings" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Confirm Delete" %}</li>
        </ol>
    </nav>
</div>

<div class="alert alert-danger d-flex align-items-center" role="alert">
<i class="bi bi-exclamation-triangle-fill flex-shrink-0 me-2"></i>
<div>
    <strong>{% trans "Warning!" %}</strong> {% trans "Deleting platform settings can have significant impacts on system behavior. Ensure you understand the consequences." %}
</div>
</div>

<div class="card border-danger shadow-sm">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">{% trans "Confirm Deletion of Setting" %}</h5>
    </div>
    <div class="card-body">
        <p class="card-text mt-3">
            {% blocktrans with name=setting.setting_name value=setting.setting_value|truncatechars:50 trimmed %}
            Are you sure you want to delete the platform setting:
            <br><strong>Name:</strong> {{ name }}
            <br><strong>Current Value:</strong> {{ value }}
            {% endblocktrans %}
        </p>
        <p class="text-danger"><strong>{% trans "This action cannot be undone." %}</strong></p>
        
        <form method="post">
            {% csrf_token %}
            <div class="text-end mt-4">
                <a href="{% url 'platform_management:platformsetting_list' %}" class="btn btn-secondary me-2">{% trans "Cancel" %}</a>
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, Delete This Setting" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock platform_admin_page_content %}


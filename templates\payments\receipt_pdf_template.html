{# D:\school_fees_saas_v2\templates\payments\receipt_pdf_template.html #}
{% load humanize core_tags %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Payment Receipt - #{{ payment.pk }}</title>
    <style>
        @page { size: A4; margin: 1.5cm; } /* Common page size */
        body { font-family: "Helvetica", "Arial", sans-serif; font-size: 10pt; line-height: 1.4; color: #333; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px; }
        .header img { max-height: 70px; max-width: 250px; margin-bottom: 5px; }
        .header h1 { margin: 0; font-size: 18pt; color: #000; }
        .header p { margin: 2px 0; font-size: 9pt; }
        .receipt-title { text-align: center; font-size: 16pt; font-weight: bold; margin-bottom: 25px; text-transform: uppercase; }
        .details-section { margin-bottom: 20px; }
        .details-section table { width: 100%; border-collapse: collapse; }
        .details-section th, .details-section td { padding: 6px; text-align: left; vertical-align: top; }
        .details-section th { width: 30%; font-weight: bold; color: #555;}
        .payment-summary { margin-top: 20px; margin-bottom: 20px; }
        .payment-summary table { width: 100%; border: 1px solid #aaa; }
        .payment-summary th, .payment-summary td { padding: 8px; border: 1px solid #ddd; }
        .payment-summary th { background-color: #f2f2f2; text-align: left;}
        .payment-summary .amount-value { text-align: right; font-weight: bold; }
        .footer-notes { margin-top: 30px; font-size: 8pt; color: #777; border-top: 1px dashed #ccc; padding-top: 10px; }
        .thank-you { text-align: center; margin-top: 30px; font-style: italic; }
        .watermark { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); opacity: 0.08; font-size: 100pt; color: #ccc; z-index: -1000; font-weight: bold; }
    </style>
</head>
<body>
    {# <div class="watermark">{{ school_profile.school.name|default:"RECEIPT" }}</div> #}

    <div class="header">
        {% if school_profile.logo %}
            <img src="{{ school_profile.logo.url }}" alt="School Logo">
        {% endif %}
        <h1>{{ school_profile.school.name|default:"Your School Name" }}</h1>
        <p>{{ school_profile.address_line1|default:"" }} {{ school_profile.address_line2|default:"" }}</p>
        <p>{{ school_profile.city|default:"" }}{% if school_profile.city and school_profile.state_province %}, {% endif %}{{ school_profile.state_province|default:"" }} {{ school_profile.postal_code|default:"" }}</p>
        <p>Phone: {{ school_profile.phone_number|default:"N/A" }} | Email: {{ school_profile.school_email|default:"N/A" }}</p>
    </div>

    <div class="receipt-title">Payment Receipt</div>

    <div class="details-section">
        <table>
            <tr>
                <th>Receipt Number:</th><td>PMT-{{ payment.pk|stringformat:"06d" }}</td>
                <th>Payment Date:</th><td>{{ payment.payment_date|date:"F d, Y H:i" }}</td>
            </tr>
            {% if payment.student %}
            <tr>
                <th>Student Name:</th><td>{{ payment.student.full_name }}</td>
                <th>Admission No:</th><td>{{ payment.student.admission_number|default:"N/A" }}</td>
            </tr>
            <tr>
                <th>Class:</th><td>{{ payment.student.current_class.name|default:"N/A" }} {% if payment.student.current_section %}- {{ payment.student.current_section.name }}{% endif %}</td>
                <th>Parent Name:</th><td>{{ payment.student.parent_profile.full_name|default:"N/A" }}</td>
            </tr>
            {% endif %}
        </table>
    </div>

    <div class="payment-summary">
        <table>
            <thead>
                <tr>
                    <th>Description</th>
                    <th style="text-align: right;">Amount Paid</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        Payment for:
                        {% if payment.allocations.exists %}
                            {% for allocation in payment.allocations.all %}
                                Invoice #{{ allocation.invoice.invoice_number|default:allocation.invoice.pk }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        {% elif payment.payment_type == 'OTHER' %}
                            {{ payment.notes|truncatewords:10|default:"Other Income" }}
                        {% else %}
                            School Fees / Dues
                        {% endif %}
                        <br>
                        <small>Method: {{ payment.payment_method.name }}</small>
                        {% if payment.reference_number %}
                            <br><small>Reference: {{ payment.reference_number }}</small>
                        {% endif %}
                    </td>
                    <td class="amount-value">{{ school_profile.currency_symbol|default:"$" }}{{ payment.amount|floatformat:2|intcomma }}</td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <th style="text-align: right;">Total Amount Paid:</th>
                    <td class="amount-value" style="font-size: 12pt;">{{ school_profile.currency_symbol|default:"$" }}{{ payment.amount|floatformat:2|intcomma }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    {% if payment.notes %}
        <p><strong>Notes:</strong> {{ payment.notes|linebreaksbr }}</p>
    {% endif %}

    <div class="thank-you">
        Thank you for your payment!
    </div>

    <div class="footer-notes">
        <p>This is a computer-generated receipt and does not require a signature.</p>
        <p>If you have any questions concerning this receipt, please contact the school office.</p>
        <p>Receipt generated on: {% now "F d, Y H:i T" %}</p>
    </div>
</body>
</html>


{# templates/announcements/platformannouncement_list.html #}
{% extends "announcements/_platform_announcement_base.html" %}
{% load humanize %}

{% block announcement_page_title %}All Platform Announcements{% endblock %}

{% block announcement_content %}
<div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span>Announcements</span>
        <a href="{% url 'announcements:platform_announcement_create' %}" class="btn btn-sm btn-success">
            <i class="bi bi-plus-circle-fill me-1"></i> Create New
        </a>
    </div>
    <div class="card-body p-0">
        {% if announcements %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Published</th>
                            <th>Publish Date</th>
                            <th>Expiry Date</th>
                            <th>Author</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for announcement in announcements %}
                        <tr>
                            <td>
                                <a href="{{ announcement.get_absolute_url }}">{{ announcement.title|truncatechars:50 }}</a>
                                {% if not announcement.is_currently_visible and announcement.is_published %}<span class="badge bg-warning ms-1">Scheduled</span>
                                {% elif not announcement.is_currently_visible and not announcement.is_published %}<span class="badge bg-secondary ms-1">Draft</span>
                                {% elif announcement.expiry_date and announcement.expiry_date < now %}<span class="badge bg-danger ms-1">Expired</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if announcement.is_published %}
                                    <i class="bi bi-check-circle-fill text-success"></i> Yes
                                {% else %}
                                    <i class="bi bi-x-circle-fill text-danger"></i> No
                                {% endif %}
                            </td>
                            <td>{{ announcement.publish_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ announcement.expiry_date|date:"Y-m-d H:i"|default:"Never" }}</td>
                            <td>{{ announcement.author.get_full_name|default:announcement.author.email|default:"System" }}</td>
                            <td>
                                <a href="{{ announcement.get_absolute_url }}" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></a>
                                <a href="{% url 'announcements:platform_announcement_update' pk=announcement.pk %}" class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil-square"></i></a>
                                <a href="{% url 'announcements:platform_announcement_delete' pk=announcement.pk %}" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p class="text-center p-3">No platform announcements found.</p>
        {% endif %}
    </div>
    {% if is_paginated %}
    <div class="card-footer">
        {% include "partials/_pagination.html" with page_obj=page_obj %} {# Assume you have this partial #}
    </div>
    {% endif %}
</div>
{% endblock %}



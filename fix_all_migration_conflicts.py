#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import psycopg
from django.conf import settings

def mark_migration_applied(cur, app_name, migration_name):
    """Mark a migration as applied in the current schema"""
    try:
        # Check if the migration record already exists
        cur.execute('''
            SELECT id FROM django_migrations 
            WHERE app = %s AND name = %s
        ''', (app_name, migration_name))
        
        existing = cur.fetchone()
        
        if existing:
            return f'✅ {app_name}.{migration_name} already marked as applied'
        else:
            # Insert the migration record
            cur.execute('''
                INSERT INTO django_migrations (app, name, applied)
                VALUES (%s, %s, NOW())
            ''', (app_name, migration_name))
            return f'✅ Marked {app_name}.{migration_name} as applied'
            
    except Exception as e:
        return f'❌ Error processing {app_name}.{migration_name}: {e}'

def check_column_exists(cur, schema, table, column):
    """Check if a column exists in a table"""
    cur.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = %s AND column_name = %s
        )
    """, (schema, table, column))
    return cur.fetchone()[0]

def get_primary_key(cur, schema, table):
    """Get the primary key column of a table"""
    cur.execute("""
        SELECT column_name
        FROM information_schema.key_column_usage 
        WHERE table_schema = %s AND table_name = %s
        AND constraint_name LIKE '%%pkey%%'
    """, (schema, table))
    result = cur.fetchone()
    return result[0] if result else None

print("Comprehensive migration conflict fix for all schemas...")

db_settings = settings.DATABASES['default']
conn = psycopg.connect(
    host=db_settings['HOST'],
    port=db_settings['PORT'],
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD']
)
conn.autocommit = True
cur = conn.cursor()

# Get all tenant schemas
cur.execute("""
    SELECT schema_name 
    FROM information_schema.schemata 
    WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'public', 'pg_toast')
    ORDER BY schema_name
""")
all_schemas = [row[0] for row in cur.fetchall()]

print(f"Found tenant schemas: {all_schemas}")

for schema in all_schemas:
    print(f'\n=== Processing schema: {schema} ===')
    
    # Set the search path to the schema
    cur.execute(f'SET search_path TO "{schema}";')
    
    migrations_to_mark = []
    
    # Check fees_feestructure table for total_amount column
    if check_column_exists(cur, schema, 'fees_feestructure', 'total_amount'):
        migrations_to_mark.append(('fees', '0004_feepermissions_feestructure_total_amount'))
        print(f'  📊 fees_feestructure has total_amount column')
    
    # Check schools_schoolprofile table structure
    schoolprofile_exists = check_column_exists(cur, schema, 'schools_schoolprofile', 'school_id') or \
                          check_column_exists(cur, schema, 'schools_schoolprofile', 'id')
    
    if schoolprofile_exists:
        pk = get_primary_key(cur, schema, 'schools_schoolprofile')
        has_id = check_column_exists(cur, schema, 'schools_schoolprofile', 'id')
        has_school_id = check_column_exists(cur, schema, 'schools_schoolprofile', 'school_id')
        
        print(f'  📊 schools_schoolprofile: PK={pk}, has_id={has_id}, has_school_id={has_school_id}')
        
        # If it has school_id as PK (old structure), mark the id-adding migration as applied
        if pk == 'school_id' and not has_id:
            migrations_to_mark.append(('schools', '0003_remove_school_foreign_key'))
            print(f'  ⚠️  Old structure detected, preventing id field addition')
        
        # If it already has id column, mark the migration as applied
        if has_id:
            migrations_to_mark.append(('schools', '0003_remove_school_foreign_key'))
        
        # Always mark the school field removal migration for existing schemas
        migrations_to_mark.append(('schools', '0006_remove_schoolprofile_school'))
    
    # Apply the migration markings
    if migrations_to_mark:
        print(f'  🔧 Marking {len(migrations_to_mark)} migrations as applied:')
        for app_name, migration_name in migrations_to_mark:
            result = mark_migration_applied(cur, app_name, migration_name)
            print(f'    {result}')
    else:
        print(f'  ✅ No migration conflicts detected')

conn.close()
print('\n🎉 Comprehensive migration conflict fix completed!')
print('You can now run: python manage.py migrate')

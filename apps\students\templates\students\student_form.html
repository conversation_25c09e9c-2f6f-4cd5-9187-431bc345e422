{# D:\school_fees_saas_v2\apps\students\templates\students\student_form.html #}
{% extends "tenant_base.html" %}

{% load static core_tags %} {# Assuming core_tags is for active_nav_link or similar, not render_field #}

{% block title %}{{ view_title|default:"Student Form" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .student-form-card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .student-form-card .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            padding: 2rem 2rem 1.5rem;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }
        .form-floating > .form-control,
        .form-floating > .form-select {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
        }
        .field-icon {
            margin-right: 0.5rem;
            color: #007bff;
        }
        .btn-primary {
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.3);
        }
        .btn-secondary {
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .fieldset-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.5rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #dee2e6;
        }
        .fieldset-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        @media (max-width: 768px) {
            .student-form-card .card-header {
                padding: 1.5rem 1rem 1rem;
            }
            .student-icon {
                font-size: 2.5rem !important;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-9">
            <div class="card student-form-card">
                <div class="card-header text-white text-center">
                    <div class="student-icon mb-3">
                        <i class="bi bi-person-badge" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 class="mb-2 fw-bold">{{ view_title|default:"Student Management" }}</h2>
                    <p class="mb-0 opacity-75">Complete student information and enrollment details</p>
                </div>
                <div class="card-body p-4 p-md-5">
                    <form method="post" enctype="multipart/form-data" novalidate> {# enctype for photo #}
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}<br>{% endfor %}
                            </div>
                        {% endif %}

                        <fieldset class="mb-5">
                            <div class="fieldset-header">
                                <h5><i class="bi bi-person-fill field-icon"></i>Core Student Information</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.admission_number }}
                                        <label for="{{ form.admission_number.id_for_label }}">
                                            <i class="bi bi-card-text field-icon"></i>{{ form.admission_number.label }}
                                        </label>
                                    </div>
                                    {% if form.admission_number.help_text %}<div class="help-text">{{ form.admission_number.help_text }}</div>{% endif %}
                                    {% if form.admission_number.errors %}<div class="invalid-feedback d-block mt-1">{{ form.admission_number.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.first_name }}
                                        <label for="{{ form.first_name.id_for_label }}">
                                            <i class="bi bi-person field-icon"></i>{{ form.first_name.label }}
                                        </label>
                                    </div>
                                    {% if form.first_name.errors %}<div class="invalid-feedback d-block mt-1">{{ form.first_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.middle_name }}
                                        <label for="{{ form.middle_name.id_for_label }}">
                                            <i class="bi bi-person field-icon"></i>{{ form.middle_name.label }}
                                        </label>
                                    </div>
                                    {% if form.middle_name.errors %}<div class="invalid-feedback d-block mt-1">{{ form.middle_name.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.last_name }}
                                        <label for="{{ form.last_name.id_for_label }}">
                                            <i class="bi bi-person field-icon"></i>{{ form.last_name.label }}
                                        </label>
                                    </div>
                                    {% if form.last_name.errors %}<div class="invalid-feedback d-block mt-1">{{ form.last_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.date_of_birth }}
                                        <label for="{{ form.date_of_birth.id_for_label }}">
                                            <i class="bi bi-calendar-date field-icon"></i>{{ form.date_of_birth.label }}
                                        </label>
                                    </div>
                                    {% if form.date_of_birth.errors %}<div class="invalid-feedback d-block mt-1">{{ form.date_of_birth.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.gender }}
                                        <label for="{{ form.gender.id_for_label }}">
                                            <i class="bi bi-gender-ambiguous field-icon"></i>{{ form.gender.label }}
                                        </label>
                                    </div>
                                    {% if form.gender.errors %}<div class="invalid-feedback d-block mt-1">{{ form.gender.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="{{ form.photo.id_for_label }}" class="form-label">
                                        <i class="bi bi-camera field-icon"></i>{{ form.photo.label }}
                                    </label>
                                    {{ form.photo }}
                                    {% if form.photo.help_text %}<div class="help-text">{{ form.photo.help_text }}</div>{% endif %}
                                    {% if form.photo.errors %}<div class="invalid-feedback d-block mt-1">{{ form.photo.errors|striptags }}</div>{% endif %}
                                    {% if object.photo %}
                                        <div class="mt-2">
                                            <small class="text-muted">Current Photo:</small>
                                            <img src="{{ object.photo.url }}" alt="Current Photo" class="img-thumbnail mt-1" style="max-height: 80px;">
                                        </div>
                                    {% elif form.initial.photo %}
                                        <div class="mt-2">
                                            <small class="text-muted">Current Photo:</small>
                                            <img src="{{ form.initial.photo.url }}" alt="Initial Photo" class="img-thumbnail mt-1" style="max-height: 80px;">
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.date_of_admission }}
                                        <label for="{{ form.date_of_admission.id_for_label }}">
                                            <i class="bi bi-calendar-plus field-icon"></i>{{ form.date_of_admission.label }}
                                        </label>
                                    </div>
                                    {% if form.date_of_admission.errors %}<div class="invalid-feedback d-block mt-1">{{ form.date_of_admission.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-5">
                            <div class="fieldset-header">
                                <h5><i class="bi bi-mortarboard field-icon"></i>Academic Information</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.current_class }}
                                        <label for="{{ form.current_class.id_for_label }}">
                                            <i class="bi bi-building field-icon"></i>{{ form.current_class.label }}
                                        </label>
                                    </div>
                                    {% if form.current_class.errors %}<div class="invalid-feedback d-block mt-1">{{ form.current_class.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.current_section }}
                                        <label for="{{ form.current_section.id_for_label }}">
                                            <i class="bi bi-diagram-3 field-icon"></i>{{ form.current_section.label }}
                                        </label>
                                    </div>
                                    {% if form.current_section.errors %}<div class="invalid-feedback d-block mt-1">{{ form.current_section.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-floating">
                                        {{ form.roll_number }}
                                        <label for="{{ form.roll_number.id_for_label }}">
                                            <i class="bi bi-hash field-icon"></i>{{ form.roll_number.label }}
                                        </label>
                                    </div>
                                    {% if form.roll_number.help_text %}<div class="help-text">{{ form.roll_number.help_text }}</div>{% endif %}
                                    {% if form.roll_number.errors %}<div class="invalid-feedback d-block mt-1">{{ form.roll_number.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.status }}
                                        <label for="{{ form.status.id_for_label }}">
                                            <i class="bi bi-check-circle field-icon"></i>{{ form.status.label }}
                                        </label>
                                    </div>
                                    {% if form.status.errors %}<div class="invalid-feedback d-block mt-1">{{ form.status.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-4 d-flex align-items-center">
                                    <div class="form-check form-switch">
                                        {{ form.is_active }}
                                        <label class="form-check-label fw-bold" for="{{ form.is_active.id_for_label }}">
                                            <i class="bi bi-toggle-on field-icon"></i>{{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.help_text %}<div class="help-text">{{ form.is_active.help_text }}</div>{% endif %}
                                    {% if form.is_active.errors %}<div class="invalid-feedback d-block mt-1">{{ form.is_active.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-5">
                            <div class="fieldset-header">
                                <h5><i class="bi bi-telephone field-icon"></i>Student Contact Information</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.student_email }}
                                        <label for="{{ form.student_email.id_for_label }}">
                                            <i class="bi bi-envelope field-icon"></i>{{ form.student_email.label }}
                                        </label>
                                    </div>
                                    {% if form.student_email.errors %}<div class="invalid-feedback d-block mt-1">{{ form.student_email.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.student_phone }}
                                        <label for="{{ form.student_phone.id_for_label }}">
                                            <i class="bi bi-phone field-icon"></i>{{ form.student_phone.label }}
                                        </label>
                                    </div>
                                    {% if form.student_phone.errors %}<div class="invalid-feedback d-block mt-1">{{ form.student_phone.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-5">
                            <div class="fieldset-header">
                                <h5><i class="bi bi-person-hearts field-icon"></i>Guardian 1 Information</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.guardian1_full_name }}
                                        <label for="{{ form.guardian1_full_name.id_for_label }}">
                                            <i class="bi bi-person-fill field-icon"></i>{{ form.guardian1_full_name.label }}
                                        </label>
                                    </div>
                                    {% if form.guardian1_full_name.errors %}<div class="invalid-feedback d-block mt-1">{{ form.guardian1_full_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        {{ form.guardian1_relationship }}
                                        <label for="{{ form.guardian1_relationship.id_for_label }}">
                                            <i class="bi bi-heart field-icon"></i>{{ form.guardian1_relationship.label }}
                                        </label>
                                    </div>
                                    {% if form.guardian1_relationship.errors %}<div class="invalid-feedback d-block mt-1">{{ form.guardian1_relationship.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.guardian1_phone.id_for_label }}" class="form-label">{{ form.guardian1_phone.label_tag|safe }}</label>
                                    {{ form.guardian1_phone }}
                                    {% if form.guardian1_phone.errors %}<div class="invalid-feedback d-block">{{ form.guardian1_phone.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.guardian1_email.id_for_label }}" class="form-label">{{ form.guardian1_email.label_tag|safe }}</label>
                                    {{ form.guardian1_email }}
                                    {% if form.guardian1_email.errors %}<div class="invalid-feedback d-block">{{ form.guardian1_email.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.guardian1_occupation.id_for_label }}" class="form-label">{{ form.guardian1_occupation.label_tag|safe }}</label>
                                    {{ form.guardian1_occupation }}
                                    {% if form.guardian1_occupation.errors %}<div class="invalid-feedback d-block">{{ form.guardian1_occupation.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Guardian 2 Information (Optional)</legend>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.guardian2_full_name.id_for_label }}" class="form-label">{{ form.guardian2_full_name.label_tag|safe }}</label>
                                    {{ form.guardian2_full_name }}
                                    {% if form.guardian2_full_name.errors %}<div class="invalid-feedback d-block">{{ form.guardian2_full_name.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.guardian2_relationship.id_for_label }}" class="form-label">{{ form.guardian2_relationship.label_tag|safe }}</label>
                                    {{ form.guardian2_relationship }}
                                    {% if form.guardian2_relationship.errors %}<div class="invalid-feedback d-block">{{ form.guardian2_relationship.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.guardian2_phone.id_for_label }}" class="form-label">{{ form.guardian2_phone.label_tag|safe }}</label>
                                    {{ form.guardian2_phone }}
                                    {% if form.guardian2_phone.errors %}<div class="invalid-feedback d-block">{{ form.guardian2_phone.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.guardian2_email.id_for_label }}" class="form-label">{{ form.guardian2_email.label_tag|safe }}</label>
                                    {{ form.guardian2_email }}
                                    {% if form.guardian2_email.errors %}<div class="invalid-feedback d-block">{{ form.guardian2_email.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                        </fieldset>

                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Address Information</legend>
                            <div class="mb-3"> {# address_line1 #}
                                <label for="{{ form.address_line1.id_for_label }}" class="form-label">{{ form.address_line1.label_tag|safe }}</label>
                                {{ form.address_line1 }}
                                {% if form.address_line1.errors %}<div class="invalid-feedback d-block">{{ form.address_line1.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="mb-3"> {# address_line2 #}
                                <label for="{{ form.address_line2.id_for_label }}" class="form-label">{{ form.address_line2.label_tag|safe }}</label>
                                {{ form.address_line2 }}
                                {% if form.address_line2.errors %}<div class="invalid-feedback d-block">{{ form.address_line2.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3"> {# city #}
                                    <label for="{{ form.city.id_for_label }}" class="form-label">{{ form.city.label_tag|safe }}</label>
                                    {{ form.city }}
                                    {% if form.city.errors %}<div class="invalid-feedback d-block">{{ form.city.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-3"> {# state_province #}
                                    <label for="{{ form.state_province.id_for_label }}" class="form-label">{{ form.state_province.label_tag|safe }}</label>
                                    {{ form.state_province }}
                                    {% if form.state_province.errors %}<div class="invalid-feedback d-block">{{ form.state_province.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-4 mb-3"> {# postal_code #}
                                    <label for="{{ form.postal_code.id_for_label }}" class="form-label">{{ form.postal_code.label_tag|safe }}</label>
                                    {{ form.postal_code }}
                                    {% if form.postal_code.errors %}<div class="invalid-feedback d-block">{{ form.postal_code.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="mb-3"> {# country #}
                                <label for="{{ form.country.id_for_label }}" class="form-label">{{ form.country.label_tag|safe }}</label>
                                {{ form.country }}
                                {% if form.country.errors %}<div class="invalid-feedback d-block">{{ form.country.errors|striptags }}</div>{% endif %}
                            </div>
                        </fieldset>

                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Medical & Other</legend>
                            <div class="row">
                                <div class="col-md-4 mb-3"> {# blood_group #}
                                    <label for="{{ form.blood_group.id_for_label }}" class="form-label">{{ form.blood_group.label_tag|safe }}</label>
                                    {{ form.blood_group }}
                                    {% if form.blood_group.errors %}<div class="invalid-feedback d-block">{{ form.blood_group.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-8 mb-3"> {# previous_school #}
                                    <label for="{{ form.previous_school.id_for_label }}" class="form-label">{{ form.previous_school.label_tag|safe }}</label>
                                    {{ form.previous_school }}
                                    {% if form.previous_school.errors %}<div class="invalid-feedback d-block">{{ form.previous_school.errors|striptags }}</div>{% endif %}
                                </div>
                            </div>
                            <div class="mb-3"> {# allergies #}
                                <label for="{{ form.allergies.id_for_label }}" class="form-label">{{ form.allergies.label_tag|safe }}</label>
                                {{ form.allergies }}
                                {% if form.allergies.help_text %}<small class="form-text text-muted">{{ form.allergies.help_text }}</small>{% endif %}
                                {% if form.allergies.errors %}<div class="invalid-feedback d-block">{{ form.allergies.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="mb-3"> {# medical_conditions #}
                                <label for="{{ form.medical_conditions.id_for_label }}" class="form-label">{{ form.medical_conditions.label_tag|safe }}</label>
                                {{ form.medical_conditions }}
                                {% if form.medical_conditions.help_text %}<small class="form-text text-muted">{{ form.medical_conditions.help_text }}</small>{% endif %}
                                {% if form.medical_conditions.errors %}<div class="invalid-feedback d-block">{{ form.medical_conditions.errors|striptags }}</div>{% endif %}
                            </div>
                            <div class="mb-3"> {# notes #}
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label_tag|safe }}</label>
                                {{ form.notes }}
                                {% if form.notes.help_text %}<small class="form-text text-muted">{{ form.notes.help_text }}</small>{% endif %}
                                {% if form.notes.errors %}<div class="invalid-feedback d-block">{{ form.notes.errors|striptags }}</div>{% endif %}
                            </div>
                        </fieldset>

                        <div class="mt-5 d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                All fields marked with * are required
                            </div>
                            <div>
                                <a href="{% if object %}{% url 'students:student_detail' object.pk %}{% else %}{% url 'students:student_list' %}{% endif %}" class="btn btn-secondary me-3">
                                    <i class="bi bi-arrow-left me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-{% if object %}pencil-square{% else %}plus-circle{% endif %} me-1"></i>
                                    {% if object %}Update Student{% else %}Create Student{% endif %}
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize Bootstrap tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Enhanced form validation and user experience
        const form = document.querySelector('form');
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;

        // Form submission enhancement
        form.addEventListener('submit', function(e) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Processing...';

            // Re-enable button after 3 seconds to prevent permanent disable on validation errors
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }, 3000);
        });

        // Enhanced field focus effects
        document.querySelectorAll('.form-floating .form-control, .form-floating .form-select').forEach(function(field) {
            field.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            field.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // Cascading dropdowns (Class -> Section) functionality
        const classSelect = document.getElementById('{{ form.current_class.id_for_label }}'); // Ensure this ID is correct
        const sectionSelect = document.getElementById('{{ form.current_section.id_for_label }}'); // Ensure this ID is correct

        if (classSelect && sectionSelect) {
            classSelect.addEventListener('change', function () {
                const classId = this.value;
                sectionSelect.innerHTML = '<option value="">-- Loading Sections --</option>';

                if (classId) {
                    fetch(`/portal/ajax/load-sections/?class_id=${classId}`) // Ensure this URL is correct
                        .then(response => {
                            if (!response.ok) { throw new Error('Network response was not ok'); }
                            return response.json();
                        })
                        .then(data => {
                            sectionSelect.innerHTML = '<option value="">-- Select Section --</option>';
                            data.forEach(function (section) {
                                const option = document.createElement('option');
                                option.value = section.id;
                                option.textContent = section.name;
                                {% if object and object.current_section_id == section.id %} // Compare IDs
                                    option.selected = true;
                                {% elif form.initial.current_section == section.id %} // For create form with initial data
                                    option.selected = true;
                                {% endif %}
                                sectionSelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Error loading sections:', error);
                            sectionSelect.innerHTML = '<option value="">-- Error Loading Sections --</option>';
                        });
                } else {
                    sectionSelect.innerHTML = '<option value="">-- Select Class First --</option>';
                }
            });

            // Trigger change on page load if a class is already selected (for edit forms)
            if (classSelect.value) {
                classSelect.dispatchEvent(new Event('change'));
            } else {
                 // Ensure section dropdown is in a sensible default state if no class is selected initially
                sectionSelect.innerHTML = '<option value="">-- Select Class First --</option>';
            }
        } else {
            if (!classSelect) console.error("Class select dropdown not found. Check ID: {{ form.current_class.id_for_label }}");
            if (!sectionSelect) console.error("Section select dropdown not found. Check ID: {{ form.current_section.id_for_label }}");
        }
    });
    </script>
{% endblock %}

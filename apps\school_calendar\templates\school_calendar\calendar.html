{% extends "base.html" %}
{% load static i18n calendar_tags %}

{% block body_override %}

{% block page_specific_css %}
<style>
    /* --- Core Full-Page Layout --- */
    html, body {
        overflow: hidden; height: 100%; margin: 0; padding: 0;
        background-color: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    .full-page-calendar { display: flex; flex-direction: column; height: 100vh; }

    /* --- Header --- */
    .calendar-header {
        flex-shrink: 0; padding: 0.75rem 1.5rem; background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid #dee2e6; display: flex; align-items: center;
        justify-content: space-between; gap: 1rem; z-index: 10;
    }
    .header-actions { display: flex; align-items: center; gap: 0.5rem; min-width: 220px; }
    .header-actions.left { justify-content: flex-start; }
    .header-actions.right { justify-content: flex-end; }
    .calendar-header h1 { font-size: 1.5rem; font-weight: 600; color: #212529; margin: 0; }

    /* --- Calendar Grid Container --- */
    .calendar-grid {
        flex-grow: 1; display: flex; flex-direction: column;
        padding: 2rem 3rem; overflow-y: auto;
    }
    
    /* --- Days of the Week Header --- */
    .week-header { display: flex; flex-shrink: 0; gap: 1rem; }
    .week-day-name {
        flex: 1 0 0; text-align: center; padding: 0.5rem;
        font-weight: 600; font-size: 0.9rem; color: #343a40;
    }

    /* --- Days Grid Container --- */
    .days-container { flex-grow: 1; display: flex; flex-wrap: wrap; gap: 1rem; }
    
    /* --- Individual Day Cell --- */
    .day-cell {
        flex: 1 0 calc(14.28% - 1rem); max-width: calc(14.28% - 1rem); min-height: 110px;
        position: relative; background-color: #ffffff; border-radius: 12px;
        border: 1px solid #e9ecef; display: flex; flex-direction: column;
        transition: all 0.2s ease-in-out;
    }
    .day-cell.other-month { background-color: transparent; border-color: transparent; }
    .day-cell:not(.other-month):hover {
        transform: translateY(-4px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #8a9bff;
    }
    .day-header { padding: 0.75rem; display: flex; justify-content: space-between; align-items: center; }
    .day-number { font-weight: 500; font-size: 0.9rem; color: #343a40; }

    /* Event Count Badge */
    .event-count {
        background: #e9ecef; color: #6c757d; border-radius: 50%; width: 22px; height: 22px;
        display: flex; align-items: center; justify-content: center;
        font-size: 0.75rem; font-weight: 600;
    }
    
    /* Today Highlighting - Simplified & Merged */
    .day-cell.today { border-color: #667eea; background-color: #f4f6ff; }
    .day-cell.today .day-number { font-weight: 700; color: #667eea; }
    .day-cell.today .event-count { background-color: #667eea; color: white; }
    
    /* Add Event Button on Hover */
    .add-event-btn { opacity: 0; transform: scale(0.8); transition: all 0.2s ease; color: #6c757d; cursor: pointer; }
    .day-cell:hover .add-event-btn { opacity: 1; transform: scale(1); }
    .add-event-btn:hover { color: #667eea; }

    /* --- Events inside the cell --- */
    .day-events { flex-grow: 1; overflow-y: auto; padding: 0 0.5rem 0.5rem; }
    .day-events::-webkit-scrollbar { width: 4px; }
    .day-events::-webkit-scrollbar-thumb { background: #dcdcdc; border-radius: 4px; }
    .event-item {
        border-left: 3px solid; padding: 0.4rem 0.7rem; margin-bottom: 0.4rem;
        border-radius: 8px; font-size: 0.8rem; font-weight: 500; cursor: pointer;
        white-space: nowrap; overflow: hidden; text-overflow: ellipsis;
        transition: all 0.2s ease;
    }
    /* Simplified Priority Styles - No Duplicates */
    .event-item.priority-urgent { background-color: #f8d7da; border-color: #721c24; color: #721c24; }
    .event-item.priority-high { background-color: #fff3cd; border-color: #856404; color: #856404; }
    .event-item.priority-medium { background-color: #d1ecf1; border-color: #0c5460; color: #0c5460; }
    .event-item.priority-low { background-color: #d4edda; border-color: #155724; color: #155724; }
    
    /* --- Bottom Action Footer --- */
    .calendar-footer {
        flex-shrink: 0; padding: 0.75rem 1.5rem; background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px); border-top: 1px solid #dee2e6;
        display: flex; align-items: center; justify-content: space-between; gap: 1rem;
    }
    .legend-item { display: flex; align-items: center; gap: 0.5rem; font-size: 0.8rem; }
    .legend-color { width: 14px; height: 14px; border-radius: 4px; }
</style>
{% endblock %}

<div class="full-page-calendar">
    <!-- Header -->
    <header class="calendar-header">
        <div class="header-actions left">
            <a href="/" class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Back to Dashboard"><i class="bi bi-arrow-left"></i></a>
            <h1 class="h5 mb-0 d-none d-md-block">{{ month_name }} {{ year }}</h1>
        </div>
        <div class="header-actions center">
             <a href="?" class="btn btn-sm btn-outline-secondary">{% trans "Today" %}</a>
            <div class="btn-group btn-group-sm">
                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-secondary" aria-label="{% trans 'Previous month' %}"><i class="bi bi-chevron-left"></i></a>
                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-secondary" aria-label="{% trans 'Next month' %}"><i class="bi bi-chevron-right"></i></a>
            </div>
        </div>
        <div class="header-actions right">
             {% if perms.school_calendar.add_event %}
                <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i><span class="d-none d-md-inline">{% trans "Add Event" %}</span>
                </a>
            {% endif %}
            <a href="{% url 'school_calendar:event_list' %}" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" title="{% trans 'List View' %}">
                <i class="bi bi-list-ul"></i>
            </a>
        </div>
    </header>

    <!-- Pure Flexbox Calendar Grid -->
    <main class="calendar-grid">
        <!-- Days of the Week Header -->
        <div class="week-header">
            <div class="week-day-name">{% trans "Sunday" %}</div>
            <div class="week-day-name">{% trans "Monday" %}</div>
            <div class="week-day-name">{% trans "Tuesday" %}</div>
            <div class="week-day-name">{% trans "Wednesday" %}</div>
            <div class="week-day-name">{% trans "Thursday" %}</div>
            <div class="week-day-name">{% trans "Friday" %}</div>
            <div class="week-day-name">{% trans "Saturday" %}</div>
        </div>

        <!-- Days Grid -->
        <div class="days-container">
            {% for week in calendar %}
                {% for day_num in week %}
                    {% get_day_data year month day_num events_by_date today as day %}
                    <div class="day-cell {% if not day.is_valid %}other-month{% endif %} {% if day.is_today %}today{% endif %}">
                        {% if day.is_valid %}
                            <div class="day-header">
                                <span class="day-number">{{ day.day_number }}</span>
                                
                                {% if day.event_count > 0 %}
                                    <span class="event-count">{{ day.event_count }}</span>
                                {% elif perms.school_calendar.add_event %}
                                    <a href="{% url 'school_calendar:admin_event_create' %}?date={{ day.date|date:'Y-m-d' }}" class="add-event-btn" data-bs-toggle="tooltip" title="Add event on {{ day.date|date:'M d' }}">
                                        <i class="bi bi-plus-circle"></i>
                                    </a>
                                {% endif %}
                            </div>
                            <div class="day-events">
                                {% for event in day.events %}
                                    <div class="event-item priority-{{ event.priority|lower }}"
                                         data-url="{% url 'school_calendar:event_detail' event.pk %}"
                                         data-bs-toggle="tooltip" 
                                         title="{{ event.title }}">
                                        {{ event.title }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
    </main>
    
    <!-- Bottom Action Footer -->
    <footer class="calendar-footer">
        <div class="d-flex flex-wrap align-items-center" style="gap: 1rem;">
            <div class="legend-item"><div class="legend-color" style="border: 2px solid #667eea;"></div>{% trans "Today" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #f8d7da;"></div>{% trans "Urgent" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #fff3cd;"></div>{% trans "High" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #d1ecf1;"></div>{% trans "Medium" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #d4edda;"></div>{% trans "Low" %}</div>
        </div>
        <div class="d-flex align-items-center" style="gap: 0.5rem;">
            {% if perms.school_calendar.add_event %}
            <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-gear me-1"></i>{% trans "Manage Events" %}
            </a>
            {% endif %}
        </div>
    </footer>
</div>

{% block page_specific_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, { container: 'body' }); // Use body container to prevent issues
        });

        document.querySelectorAll('.event-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.stopPropagation();
                const url = this.dataset.url;
                if (url) window.location.href = url;
            });
        });

        document.addEventListener('keydown', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            if (e.key === 'ArrowLeft') {
                document.querySelector('a[aria-label*="Previous"]').click();
            } else if (e.key === 'ArrowRight') {
                document.querySelector('a[aria-label*="Next"]').click();
            }
        });
    });
</script>
{% endblock %}

{% endblock body_override %}











{% comment %} {% extends "base.html" %}
{% load static i18n calendar_tags %}

{# We use the special `body_override` block to take full control of the page. #}
{% block body_override %}

{% block page_specific_css %}
<style>
    /* --- Core Full-Page Layout --- */
    html, body {
        overflow: hidden;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa; /* Light grey background */
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    .full-page-calendar {
        display: flex;
        flex-direction: column;
        height: 100vh;
    }

    /* --- Header --- */
    .calendar-header {
        flex-shrink: 0;
        padding: 0.75rem 1.5rem;
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
        z-index: 10;
    }
    .header-actions { display: flex; align-items: center; gap: 0.5rem; min-width: 220px; }
    .header-actions.left { justify-content: flex-start; }
    .header-actions.right { justify-content: flex-end; }
    .calendar-header h1 { font-size: 1.5rem; font-weight: 600; color: #212529; margin: 0; }

    /* --- UPDATED: More Padding, Smaller Boxes --- */
    .calendar-grid {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        padding: 2rem 3rem; /* INCREASED PADDING */
        overflow-y: auto;
    }
    
    /* --- UPDATED: Full, Bold Day Names --- */
    .week-header {
        display: flex;
        flex-shrink: 0;
        gap: 1rem; /* Gap between day names */
    }
    .week-day-name {
        flex: 1 0 0; /* Let flexbox handle sizing */
        text-align: center;
        padding: 0.5rem;
        font-weight: 600; /* BOLDED */
        font-size: 0.9rem;
        color: #343a40;
    }

    /* --- Container for Day Cells --- */
    .days-container {
        flex-grow: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem; /* UPDATED GAP for more space */
    }
    
    /* --- UPDATED: Individual Day Cell Sizing --- */
    .day-cell {
        flex: 1 0 calc(14.28% - 1rem); /* Adjusted for new gap */
        max-width: calc(14.28% - 1rem); /* Prevent growing too large */
        min-height: 110px; /* Reduced min-height */
        position: relative;
        background-color: #ffffff;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        display: flex;
        flex-direction: column;
        transition: all 0.2s ease-in-out;
    }
    .day-cell.other-month { background-color: transparent; border-color: transparent; }
    .day-cell:not(.other-month):hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }
    .day-header { padding: 0.75rem; display: flex; justify-content: space-between; align-items: center; }
    .day-number { font-weight: 500; font-size: 0.9rem; color: #343a40; }
    .day-cell.today .day-number {
        font-weight: 700;
        color: #667eea;
        position: relative;
    }
     .day-cell.today .day-number::after { /* Underline effect for today */
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #667eea;
     }
    
    .add-event-btn { opacity: 0; transform: scale(0.8); transition: all 0.2s ease; color: #6c757d; cursor: pointer; }
    .day-cell:hover .add-event-btn { opacity: 1; transform: scale(1); }
    .add-event-btn:hover { color: #667eea; }

    /* Events inside the cell */
    .day-events { flex-grow: 1; overflow-y: auto; padding: 0 0.5rem 0.5rem; }
    .day-events::-webkit-scrollbar { width: 4px; }
    .day-events::-webkit-scrollbar-thumb { background: #dcdcdc; border-radius: 4px; }
    .event-item {
        background-color: #f0f3ff;
        border-left: 3px solid #667eea;
        padding: 0.3rem 0.6rem;
        margin-bottom: 0.4rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .event-item.priority-urgent { background-color: #ffebee; border-color: #e53935; }
    .event-item.priority-high { background-color: #fff3e0; border-color: #fb8c00; }
    .event-item.priority-medium { background-color: #e3f2fd; border-color: #1e88e5; }
    .event-item.priority-low { background-color: #e8f5e9; border-color: #43a047; }
    
    /* --- Bottom Action Footer --- */
    .calendar-footer {
        flex-shrink: 0;
        padding: 0.75rem 1.5rem;
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(10px);
        border-top: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }
    .legend-item { display: flex; align-items: center; gap: 0.5rem; font-size: 0.8rem; }
    .legend-color { width: 14px; height: 14px; border-radius: 4px; }
</style>
{% endblock %}

<div class="full-page-calendar">
    <!-- Header -->
    <header class="calendar-header">
        <div class="header-actions left">
            <a href="/" class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Back to Dashboard"><i class="bi bi-arrow-left"></i></a>
            <h1 class="h5 mb-0 d-none d-md-block">{{ month_name }} {{ year }}</h1>
        </div>
        <div class="header-actions center">
            <a href="?" class="btn btn-sm btn-outline-secondary">{% trans "Today" %}</a>
            <div class="btn-group btn-group-sm">
                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-secondary" aria-label="{% trans 'Previous month' %}"><i class="bi bi-chevron-left"></i></a>
                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-secondary" aria-label="{% trans 'Next month' %}"><i class="bi bi-chevron-right"></i></a>
            </div>
        </div>
        <div class="header-actions right">
            {% if perms.school_calendar.add_event %}
                <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i><span class="d-none d-md-inline">{% trans "Add Event" %}</span>
                </a>
            {% endif %}
            <a href="{% url 'school_calendar:event_list' %}" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" title="{% trans 'List View' %}">
                <i class="bi bi-list-ul"></i>
            </a>
        </div>
    </header>

    <!-- Pure Flexbox Calendar Grid -->
    <main class="calendar-grid">
        <!-- Days of the Week Header -->
        <div class="week-header">
            <div class="week-day-name">{% trans "Sunday" %}</div>
            <div class="week-day-name">{% trans "Monday" %}</div>
            <div class="week-day-name">{% trans "Tuesday" %}</div>
            <div class="week-day-name">{% trans "Wednesday" %}</div>
            <div class="week-day-name">{% trans "Thursday" %}</div>
            <div class="week-day-name">{% trans "Friday" %}</div>
            <div class="week-day-name">{% trans "Saturday" %}</div>
        </div>

        <!-- Days Grid -->
        <div class="days-container">
            {% for week in calendar %}
                {% for day_num in week %}
                    {% get_day_data year month day_num events_by_date today as day %}
                    <div class="day-cell {% if not day.is_valid %}other-month{% endif %} {% if day.is_today %}today{% endif %}">
                        {% if day.is_valid %}
                            <div class="day-header">
                                <span class="day-number">{{ day.day_number }}</span>
                                {% if perms.school_calendar.add_event %}
                                    <a href="{% url 'school_calendar:admin_event_create' %}?date={{ day.date|date:'Y-m-d' }}" class="add-event-btn" data-bs-toggle="tooltip" title="Add event on {{ day.date|date:'M d' }}">
                                        <i class="bi bi-plus-circle"></i>
                                    </a>
                                {% endif %}
                            </div>
                            <div class="day-events">
                                {% for event in day.events %}
                                    <div class="event-item priority-{{ event.priority|lower }}"
                                         data-url="{% url 'school_calendar:event_detail' event.pk %}"
                                         data-bs-toggle="tooltip" 
                                         title="{{ event.title }}">
                                        {{ event.title }}
                                    </div>
                                {% endfor %}
                                </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
    </main>
    
    <!-- Bottom Action Footer -->
    <footer class="calendar-footer">
        <div class="d-flex flex-wrap align-items-center" style="gap: 1rem;">
            <div class="legend-item"><div class="legend-color" style="background-color: #667eea; border-radius: 50%;"></div>{% trans "Today" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #ffebee;"></div>{% trans "Urgent" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #fff3e0;"></div>{% trans "High" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #e3f2fd;"></div>{% trans "Medium" %}</div>
            <div class="legend-item"><div class="legend-color" style="background-color: #e8f5e9;"></div>{% trans "Low" %}</div>
        </div>
        <div class="d-flex align-items-center" style="gap: 0.5rem;">
            <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-gear me-1"></i>{% trans "Manage Events" %}
            </a>
        </div>
    </footer>
</div>

{% block page_specific_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        document.querySelectorAll('.event-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.stopPropagation();
                const url = this.dataset.url;
                if (url) window.location.href = url;
            });
        });

        document.addEventListener('keydown', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            if (e.key === 'ArrowLeft') {
                document.querySelector('a[aria-label*="Previous"]').click();
            } else if (e.key === 'ArrowRight') {
                document.querySelector('a[aria-label*="Next"]').click();
            }
        });
    });
</script>
{% endblock %}

{% endblock body_override %}


{% endcomment %}










{% comment %} {% extends "base.html" %}
{% load static i18n calendar_tags %}

{% block title %}{% trans "School Calendar" %} - {{ month_name }} {{ year }}{% endblock %}

{% block extra_css %}
<style>
    .calendar-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 0;
        padding: 0;
        width: 100%;
        height: auto;
    }

    .calendar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }

    .calendar-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(-50px) translateY(-50px); }
        100% { transform: translateX(50px) translateY(50px); }
    }

    .calendar-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .calendar-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 2;
    }

    .calendar-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .nav-btn {
        background: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: inline-block;
        margin: 0 0.5rem;
    }

    .nav-btn:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.25);
    }

    .calendar-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 4px;
        background: #f8f9fa;
        table-layout: fixed;
        margin: 0;
        font-size: 1.1rem;
    }

    .calendar-table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem 0.5rem;
        text-align: center;
        font-weight: 700;
        font-size: 1rem;
        color: #495057;
        border: 2px solid #dee2e6;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .calendar-table th::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .calendar-table td {
        height: 180px;
        width: 14.28%;
        vertical-align: top;
        padding: 12px;
        border: 2px solid #dee2e6;
        position: relative;
        background: white;
        transition: all 0.3s ease;
        box-shadow: 0 3px 6px rgba(0,0,0,0.08);
        border-radius: 8px;
    }

    .calendar-table td:hover {
        background: #f8f9ff;
        box-shadow: inset 0 0 0 2px #667eea;
    }

    .calendar-date {
        position: absolute;
        top: 8px;
        left: 8px;
        font-weight: 700;
        font-size: 1.1rem;
        color: #495057;
        z-index: 2;
        min-width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: all 0.3s ease;
        background: #f8f9fa;
        border: 2px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .calendar-date.today {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transform: scale(1.1);
        font-weight: 800;
    }

    .calendar-date.other-month {
        color: #adb5bd;
        opacity: 0.5;
    }

    .calendar-date.has-events {
        background: rgba(102, 126, 234, 0.1);
        border: 2px solid rgba(102, 126, 234, 0.3);
    }

    .calendar-events {
        margin-top: 2.8rem;
        padding: 4px;
        max-height: 120px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #667eea #f1f3f4;
    }

    .calendar-events::-webkit-scrollbar {
        width: 4px;
    }

    .calendar-events::-webkit-scrollbar-track {
        background: #f1f3f4;
        border-radius: 2px;
    }

    .calendar-events::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 2px;
    }

    .event-item {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 2px solid #2196f3;
        border-left: 4px solid #2196f3;
        padding: 8px 10px;
        margin-bottom: 6px;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 3px 6px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
        color: #1976d2;
        text-shadow: none;
        min-height: 32px;
        display: flex;
        align-items: center;
        word-wrap: break-word;
        line-height: 1.3;
    }

    .event-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 100%);
        pointer-events: none;
    }

    .event-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left-width: 6px;
    }

    .event-item.priority-urgent {
        border-color: #e91e63;
        border-left-color: #e91e63;
        background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
        color: #ad1457;
        animation: urgentPulse 2s infinite;
    }

    .event-item.priority-high {
        border-color: #f44336;
        border-left-color: #f44336;
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        color: #c62828;
    }

    .event-item.priority-medium {
        border-color: #ff9800;
        border-left-color: #ff9800;
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        color: #ef6c00;
    }

    .event-item.priority-low {
        border-color: #4caf50;
        border-left-color: #4caf50;
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        color: #2e7d32;
    }

    @keyframes urgentPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
    }

    .event-time {
        font-size: 0.7rem;
        color: #666;
        font-weight: 500;
    }

    .event-title {
        font-weight: 600;
        color: #333;
        display: block;
        margin-top: 0.1rem;
    }
    
    .legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    
    .view-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }



    @media (max-width: 768px) {
        .calendar-table td {
            height: 80px;
            padding: 0.25rem;
        }

        .event-item {
            font-size: 0.65rem;
            padding: 1px 4px;
        }

        .calendar-nav {
            flex-direction: column;
            gap: 1rem;
        }

        .priority-items {
            flex-direction: column;
            align-items: center;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="calendar-container">
        <!-- Calendar Header -->
        <div class="calendar-header">
            <div class="calendar-nav">
                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="nav-btn">
                    <i class="bi bi-chevron-left me-1"></i>{% trans "Previous" %}
                </a>

                <div class="text-center">
                    <h1 class="calendar-title">{{ month_name }} {{ year }}</h1>
                    <p class="calendar-subtitle">{% trans "School Calendar" %}</p>
                </div>

                <a href="?year={{ next_year }}&month={{ next_month }}" class="nav-btn">
                    {% trans "Next" %}<i class="bi bi-chevron-right ms-1"></i>
                </a>
            </div>

            <div class="view-controls">
                {% if can_manage %}
                    <a href="{% url 'school_calendar:admin_event_create' %}" class="nav-btn">
                        <i class="bi bi-plus-circle me-1"></i>{% trans "Add Event" %}
                    </a>
                    <a href="{% url 'school_calendar:admin_event_list' %}" class="nav-btn">
                        <i class="bi bi-gear me-1"></i>{% trans "Manage" %}
                    </a>
                {% endif %}

                <a href="{% url 'school_calendar:event_list' %}" class="nav-btn">
                        <i class="bi bi-list me-1"></i>{% trans "List View" %}
                    </a>
                </div>
            </div>

        </div>

        <!-- Calendar Grid -->
        <div class="p-0">
            <table class="calendar-table">
                <thead>
                    <tr>
                        <th>Sun</th>
                        <th>Mon</th>
                        <th>Tue</th>
                        <th>Wed</th>
                        <th>Thu</th>
                        <th>Fri</th>
                        <th>Sat</th>
                    </tr>
                </thead>
                <tbody>
                    {% for week in calendar %}
                    <tr>
                        {% for day in week %}
                        {% if day != 0 %}
                            {% with year|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d" as current_date_str %}
                            {% with current_date_str|date:"Y-m-d" as date_key %}
                            {% with current_date_str|date:"w" as day_of_week %}
                            <td class="{% if day_of_week == '0' or day_of_week == '6' %}weekend{% endif %}">
                                <div class="calendar-date {% if current_date_str == today|date:'Y-m-d' %}today{% endif %}{% if events_by_date|lookup:date_key %} has-events{% endif %}">
                                    <span class="day-number">{{ day }}</span>
                                    {% with events_by_date|lookup:date_key as day_events %}
                                    {% if day_events|length > 3 %}
                                        <span class="event-count">{{ day_events|length }}</span>
                                    {% endif %}
                                    {% endwith %}
                                </div>

                                <!-- Events for this day -->
                                <div class="calendar-events">
                                    {% for event in events_by_date|lookup:date_key %}
                                    <div class="event-item priority-{{ event.priority|lower }}"
                                        onclick="window.location.href='{% url 'school_calendar:event_detail' event.pk %}'"
                                        title="{{ event.title }} - {{ event.description|truncatewords:10 }}">
                                        {% if not event.is_all_day %}
                                        <div class="event-time">{{ event.start_time|time:"g:i A" }}</div>
                                        {% endif %}
                                        <div class="event-title">{{ event.title|truncatewords:3 }}</div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </td>
                            {% endwith %}
                            {% endwith %}
                            {% endwith %}
                        {% else %}
                            <td class="other-month"></td>
                        {% endif %}
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Calendar Legend -->
        <div class="calendar-legend">
            <h6 class="mb-3">
                <i class="bi bi-info-circle me-2"></i>
                {% trans "Legend" %}
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                        <span>{% trans "Today" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #e8f5e8;"></div>
                        <span>{% trans "Low Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #fff3e0;"></div>
                        <span>{% trans "Medium Priority" %}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ffebee;"></div>
                        <span>{% trans "High Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #fce4ec;"></div>
                        <span>{% trans "Urgent Priority" %}</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f5f5f5;"></div>
                        <span>{% trans "Weekend" %}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h6 class="mb-3">
                <i class="bi bi-lightning-charge me-2"></i>
                {% trans "Quick Actions" %}
            </h6>
            <div class="d-flex flex-wrap">
                {% if can_manage %}
                <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-primary action-btn">
                    <i class="bi bi-plus-circle me-1"></i>{% trans "Create Event" %}
                </a>
                <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-outline-primary action-btn">
                    <i class="bi bi-gear me-1"></i>{% trans "Manage Events" %}
                </a>
                {% endif %}
                <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-secondary action-btn">
                    <i class="bi bi-list me-1"></i>{% trans "List View" %}
                </a>
                <a href="?" class="btn btn-outline-info action-btn">
                    <i class="bi bi-arrow-clockwise me-1"></i>{% trans "Current Month" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for event items
    const eventItems = document.querySelectorAll('.event-item');
    eventItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.stopPropagation();
            // The onclick is already handled in the template
        });
    });

    // Add hover effects for calendar dates
    const calendarDates = document.querySelectorAll('.calendar-date');
    calendarDates.forEach(date => {
        date.addEventListener('mouseenter', function() {
            if (!this.classList.contains('today')) {
                this.style.background = 'rgba(102, 126, 234, 0.1)';
                this.style.borderRadius = '8px';
            }
        });

        date.addEventListener('mouseleave', function() {
            if (!this.classList.contains('today')) {
                this.style.background = '';
                this.style.borderRadius = '';
            }
        });
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            const prevBtn = document.querySelector('a[href*="prev"]');
            if (prevBtn) window.location.href = prevBtn.href;
        } else if (e.key === 'ArrowRight') {
            const nextBtn = document.querySelector('a[href*="next"]');
            if (nextBtn) window.location.href = nextBtn.href;
        }
    });
});
</script>
{% endblock %}
 {% endcomment %}

{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\student_payment_history.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static humanize i18n %}

{% block parent_portal_page_title %}{{ view_title }}{% endblock parent_portal_page_title %}

{% block parent_portal_main_content %}
<div class="container mt-4">

    {# Page Header with Breadcrumbs #}
    <div class="row mb-4 align-items-center">
        <div class="col">
            <h1 class="display-6">{{ view_title }}</h1>
            {% if student %}
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">{% trans "Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'parent_portal:children_fees_summary' %}">{% trans "Children's Fees" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}">{{ student.get_full_name }}'s Fees</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{% trans "Payment History" %}</li>
                </ol>
            </nav>
            {% endif %}
        </div>
    </div>

    {# Display messages if any #}
    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-receipt-cutoff me-2"></i>{% trans "Completed Payments Record" %}</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Payment Date" %}</th>
                            <th>{% trans "Amount" %}</th>
                            <th>{% trans "Method" %}</th>
                            <th>{% trans "Allocated To Invoice(s)" %}</th>
                            <th>{% trans "Notes / Reference" %}</th>
                            <th class="text-center">{% trans "Receipt" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {# The view passes the list as 'payment_list' #}
                        {% for payment in payment_list %}
                            <tr>
                                <td>{{ payment.payment_date|date:"F d, Y" }}</td>
                                <td class="fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ payment.amount|floatformat:2|intcomma }}</td>
                                <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                                <td>
                                    {# Loop through the invoices this payment was allocated to #}
                                    {% for allocation in payment.allocations.all %}
                                        <a href="{% url 'fees:invoice_detail' pk=allocation.invoice.pk %}">#{{ allocation.invoice.invoice_number_display }}</a><br>
                                    {% empty %}
                                        <span class="text-muted">{% trans "Not allocated" %}</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    {% if payment.reference_number %}
                                        <strong>Ref:</strong> {{ payment.reference_number }}<br>
                                    {% endif %}
                                    {% if payment.notes %}
                                        <small class="text-muted">{{ payment.notes|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <a href="{% url 'payments:payment_receipt_pdf_public' pk=payment.pk %}" target="_blank" class="btn btn-sm btn-outline-primary" title="{% trans 'View Receipt' %}">
                                        <i class="bi bi-receipt"></i>
                                    </a>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center p-5">
                                    <h4 class="text-muted">{% trans "No Payment History" %}</h4>
                                    <p class="mb-0">{% trans "There are no completed payments on record for this student." %}</p>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if is_paginated %}
            <div class="card-footer bg-light">
                {% include "partials/_pagination.html" %} {# Assuming you have a pagination partial #}
            </div>
        {% endif %}
    </div>

</div>
{% endblock parent_portal_main_content %}


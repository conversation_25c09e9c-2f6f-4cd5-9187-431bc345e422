{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}School Fees SaaS{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{% static 'css/custom_base_styles.css' %}">
    
    {% block page_specific_css %}
    {# This block is now INSIDE head, where it belongs #}
    {% endblock %}
</head>
<body>

    {# --- THIS IS THE NEW, CRUCIAL BLOCK --- #}
    {% block body_override %}

        {# DEFAULT LAYOUT: This will only render if a child template does NOT override this block. #}
        {% block navbar %}
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-navbar">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">Platform Name</a>
                </div>
            </nav>
        {% endblock navbar %}

        <main role="main" class="container-fluid py-3">
            {% block content %}
            <p>Base content - this should be overridden.</p>
            {% endblock content %}
        </main>

        {% block footer %}
            <footer class="footer mt-auto py-3 bg-secondary text-white fixed-footer">
                <div class="container text-center">
                    <span>© {% now "Y" %} Total Systems & Technologies Solutions SA (Pty) Ltd - All rights reserved.</span>
                </div>
            </footer>
        {% endblock footer %}

    {% endblock body_override %}


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block page_specific_js %}
    {# This block is now at the END of the body, where it belongs #}
    {% endblock %}
</body>
</html>

















{% comment %} {% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}School Fees SaaS{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{% static 'css/custom_base_styles.css' %}">
    {% block page_specific_css %}{% endblock %}
</head>
<body>
    {% block navbar %}
        {# Default navbar if not overridden by public_base or tenant_base #}
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-navbar">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">Platform Name</a>
            </div>
        </nav>
    {% endblock navbar %}

    <main role="main" class="container-fluid py-3"> {# Main content area #}
        {% block content %}
        <p>Base content - this should be overridden.</p>
        {% endblock content %}
    </main>

    {% block footer %}
        <footer class="footer mt-auto py-3 bg-secondary text-white fixed-footer">
            <div class="container text-center">
                <span>© {% now "Y" %} Total Systems & Technologies Solutions SA (Pty) Ltd - All rights reserved.</span>
            </div>
        </footer>
    {% endblock footer %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block page_specific_js %}{% endblock %}
</body>
</html> {% endcomment %}


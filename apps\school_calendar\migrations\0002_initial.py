# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('school_calendar', '0001_initial'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='schoolevent',
            name='created_by_staff',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_events', to='schools.staffuser', verbose_name='Created By Staff'),
        ),
        migrations.AddField(
            model_name='eventattendee',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendees', to='school_calendar.schoolevent'),
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['start_date', 'end_date'], name='school_cale_start_d_c27ace_idx'),
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['event_type'], name='school_cale_event_t_5c7941_idx'),
        ),
        migrations.AddIndex(
            model_name='schoolevent',
            index=models.Index(fields=['is_public', 'is_active'], name='school_cale_is_publ_162aa9_idx'),
        ),
    ]

{# templates/announcements/platformannouncement_detail.html #}
{% extends "announcements/_platform_announcement_base.html" %}
{% load humanize %}

{% block announcement_page_title %}{{ announcement.title }}{% endblock %}

{% block announcement_content %}
<div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5>{{ announcement.title }}</h5>
        <div>
            <a href="{% url 'announcements:platform_announcement_update' pk=announcement.pk %}" class="btn btn-sm btn-secondary"><i class="bi bi-pencil-square me-1"></i> Edit</a>
            <a href="{% url 'announcements:platform_announcement_delete' pk=announcement.pk %}" class="btn btn-sm btn-danger"><i class="bi bi-trash me-1"></i> Delete</a>
        </div>
    </div>
    <div class="card-body">
        <p><strong>Status:</strong> 
            {% if announcement.is_currently_visible %}
                <span class="badge bg-success">Currently Visible</span>
            {% elif announcement.is_published and announcement.publish_date > now %}
                <span class="badge bg-warning text-dark">Scheduled ({{ announcement.publish_date|naturaltime }})</span>
            {% elif not announcement.is_published %}
                <span class="badge bg-secondary">Draft</span>
            {% elif announcement.expiry_date and announcement.expiry_date < now %}
                <span class="badge bg-danger">Expired</span>
            {% endif %}
        </p>
        <p><strong>Publish Date:</strong> {{ announcement.publish_date|date:"F j, Y, P" }}</p>
        <p><strong>Expiry Date:</strong> {{ announcement.expiry_date|date:"F j, Y, P"|default:"Does not expire" }}</p>
        <p><strong>Author:</strong> {{ announcement.author.get_full_name|default:announcement.author.email|default:"N/A" }}</p>
        <p><strong>Last Updated:</strong> {{ announcement.updated_at|naturaltime }}</p>
        <hr>
        <div class="announcement-content">
            {{ announcement.content|linebreaksbr }}
        </div>
    </div>
    <div class="card-footer">
        <a href="{% url 'announcements:platform_announcement_list' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left"></i> Back to List</a>
    </div>
</div>
{% endblock %}



from django.urls import path
from . import views

app_name = 'school_calendar'

urlpatterns = [
    # Public Calendar Views
    
    # path('dashboard/', views.dashboard_view, name='dashboard'),
    
    path('', views.CalendarView.as_view(), name='calendar'),
    path('events/', views.EventListView.as_view(), name='event_list'),
    path('events/<int:pk>/', views.EventDetailView.as_view(), name='event_detail'),
    
    # RSVP
    path('events/<int:pk>/rsvp/', views.rsvp_event, name='rsvp_event'),
    
    # API endpoints
    path('api/events.json', views.events_json, name='events_json'),
    
    # Admin Views (Staff Only)
    path('admin/', views.AdminEventListView.as_view(), name='admin_event_list'),
    path('admin/events/create/', views.EventCreateView.as_view(), name='admin_event_create'),
    path('admin/events/<int:pk>/edit/', views.EventUpdateView.as_view(), name='admin_event_edit'),
    path('admin/events/<int:pk>/delete/', views.EventDeleteView.as_view(), name='admin_event_delete'),
]

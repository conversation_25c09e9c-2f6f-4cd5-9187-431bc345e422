# D:\school_fees_saas_v2\apps\announcements\urls.py
from django.urls import path
from . import views

app_name = 'announcements'

urlpatterns = [
    path('', views.AnnouncementListView.as_view(), name='announcement_list'),
    path('create/', views.AnnouncementCreateView.as_view(), name='create'),
    path('<int:pk>/', views.AnnouncementDetailView.as_view(), name='announcement_detail'),
    path('<int:pk>/update/', views.AnnouncementUpdateView.as_view(), name='update'),
    path('<int:pk>/delete/', views.AnnouncementDeleteView.as_view(), name='delete'),
    
    
    # path('', views.TenantAnnouncementListView.as_view(), name='tenant_announcement_list'),
    # path('<int:pk>/', views.TenantAnnouncementDetailView.as_view(), name='tenant_announcement_detail'),
]


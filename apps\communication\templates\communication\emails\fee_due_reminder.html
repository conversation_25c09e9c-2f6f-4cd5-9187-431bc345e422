{# apps/communication/templates/communication/emails/fee_due_reminder.html #}
{% load i18n humanize %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% blocktrans with school_name=school_name %}<PERSON><PERSON> from {{ school_name }}{% endblocktrans %}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { width: 100%; max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
        .header { background-color: {{ school_primary_color|default:'#007bff' }}; color: white; padding: 10px 20px; text-align: center; border-top-left-radius: 5px; border-top-right-radius: 5px;}
        .header h1 { margin: 0; font-size: 24px; }
        .content { padding: 20px; background-color: #fff; }
        .content p { margin-bottom: 15px; }
        .footer { text-align: center; padding: 15px; font-size: 0.9em; color: #777; }
        .button { display: inline-block; padding: 10px 20px; margin-top: 15px; background-color: {{ school_secondary_color|default:'#28a745' }}; color: white; text-decoration: none; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header" {% if school_logo_url %}style="padding: 20px;"{% endif %}>
            {% if school_logo_url %}
                <img src="{{ school_logo_url }}" alt="{{ school_name }} Logo" style="max-height: 70px; margin-bottom: 10px;">
            {% else %}
                <h1>{{ school_name }}</h1>
            {% endif %}
        </div>
        <div class="content">
            <p>{% blocktrans with parent_name=parent_name %}Dear {{ parent_name }},{% endblocktrans %}</p>
            
            <p>{% blocktrans %}This is a friendly reminder regarding outstanding school fees for your child/children at {{ school_name }}.{% endblocktrans %}</p>

            {% if student_specific_fees %}
                {% for item in student_specific_fees %}
                    <h4>{% blocktrans with student_name=item.student_name %}Fees for {{ student_name }}:{% endblocktrans %}</h4>
                    {% if item.invoices %}
                        <p>{% trans "The following invoice(s) are currently outstanding:" %}</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>{% trans "Invoice No." %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Amount Due" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in item.invoices %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>{{ invoice.due_date|date:"d M Y" }}</td>
                                    <td>{{ school_currency_symbol }}{{ invoice.balance_due|floatformat:2|intcomma }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        <p><strong>{% blocktrans with currency_symbol=school_currency_symbol student_total_due=item.total_due|floatformat:2|intcomma %}Total outstanding for {{ item.student_name }}: {{ currency_symbol }}{{ student_total_due }}{% endblocktrans %}</strong></p>
                    {% else %}
                        <p>{% trans "No specific outstanding invoices found for this student at the moment." %}</p>
                    {% endif %}
                    <hr>
                {% endfor %}
            {% endif %}

            {% if overall_total_due > 0 %}
                <p><strong>{% blocktrans with currency_symbol=school_currency_symbol total_due=overall_total_due|floatformat:2|intcomma %}Your total outstanding amount is: {{ currency_symbol }}{{ total_due }}{% endblocktrans %}</strong></p>
            {% endif %}

            <p>{% trans "Please make the payment at your earliest convenience to avoid any late fees or service interruptions." %}</p>
            
            {% if payment_portal_url %}
                <p>{% trans "You can view details and make payments through the parent portal:" %}</p>
                <p style="text-align: center;">
                    <a href="{{ payment_portal_url }}" class="button">{% trans "Go to Parent Portal" %}</a>
                </p>
            {% endif %}

            <p>{% blocktrans %}If you have already made the payment, please disregard this reminder. If you have any questions or believe this is an error, please contact the school office.{% endblocktrans %}</p>
            
            <p>{% trans "Thank you," %}<br>
            {% blocktrans with school_name=school_name %}The {{ school_name }} Administration{% endblocktrans %}</p>
        </div>
        <div class="footer">
            <p>{{ school_name }}<br>
            {{ school_address|default:"" }}<br>
            {{ school_contact_info|default:"" }}</p>
            <p>© {% now "Y" %} {{ school_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>


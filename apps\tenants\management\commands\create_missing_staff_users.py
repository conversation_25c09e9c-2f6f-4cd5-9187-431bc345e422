# apps/tenants/management/commands/create_missing_staff_users.py

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.schools.models import StaffUser
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create missing StaffUser records for tenant owners'

    def add_arguments(self, parser):
        parser.add_argument(
            '--schema',
            type=str,
            help='Specific tenant schema to process (optional, processes all if not specified)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything'
        )

    def handle(self, *args, **options):
        schema_name = options.get('schema')
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get tenants to process
        if schema_name:
            try:
                tenants = [School.objects.get(schema_name=schema_name)]
            except School.DoesNotExist:
                raise CommandError(f'Tenant with schema "{schema_name}" does not exist')
        else:
            tenants = School.objects.exclude(schema_name='public').filter(owner__isnull=False)
        
        self.stdout.write(f'Processing {len(tenants)} tenant(s)...')
        
        for tenant in tenants:
            self.stdout.write(f'\n--- Processing tenant: {tenant.schema_name} ({tenant.name}) ---')
            
            if not tenant.owner:
                self.stdout.write(self.style.ERROR(f'Tenant {tenant.schema_name} has no owner. Skipping.'))
                continue
            
            owner = tenant.owner
            self.stdout.write(f'Owner: {owner.email} ({owner.first_name} {owner.last_name})')
            
            # Check if StaffUser already exists
            with schema_context(tenant.schema_name):
                try:
                    existing_staff = StaffUser.objects.filter(email__iexact=owner.email).first()
                    
                    if existing_staff:
                        self.stdout.write(self.style.SUCCESS(f'StaffUser already exists: {existing_staff.email} (ID: {existing_staff.pk})'))
                        
                        # Check if it's marked as owner profile
                        if hasattr(existing_staff, 'is_owner_profile') and not existing_staff.is_owner_profile:
                            if not dry_run:
                                existing_staff.is_owner_profile = True
                                existing_staff.save(update_fields=['is_owner_profile'])
                                self.stdout.write(self.style.SUCCESS(f'Updated is_owner_profile to True for {existing_staff.email}'))
                            else:
                                self.stdout.write(self.style.WARNING(f'Would update is_owner_profile to True for {existing_staff.email}'))
                    else:
                        # Create StaffUser
                        self.stdout.write(self.style.WARNING(f'StaffUser missing for owner {owner.email}. Creating...'))
                        
                        staff_user_defaults = {
                            'first_name': owner.first_name or 'School',
                            'last_name': owner.last_name or 'Admin',
                            'is_staff': True,
                            'is_active': True,
                            'is_superuser': True,
                            'employee_id': f'OWNER-{owner.pk}',
                            'designation': 'School Owner/Director',
                        }
                        
                        # Add is_owner_profile if field exists
                        if hasattr(StaffUser, 'is_owner_profile'):
                            staff_user_defaults['is_owner_profile'] = True
                        
                        if not dry_run:
                            staff_user = StaffUser.objects.create(
                                email=owner.email,
                                **staff_user_defaults
                            )
                            staff_user.set_unusable_password()  # Owner uses their platform password
                            staff_user.save()
                            
                            self.stdout.write(self.style.SUCCESS(f'Created StaffUser: {staff_user.email} (ID: {staff_user.pk})'))
                            
                            # Create or assign to admin group
                            admin_group_name = "School Administrators"
                            admin_group, group_created = Group.objects.get_or_create(name=admin_group_name)
                            staff_user.groups.add(admin_group)
                            
                            if group_created:
                                self.stdout.write(self.style.SUCCESS(f'Created admin group: {admin_group_name}'))
                            else:
                                self.stdout.write(f'Added to existing admin group: {admin_group_name}')
                        else:
                            self.stdout.write(self.style.WARNING(f'Would create StaffUser for {owner.email} with defaults: {staff_user_defaults}'))
                
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing tenant {tenant.schema_name}: {str(e)}'))
                    logger.exception(f'Error creating StaffUser for tenant {tenant.schema_name}')
        
        self.stdout.write(self.style.SUCCESS('\nProcessing complete!'))

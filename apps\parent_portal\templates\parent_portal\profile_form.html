{% extends "parent_portal/parent_portal_base.html" %}
{% load static widget_tweaks humanize %}

{% block parent_portal_page_title %}{{ view_title|default:"Edit My Profile" }}{% endblock %}

{% block parent_portal_extra_css %}
    {{ block.super }}
    {# Assuming tenant_form_styles.css provides basic form styling if not using Bootstrap's default #}
    {# <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}"> #}
    <style>
        .profile-form-card {
            max-width: 700px; /* Or col-lg-8 for Bootstrap consistency */
            margin: 2rem auto; /* Center card */
        }
        /* Add any specific styling needed for this form if different from record_payment_form */
    </style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-md-4">
    <div class="row justify-content-center">
        <div class="col-lg-8"> {# Consistent width #}
            
            {# Breadcrumb from your original snippet - good for navigation #}
            <div class="pagetitle mb-3">
                <h1 class="h2">{{ view_title|default:"Edit My Profile" }}</h1>
                <nav>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'parent_portal:profile_display' %}">My Profile</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </nav>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm profile-form-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Update Your Information</h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate> {# novalidate for custom/server-side validation focus #}
                        {% csrf_token %}
                        
                        {# Render form fields systematically #}
                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger py-2">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        {# Example: Assuming ParentProfileUpdateForm has fields like first_name, last_name, phone_number #}
                        {# You might group them or render them all #}
                        
                        {% for field in form.visible_fields %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">
                                    {{ field.label }}{% if field.field.required %}<span class="text-danger ms-1">*</span>{% endif %}
                                </label>
                                {% if field.name == 'old_password' or field.name == 'new_password1' or field.name == 'new_password2' %}
                                    {% render_field field class+="form-control" type="password" %}
                                {% else %}
                                    {% render_field field class+="form-control" %}
                                {% endif %}
                                {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text|safe }}</small>
                                {% endif %}
                                {% for error in field.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endfor %}
                        
                        <div class="mt-4 d-flex justify-content-between align-items-center">
                            <a href="{% url 'parent_portal:profile_display' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success px-4">
                                <i class="bi bi-check-circle-fill me-1"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock parent_portal_main_content %}

















{% comment %} {% extends "tenant_base.html" %} {# Or "parent_base.html" #}
{% load static %}
{% load crispy_forms_tags %} {# If you are using crispy-forms, otherwise remove #}

{% block title %}
    {{ view_title|default:"Edit Profile" }} - {{ request.tenant.name|default:"Parent Portal" }}
{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}"> {# Your teal label styles #}
    <style>
        .profile-form-card {
            max-width: 700px;
            margin: 2rem auto;
        }
    </style>
{% endblock page_specific_css %}

{% block content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>{{ view_title|default:"Edit Profile" }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'parent_portal:profile_display' %}">My Profile</a></li>
                <li class="breadcrumb-item active">Edit Profile</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow-sm profile-form-card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Update Your Information</h5>
        </div>
        <div class="card-body p-4">
            <form method="post" novalidate>
                {% csrf_token %}
                
                {# If using crispy-forms: #}
                {# {{ form|crispy }} #}

                {# Manual rendering (example): #}
                {% for field in form %}
                    <div class="mb-3">
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field }}
                        {% if field.help_text %}
                            <small id="{{ field.id_for_label }}_help_text" class="form-text text-muted helptext">{{ field.help_text }}</small>
                        {% endif %}
                        {% for error in field.errors %}
                            <div class="invalid-feedback d-block">
                                {{ error }}
                            </div>
                        {% endfor %}
                    </div>
                {% endfor %}
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="{% url 'parent_portal:profile_display' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle-fill"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock content %} {% endcomment %}





# D:\school_fees_saas_v2\apps\common\widgets.py
from django import forms

class DatePickerInput(forms.DateInput):
    input_type = 'date'
    def __init__(self, attrs=None, format=None):
        final_attrs = {'class': 'form-control'} # Default class
        if attrs:
            final_attrs.update(attrs)
        super().__init__(attrs=final_attrs, format=format)

class DateTimePickerInput(forms.DateTimeInput):
    input_type = 'datetime-local'
    def __init__(self, attrs=None, format=None):
        # HTML5 datetime-local format is 'YYYY-MM-DDTHH:MM'
        # Django's default for DateTimeField might be different,
        # so ensure your form field handles parsing correctly or specify format.
        # Example: self.format = '%Y-%m-%dT%H:%M'
        final_attrs = {'class': 'form-control'} # Default class
        if attrs:
            final_attrs.update(attrs)
        super().__init__(attrs=final_attrs, format=format if format else '%Y-%m-%dT%H:%M')

# If you had other custom widgets, define them here too.
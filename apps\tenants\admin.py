# D:\school_fees_saas_v2\apps\tenants\admin.py
from django.contrib import admin
from django_tenants.admin import TenantAdminMixin 
from .models import School, Domain # Assuming your tenant model is School
from django.utils.translation import gettext_lazy as _

# D:\school_fees_saas_v2\apps\tenants\admin.py

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django_tenants.admin import TenantAdminMixin

from .models import School, Domain # Import your tenant models

@admin.register(School)
class SchoolAdmin(TenantAdminMixin, admin.ModelAdmin):
    """
    Admin interface for managing School (Tenant) objects in the public schema.
    """
    # --- List Display Configuration ---
    list_display = (
        'name', 
        'schema_name', 
        'owner_link', 
        'is_active',
        'subscription_status_colored', # Using a method that adds color
        'subscription_period_end',     # Using a method for clearer date display
        'created_on'
    )
    list_filter = (
        'is_active',
        'subscription__status',
        'subscription__plan',
        'created_on',
    )
    search_fields = ('name', 'schema_name', 'owner__email')
    ordering = ('-created_on',)
    
    # Performance optimization: pre-fetches related models in a single query
    list_select_related = ('owner', 'subscription', 'subscription__plan')

    # --- Form Layout (fieldsets) ---
    # Organizes the fields in the add/change form for better usability.
    fieldsets = (
        (_("Core Tenant Details"), {
            'fields': ('name', 'schema_name', 'is_active')
        }),
        (_("Ownership"), {
            'fields': ('owner',)
        }),
        (_("Timestamps"), {
            'classes': ('collapse',), # Collapsed by default
            'fields': ('created_on',),
        }),
    )
    
    # Make owner searchable for easier assignment
    autocomplete_fields = ['owner']
    
    # Read-only fields in the form view
    readonly_fields = ('created_on',)

    # --- Custom Display Methods for List View ---
    
    @admin.display(description=_('Owner'), ordering='owner__email')
    def owner_link(self, obj):
        """
        Provides a clickable link to the owner's admin change page.
        """
        if obj.owner:
            # Assumes your owner model is registered in an app called 'users'
            # with a model name like 'schoolowner'. Adjust if needed.
            url = reverse(f"admin:{obj.owner._meta.app_label}_{obj.owner._meta.model_name}_change", args=[obj.owner.pk])
            return format_html('<a href="{}">{}</a>', url, obj.owner.email)
        return _("No Owner Assigned")

    @admin.display(description=_('Subscription Status'), ordering='subscription__status')
    def subscription_status_colored(self, obj):
        """
        Displays the subscription status with a colored bootstrap badge.
        """
        if hasattr(obj, 'subscription') and obj.subscription:
            status = obj.subscription.status
            if status in ['ACTIVE', 'TRIALING']:
                color_class = 'bg-success'
            elif status in ['PAST_DUE', 'UNPAID']:
                color_class = 'bg-warning text-dark'
            elif status in ['CANCELLED', 'INCOMPLETE_EXPIRED']:
                color_class = 'bg-danger'
            else:
                color_class = 'bg-secondary'
            
            return format_html('<span class="badge {}">{}</span>', color_class, obj.subscription.get_status_display())
        return format_html('<span class="badge bg-dark">{}</span>', "No Subscription")

    @admin.display(description=_('Period/Trial Ends'), ordering='subscription__current_period_end')
    def subscription_period_end(self, obj):
        """
        Displays the relevant end date (trial or current period).
        """
        if hasattr(obj, 'subscription') and obj.subscription:
            if obj.subscription.status == 'TRIALING' and obj.subscription.trial_end_date:
                return obj.subscription.trial_end_date
            elif obj.subscription.current_period_end:
                return obj.subscription.current_period_end
        return _("N/A")


@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    """
    Admin interface for managing tenant domains.
    """
    list_display = ('domain', 'tenant', 'is_primary')
    list_filter = ('is_primary', 'tenant')
    search_fields = ('domain', 'tenant__name')
    autocomplete_fields = ['tenant']
    ordering = ('tenant', 'domain')



# @admin.register(School)
# class SchoolAdmin(TenantAdminMixin, admin.ModelAdmin): 
#     list_display = (
#         'name', 
#         'schema_name', 
#         'owner_display', 
#         'is_active',     
#         'get_subscription_status_display', 
#         'get_subscription_ends_display', 
#         'created_on'
#     )
#     search_fields = ('name', 'schema_name', 'owner__email') 
#     list_filter = (
#         'is_active',                    
#         'subscription__status',         
#         'subscription__plan',           
#         'subscription__trial_end_date', 
#         'subscription__current_period_end', 
#         'created_on',                   
#         'owner',                        
#     )
#     list_select_related = ('owner', 'subscription', 'subscription__plan') 
#     ordering = ('name',)

#     @admin.display(description=_('Owner'), ordering='owner__email')
#     def owner_display(self, obj):
#         if obj.owner:
#             return obj.owner.email 
#         return None

#     @admin.display(description=_('Subscription Status'), ordering='subscription__status')
#     def get_subscription_status_display(self, obj):
#         if hasattr(obj, 'subscription') and obj.subscription:
#             return obj.subscription.get_status_display()
#         return "No Subscription"

#     @admin.display(description=_('Trial/Period Ends'), ordering='subscription__current_period_end')
#     def get_subscription_ends_display(self, obj):
#         if hasattr(obj, 'subscription') and obj.subscription:
#             if obj.subscription.status == 'TRIALING' and obj.subscription.trial_end_date:
#                 return obj.subscription.trial_end_date.strftime("%Y-%m-%d")
#             elif obj.subscription.current_period_end: 
#                 return obj.subscription.current_period_end.strftime("%Y-%m-%d")
#         return "N/A"


# @admin.register(Domain)
# class DomainAdmin(admin.ModelAdmin):
    list_display = ('domain', 'tenant_name_display', 'is_primary') # Removed 'is_fallback'
    search_fields = ('domain', 'tenant__schema_name', 'tenant__name')
    list_filter = (
        'is_primary', # Removed 'is_fallback'
        'tenant', 
    )
    list_select_related = ('tenant',)
    ordering = ('tenant__name', 'domain')

    @admin.display(description=_('Tenant'), ordering='tenant__name')
    def tenant_name_display(self, obj):
        return obj.tenant.name if obj.tenant else None
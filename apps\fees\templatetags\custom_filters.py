# apps/fees/templatetags/custom_filters.py
from django import template

register = template.Library()

@register.filter
def class_name(obj):
    """
    Return the class name of an object
    Usage: {{ object|class_name }}
    """
    if obj is None:
        return ''
    return obj.__class__.__name__

@register.filter
def verbose_name(obj):
    """
    Return the verbose name of a model instance
    Usage: {{ object|verbose_name }}
    """
    if obj is None:
        return ''
    return obj._meta.verbose_name

@register.filter
def verbose_name_plural(obj):
    """
    Return the verbose name plural of a model instance
    Usage: {{ object|verbose_name_plural }}
    """
    if obj is None:
        return ''
    return obj._meta.verbose_name_plural



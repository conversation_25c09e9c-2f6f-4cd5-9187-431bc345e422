# D:\school_fees_saas_v2\apps\common\templatetags\core_tags.py
from django import template
from django.urls import reverse, NoReverseMatch
from django.contrib.auth.models import Group
from django.utils.safestring import mark_safe
from decimal import Decimal, InvalidOperation
from django.conf import settings

register = template.Library()

# Example: A simple tag (add your actual tags here later)
@register.simple_tag
def my_simple_test_tag():
    return "Core Tags Loaded!"



@register.filter(name='has_group')
def has_group(user, group_name):
    """
    Checks if a user belongs to a specific group.
    Usage: {% if request.user|has_group:"GroupName" %}
    """
    if not user.is_authenticated:
        return False
    try:
        # For StaffUser, groups are directly on the user object
        # For default User, groups are also directly on the user object
        group = Group.objects.get(name=group_name)
        return group in user.groups.all()
    except Group.DoesNotExist:
        return False
    except AttributeError: # If user object somehow doesn't have .groups (e.g., Anony<PERSON><PERSON>ser if not checked)
        return False



@register.filter(name='add_class')
def add_class(field_widget_tag, css_class):
    """
    Adds a CSS class to a Django form field's widget.
    Assumes field_widget_tag is the rendered widget tag string.
    More robust version handles different widget types.
    """
    # A simple way: find '>' and insert class. This might be fragile.
    # A better way is to parse the widget's attrs.
    # For CheckboxInput, the class should often be on the label or a wrapper.
    # However, for basic input, this might work if the widget is just an <input ...>
    
    # More robust method for most input types:
    parts = field_widget_tag.split(' ', 1)
    attrs_str = ''
    if len(parts) > 1:
        attrs_str = parts[1][:-1] # Remove trailing '>'

    current_classes = []
    import re
    class_match = re.search(r'class="([^"]*)"', attrs_str)
    if class_match:
        current_classes = class_match.group(1).split()
        # Remove existing class attribute to avoid duplicates
        attrs_str = re.sub(r'class="[^"]*"', '', attrs_str).strip()
    
    for cls in css_class.split():
        if cls not in current_classes:
            current_classes.append(cls)
    
    new_class_attr = 'class="%s"' % ' '.join(current_classes)
    
    if attrs_str:
        return f'{parts[0]} {new_class_attr} {attrs_str}>'
    else:
        return f'{parts[0]} {new_class_attr}>'
    


@register.simple_tag(takes_context=True)
def active_nav_link(context, view_name, link_text, nav_item_class="nav-item", link_class="nav-link", icon_class=None):
    request = context.get('request')
    active = ""
    url = "#" # Default if URL cannot be reversed
    try:
        path = reverse(view_name)
        if request and request.path == path:
            active = "active"
        url = path
    except NoReverseMatch:
        # Optionally log this error or handle it
        print(f"Warning: NoReverseMatch for view_name '{view_name}' in active_nav_link tag.")
        pass # Keep url as '#'

    icon_html = f'<i class="{icon_class} me-1"></i>' if icon_class else ""

    # Mark safe because we are constructing HTML
    return mark_safe(f'<li class="{nav_item_class}"><a class="{link_class} {active}" href="{url}">{icon_html}{link_text}</a></li>')


# @register.simple_tag(takes_context=True)
# def active_nav_link(context, view_name, link_text, css_class="nav-link", icon_class=None):
#     request = context.get('request')
#     active_class = ""
#     if request:
#         try:
#             path = reverse(view_name)
#             if request.path == path:
#                 active_class = "active" # Bootstrap's active class
#         except NoReverseMatch:
#             pass # URL name might not exist, don't make it active
    
#     # Construct the HTML for the link
#     # Ensure view_name can be reversed, otherwise handle error or make link '#'
#     try:
#         url = reverse(view_name)
#     except NoReverseMatch:
#         url = "#" # Fallback URL
#         print(f"Warning: NoReverseMatch for '{view_name}' in active_nav_link tag.")

#     return mark_safe(f'<li class="nav-item"><a class="{css_class} {active_class}" href="{url}">{link_text}</a></li>')



@register.filter
def get_permission_by_id(permission_list, perm_id):
    """
    Helper to find a permission object in a list by its ID.
    Used in assign_permissions_to_group.html to match form choices to grouped perms.
    """
    perm_id = int(perm_id) # Ensure ID is an integer for comparison
    for perm in permission_list:
        if perm.pk == perm_id:
            return perm
    return None



@register.filter(name='currency')
def currency_format(value, symbol=None):
    """
    Formats a decimal value as currency.
    Adds commas and two decimal places.
    Prepends a currency symbol.
    Usage: {{ some_value|currency }} or {{ some_value|currency:"€" }}
    """
    try:
        # Ensure value is a Decimal
        if value is None:
            value = Decimal('0.00')
        if not isinstance(value, Decimal):
            value = Decimal(str(value)) # Convert if it's float or string

        # Format with commas and two decimal places
        # Using f-string formatting for decimals to ensure trailing zeros
        formatted_value = "{:,.2f}".format(value) # This will use intcomma logic via format

        # Determine currency symbol
        if symbol is None:
            # Get default from settings or fallback
            # Assumes you have a context variable 'school_profile.currency_symbol'
            # or a global setting. For simplicity, let's try settings first.
            symbol_to_use = getattr(settings, 'DEFAULT_CURRENCY_SYMBOL', '$')
            # A better way if request context is available or per-tenant symbol
            # is to get it from request.tenant.profile.currency_symbol if available.
            # This filter doesn't have direct access to 'request' here.
        else:
            symbol_to_use = symbol
        
        return f"{symbol_to_use}{formatted_value}"

    except (TypeError, ValueError, InvalidOperation):
        # Handle cases where value can't be converted to Decimal or formatted
        return value # Return original value or an error string
    
# # In your app/templatetags/custom_filters.py
# from django import template

# register = template.Library()

# @register.filter
# def contains(value, substring):
#     """
#     Returns True if substring is found in value, False otherwise.
#     Usage: {{ value|contains:"substring" }}
#     """
#     if value is None:
#         return False
#     return str(substring) in str(value)


# @register.filter(name='contains')
# def contains(value, arg):
#     """
#     Checks if a string 'value' contains the substring 'arg'.
#     Usage: {{ some_string|contains:"substring" }}
#     """
#     if value is None or arg is None:
#         return False
#     return str(arg) in str(value)



# Additional filters and tags can be added here using the same register object

@register.filter
def class_name(value):
    """Returns the name of the class for a given object."""
    if hasattr(value, '__class__'):
        return value.__class__.__name__
    return type(value).__name__



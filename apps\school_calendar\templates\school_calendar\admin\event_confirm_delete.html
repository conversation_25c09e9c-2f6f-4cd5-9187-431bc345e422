{% extends "base.html" %}
{% load static i18n %}

{% block title %}{% trans "Delete Event" %} - {{ object.title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px 10px 0 0;
    }
    
    .warning-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .event-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .detail-item:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
    }
    
    .detail-value {
        color: #6c757d;
    }
    
    .impact-list {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .impact-list ul {
        margin-bottom: 0;
    }
    
    .action-buttons {
        background: white;
        padding: 1.5rem;
        border-radius: 0 0 10px 10px;
        border-top: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:calendar' %}">{% trans "Calendar" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:admin_event_list' %}">{% trans "Manage Events" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Delete Event" %}</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <!-- Delete Header -->
                <div class="delete-header">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                        <h1 class="mb-2">{% trans "Delete Event" %}</h1>
                        <p class="mb-0 opacity-75">{% trans "This action cannot be undone" %}</p>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Warning Section -->
                    <div class="warning-section">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-exclamation-triangle-fill text-warning me-3 mt-1" style="font-size: 1.5rem;"></i>
                            <div>
                                <h5 class="text-warning mb-2">{% trans "Warning" %}</h5>
                                <p class="mb-0">
                                    {% trans "You are about to permanently delete this event. This action cannot be undone and will remove all associated data." %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Event Details -->
                    <div class="event-details">
                        <h5 class="mb-3">
                            <i class="bi bi-info-circle me-2"></i>
                            {% trans "Event Details" %}
                        </h5>
                        
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Title:" %}</span>
                            <span class="detail-value">{{ object.title }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Type:" %}</span>
                            <span class="detail-value">{{ object.get_event_type_display }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Date:" %}</span>
                            <span class="detail-value">
                                {% if object.is_multi_day %}
                                    {{ object.start_date|date:"M j, Y" }} - {{ object.end_date|date:"M j, Y" }}
                                {% else %}
                                    {{ object.start_date|date:"M j, Y" }}
                                {% endif %}
                            </span>
                        </div>
                        
                        {% if not object.is_all_day and object.start_time %}
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Time:" %}</span>
                            <span class="detail-value">
                                {{ object.start_time|time:"g:i A" }}
                                {% if object.end_time %}
                                    - {{ object.end_time|time:"g:i A" }}
                                {% endif %}
                            </span>
                        </div>
                        {% endif %}
                        
                        {% if object.location %}
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Location:" %}</span>
                            <span class="detail-value">{{ object.location }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Priority:" %}</span>
                            <span class="detail-value">{{ object.get_priority_display }}</span>
                        </div>
                        
                        {% if object.category %}
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Category:" %}</span>
                            <span class="detail-value">{{ object.category.name }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Created:" %}</span>
                            <span class="detail-value">{{ object.created_at|date:"M j, Y g:i A" }}</span>
                        </div>
                        
                        {% if object.created_by %}
                        <div class="detail-item">
                            <span class="detail-label">{% trans "Created by:" %}</span>
                            <span class="detail-value">{{ object.created_by.get_full_name|default:object.created_by.email }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Impact Assessment -->
                    {% if object.requires_rsvp or object.attendees.exists %}
                    <div class="impact-list">
                        <h6 class="text-danger mb-2">
                            <i class="bi bi-exclamation-circle me-2"></i>
                            {% trans "Deletion Impact" %}
                        </h6>
                        <ul class="text-danger">
                            {% if object.requires_rsvp %}
                            <li>{% trans "This event requires RSVP - all responses will be lost" %}</li>
                            {% endif %}
                            
                            {% if object.attendees.exists %}
                            <li>
                                {% blocktrans count counter=object.attendees.count %}
                                {{ counter }} RSVP response will be deleted
                                {% plural %}
                                {{ counter }} RSVP responses will be deleted
                                {% endblocktrans %}
                            </li>
                            {% endif %}
                            
                            {% if object.recurrence != 'NONE' %}
                            <li>{% trans "This is a recurring event - only this instance will be deleted" %}</li>
                            {% endif %}
                            
                            <li>{% trans "Event will be removed from all user calendars" %}</li>
                            <li>{% trans "All event history and logs will be permanently lost" %}</li>
                        </ul>
                    </div>
                    {% endif %}
                    
                    <!-- Confirmation Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                {% trans "I understand that this action cannot be undone and want to proceed with deleting this event." %}
                            </label>
                        </div>
                        
                        <div class="alert alert-danger">
                            <strong>{% trans "Final Confirmation:" %}</strong>
                            {% blocktrans with title=object.title %}
                            Type "DELETE" in the box below to confirm deletion of "{{ title }}":
                            {% endblocktrans %}
                            <input type="text" class="form-control mt-2" id="deleteConfirmation" placeholder="DELETE" required>
                        </div>
                    </form>
                </div>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>{% trans "Cancel" %}
                            </a>
                            <a href="{% url 'school_calendar:event_detail' object.pk %}" class="btn btn-outline-primary">
                                <i class="bi bi-eye me-1"></i>{% trans "View Event" %}
                            </a>
                        </div>
                        
                        <button type="submit" form="deleteForm" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="bi bi-trash me-1"></i>{% trans "Delete Event" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for actual deletion -->
<form method="post" id="deleteForm" style="display: none;">
    {% csrf_token %}
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteConfirmation = document.getElementById('deleteConfirmation');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    function checkFormValidity() {
        const isChecked = confirmCheckbox.checked;
        const isTextCorrect = deleteConfirmation.value.trim().toUpperCase() === 'DELETE';
        
        deleteButton.disabled = !(isChecked && isTextCorrect);
        
        if (isChecked && isTextCorrect) {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-danger');
        }
    }
    
    confirmCheckbox.addEventListener('change', checkFormValidity);
    deleteConfirmation.addEventListener('input', checkFormValidity);
    
    deleteButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        if (confirmCheckbox.checked && deleteConfirmation.value.trim().toUpperCase() === 'DELETE') {
            if (confirm('{% trans "Are you absolutely sure you want to delete this event? This action cannot be undone." %}')) {
                deleteForm.submit();
            }
        }
    });
    
    // Prevent accidental form submission
    deleteConfirmation.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}

# D:\school_fees_saas_v2\apps\subscriptions\mixins.py
from django.core.exceptions import PermissionDenied
from django.contrib import messages
from django.shortcuts import redirect
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import AccessMixin # Base for custom access control

import logging
logger = logging.getLogger(__name__)

class FeatureRequiredMixin(AccessMixin):
    """
    Mixin that checks if the current tenant's subscription has a specific feature.
    If not, raises PermissionDenied or redirects.
    """
    feature_code = None  # Must be set by the subclassing view
    permission_denied_message = _("This feature is not included in your current subscription plan. Please upgrade your plan to access it.")
    redirect_url_on_denial = None # e.g., 'schools:dashboard' or 'subscriptions:plan_upgrade_page'

    def get_feature_code(self):
        if self.feature_code is None:
            raise NotImplementedError(
                "FeatureRequiredMixin requires either a 'feature_code' attribute "
                "or an implementation of 'get_feature_code()'."
            )
        return self.feature_code

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request, 'tenant') or not request.tenant:
            # This should ideally be caught by tenant-aware middleware earlier
            logger.error("FeatureRequiredMixin: Tenant not found on request.")
            raise PermissionDenied(_("School context not available."))

        # Get the subscription; it might be None if not set up
        subscription = getattr(request.tenant, 'subscription', None)
        
        required_feature = self.get_feature_code()

        if not subscription or not subscription.is_usable or not subscription.has_feature(required_feature):
            logger.warning(
                f"Feature '{required_feature}' access denied for tenant '{request.tenant.schema_name}'. "
                f"Subscription: {subscription}, Usable: {subscription.is_usable if subscription else 'N/A'}."
            )
            messages.error(request, self.permission_denied_message)
            if self.redirect_url_on_denial:
                return redirect(self.redirect_url_on_denial)
            # If no redirect_url, raise PermissionDenied which results in a 403 error page
            raise PermissionDenied(self.permission_denied_message)
            
        return super().dispatch(request, *args, **kwargs)
    
    
{% extends "tenant_base.html" %}
{% load static %}

{% block title %}Update Leave Request{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .leave-details {
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9ff 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #e9ecef;
    }

    .leave-details h5 {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }

    .detail-label i {
        color: #667eea;
        margin-right: 0.5rem;
        width: 1.2rem;
    }

    .detail-value {
        color: #6c757d;
        text-align: right;
        flex: 1;
        margin-left: 1rem;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select,
    .form-floating > textarea {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > textarea {
        height: auto;
        min-height: calc(3.5rem + 2px);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus,
    .form-floating > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label,
    .form-floating > textarea:focus ~ label,
    .form-floating > textarea:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .status-approved {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .status-rejected {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-user-edit me-3"></i>
                            Update Leave Request
                        </h3>
                    </div>
                    <div class="premium-body">
                        <!-- Leave Request Details -->
                        <div class="leave-details">
                            <h5><i class="fas fa-info-circle me-2"></i>Leave Request Details</h5>

                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-user"></i>Employee
                                </div>
                                <div class="detail-value">{{ leave_request.employee.user.full_name }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-tag"></i>Leave Type
                                </div>
                                <div class="detail-value">{{ leave_request.leave_type.name }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-calendar-alt"></i>Duration
                                </div>
                                <div class="detail-value">
                                    {{ leave_request.start_date|date:"d M Y" }} to {{ leave_request.end_date|date:"d M Y" }}
                                    <br><small>({{ leave_request.number_of_days|floatformat:1 }} days)</small>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">
                                    <i class="fas fa-comment"></i>Reason
                                </div>
                                <div class="detail-value">{{ leave_request.reason|linebreaksbr }}</div>
                            </div>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Admin Decision Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-gavel me-2"></i>Administrative Decision</h4>
                            </div>

                            <!-- Status Field -->
                            <div class="form-floating">
                                <select class="form-select{% if form.status.errors %} is-invalid{% endif %}"
                                        id="{{ form.status.id_for_label }}"
                                        name="{{ form.status.name }}"
                                        {% if form.status.field.required %}required{% endif %}>
                                    <option value="">Select status</option>
                                    {% for choice in form.status.field.choices %}
                                        {% if choice.0 %}
                                            <option value="{{ choice.0 }}" {% if form.status.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                <label for="{{ form.status.id_for_label }}">
                                    <i class="fas fa-check-circle icon-input"></i>Decision Status
                                </label>
                                {% if form.status.help_text %}
                                    <div class="form-text">{{ form.status.help_text }}</div>
                                {% endif %}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">{{ form.status.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Admin Notes Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.admin_notes.errors %} is-invalid{% endif %}"
                                          id="{{ form.admin_notes.id_for_label }}"
                                          name="{{ form.admin_notes.name }}"
                                          placeholder="Enter admin notes"
                                          style="height: 120px;">{{ form.admin_notes.value|default:'' }}</textarea>
                                <label for="{{ form.admin_notes.id_for_label }}">
                                    <i class="fas fa-sticky-note icon-input"></i>Admin Notes
                                </label>
                                {% if form.admin_notes.help_text %}
                                    <div class="form-text">{{ form.admin_notes.help_text }}</div>
                                {% endif %}
                                {% if form.admin_notes.errors %}
                                    <div class="invalid-feedback">{{ form.admin_notes.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-save me-2"></i>Update Status
                                </button>
                                <a href="{% url 'hr:leave_request_detail' leave_request.pk %}" class="btn-secondary-premium">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-premium[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
            submitBtn.disabled = true;
        });
    }

    // Enhanced form validation feedback
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });

    // Status change confirmation
    const statusSelect = document.querySelector('select[name="status"]');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const statusText = selectedOption.text;

            if (statusText.toLowerCase().includes('reject')) {
                const adminNotesField = document.querySelector('textarea[name="admin_notes"]');
                if (adminNotesField && !adminNotesField.value.trim()) {
                    adminNotesField.focus();
                    adminNotesField.placeholder = 'Please provide a reason for rejection...';
                    adminNotesField.style.borderColor = '#ffc107';
                }
            }
        });
    }
});
</script>
{% endblock %}

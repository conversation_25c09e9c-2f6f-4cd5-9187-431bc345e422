# management/commands/cleanup_orphaned_tenants.py
from django.core.management.base import BaseCommand
from django.db import connection
from apps.tenants.models import School

class Command(BaseCommand):
    help = 'Clean up orphaned tenant records and schemas'

    def add_arguments(self, parser):
        parser.add_argument(
            '--schema-name',
            type=str,
            help='Specific schema name to clean up',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        schema_name = options.get('schema_name')
        dry_run = options.get('dry_run', False)

        if schema_name:
            self.cleanup_single_tenant(schema_name, dry_run)
        else:
            self.cleanup_all_orphaned_tenants(dry_run)

    def cleanup_single_tenant(self, schema_name, dry_run=False):
        """Clean up a specific tenant"""
        self.stdout.write(f"Cleaning up tenant: {schema_name}")
        
        with connection.cursor() as cursor:
            # Check if schema exists in PostgreSQL
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name = %s
            """, [schema_name])
            
            schema_exists = cursor.fetchone() is not None
            
            if schema_exists:
                if not dry_run:
                    try:
                        cursor.execute(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE;")
                        self.stdout.write(f"✓ Dropped schema: {schema_name}")
                    except Exception as e:
                        self.stdout.write(f"✗ Error dropping schema {schema_name}: {e}")
                else:
                    self.stdout.write(f"Would drop schema: {schema_name}")
            else:
                self.stdout.write(f"Schema {schema_name} doesn't exist in database")

        # Remove tenant record from public schema
        connection.set_schema_to_public()
        try:
            tenant_count = School.objects.filter(schema_name=schema_name).count()
            if tenant_count > 0:
                if not dry_run:
                    # Try Django ORM first
                    try:
                        School.objects.filter(schema_name=schema_name).delete()
                        self.stdout.write(f"✓ Removed tenant record: {schema_name}")
                    except Exception as orm_error:
                        # If Django ORM fails, use raw SQL
                        self.stdout.write(f"Django ORM failed, trying raw SQL: {orm_error}")
                        self._force_delete_tenant_record(schema_name)
                else:
                    self.stdout.write(f"Would remove tenant record: {schema_name}")
            else:
                self.stdout.write(f"No tenant record found for: {schema_name}")
        except Exception as e:
            self.stdout.write(f"✗ Error removing tenant record {schema_name}: {e}")

    def _force_delete_tenant_record(self, schema_name):
        """Force delete tenant record using raw SQL when Django ORM fails"""
        with connection.cursor() as cursor:
            try:
                # Get tenant ID first
                cursor.execute("""
                    SELECT id FROM tenants_school 
                    WHERE schema_name = %s
                """, [schema_name])
                result = cursor.fetchone()
                
                if result:
                    tenant_id = result[0]
                    
                    # Delete related domain records first
                    cursor.execute("""
                        DELETE FROM tenants_domain 
                        WHERE tenant_id = %s
                    """, [tenant_id])
                    
                    # Delete the tenant record
                    cursor.execute("""
                        DELETE FROM tenants_school 
                        WHERE id = %s
                    """, [tenant_id])
                    
                    self.stdout.write(f"✓ Force deleted tenant record: {schema_name}")
                else:
                    self.stdout.write(f"No tenant record found with schema_name: {schema_name}")
                    
            except Exception as e:
                self.stdout.write(f"✗ Force delete failed for {schema_name}: {e}")

    def cleanup_all_orphaned_tenants(self, dry_run=False):
        """Find and clean up all orphaned tenants"""
        self.stdout.write("Scanning for orphaned tenants...")
        
        # Get all tenant records
        connection.set_schema_to_public()
        tenants = School.objects.all()
        
        with connection.cursor() as cursor:
            # Get all existing schemas
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'public')
            """)
            existing_schemas = {row[0] for row in cursor.fetchall()}
            
            # Find orphaned schemas (exist in DB but not in tenant records)
            tenant_schemas = {t.schema_name for t in tenants}
            orphaned_schemas = existing_schemas - tenant_schemas
            
            # Find orphaned tenant records (exist in records but not in DB)
            orphaned_records = tenant_schemas - existing_schemas
            
            self.stdout.write(f"Found {len(orphaned_schemas)} orphaned schemas")
            self.stdout.write(f"Found {len(orphaned_records)} orphaned tenant records")
            
            # Clean up orphaned schemas
            for schema in orphaned_schemas:
                if not dry_run:
                    try:
                        cursor.execute(f"DROP SCHEMA IF EXISTS {schema} CASCADE;")
                        self.stdout.write(f"✓ Dropped orphaned schema: {schema}")
                    except Exception as e:
                        self.stdout.write(f"✗ Error dropping schema {schema}: {e}")
                else:
                    self.stdout.write(f"Would drop orphaned schema: {schema}")
            
            # Clean up orphaned tenant records
            for schema in orphaned_records:
                if not dry_run:
                    try:
                        School.objects.filter(schema_name=schema).delete()
                        self.stdout.write(f"✓ Removed orphaned tenant record: {schema}")
                    except Exception as e:
                        self.stdout.write(f"✗ Error removing tenant record {schema}: {e}")
                else:
                    self.stdout.write(f"Would remove orphaned tenant record: {schema}")
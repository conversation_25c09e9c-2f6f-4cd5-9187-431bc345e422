# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0002_initial'),
        ('finance', '0001_initial'),
        ('payments', '0001_initial'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='budgetamount',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='budget_amounts', to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='budgetamount',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='budget_amounts', to='schools.term'),
        ),
        migrations.AddField(
            model_name='budgetitem',
            name='linked_coa_account',
            field=models.ForeignKey(help_text='The Income, COGS or Expense account in CoA this budget item relates to.', on_delete=django.db.models.deletion.PROTECT, related_name='budget_items', to='accounting.account'),
        ),
        migrations.AddField(
            model_name='budgetamount',
            name='budget_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='amounts', to='finance.budgetitem'),
        ),
        migrations.AddField(
            model_name='expense',
            name='payment_method',
            field=models.ForeignKey(blank=True, help_text='How this expense was paid. Leave blank if recorded as a payable.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.paymentmethod'),
        ),
        migrations.AddField(
            model_name='expense',
            name='recorded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses_recorded', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='expensecategory',
            name='expense_account',
            field=models.ForeignKey(help_text='The Expense account in CoA this category posts to.', limit_choices_to={'account_type': 'EXPENSE'}, on_delete=django.db.models.deletion.PROTECT, related_name='expense_categories', to='accounting.account'),
        ),
        migrations.AddField(
            model_name='expense',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='expenses', to='finance.expensecategory'),
        ),
        migrations.AddField(
            model_name='expense',
            name='vendor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses', to='finance.vendor'),
        ),
        migrations.AlterUniqueTogether(
            name='budgetamount',
            unique_together={('budget_item', 'academic_year', 'term')},
        ),
    ]

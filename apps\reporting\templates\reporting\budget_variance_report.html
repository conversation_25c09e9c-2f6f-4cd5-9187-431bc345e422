{% extends "tenant_base.html" %}
{% load humanize static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
    
{% comment %} <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div> {% endcomment %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}
    {% include "./_report_filter_export_card.html" %}

    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Budget Variance for 
                {% if report_data.academic_year %}
                    {{ report_data.academic_year.name }}
                    {% if report_data.term %} - {{ report_data.term.name }}{% endif %}
                {% else %}
                    Selected Period
                {% endif %}
            </h6>
        </div>
        <div class="card-body">
            {% if report_data.academic_year %}
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover table-sm" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Budget Item</th>
                            <th>Account Code</th>
                            <th class="text-end">Budgeted Amount</th>
                            <th class="text-end">Actual Amount</th>
                            <th class="text-end">Variance</th>
                            <th class="text-end">Variance %</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in report_data.budget_items_variance %}
                        <tr>
                            <td>{{ item.item_name }}</td>
                            <td>{{ item.account_code }}</td>
                            <td class="text-end">{{ item.budgeted|floatformat:2|intcomma }}</td>
                            <td class="text-end">{{ item.actual|floatformat:2|intcomma }}</td>
                            <td class="text-end {% if item.variance < 0 and item.is_favorable or item.variance > 0 and not item.is_favorable %}text-danger{% elif item.variance != 0 %}text-success{% endif %}">
                                {{ item.variance|floatformat:2|intcomma }}
                            </td>
                            <td class="text-end">{{ item.variance_percentage|floatformat:1 }}%</td>
                            <td class="text-center">
                                {% if item.variance == 0 %}
                                    <span class="badge bg-secondary">On Track</span>
                                {% elif item.is_favorable %}
                                    <span class="badge bg-success">Favorable</span>
                                {% else %}
                                    <span class="badge bg-danger">Unfavorable</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">No budget items found or amounts set for the selected criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if report_data.budget_items_variance %}
                    <tfoot>
                        <tr class="table-light fw-bold fs-5">
                            <th colspan="2" class="text-end">TOTALS:</th>
                            <th class="text-end">{{ report_data.total_budgeted|floatformat:2|intcomma }}</th>
                            <th class="text-end">{{ report_data.total_actual|floatformat:2|intcomma }}</th>
                            <th class="text-end {% if report_data.total_variance > 0 %}text-success{% elif report_data.total_variance < 0 %}text-danger{% endif %}">
                                {{ report_data.total_variance|floatformat:2|intcomma }}
                            </th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">Please select an Academic Year to view the budget variance report.</div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}






{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Budget Variance Report" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Budget Variance Report" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %} {% endcomment %}
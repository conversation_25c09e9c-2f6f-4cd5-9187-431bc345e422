{# apps/accounting/templates/accounting/_account_node.html #}
{% load core_tags %} {# Assuming your active_nav_link or other useful tags might be here #}

<li class="list-group-item py-1 {% if not account.is_active %}list-group-item-light text-muted{% endif %}" style="padding-left: {{ level|default:0|multiply:20 }}px; border-left: {{ level|default:0|multiply:2 }}px solid #dee2e6 !important;">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            {% if account.child_accounts.exists %}
                <i class="bi bi-folder-fill me-1 text-primary"></i>
            {% else %}
                <i class="bi bi-file-earmark-text me-1 text-secondary"></i>
            {% endif %}
            <strong>{{ account.code }}</strong> - {{ account.name }}
            <small class="text-muted ms-2">({{ account.get_account_type_display }})</small>
            {% if not account.is_active %}
                <span class="badge bg-warning text-dark ms-2">Inactive</span>
            {% endif %}
        </div>
        <div class="btn-group btn-group-sm" role="group">
            {% if perms.accounting.change_account %}
            <a href="{% url 'accounting:account_update' account.pk %}" class="btn btn-outline-secondary py-0 px-1" title="Edit {{ account.name }}">
                <i class="bi bi-pencil-square"></i>
            </a>
            {% endif %}
            {% if perms.accounting.add_account %} {# Link to create a child under this one #}
            <a href="{% url 'accounting:account_create' %}?parent_id={{ account.pk }}" class="btn btn-outline-success py-0 px-1" title="Add Child to {{ account.name }}">
                <i class="bi bi-plus-lg"></i>
            </a>
            {% endif %}
            {% if perms.accounting.delete_account %}
            <a href="{% url 'accounting:account_delete' account.pk %}" class="btn btn-outline-danger py-0 px-1" title="Delete {{ account.name }}">
                <i class="bi bi-trash3"></i>
            </a>
            {% endif %}
        </div>
    </div>

    {% if account.child_accounts.exists %}
        <ul class="list-group list-group-flush ps-3 mt-1"> {# Use list-group-flush for no borders on inner list #}
            {% for child in account.child_accounts.all %}
                {% include "accounting/_account_node.html" with account=child level=level|add:1 %}
            {% endfor %}
        </ul>
    {% endif %}
</li>
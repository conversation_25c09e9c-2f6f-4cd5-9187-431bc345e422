{# templates/parent_portal/my_children_list.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static core_tags humanize %}

{% block parent_portal_page_title %}{{ view_title|default:"My Children" }}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-md-4">
    <div class="page-header mb-4">
        <h1 class="h2 mb-1">{{ view_title|default:"My Children" }}</h1>
        <p class="text-muted">Active students linked to your profile: {{ parent.get_full_name|default:parent.email }}.</p>
    </div>

    {% include "partials/_messages.html" %}

    {% if linked_students %}
        <div class="list-group shadow-sm">
            {% for student_obj in linked_students %}
                <a href="{% if student_obj.pk %}{% url 'parent_portal:student_fees' student_pk=student_obj.pk %}{% else %}#{% endif %}" 
                    class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                    <div>
                        <strong class="d-block fs-6">{{ student_obj.get_full_name|default:"N/A" }}</strong>
                        <small class="text-muted">
                            Class: {{ student_obj.current_class.name|default:"-" }}
                            {% if student_obj.current_section %}/ {{ student_obj.current_section.name }}{% endif %}
                            <span class="mx-1">|</span> Adm#: {{ student_obj.admission_number|default:"-" }}
                        </small>
                    </div>
                    <span class="badge bg-primary rounded-pill">View Fee Details <i class="bi bi-chevron-right ms-1"></i></span>
                </a>
            {% endfor %}
        </ul>
    {% else %}
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle-fill me-2"></i>No active children are currently linked to your profile. If this is incorrect, please contact the school administration.
        </div>
    {% endif %}

    <div class="footer-actions mt-4">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i>Back to Dashboard
        </a>
    </div>
</div>
{% endblock parent_portal_main_content %}















{% comment %} {# templates/parent_portal/my_children.html #}
{% extends "parent_portal_base.html" %}
{% load core_tags %}

{% block title %}{{ view_title|default:"My Children" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Showing active students linked to your profile ({{ parent.email }}).</p>

    {% include "includes/_messages.html" %}

    {% if linked_students %}
        <ul class="list-group">
            {% for student in linked_students %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{ student.full_name }}</strong><br>
                        <small class="text-muted">
                            Class: {{ student.school_class.name|default:'-' }} - {{ student.section.name|default:'-' }} | Adm#: {{ student.admission_number }}
                        </small>
                    </div>
                    {# Link to detailed fee view for this specific student #}
                    <a href="{% url 'parent_portal:student_fee_details' student.pk %}" class="btn btn-sm btn-outline-primary">View Fee Details</a>
                </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="alert alert-info">No active children linked to your profile. Please contact the school office if this is incorrect.</div>
    {% endif %}

    <div class="footer-actions mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock %} {% endcomment %}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n humanize %} {# Assuming 'humanize' for filters like naturalday or intcomma if needed elsewhere #}

{% block platform_admin_page_title %}{{ view_title|default:_("System Notifications") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <style>
        .actions-column .btn {
            margin-right: 0.25rem;
        }
        .actions-column .btn:last-child {
            margin-right: 0;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Manage System Notifications") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "System Notifications" %}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<div class="mb-3">
    <a href="{% url 'platform_management:systemnotification_create' %}" class="btn btn-primary">
        <i class="bi bi-plus-circle-fill me-1"></i> {% trans "Create New System Notification" %}
    </a>
</div>

{% include "partials/_messages.html" %} {# Assuming you have a partial for messages #}

<div class="card shadow-sm">
    <div class="card-body">
        <h5 class="card-title">{% trans "All System Notifications" %}</h5>

        {% if notifications %} {# 'notifications' is the context_object_name from SystemNotificationListView #}
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th scope="col">{% trans "Title" %}</th>
                            <th scope="col">{% trans "Type" %}</th>
                            <th scope="col">{% trans "Created By" %}</th>
                            <th scope="col">{% trans "Publish Date" %}</th>
                            <th scope="col">{% trans "Expires At" %}</th>
                            <th scope="col" class="text-center">{% trans "Status" %}</th>
                            <th scope="col">{% trans "Targeted Users" %}</th>
                            <th scope="col" class="text-end">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for notification in notifications %}
                        <tr>
                            <td>{{ notification.title|truncatechars:50 }}</td>
                            <td>
                                <span class="badge 
                                    {% if notification.notification_type == 'ERROR' %}bg-danger
                                    {% elif notification.notification_type == 'WARNING' %}bg-warning text-dark
                                    {% elif notification.notification_type == 'SUCCESS' %}bg-success
                                    {% else %}bg-info text-dark{% endif %}">
                                    {{ notification.get_notification_type_display }}
                                </span>
                            </td>
                            <td>{{ notification.created_by.username|default:"N/A" }}</td>
                            <td>{{ notification.publish_date|date:"d M Y, P" }}</td>
                            <td>{{ notification.expires_at|date:"d M Y, P"|default:"Never" }}</td>
                            <td class="text-center">
                                {% if notification.is_active and notification.is_currently_visible %}
                                    <span class="badge bg-success" title="{% trans 'Currently visible to targeted users' %}">{% trans "Visible" %}</span>
                                {% elif notification.is_active %}
                                    <span class="badge bg-info text-dark" title="{% trans 'Scheduled for future or already expired' %}">{% trans "Scheduled/Expired" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary" title="{% trans 'Not active / Draft' %}">{% trans "Inactive" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if notification.target_users.exists %}
                                    <small>{{ notification.target_users.count }} {% trans "user(s)" %}</small>
                                    {# Consider a tooltip or modal to list users if many #}
                                {% else %}
                                    <small class="text-muted">{% trans "General (All Platform Users)" %}</small>
                                {% endif %}
                            </td>
                            <td class="text-end actions-column">
                                {# Add perm checks if you have granular permissions #}
                                {# Example: {% if perms.platform_management.change_systemnotification %} #}
                                <a href="{% url 'platform_management:systemnotification_update' pk=notification.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit Notification' %}">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                {# {% endif %} #}
                                {# Example: {% if perms.platform_management.delete_systemnotification %} #}
                                <a href="{% url 'platform_management:systemnotification_delete' pk=notification.pk %}" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete Notification' %}">
                                    <i class="bi bi-trash"></i>
                                </a>
                                {# {% endif %} #}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if is_paginated %} {# Check if pagination is active #}
                {% include "partials/_pagination.html" with page_obj=page_obj %} {# Use page_obj from ListView context #}
            {% endif %}
        {% else %}
            <div class="alert alert-light text-center mt-3" role="alert">
                <i class="bi bi-info-circle me-2"></i>{% trans "No system notifications have been created yet." %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    {# Add any JS specific to this list page if needed #}
{% endblock %}



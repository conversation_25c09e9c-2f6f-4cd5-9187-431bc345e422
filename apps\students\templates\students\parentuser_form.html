{# D:\school_fees_saas_v2\apps\students\templates\students\parentuser_form.html #}
{% extends "tenant_base.html" %} {# Assuming this is the correct base for tenant staff facing forms #}
{% load static i18n widget_tweaks %} {# Ensure widget_tweaks is in INSTALLED_APPS if used #}

{% block tenant_page_title %}
    {# Using view_title passed from context; the trans block is a fallback if view_title is missing #}
    {% trans "Parent Account Management" as default_title_string %} 
    {{ view_title|default:default_title_string }}
{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Add any specific CSS for this form if needed #}
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle mb-4"> {# Added mb-4 for spacing #}
        <h1>
            {% trans "Manage Parent Account" as default_h1_title %}
            {{ view_title|default:default_h1_title }}
        </h1>

        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'students:parentuser_list' %}">{% trans "Parent Accounts" %}</a></li> 
                <li class="breadcrumb-item active">
                    {% if object %}{% trans "Edit" %}{% else %}{% trans "Create" %}{% endif %}
                    {# Using form_mode is also good: {{ form_mode|capfirst|default:default_breadcrumb_text }} #}
                </li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow-sm"> {# Added shadow-sm for consistency #}
                <div class="card-body pt-4"> {# Increased top padding #}
                    {# Card title moved inside card-body for better alignment with padding #}
                    {# <h5 class="card-title pb-2">{% if object %}{% trans "Edit Parent Account" %}{% else %}{% trans "Create New Parent Account" %}{% endif %}</h5> #}

                    {% include "partials/_messages.html" %} {# Assuming you have this for messages #}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="row g-3">
                            <div class="col-md-12 mb-3"> {# Use mb-3 on col for consistent bottom spacing #}
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {% render_field form.email class+="form-control" placeholder=form.email.label %}
                                {% if form.email.help_text %}<small class="form-text text-muted">{{ form.email.help_text }}</small>{% endif %}
                                {% for error in form.email.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                {% render_field form.first_name class+="form-control" placeholder=form.first_name.label %}
                                {% for error in form.first_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                {% render_field form.last_name class+="form-control" placeholder=form.last_name.label %}
                                {% for error in form.last_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }}</label>
                                {% render_field form.phone_number class+="form-control" placeholder=form.phone_number.label %}
                                {% if form.phone_number.help_text %}<small class="form-text text-muted">{{ form.phone_number.help_text }}</small>{% endif %}
                                {% for error in form.phone_number.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            
                            {# Password fields - only for create mode (form_mode == 'create' or not object) #}
                            {% if form_mode == "create" or not object %} 
                                <div class="col-md-6 mb-3"> {# This div ensures password fields stay on their own row if needed #}
                                    {# No content here if not create mode, just to balance the row if phone_number is alone #}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label|default:"Password" }}</label>
                                    {% render_field form.password class+="form-control" %}
                                    {% if form.password.help_text %}<small class="form-text text-muted">{{ form.password.help_text|safe }}</small>{% endif %}
                                    {% for error in form.password.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                                {# Assuming your ParentUserCreationForm has password_confirm, not password2 #}
                                {% if form.password_confirm %} 
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.password_confirm.id_for_label }}" class="form-label">{{ form.password_confirm.label|default:"Confirm Password" }}</label>
                                    {% render_field form.password_confirm class+="form-control" %}
                                    {% for error in form.password_confirm.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                                {% else %}
                                <div class="col-md-6 mb-3"></div> {# Empty col to balance if no password_confirm field in form #}
                                {% endif %}
                            {% endif %}

                            {% if form.is_active %} {# Only if 'is_active' is part of the current form (ParentUserChangeForm) #}
                            <div class="col-12 mt-2 mb-3"> {# Adjusted margin #}
                                <div class="form-check">
                                    {% render_field form.is_active class+="form-check-input" %}
                                    <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                                    {% if form.is_active.help_text %}<small class="form-text text-muted d-block">{{ form.is_active.help_text }}</small>{% endif %}
                                    {% for error in form.is_active.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        {# Hidden fields are not typically needed if form action and GET params are handled by the view #}
                        {# The view's get_success_url can access request.GET directly #}
                        {# However, if you need to pass them through specifically: #}
                        {% if request.GET.student_pk_to_link %}
                            {# <input type="hidden" name="student_pk_to_link_passthrough" value="{{ request.GET.student_pk_to_link }}"> #}
                        {% endif %}
                        {% if request.GET.next %}
                            {# <input type="hidden" name="next_passthrough" value="{{ request.GET.next }}"> #}
                        {% endif %}

                        <hr class="my-4">
                        <div class="d-flex justify-content-end">
                            <a href="{% firstof request.GET.next success_url object.get_absolute_url_for_staff_view|default:None request.META.HTTP_REFERER|default:None %}{% url 'students:parentuser_list' %}" 
                                class="btn btn-outline-secondary me-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> 
                                {% if object %}{% trans "Save Changes" %}{% else %}{% trans "Create Parent Account" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



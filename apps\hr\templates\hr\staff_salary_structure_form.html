{# D:\school_fees_saas_v2\apps\hr\templates\hr\staff_salary_structure_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .formset-row {
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
        .formset-row:first-of-type {
            border-top: none;
            padding-top: 0;
        }
    </style>
{% endblock %}


{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">{{ view_title }}</h1>
        <a href="{% url 'schools:staff_detail' pk=staff_member.pk %}" class="btn btn-secondary btn-sm">
            <i class="bi bi-arrow-left-circle me-1"></i> Back to Staff Profile
        </a>
    </div>

    {% include "partials/_messages.html" %}

    <form method="post" novalidate>
        {% csrf_token %}
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h5 class="mb-0">Structure Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.staff_user.id_for_label }}" class="form-label">Staff Member</label>
                        {{ form.staff_user }} {# This field is disabled #}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.effective_date.id_for_label }}" class="form-label required">Effective Date</label>
                        {% render_field form.effective_date class="form-control" %}
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">Salary Components (Earnings & Deductions)</h5>
            </div>
            <div class="card-body">
                {{ component_formset.management_form }}
                
                <div id="component-form-container">
                    {% for form in component_formset %}
                    <div class="row align-items-center formset-row">
                        <div class="col-md-6">{{ form.component }}</div>
                        <div class="col-md-4">{{ form.amount }}</div>
                        <div class="col-md-2">
                            {% if form.instance.pk and component_formset.can_delete %}
                                <div class="form-check">
                                    {{ form.DELETE }}
                                    <label for="{{ form.DELETE.id_for_label }}" class="form-check-label text-danger">Delete</label>
                                </div>
                            {% endif %}
                        </div>
                        {{ form.id }} {# Hidden ID field #}
                    </div>
                    {% endfor %}
                </div>
                
                <div id="empty-form-template" style="display: none;">
                    <div class="row align-items-center formset-row">
                        <div class="col-md-6">{{ component_formset.empty_form.component }}</div>
                        <div class="col-md-4">{{ component_formset.empty_form.amount }}</div>
                        <div class="col-md-2"></div>
                        {{ component_formset.empty_form.id }}
                    </div>
                </div>
                
                <button type="button" id="add-component-button" class="btn btn-outline-success btn-sm mt-3">
                    <i class="bi bi-plus-circle"></i> Add Component
                </button>
            </div>
        </div>

        <div class="mt-4 d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">Save Salary Structure</button>
        </div>
    </form>
</div>
{% endblock tenant_specific_content %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addButton = document.getElementById('add-component-button');
    const formContainer = document.getElementById('component-form-container');
    const emptyFormTemplate = document.getElementById('empty-form-template').innerHTML;
    const totalFormsInput = document.querySelector('input[id$="-TOTAL_FORMS"]'); // More robust selector
    const formsetPrefix = '{{ component_formset.prefix }}';

    addButton.addEventListener('click', function() {
        let currentFormCount = parseInt(totalFormsInput.value, 10);
        const newFormHtml = emptyFormTemplate.replace(/__prefix__/g, currentFormCount);
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newFormHtml;
        const newFormRow = tempDiv.firstElementChild;
        
        formContainer.appendChild(newFormRow);
        
        totalFormsInput.value = currentFormCount + 1;
    });
});
</script>
{% endblock page_specific_js %}
























{% comment %} {% extends "tenant_base.html" %}
{% load static %}

{% block title %}
    {% if object %}Edit Salary Structure{% else %}Create Salary Structure{% endif %} - {{ staff_member.full_name }}
{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .staff-info {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 1rem;
        padding: 1rem;
        margin-top: 1rem;
        position: relative;
        z-index: 2;
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select,
    .form-floating > textarea {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > textarea {
        height: auto;
        min-height: calc(3.5rem + 2px);
    }

    .form-floating > input[type="date"] {
        height: calc(3.5rem + 2px);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus,
    .form-floating > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label,
    .form-floating > textarea:focus ~ label,
    .form-floating > textarea:not(:placeholder-shown) ~ label,
    .form-floating > input[type="date"] ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .formset-container {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-radius: 1.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #e9ecef;
    }

    .formset-item {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
    }

    .formset-item:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .formset-item.empty-form {
        display: none;
    }

    .delete-checkbox {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-add-component {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 1rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        margin-bottom: 1rem;
    }

    .btn-add-component:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
        color: white;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .component-row {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .component-row:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .component-number {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 50%;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-money-check-alt me-3"></i>
                            {% if object %}Edit Salary Structure{% else %}Create Salary Structure{% endif %}
                        </h3>
                        <div class="staff-info">
                            <i class="fas fa-user me-2"></i>
                            <strong>{{ staff_member.full_name }}</strong>
                            {% if staff_member.employee_id %}
                                <span class="ms-2">ID: {{ staff_member.employee_id }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Basic Information Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-info-circle me-2"></i>Salary Structure Details</h4>
                            </div>

                            <!-- Effective Date Field -->
                            <div class="form-floating">
                                <input type="date"
                                       class="form-control{% if form.effective_date.errors %} is-invalid{% endif %}"
                                       id="{{ form.effective_date.id_for_label }}"
                                       name="{{ form.effective_date.name }}"
                                       value="{{ form.effective_date.value|default:'' }}"
                                       {% if form.effective_date.field.required %}required{% endif %}>
                                <label for="{{ form.effective_date.id_for_label }}">
                                    <i class="fas fa-calendar-day icon-input"></i>Effective Date
                                </label>
                                {% if form.effective_date.help_text %}
                                    <div class="form-text">{{ form.effective_date.help_text }}</div>
                                {% endif %}
                                {% if form.effective_date.errors %}
                                    <div class="invalid-feedback">{{ form.effective_date.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Notes Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.notes.errors %} is-invalid{% endif %}"
                                          id="{{ form.notes.id_for_label }}"
                                          name="{{ form.notes.name }}"
                                          placeholder="Enter notes"
                                          style="height: 120px;">{{ form.notes.value|default:'' }}</textarea>
                                <label for="{{ form.notes.id_for_label }}">
                                    <i class="fas fa-sticky-note icon-input"></i>Notes
                                </label>
                                {% if form.notes.help_text %}
                                    <div class="form-text">{{ form.notes.help_text }}</div>
                                {% endif %}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Salary Components Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-list-ul me-2"></i>Salary Components</h4>
                            </div>

                            <div class="formset-container">
                                {{ component_formset.management_form }}
                                <div id="component-formset">
                                    {% for comp_form in component_formset %}
                                    <div class="component-row formset-item{% if forloop.last and not comp_form.instance.pk %} empty-form{% endif %}">
                                        {{ comp_form.id }}

                                        <div class="component-number">{{ forloop.counter }}</div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <select class="form-select{% if comp_form.component.errors %} is-invalid{% endif %}"
                                                            id="{{ comp_form.component.id_for_label }}"
                                                            name="{{ comp_form.component.name }}"
                                                            {% if comp_form.component.field.required %}required{% endif %}>
                                                        <option value="">Select component</option>
                                                        {% for choice in comp_form.component.field.choices %}
                                                            {% if choice.0 %}
                                                                <option value="{{ choice.0 }}" {% if comp_form.component.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </select>
                                                    <label for="{{ comp_form.component.id_for_label }}">
                                                        <i class="fas fa-tags icon-input"></i>Salary Component
                                                    </label>
                                                    {% if comp_form.component.errors %}
                                                        <div class="invalid-feedback">{{ comp_form.component.errors.0 }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="number"
                                                           step="0.01"
                                                           class="form-control{% if comp_form.amount.errors %} is-invalid{% endif %}"
                                                           id="{{ comp_form.amount.id_for_label }}"
                                                           name="{{ comp_form.amount.name }}"
                                                           value="{{ comp_form.amount.value|default:'' }}"
                                                           placeholder="Enter amount"
                                                           {% if comp_form.amount.field.required %}required{% endif %}>
                                                    <label for="{{ comp_form.amount.id_for_label }}">
                                                        <i class="fas fa-dollar-sign icon-input"></i>Amount
                                                    </label>
                                                    {% if comp_form.amount.errors %}
                                                        <div class="invalid-feedback">{{ comp_form.amount.errors.0 }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>

                                        {% if component_formset.can_delete and comp_form.instance.pk %}
                                        <div class="delete-checkbox">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       id="{{ comp_form.DELETE.id_for_label }}"
                                                       name="{{ comp_form.DELETE.name }}">
                                                <label class="form-check-label text-white" for="{{ comp_form.DELETE.id_for_label }}">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </label>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if comp_form.non_field_errors %}
                                            <div class="alert alert-danger mt-2">
                                                {{ comp_form.non_field_errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>

                                <button type="button" id="add-component-item" class="btn-add-component">
                                    <i class="fas fa-plus me-2"></i>Add Component
                                </button>
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}Update Salary Structure{% else %}Create Salary Structure{% endif %}
                                </button>
                                <a href="{% url 'schools:staff_detail' staff_member.pk %}" class="btn-secondary-premium">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-premium[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
        });
    }

    // Enhanced form validation feedback
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });

    // Formset management
    const formsetContainer = document.getElementById('component-formset');
    const addButton = document.getElementById('add-component-item');
    const totalFormsInput = document.querySelector('input[name$="-TOTAL_FORMS"]');

    if (formsetContainer && addButton && totalFormsInput) {
        // Get the empty form template
        const emptyForm = formsetContainer.querySelector('.empty-form');

        addButton.addEventListener('click', function() {
            const currentFormCount = parseInt(totalFormsInput.value);

            if (emptyForm) {
                // Clone the empty form
                const newForm = emptyForm.cloneNode(true);

                // Remove empty-form class and show the form
                newForm.classList.remove('empty-form');
                newForm.style.display = 'block';

                // Update form indices
                const formRegex = new RegExp('__prefix__', 'g');
                newForm.innerHTML = newForm.innerHTML.replace(formRegex, currentFormCount);

                // Update component number
                const componentNumber = newForm.querySelector('.component-number');
                if (componentNumber) {
                    componentNumber.textContent = currentFormCount + 1;
                }

                // Insert before the empty form
                formsetContainer.insertBefore(newForm, emptyForm);

                // Update total forms count
                totalFormsInput.value = currentFormCount + 1;

                // Add event listeners to new form elements
                const newInputs = newForm.querySelectorAll('.form-control, .form-select');
                newInputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        }
                    });

                    input.addEventListener('input', function() {
                        if (this.classList.contains('is-invalid')) {
                            if (this.checkValidity()) {
                                this.classList.remove('is-invalid');
                                this.classList.add('is-valid');
                            }
                        }
                    });
                });

                // Add delete functionality for new forms
                const deleteBtn = newForm.querySelector('.delete-checkbox input[type="checkbox"]');
                if (deleteBtn) {
                    deleteBtn.addEventListener('change', function() {
                        if (this.checked) {
                            newForm.style.opacity = '0.5';
                            newForm.style.transform = 'scale(0.95)';
                        } else {
                            newForm.style.opacity = '1';
                            newForm.style.transform = 'scale(1)';
                        }
                    });
                }

                // Focus on the first input of the new form
                const firstInput = newForm.querySelector('.form-control, .form-select');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        });

        // Add delete functionality to existing forms
        const deleteCheckboxes = formsetContainer.querySelectorAll('.delete-checkbox input[type="checkbox"]');
        deleteCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const formRow = this.closest('.component-row');
                if (this.checked) {
                    formRow.style.opacity = '0.5';
                    formRow.style.transform = 'scale(0.95)';
                } else {
                    formRow.style.opacity = '1';
                    formRow.style.transform = 'scale(1)';
                }
            });
        });
    }

    // Calculate total salary
    function calculateTotal() {
        const amountInputs = document.querySelectorAll('input[name$="-amount"]');
        let total = 0;

        amountInputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            const deleteCheckbox = input.closest('.component-row').querySelector('input[name$="-DELETE"]');

            if (!deleteCheckbox || !deleteCheckbox.checked) {
                total += value;
            }
        });

        // Display total if there's a total display element
        const totalDisplay = document.getElementById('salary-total');
        if (totalDisplay) {
            totalDisplay.textContent = total.toFixed(2);
        }
    }

    // Add event listeners for amount changes
    document.addEventListener('input', function(e) {
        if (e.target.name && e.target.name.includes('-amount')) {
            calculateTotal();
        }
    });

    document.addEventListener('change', function(e) {
        if (e.target.name && e.target.name.includes('-DELETE')) {
            calculateTotal();
        }
    });

    // Initial calculation
    calculateTotal();
});
</script>
{% endblock %} {% endcomment %}
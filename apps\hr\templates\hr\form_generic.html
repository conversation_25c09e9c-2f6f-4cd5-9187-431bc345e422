{# D:\school_fees_saas_v2\apps\hr\templates\hr\form_generic.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <h1 class="h3 mb-4">{{ view_title }}</h1>
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                        {% endif %}
                        
                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label {% if field.field.required %}required{% endif %}">{{ field.label }}</label>
                                {% render_field field class="form-control" %}
                                {% if field.help_text %}<div class="form-text text-muted small">{{ field.help_text }}</div>{% endif %}
                                {% for error in field.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        {% endfor %}
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ cancel_url }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}





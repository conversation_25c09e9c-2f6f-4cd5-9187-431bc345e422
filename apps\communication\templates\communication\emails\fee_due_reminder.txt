{% load i18n humanize %}
{% autoescape off %}
{% blocktrans with school_name=school_name %}Fe<PERSON> from {{ school_name }}{% endblocktrans %}

{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}

{% blocktrans with school_name=school_name %}This is a friendly reminder regarding outstanding or upcoming school fees for your child/children at {{ school_name }}.{% endblocktrans %}

{% if student_specific_fees %}
{% for item in student_specific_fees %}
{% blocktrans with student_name=item.student_name %}Fees for {{ student_name }}:{% endblocktrans %}
{% if item.invoices %}
{% trans "The following invoice(s) require your attention:" %}
{% for invoice in item.invoices %}
- Invoice #: {{ invoice.invoice_number }}, Due Date: {{ invoice.due_date|date:"d M Y" }}, Amount Due: {{ school_currency_symbol|default:'$' }}{{ invoice.balance_due|floatformat:2|intcomma }}
{% endfor %}
Subtotal for {{ item.student_name }}: {{ school_currency_symbol|default:'$' }}{{ item.total_due|floatformat:2|intcomma }}
{% else %}
{% trans "No specific outstanding/upcoming invoices found for this student requiring immediate attention." %}
{% endif %}
--------------------
{% endfor %}
{% endif %}

{% if overall_total_due > 0 %}
{% blocktrans with currency_symbol=school_currency_symbol|default:'$' total_due=overall_total_due|floatformat:2|intcomma %}Your total outstanding amount requiring attention is: {{ currency_symbol }}{{ total_due }}{% endblocktrans %}
{% endif %}

{% trans "Please ensure payments are made by the due dates to avoid any late fees or service interruptions." %}

{% if payment_portal_url %}
{% trans "You can view details and make payments through the parent portal:" %}
{{ payment_portal_url }}
{% endif %}

{% blocktrans %}If you have already made the payment, please disregard this reminder. If you have any questions or believe this is an error, please contact the school office.{% endblocktrans %}

{% trans "Thank you," %}
{% blocktrans with school_name=school_name %}The {{ school_name }} Administration{% endblocktrans %}

{{ school_name }}
{% if school_address %}{{ school_address }}{% endif %}
{% if school_contact_info %}{{ school_contact_info }}{% endif %}
© {% now "Y" %} {{ school_name }}.
{% endautoescape %}
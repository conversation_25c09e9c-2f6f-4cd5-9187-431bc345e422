{% extends "tenant_base.html" %}
{% load static core_tags humanize widget_tweaks %}

{% block title %}{{ view_title|default:"Manage Students" }} - {{ request.tenant.name }}{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    {# Optional: Select2 CSS if you plan to use it for filter dropdowns #}
    {# <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" /> #}
    {# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" /> #}
    <style>
        .filter-form .form-label-sm { 
            font-size: .875em; 
            margin-bottom: .25rem; 
            font-weight: 500;
        }
        .actions-column { 
            min-width: 150px; /* Adjust as needed for your buttons */
            white-space: nowrap; 
        }
        .data-table th, .data-table td { 
            vertical-align: middle; 
        }
        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 10px;
            flex-shrink: 0; /* Prevent avatar from shrinking */
        }
        .student-info {
            display: flex;
            align-items: center;
        }
        .filter-toggle { /* Button to toggle filter visibility on mobile */
            cursor: pointer;
        }
        .filter-card.collapsed { /* Controlled by JS for mobile */
            display: none;
        }
        @media (min-width: 768px) { /* Bootstrap 'md' breakpoint */
            .filter-card.collapsed { /* On md and up, filters are always visible if 'collapsed' is removed or overridden */
                display: block !important; 
            }
            #filterToggle { /* Hide the mobile toggle button on larger screens */
                display: none !important;
            }
        }
        .bulk-actions {
            background-color: #f8f9fa; /* Light background for bulk actions bar */
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            display: none; /* Initially hidden, shown by JS */
        }
        .student-row.selected {
            background-color: #cfe2ff !important; /* Bootstrap primary-bg-subtle */
        }
        /* Ensure error messages for form fields are visible */
        .invalid-feedback.d-block {
            display: block !important; 
        }
    </style>
{% endblock page_specific_css %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" with current_page_title=view_title|default:"Manage Students" %}

    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                <div>
                    <h1 class="mb-0 h2">{{ view_title|default:"Manage Students" }}</h1>
                    <small class="text-muted">
                        {% if students %}
                            {% if paginator %}
                                Showing {{ students|length }} of {{ paginator.count }} students
                            {% else %}
                                Showing {{ students|length }} students
                            {% endif %}
                            {% if request.GET.name or request.GET.current_class or request.GET.current_section or request.GET.status %}
                                (filtered)
                            {% endif %}
                        {% else %}
                            No students found
                        {% endif %}
                    </small>
                </div>
                <div class="d-flex gap-2 flex-wrap mt-2 mt-md-0">
                    {% if perms.students.add_student %}
                        <a href="{% url 'students:student_create' %}" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-1"></i>Add Student
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-outline-secondary filter-toggle" id="filterToggle">
                        <i class="bi bi-funnel me-1"></i>Filters <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                </div>
            </div>

            {% include "partials/_messages.html" %}

            <!-- Toggle Filter Section -->
            {% include "students/_student_filter_toggle.html" %}

            <!-- Bulk Actions -->
            {% if perms.students.delete_student or perms.students.change_student %}
            <div class="bulk-actions">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                    <span><strong><span id="selectedCount">0</span></strong> students selected</span>
                    <div class="btn-group btn-group-sm">
                        {% if perms.students.change_student %}
                            {# Add actual functionality for bulk status change later #}
                            <button type="button" class="btn btn-outline-primary" id="bulkStatusChangeBtn" title="Bulk change status (feature coming soon)">
                                <i class="bi bi-arrow-repeat me-1"></i>Change Status
                            </button>
                        {% endif %}
                        {% if perms.students.delete_student %}
                            <button type="button" class="btn btn-outline-danger" onclick="if(confirmBulkDelete()) document.getElementById('bulkDeleteForm').submit();">
                                <i class="bi bi-trash me-1"></i>Delete Selected
                            </button>
                        {% endif %}
                    </div>
                </div>
                {% if perms.students.delete_student %}
                <form id="bulkDeleteForm" method="post" action="{% url 'students:bulk_delete' %}" style="display: none;">
                    {% csrf_token %}
                    {# JavaScript will populate this with selected student PKs #}
                </form>
                {% endif %}
            </div>
            {% endif %}

            <!-- Students Table -->
            {% if students %}
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm data-table mb-0">
                            <thead class="table-dark">
                                <tr>
                                    {% if perms.students.delete_student or perms.students.change_student %}
                                    <th style="width: 40px;" class="text-center">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    {% endif %}
                                    <th>Student</th>
                                    <th>Admission No.</th>
                                    <th>Class & Section</th>
                                    <th>Status</th>
                                    <th>Guardian</th>
                                    <th>Contact</th>
                                    <th class="text-end actions-column">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student_obj in students %} 
                                <tr class="student-row">
                                    {% if perms.students.delete_student or perms.students.change_student %}
                                    <td class="text-center">
                                        <input type="checkbox" name="selected_students" value="{{ student_obj.pk }}" class="form-check-input student-item-checkbox">
                                    </td>
                                    {% endif %}
                                    <td>
                                        <div class="student-info">
                                            <div class="student-avatar" style="background-color: {{ student_obj.pk|divisibleby:7|yesno:'#FF6B6B,#4ECDC4,#45B7D1,#96CEB4,#FFEAA7,#DDA0DD,#98D8C8' }};">
                                                {# A more deterministic way to cycle colors based on PK #}
                                                {% with first_initial=student_obj.first_name|first|default:"" last_initial=student_obj.last_name|first|default:"" adm_initial=student_obj.admission_number|first|default:"?" %}
                                                    {{ first_initial }}{% if first_initial and last_initial %}{{ last_initial }}{% elif not first_initial and not last_initial %}{{ adm_initial }}{% endif %}
                                                {% endwith %}
                                            </div>
                                            <div>
                                                {% if perms.students.view_student %}
                                                    <a href="{% url 'students:student_detail' student_obj.pk %}" class="text-decoration-none fw-bold">
                                                        {{ student_obj.get_full_name|default:"Unnamed Student" }}
                                                    </a>
                                                {% else %}
                                                    <strong class="fw-bold">{{ student_obj.get_full_name|default:"Unnamed Student" }}</strong>
                                                {% endif %}
                                                {% if student_obj.date_of_birth %}
                                                    <br><small class="text-muted">DOB: {{ student_obj.date_of_birth|date:"M d, Y" }} ({{ student_obj.calculated_age }} yrs)</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary-subtle text-secondary-emphasis">{{ student_obj.admission_number }}</span>
                                        {% if student_obj.date_of_admission %}
                                            <br><small class="text-muted">Admitted: {{ student_obj.date_of_admission|date:"M Y" }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            {{ student_obj.current_class.name|default:"-" }}
                                            {% if student_obj.current_section %}
                                                <small class="text-muted"> / {{ student_obj.current_section.name }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge status-badge bg-{% if student_obj.status == 'ACTIVE' %}success{% elif student_obj.status == 'INACTIVE' %}danger{% elif student_obj.status == 'GRADUATED' %}info{% elif student_obj.status == 'LEFT_SCHOOL' or student_obj.status == 'SUSPENDED' %}warning text-dark{% else %}secondary{% endif %}">
                                            {{ student_obj.get_status_display|default:student_obj.status|capfirst }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if student_obj.guardian1_full_name %}
                                            {{ student_obj.guardian1_full_name }}
                                            {% if student_obj.guardian1_relationship %}
                                                <br><small class="text-muted">{{ student_obj.guardian1_relationship|capfirst }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted fst-italic">Not specified</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if student_obj.guardian1_phone %}
                                            <a href="tel:{{ student_obj.guardian1_phone }}" class="text-decoration-none d-block">
                                                <i class="bi bi-telephone me-1"></i>{{ student_obj.guardian1_phone }}
                                            </a>
                                        {% endif %}
                                        {% if student_obj.guardian1_email %}
                                            <a href="mailto:{{ student_obj.guardian1_email }}" class="text-decoration-none d-block">
                                                <i class="bi bi-envelope me-1"></i><small>{{ student_obj.guardian1_email|truncatechars:22 }}</small>
                                            </a>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        <div class="d-none d-md-flex" role="group">
                                            {% if perms.students.view_student %}
                                                <a href="{% url 'students:student_detail' student_obj.pk %}" class="btn btn-sm btn-outline-info mx-1" title="View Details"><i class="bi bi-eye-fill"></i></a>
                                            {% endif %}
                                            {% if perms.students.change_student %}
                                                <a href="{% url 'students:student_update' student_obj.pk %}" class="btn btn-sm btn-outline-secondary mx-1" title="Edit Student"><i class="bi bi-pencil-square"></i></a>
                                            {% endif %}
{% comment %} 
                                            {% if perms.fees.add_invoice %} {# Example: Link to create invoice for this student #}
                                                <a href="{% url 'fees:invoice_create_for_student' student_pk=student_obj.pk %}" class="btn btn-outline-primary" title="Create Invoice"><i class="bi bi-receipt"></i></a>
                                            {% endif %}
                                            {% endcomment %}
                                            {% if perms.students.delete_student %}
                                                <form action="{% url 'students:student_delete' student_obj.pk %}" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete {{ student_obj.get_full_name|escapejs }}? This action cannot be undone.');">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-sm btn-outline-danger mx-1" title="Delete Student"><i class="bi bi-trash"></i></button>
                                                </form>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="dropdown d-md-none">
                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="actionsDropdown{{student_obj.pk}}" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionsDropdown{{student_obj.pk}}">
                                                {% if perms.students.view_student %}
                                                    <li><a class="dropdown-item" href="{% url 'students:student_detail' student_obj.pk %}"><i class="bi bi-eye me-2"></i>View Details</a></li>
                                                {% endif %}
                                                {% if perms.students.change_student %}
                                                    <li><a class="dropdown-item" href="{% url 'students:student_update' student_obj.pk %}"><i class="bi bi-pencil me-2"></i>Edit Student</a></li>
                                                {% endif %}
{% comment %}                                                 
                                                {% if perms.fees.add_invoice %}
                                                    <li><a class="dropdown-item" href="{% url 'fees:invoice_create_for_student' student_pk=student_obj.pk %}"><i class="bi bi-receipt me-2"></i>Create Invoice</a></li>
                                                {% endif %} {% endcomment %}

                                                {% if perms.students.delete_student %}
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form action="{% url 'students:student_delete' student_obj.pk %}" method="post" onsubmit="return confirm('Are you sure you want to delete {{ student_obj.get_full_name|escapejs }}? This action cannot be undone.');" class="d-inline">
                                                            {% csrf_token %}
                                                            <button type="submit" class="dropdown-item text-danger"><i class="bi bi-trash me-2"></i>Delete Student</button>
                                                        </form>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div> 
                
                {% if is_paginated %}
                    <div class="card-footer bg-light py-2">
                        {% include "partials/_pagination.html" with page_obj=students filter_params=filter_params %}
                    </div>
                {% endif %}
            </div> 

            {# For the "No Students Found" block #}
            {% else %} {# This is the 'else' for {% if students %} #}
            <div class="text-center py-5 my-5 border rounded bg-light-subtle">
                <div class="mb-3">
                    <i class="bi bi-people display-1 text-body-tertiary"></i>
                </div>
                <h4 class="text-muted">No Students Found</h4>
                <p class="text-muted mb-3">
                    {# More direct check for active filters #}
                    {% if request.GET.name or request.GET.current_class or request.GET.current_section or request.GET.status %}
                        No students match your current filters. Try adjusting your search or
                        <a href="{% url 'students:student_list' %}" class="text-decoration-none">clear all filters</a>.
                    {% else %}
                        {% if perms.students.add_student %}
                            Get started by adding your first student to the system.
                        {% else %}
                            Please contact your administrator to add students.
                        {% endif %}
                    {% endif %}
                </p>
                {# Show "Add First Student" button only if no filters are active AND user has perm #}
                {% if perms.students.add_student and not request.GET.name and not request.GET.current_class and not request.GET.current_section and not request.GET.status %}
                    <a href="{% url 'students:student_create' %}" class="btn btn-lg btn-primary">
                        <i class="bi bi-person-plus-fill me-2"></i>Add First Student
                    </a>
                {% endif %}
            </div>
            {% endif %}

            <!-- Footer Actions -->
            <div class="footer-actions mt-4 d-flex justify-content-between align-items-center">
                <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left-circle me-1"></i>Back to Dashboard
                </a>
                

            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
{% include "students/_import_modal.html" %}

{% endblock tenant_specific_content %}
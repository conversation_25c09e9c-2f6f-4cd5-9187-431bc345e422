{# D:\school_fees_saas_v2\templates\reporting\pdf\_pdf_base.html #}
{% load static humanize %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block pdf_title %}School Document{% endblock pdf_title %}</title>
    <style type="text/css">

        /* --- Font Definition --- */
        @font-face {
            font-family: 'Inter';
            src: url('{{ settings.STATIC_ROOT }}/fonts/inter/Inter-Regular.ttf');
            font-weight: normal;
            font-style: normal;
        }
        @font-face {
            font-family: 'Inter';
            src: url('{{ settings.STATIC_ROOT }}/fonts/inter/Inter-Bold.ttf');
            font-weight: bold;
            font-style: normal;
        }
        @font-face {
            font-family: 'Inter';
            src: url('{{ settings.STATIC_ROOT }}/fonts/inter/Inter-Italic.ttf');
            font-weight: normal;
            font-style: italic;
        }
        
        /* --- Base Styles --- */
        @page {
            size: a4 portrait;
            margin: 1.5cm; /* Standard margin for printable documents */
            
            @frame header_frame {
                -pdf-frame-content: pdf_header_content;
                left: 1.5cm; right: 1.5cm; top: 1cm; height: 4cm;
            }
            @frame content_frame {
                left: 1.5cm; right: 1.5cm; top: 5.5cm; bottom: 2.5cm; /* Main content area */
            }
            @frame footer_frame {
                -pdf-frame-content: pdf_footer_content;
                left: 1.5cm; right: 1.5cm; bottom: 1cm; height: 1.5cm;
            }
        }
        
        body {
            font-family: 'Inter', sans-serif;
            font-size: 9pt;
            color: #333;
        }

        
        @page {
            size: a4 portrait;
            margin: 1.5cm; /* Overall page margin */

            @frame header_frame {
                -pdf-frame-content: pdf_header_content_wrapper;
                left: 1.2cm; right: 1.2cm; top: 0.7cm; height: 3.8cm; /* Adjusted height slightly */
            }
            @frame content_frame {
                left: 1.5cm; right: 1.5cm;
                top: 4.8cm; /* header.top + header.height + gap (0.7 + 3.8 + 0.3) */
                bottom: 2.0cm; /* footer.bottom + footer.height + gap (0.5 + 1.2 + 0.3) */
            }
            @frame footer_frame {
                -pdf-frame-content: pdf_footer_content_wrapper;
                left: 1.2cm; right: 1.2cm;
                bottom: 0.5cm; height: 1.2cm;
            }
        }
        body { font-family: "Helvetica", "Arial", sans-serif; font-size: 9pt; color: #333; line-height: 1.3; }
        
        /* Basic Resets/Defaults for elements often used in PDF content */
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 1.5mm 2mm; text-align: left; vertical-align: top; /* Default thin border if needed globally */ /* border: 0.25px solid #ddd; */ }
        h1, h2, h3, h4, p { margin: 0; padding: 0; font-weight: normal; } /* Reset margins */
        h1 { font-size: 18pt; } h2 { font-size: 14pt; } h3 { font-size: 12pt; } h4 { font-size: 10pt; }
        
        /* Utility classes (can be used by any child PDF template) */
        .fw-bold { font-weight: bold !important; }
        .text-right { text-align: right !important; }
        .text-center { text-align: center !important; }
        .text-uppercase { text-transform: uppercase !important; }
        .italic { font-style: italic !important; }
        .muted { color: #6c757d !important; }
        .primary-color { color: #0056b3 !important; } /* Example primary color */
        .accent-color { color: #007bff !important; } /* Example accent color */
        .warning-text { color: #c0392b !important; } /* Example warning/due date color */

        /* Header specific base styles - child can override or add to */
        #pdf_header_content_wrapper { width: 100%; }
        #pdf_header_content_wrapper table { border: none; }
        #pdf_header_content_wrapper td { border: none; }

        /* Footer specific base styles */
        #pdf_footer_content_wrapper { text-align: center; font-size: 8pt; color: #6c757d; }
        #pdf_footer_content_wrapper .page-number::after { content: "Page " counter(page) " of " counter(pages); }
        #pdf_footer_content_wrapper p { margin: 1mm 0; }

        {% block pdf_base_extra_styles %}{% endblock pdf_base_extra_styles %}
    </style>
    {% block pdf_extra_styles %}{% endblock pdf_extra_styles %}
</head>
<body>
    <div id="pdf_header_content_wrapper">
        {% block pdf_header_content %}
            {# Default header - often overridden entirely by specific document templates like invoice #}
            <table style="width:100%;"><tr>
                <td style="width:70%;">
                    {% if school_profile %}
                        <h2 class="fw-bold" style="color:#333;">{{ school_profile.school_name_on_reports|default:tenant.name }}</h2>
                    {% else %}
                        <h2 class="fw-bold" style="color:#333;">{{ tenant.name|default:"School Name" }}</h2>
                    {% endif %}
                </td>
                <td style="width:30%; text-align:right;">
                    <h1 style="color:#555;">{% block document_name_header %}DOCUMENT{% endblock document_name_header %}</h1>
                </td>
            </tr></table>
            <hr style="margin:1mm 0; border:none; border-top: 0.5px solid #999;">
        {% endblock pdf_header_content %}
    </div>

    <div id="pdf_footer_content_wrapper">
        {% block pdf_footer_content %}
        <p class="page-number"></p>
        <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
        {% endblock pdf_footer_content %}
    </div>

    {# Main content flows here - xhtml2pdf puts this into content_frame #}
    {% block pdf_main_content %}
        <p style="color:red; text-align:center;">Error: PDF Main Content block not defined by child template.</p>
    {% endblock pdf_main_content %}
</body>
</html>


{# D:\school_fees_saas_v2\templates\platform_management\generic_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Manage Item") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .form-check {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .form-check-input {
            width: 1.5rem;
            height: 1.5rem;
            margin-top: 0.125rem;
            border: 2px solid #667eea;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .generic-form-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border: 2px solid #28a745;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .generic-form-info h6 {
            color: #155724;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .generic-form-info p {
            color: #155724;
            margin: 0;
            font-size: 0.9rem;
        }

        /* Legacy field styling for backward compatibility */
        .legacy-field-row {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
        }

        .legacy-field-row label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .legacy-field-row .form-control,
        .legacy-field-row .form-select {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .legacy-field-row .form-control:focus,
        .legacy-field-row .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }
    </style>
{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Platform Management") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{{ view_title|default:_("Form") }}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-gear-fill me-2"></i>
                        {% if form_mode == "create" %}
                            {% trans "Create New Item" %}
                        {% else %}
                            {% trans "Update Item" %}
                        {% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Information Section -->
                    <div class="generic-form-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "Generic Platform Form" %}</h6>
                        <p>{% trans "This is a generic form template for platform management. It provides consistent styling and functionality for all platform administration forms." %}</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        {% for field in form.visible_fields %}
                            {% if field|widget_type == 'checkboxinput' %}
                                <!-- Checkbox Field -->
                                <div class="form-check">
                                    <input type="checkbox"
                                           class="form-check-input{% if field.errors %} is-invalid{% endif %}"
                                           id="{{ field.id_for_label }}"
                                           name="{{ field.name }}"
                                           value="1"
                                           {% if field.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ field.id_for_label }}">
                                        <i class="bi bi-check-square me-2"></i>{{ field.label }}
                                    </label>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% elif field|widget_type == 'checkboxselectmultiple' or field|widget_type == 'radioselect' %}
                                <!-- Multiple Choice Field -->
                                <div class="legacy-field-row">
                                    <label class="form-label">
                                        <i class="bi bi-list-check me-2"></i>{{ field.label }}
                                        {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    <div class="checkbox-select-multiple-custom">
                                        {% for choice in field %}
                                            <div class="form-check form-check-inline">
                                                {{ choice.tag }}
                                                <label for="{{ choice.id_for_label }}" class="form-check-label">{{ choice.choice_label }}</label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% elif field|widget_type == 'textarea' %}
                                <!-- Textarea Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if field.errors %} is-invalid{% endif %}"
                                              id="{{ field.id_for_label }}"
                                              name="{{ field.name }}"
                                              placeholder="{{ field.label }}"
                                              style="height: 120px;"
                                              {% if field.field.required %}required{% endif %}>{{ field.value|default:'' }}</textarea>
                                    <label for="{{ field.id_for_label }}">
                                        <i class="bi bi-textarea-resize icon-input"></i>{{ field.label }}
                                    </label>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% elif field|widget_type == 'select' %}
                                <!-- Select Field -->
                                <div class="form-floating">
                                    <select class="form-select{% if field.errors %} is-invalid{% endif %}"
                                            id="{{ field.id_for_label }}"
                                            name="{{ field.name }}"
                                            {% if field.field.required %}required{% endif %}>
                                        <option value="">{% trans "Select Option" %}</option>
                                        {% for value, label in field.field.choices %}
                                            <option value="{{ value }}" {% if field.value == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="{{ field.id_for_label }}">
                                        <i class="bi bi-list icon-input"></i>{{ field.label }}
                                    </label>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% elif field|widget_type == 'datetimeinput' or field|widget_type == 'dateinput' %}
                                <!-- Date/DateTime Field -->
                                <div class="form-floating">
                                    <input type="{% if field|widget_type == 'datetimeinput' %}datetime-local{% else %}date{% endif %}"
                                           class="form-control{% if field.errors %} is-invalid{% endif %}"
                                           id="{{ field.id_for_label }}"
                                           name="{{ field.name }}"
                                           value="{{ field.value|default:'' }}"
                                           {% if field.field.required %}required{% endif %}>
                                    <label for="{{ field.id_for_label }}">
                                        <i class="bi bi-calendar-event icon-input"></i>{{ field.label }}
                                    </label>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <!-- Default Text Input Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if field.errors %} is-invalid{% endif %}"
                                           id="{{ field.id_for_label }}"
                                           name="{{ field.name }}"
                                           value="{{ field.value|default:'' }}"
                                           placeholder="{{ field.label }}"
                                           {% if field.field.required %}required{% endif %}>
                                    <label for="{{ field.id_for_label }}">
                                        <i class="bi bi-input-cursor-text icon-input"></i>{{ field.label }}
                                    </label>
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{{ cancel_url|default:request.META.HTTP_REFERER|default_if_none:'javascript:history.back();' }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if form_mode == "create" %}{% trans "Create Item" %}{% else %}{% trans "Update Item" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation and enhancement
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Auto-focus first input
        const firstInput = form.querySelector('input[type="text"], textarea, select');
        if (firstInput) {
            firstInput.focus();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // Character counter for textarea fields
        const textareaFields = document.querySelectorAll('textarea');
        textareaFields.forEach(function(textarea) {
            const maxLength = 500; // Default max length
            const counter = document.createElement('div');
            counter.className = 'form-text text-end';
            counter.style.fontSize = '0.8rem';

            function updateCounter() {
                const remaining = maxLength - textarea.value.length;
                counter.textContent = `${textarea.value.length}/${maxLength} characters`;
                counter.style.color = remaining < 50 ? '#dc3545' : '#6c757d';
            }

            textarea.addEventListener('input', updateCounter);
            textarea.parentElement.appendChild(counter);
            updateCounter();
        });

        // Date field validation
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
        dateInputs.forEach(function(dateInput) {
            dateInput.addEventListener('change', function() {
                const selectedDate = new Date(this.value);
                const today = new Date();

                // Add custom validation logic here if needed
                if (selectedDate < today && this.dataset.allowPast !== 'true') {
                    this.setCustomValidity('{% trans "Date cannot be in the past" %}');
                } else {
                    this.setCustomValidity('');
                }
            });
        });

        // Enhanced checkbox styling
        const checkboxes = document.querySelectorAll('.form-check-input');
        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                const checkContainer = this.closest('.form-check');
                if (this.checked) {
                    checkContainer.style.background = 'rgba(102, 126, 234, 0.1)';
                    checkContainer.style.borderColor = '#667eea';
                } else {
                    checkContainer.style.background = '#f8f9fa';
                    checkContainer.style.borderColor = '#e9ecef';
                }
            });
        });

        // Form field validation feedback
        const invalidFields = document.querySelectorAll('.is-invalid');
        invalidFields.forEach(function(field) {
            field.addEventListener('input', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    });
    </script>
{% endblock %}
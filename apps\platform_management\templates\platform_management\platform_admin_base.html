{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platform_admin_base.html #}
{% extends "base.html" %} {# Or your absolute project base if different #}
{% load static i18n %}

{% block title %}
    {% block platform_admin_page_title %}Platform Admin{% endblock platform_admin_page_title %} - {{ settings.PLATFORM_NAME|default:"School Fees SaaS" }}
{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    {# Load Bootstrap Icons if not loaded in base.html #}
    {# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"> #}
    
    {# Load Google Fonts if not loaded in base.html #}
    {# <link rel="preconnect" href="https://fonts.googleapis.com"> ... #}

    <link rel="stylesheet" href="{% static 'platform_management/css/dashboard_styles.css' %}">
    {# Add a more general platform_admin_styles.css if you have one for overall admin area #}
    {# <link rel="stylesheet" href="{% static 'platform_management/css/platform_admin_styles.css' %}"> #}
    
    {% block extra_platform_admin_css %}{% endblock %}
{% endblock page_specific_css %}

{% block body_class %}platform-admin-dashboard-page{% endblock %} {# Specific class for this page type #}

{% block body_content %}
    {# Platform Admin Navbar - Could be different from public_base or tenant_base #}
    {% include "platform_management/includes/_platform_admin_navbar.html" %} 

    {# Optional: Platform Admin Sidebar #}
    {# {% include "platform_management/includes/_platform_admin_sidebar.html" %} #}

    <main id="main" class="main-content p-3 ms-sm-auto" {% if PLATFORM_ADMIN_SIDEBAR_ENABLED %}style="margin-left: 250px;"{% endif %}> {# Adjust margin if sidebar exists #}
        {% block platform_admin_page_content_wrapper %}
            {# Messages can go here, or inside the specific content block #}
            {% if messages %}
            <div class="container-fluid"> {# Or just container based on preference #}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block platform_admin_page_content %}
                <p class="alert alert-warning">Content for 'platform_admin_page_content' block is missing in the child template.</p>
            {% endblock platform_admin_page_content %}
        {% endblock platform_admin_page_content_wrapper %}
    </main>

    {# Optional: Platform Admin Footer #}
    {% include "platform_management/includes/_platform_admin_footer.html" %}

{% endblock body_content %}

{% block page_specific_js %}
    {{ block.super }}
    {% block extra_platform_admin_js %}{% endblock %}
{% endblock page_specific_js %}



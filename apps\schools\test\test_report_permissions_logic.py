# D:\school_fees_saas_v2\apps\schools\tests\test_report_permissions_logic.py

from django.test import TestCase
from unittest.mock import MagicMock # unittest.mock is built into Python 3.3+

# Adjust the import path based on where calculate_reports_permissions is defined
# If it's in apps.schools.views:
from apps.schools.views import calculate_reports_permissions
# If you move it to apps.schools.utils:
# from apps.schools.utils import calculate_reports_permissions
# If you move it to apps.common.utils:
# from apps.common.utils import calculate_reports_permissions


class TestCalculateReportsPermissions(TestCase):
    
    def setUp(self):
        """Set up common mocks for each test method."""
        self.user = MagicMock()  # Mocks a user object
        
        # Default tenant_features: all features off
        self.tenant_features = {
            'REPORTS_BASIC': False,
            'REPORTS_ADVANCED': False,
            'BUDGETING': False,
            # Add any other tenant_features your function might implicitly access or expect
        }

    def test_all_features_off_no_perms(self):
        """Test when all features are off and user has no permissions."""
        self.user.has_perm.return_value = False # User has no permissions at all

        result = calculate_reports_permissions(self.user, self.tenant_features)

        # Assertions for link visibility
        self.assertFalse(result['show_outstanding_fees_link'])
        self.assertFalse(result['show_collection_report_link'])
        self.assertFalse(result['show_trial_balance_link'])
        self.assertFalse(result['show_income_statement_link'])
        self.assertFalse(result['show_balance_sheet_link'])
        self.assertFalse(result['show_cash_flow_link'])
        self.assertFalse(result['show_budget_variance_link'])
        
        # Assertions for overall dropdown and dividers
        self.assertFalse(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_basic_advanced_divider'])
        self.assertFalse(result['show_advanced_budget_divider'])

    def test_basic_feature_on_with_specific_perm(self):
        """Test basic reports feature ON, user has one specific basic perm."""
        self.tenant_features['REPORTS_BASIC'] = True
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_outstanding_fees_report'

        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertTrue(result['show_outstanding_fees_link'])
        self.assertFalse(result['show_collection_report_link']) # No perm for this
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_basic_advanced_divider']) # No advanced reports visible

    def test_advanced_feature_on_with_specific_perm(self):
        """Test advanced reports feature ON, user has one specific advanced perm."""
        self.tenant_features['REPORTS_ADVANCED'] = True
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_trial_balance_report'

        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertFalse(result['show_outstanding_fees_link']) # Basic feature off
        self.assertTrue(result['show_trial_balance_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_basic_advanced_divider']) # No basic reports visible

    def test_budgeting_on_advanced_off_specific_budget_perm(self):
        """Test BUDGETING on, REPORTS_ADVANCED off, user has budget variance perm."""
        self.tenant_features['BUDGETING'] = True
        self.tenant_features['REPORTS_ADVANCED'] = False 
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_budget_variance_report'

        result = calculate_reports_permissions(self.user, self.tenant_features)
        
        # Based on current Python logic for show_budget_variance_link:
        # show_budget_variance_link = (
        #    tenant_features.get('BUDGETING', False) and 
        #    (tenant_features.get('REPORTS_ADVANCED', False) or user.has_perm('reporting.view_budget_variance_report'))
        # )
        # So, (True and (False or True)) -> True
        self.assertTrue(result['show_budget_variance_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_advanced_budget_divider']) # No other basic/advanced reports

    def test_budgeting_on_advanced_on_no_specific_budget_perm(self):
        """Test BUDGETING on, REPORTS_ADVANCED on, user lacks budget variance perm but has other advanced."""
        self.tenant_features['BUDGETING'] = True
        self.tenant_features['REPORTS_ADVANCED'] = True
        # User has trial balance perm, but not budget variance perm
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_trial_balance_report'

        result = calculate_reports_permissions(self.user, self.tenant_features)

        # Based on current Python logic for show_budget_variance_link:
        # (True and (True or False)) -> True
        self.assertTrue(result['show_budget_variance_link'])
        self.assertTrue(result['show_trial_balance_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertTrue(result['show_advanced_budget_divider']) # Both advanced and budget sections have something

    def test_all_features_on_all_perms_on(self):
        """Test all features ON and user has all relevant permissions."""
        self.tenant_features['REPORTS_BASIC'] = True
        self.tenant_features['REPORTS_ADVANCED'] = True
        self.tenant_features['BUDGETING'] = True
        self.user.has_perm.return_value = True # User has all permissions

        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertTrue(result['show_outstanding_fees_link'])
        self.assertTrue(result['show_collection_report_link'])
        self.assertTrue(result['show_trial_balance_link'])
        self.assertTrue(result['show_income_statement_link'])
        self.assertTrue(result['show_balance_sheet_link'])
        self.assertTrue(result['show_cash_flow_link'])
        self.assertTrue(result['show_budget_variance_link'])
        
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertTrue(result['show_basic_advanced_divider'])
        self.assertTrue(result['show_advanced_budget_divider'])

    def test_basic_on_no_perms_for_basic(self):
        """Test basic feature ON, but user has no permissions for basic reports."""
        self.tenant_features['REPORTS_BASIC'] = True
        self.user.has_perm.return_value = False # User has no perms

        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertFalse(result['show_outstanding_fees_link'])
        self.assertFalse(result['show_collection_report_link'])
        self.assertFalse(result['can_see_reports_dropdown']) # Because no links are actually showable

    def test_only_one_basic_report_visible(self):
        """Test when only one basic report is visible, no advanced."""
        self.tenant_features['REPORTS_BASIC'] = True
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_collection_report'
        
        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertFalse(result['show_outstanding_fees_link'])
        self.assertTrue(result['show_collection_report_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_basic_advanced_divider']) # No advanced reports shown

    def test_only_one_advanced_report_visible(self):
        """Test when only one advanced report is visible, no basic."""
        self.tenant_features['REPORTS_ADVANCED'] = True
        self.user.has_perm.side_effect = lambda perm_string: perm_string == 'reporting.view_balance_sheet_report'

        result = calculate_reports_permissions(self.user, self.tenant_features)
        
        self.assertFalse(result['show_outstanding_fees_link']) # Basic feature off
        self.assertTrue(result['show_balance_sheet_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertFalse(result['show_basic_advanced_divider']) # No basic reports shown
        self.assertFalse(result['show_advanced_budget_divider']) # Budget variance not shown

    def test_basic_and_advanced_visible_show_divider(self):
        """Test that basic_advanced_divider shows when both sections have links."""
        self.tenant_features['REPORTS_BASIC'] = True
        self.tenant_features['REPORTS_ADVANCED'] = True
        def mock_has_perm(perm_string):
            return perm_string in [
                'reporting.view_outstanding_fees_report', 
                'reporting.view_trial_balance_report'
            ]
        self.user.has_perm.side_effect = mock_has_perm

        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertTrue(result['show_outstanding_fees_link'])
        self.assertTrue(result['show_trial_balance_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertTrue(result['show_basic_advanced_divider'])

    def test_advanced_and_budget_visible_show_divider(self):
        """Test that advanced_budget_divider shows when advanced and budget sections have links."""
        self.tenant_features['REPORTS_ADVANCED'] = True
        self.tenant_features['BUDGETING'] = True
        def mock_has_perm(perm_string):
            return perm_string in [
                'reporting.view_trial_balance_report', 
                'reporting.view_budget_variance_report'
            ]
        self.user.has_perm.side_effect = mock_has_perm
        
        result = calculate_reports_permissions(self.user, self.tenant_features)

        self.assertTrue(result['show_trial_balance_link'])
        self.assertTrue(result['show_budget_variance_link'])
        self.assertTrue(result['can_see_reports_dropdown'])
        self.assertTrue(result['show_advanced_budget_divider'])
        
    # You can add more tests to cover various edge cases and combinations.
    
    
    
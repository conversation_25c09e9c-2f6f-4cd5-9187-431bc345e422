# Generated by Django 5.1.9 on 2025-07-07 02:40

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0006_alter_payslip_options_and_more'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='salarygradecomponent',
            options={'ordering': ['component__name']},
        ),
        migrations.AlterUniqueTogether(
            name='salarygrade',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='salarycomponent',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Only active grades can be assigned to staff.'),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='name',
            field=models.CharField(help_text='e.g., Grade A, Level 3, etc.', max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='salarygradecomponent',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='salarygradecomponent',
            name='component',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='hr.salarycomponent'),
        ),
        migrations.CreateModel(
            name='StaffSalaryStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(default=django.utils.timezone.now, help_text='The date this salary structure is effective from.')),
                ('staff_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='salary_structure', to='schools.staffuser')),
            ],
        ),
        migrations.CreateModel(
            name='SalaryStructureComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='structure_lines', to='hr.salarycomponent')),
                ('salary_structure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='hr.staffsalarystructure')),
            ],
            options={
                'unique_together': {('salary_structure', 'component')},
            },
        ),
        migrations.DeleteModel(
            name='StaffSalary',
        ),
    ]

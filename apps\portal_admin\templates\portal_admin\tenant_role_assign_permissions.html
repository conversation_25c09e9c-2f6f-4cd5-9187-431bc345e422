{# apps/portal_admin/templates/portal_admin/tenant_role_assign_permissions.html - Premium Design Version #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block title %}{{ view_title }}{% endblock %}

{% block page_specific_css %}
{{ block.super }}
<style>
    /* Premium Form Design - Tenant Role Assign Permissions */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-card-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-card-header h3 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
    }

    .premium-card-body {
        padding: 2.5rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .breadcrumb-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: transparent;
    }

    .breadcrumb-item a {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .info-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #2196f3;
    }

    .info-section h5 {
        color: #1976d2;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .info-section p {
        color: #424242;
        margin: 0;
        line-height: 1.6;
    }

    .role-info-card {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #ffc107;
        text-align: center;
    }

    .role-info-card h5 {
        color: #856404;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .role-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #495057;
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        display: inline-block;
        margin-top: 0.5rem;
    }

    .permission-app-group {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .permission-app-group:hover {
        border-color: #007bff;
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
    }

    .permission-app-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        position: relative;
        overflow: hidden;
    }

    .permission-app-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .permission-app-header:hover::before {
        left: 100%;
    }

    .permission-app-body {
        padding: 1.5rem;
    }

    .permission-model-group {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e9ecef;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .permission-model-group:hover {
        background: rgba(0, 123, 255, 0.05);
        border-color: #007bff;
    }

    .permission-model-header {
        font-size: 0.95rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .permission-item {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .permission-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .permission-item:hover {
        border-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
        transform: translateX(3px);
    }

    .permission-item:hover::before {
        left: 100%;
    }

    .permission-item.checked {
        border-color: #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    .permission-item.checked::after {
        content: '✓';
        position: absolute;
        top: 0.5rem;
        right: 0.75rem;
        color: #28a745;
        font-weight: bold;
        font-size: 1rem;
    }

    .permission-checkbox {
        margin-right: 0.75rem;
        transform: scale(1.2);
        accent-color: #007bff;
    }

    .permission-checkbox:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .permission-label {
        font-size: 0.9rem;
        color: #495057;
        font-weight: 500;
        line-height: 1.4;
        cursor: pointer;
        flex: 1;
        transition: color 0.3s ease;
    }

    .permission-item:hover .permission-label {
        color: #007bff;
    }

    .permission-checkbox:checked + .permission-label {
        color: #28a745;
        font-weight: 600;
    }

    .permission-codename {
        font-size: 0.75rem;
        color: #6c757d;
        font-style: italic;
        margin-left: 0.5rem;
    }

    .btn {
        border-radius: 0.75rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .selection-summary {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
        text-align: center;
    }

    .selection-summary h6 {
        color: #155724;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .selection-counter {
        font-size: 1.5rem;
        font-weight: 700;
        color: #28a745;
    }

    @media (max-width: 768px) {
        .premium-card-header {
            padding: 1.5rem;
        }

        .premium-card-header h3 {
            font-size: 1.5rem;
        }

        .premium-card-body {
            padding: 1.5rem;
        }

        .permission-app-body {
            padding: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .permission-model-group {
            padding: 0.75rem;
        }

        .permission-item {
            padding: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb-container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'portal_admin:tenant_role_list' %}">
                                <i class="bi bi-shield-lock me-1"></i>{% trans "Tenant Roles" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            {% trans "Assign Permissions" %}
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h5><i class="bi bi-shield-check me-2"></i>{% trans "Permission Assignment" %}</h5>
                <p>
                    {% trans "Configure the specific system permissions that staff members belonging to this role will have. Permissions are organized by application and model for easy management." %}
                </p>
            </div>

            <!-- Role Information Card -->
            <div class="role-info-card">
                <h5><i class="bi bi-person-badge me-2"></i>{% trans "Configuring Permissions for Role" %}</h5>
                <div class="role-name">{{ tenant_role.name }}</div>
            </div>

            <!-- Premium Form Card -->
            <div class="card premium-card">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-shield-lock-fill me-2"></i>
                        {{ view_title }}
                    </h3>
                </div>
                <div class="premium-card-body">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="permissionAssignmentForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <!-- Selection Summary -->
                        <div class="selection-summary" id="selectionSummary">
                            <h6><i class="bi bi-check-circle me-2"></i>{% trans "Selected Permissions" %}</h6>
                            <div class="selection-counter" id="selectionCounter">0</div>
                            <small class="text-muted">{% trans "permissions selected" %}</small>
                        </div>

                        <!-- Permission Groups -->
                        {% for app_group_name, models_with_perms in grouped_permissions.items %}
                            <div class="permission-app-group">
                                <div class="permission-app-header">
                                    <i class="bi bi-folder me-2"></i>{{ app_group_name }}
                                    <span class="badge bg-light text-dark ms-2" id="appCounter_{{ forloop.counter }}">0</span>
                                </div>
                                <div class="permission-app-body">
                                    {% for model_group_name, perms_list in models_with_perms.items %}
                                        <div class="permission-model-group">
                                            <div class="permission-model-header">
                                                <i class="bi bi-gear me-2"></i>{{ model_group_name }}
                                                <span class="badge bg-secondary ms-2">{{ perms_list|length }}</span>
                                            </div>
                                            {% for perm in perms_list %}
                                                <div class="permission-item" data-app="{{ forloop.parentloop.parentloop.counter }}" data-perm-id="{{ perm.pk }}">
                                                    <div class="d-flex align-items-center">
                                                        <input
                                                            type="checkbox"
                                                            class="permission-checkbox"
                                                            name="{{ form.permissions.html_name }}"
                                                            value="{{ perm.pk }}"
                                                            id="id_perm_{{ perm.pk }}"
                                                            {% if perm in form.permissions.initial %}checked{% endif %}
                                                        >
                                                        <label class="permission-label" for="id_perm_{{ perm.pk }}">
                                                            <i class="bi bi-key me-2"></i>{{ perm.name }}
                                                            <span class="permission-codename">({{ perm.content_type.app_label }}.{{ perm.codename }})</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        {% if not forloop.last %}<hr class="my-2">{% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% empty %}
                            <div class="text-center py-4">
                                <i class="bi bi-info-circle text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mt-2">{% trans "No system permissions available to assign." %}</p>
                            </div>
                        {% endfor %}

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'portal_admin:tenant_role_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-shield-check me-2"></i>{% trans "Save Permissions for Role" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Permission selection counter
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    const selectionCounter = document.getElementById('selectionCounter');
    const selectionSummary = document.getElementById('selectionSummary');

    function updateSelectionCounter() {
        const checkedCount = document.querySelectorAll('.permission-checkbox:checked').length;
        selectionCounter.textContent = checkedCount;

        // Update summary card appearance based on selection
        if (checkedCount > 0) {
            selectionSummary.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            selectionSummary.style.borderLeftColor = '#28a745';
        } else {
            selectionSummary.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
            selectionSummary.style.borderLeftColor = '#6c757d';
        }

        // Update app counters
        for (let i = 1; i <= 10; i++) {
            const appCounter = document.getElementById('appCounter_' + i);
            if (appCounter) {
                const appCheckboxes = document.querySelectorAll(`.permission-item[data-app="${i}"] .permission-checkbox:checked`);
                appCounter.textContent = appCheckboxes.length;
            }
        }
    }

    // Add event listeners to all checkboxes
    checkboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelectionCounter);
    });

    // Initial count
    updateSelectionCounter();

    // Form submission enhancement
    const form = document.getElementById('permissionAssignmentForm');
    const submitBtn = document.getElementById('submitBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Saving..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-shield-check me-2"></i>{% trans "Save Permissions for Role" %}';
            }, 5000);
        });
    }

    // Enhanced permission item interactions
    const permissionItems = document.querySelectorAll('.permission-item');
    permissionItems.forEach(function(item) {
        const checkbox = item.querySelector('.permission-checkbox');
        const label = item.querySelector('.permission-label');

        // Click anywhere on the item to toggle checkbox
        item.addEventListener('click', function(e) {
            if (e.target !== checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Visual feedback for checked state
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                item.classList.add('checked');
            } else {
                item.classList.remove('checked');
            }
        });

        // Initial state
        if (checkbox.checked) {
            item.classList.add('checked');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+A to select all permissions
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            const allCheckboxes = document.querySelectorAll('.permission-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            allCheckboxes.forEach(function(cb) {
                cb.checked = !allChecked;
                cb.dispatchEvent(new Event('change'));
            });
        }

        // Escape to cancel
        if (e.key === 'Escape') {
            const cancelBtn = document.querySelector('a[href*="tenant_role_list"]');
            if (cancelBtn) {
                cancelBtn.click();
            }
        }
    });

    console.log('Permission assignment form initialized with', checkboxes.length, 'permissions');
});
</script>
{% endblock %}



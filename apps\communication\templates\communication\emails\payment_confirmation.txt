{% load i18n humanize %}
{% autoescape off %}
{% blocktrans with school_name=school_name %}Payment Confirmation - {{ school_name }}{% endblocktrans %}

{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}

{% blocktrans with school_name=school_name %}Thank you for your payment. We have successfully received it. Please find the details below:{% endblocktrans %}

{% trans "Payment Summary" %}
--------------------
{% trans "Payment ID/Receipt No.:" %} {{ payment_details.id|default:"N/A" }}
{% trans "Payment Date:" %} {{ payment_details.date|date:"d M Y, P" }}
{% trans "Amount Paid:" %} {{ school_currency_symbol|default:'$' }}{{ payment_details.amount|floatformat:2|intcomma }}
{% trans "Payment Method:" %} {{ payment_details.method|default:"N/A" }}
{% trans "Status:" %} {{ payment_details.status|default:"Processed" }}
{% if payment_details.paid_for_student %}
{% trans "For Student:" %} {{ payment_details.paid_for_student }}
{% endif %}
{% if payment_details.invoices_covered %}
{% trans "Applied to Invoice(s):" %} {{ payment_details.invoices_covered|join:", " }}
{% endif %}
--------------------

{% if payment_portal_url %}
{% blocktrans %}You can view your updated balance and payment history in the parent portal:{% endblocktrans %}
{{ payment_portal_url }}
{% endif %}

{% blocktrans %}If you have any questions regarding this payment, please do not hesitate to contact the school office.{% endblocktrans %}

{% trans "Sincerely," %}
{% blocktrans with school_name=school_name %}The {{ school_name }} Accounts Team{% endblocktrans %}

{{ school_name }}
{% if school_address %}{{ school_address }}{% endif %}
{% if school_contact_info %}{{ school_contact_info }}{% endif %}
© {% now "Y" %} {{ school_name }}.
{% endautoescape %}



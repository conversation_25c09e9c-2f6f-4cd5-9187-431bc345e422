import os
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from django.conf import settings

# Import all necessary models
from apps.tenants.models import School, Domain
from apps.users.models import User as SchoolOwner # Using an alias for clarity
from apps.subscriptions.models import SubscriptionPlan, Subscription

class Command(BaseCommand):
    help = 'Creates a new tenant, domain, owner, and subscription for development purposes.'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help="The schema name and subdomain for the tenant (e.g., 'alpha').")
        parser.add_argument('school_name', type=str, help="The full name of the school (e.g., 'Danhamombe High School').")
        parser.add_argument('owner_email', type=str, help="The email for the school's owner/admin account.")
        parser.add_argument('owner_password', type=str, help="The password for the owner's account.")
        parser.add_argument('plan_id', type=int, help="The ID of the SubscriptionPlan to assign to this tenant.")
        parser.add_argument(
            '--cycle',
            type=str,
            choices=['MONTHLY', 'ANNUALLY'],
            default='MONTHLY',
            help="The billing cycle for the subscription (MONTHLY or ANNUALLY). Defaults to MONTHLY."
        )

    @transaction.atomic
    def handle(self, *args, **options):
        schema_name = options['schema_name']
        school_name = options['school_name']
        owner_email = options['owner_email']
        owner_password = options['owner_password']
        plan_id = options['plan_id']
        billing_cycle = options['cycle']

        # --- Validations ---
        if School.objects.filter(schema_name=schema_name).exists() or \
           Domain.objects.filter(domain__startswith=schema_name).exists():
            raise CommandError(f"A tenant with schema or domain '{schema_name}' already exists.")

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id)
            self.stdout.write(self.style.SUCCESS(f"Found subscription plan: '{plan.name}'"))
        except SubscriptionPlan.DoesNotExist:
            raise CommandError(f"SubscriptionPlan with ID '{plan_id}' does not exist.")

        # --- Get the base domain from settings ---
        base_domain = settings.TENANT_BASE_DOMAIN.split(':')[0]
        tenant_domain = f"{schema_name}.{base_domain}"

        # --- Create School Owner (User) in Public Schema ---
        self.stdout.write(f"Creating owner account: {owner_email}...")
        try:
            # Using your custom user model logic
            owner = SchoolOwner.objects.create_user(
                email=owner_email,
                password=owner_password,
                first_name=school_name.split()[0] if school_name else "School",
                last_name="Admin"
            )
            # No need for is_active=True, create_user handles it.
            # You might have other flags to set, e.g., owner.is_school_owner = True
            self.stdout.write(self.style.SUCCESS(f"Owner account (PK: {owner.pk}) created successfully."))
        except Exception as e:
            raise CommandError(f"Failed to create owner account: {e}")


        # --- Create School (Tenant) in Public Schema ---
        self.stdout.write(f"Creating tenant '{school_name}' with schema '{schema_name}'...")
        try:
            # This triggers auto_create_schema=True
            tenant = School.objects.create(
                schema_name=schema_name,
                name=school_name,
                owner=owner,
            )
            self.stdout.write(self.style.SUCCESS(f"Tenant object (PK: {tenant.pk}) created. Schema creation initiated by django-tenants."))
        except Exception as e:
            raise CommandError(f"Failed to create tenant: {e}")


        # --- Create Domain for the Tenant ---
        self.stdout.write(f"Creating domain '{tenant_domain}'...")
        try:
            Domain.objects.create(
                domain=tenant_domain,
                tenant=tenant,
                is_primary=True
            )
            self.stdout.write(self.style.SUCCESS("Domain created successfully."))
        except Exception as e:
            raise CommandError(f"Failed to create domain: {e}")


        # --- Create the Subscription record ---
        self.stdout.write(f"Creating initial subscription record for '{billing_cycle}' cycle...")
        
        # Determine the price to snapshot based on the chosen cycle
        if billing_cycle == 'MONTHLY':
            price_snapshot = plan.price_monthly
        else:
            price_snapshot = plan.price_annually
            
        try:
            Subscription.objects.create(
                school=tenant,
                plan=plan,
                status='ACTIVE', # Use 'TRIALING' if you have trial logic
                billing_cycle=billing_cycle,
                
                # --- THIS IS THE KEY ADDITION ---
                price_at_subscription=price_snapshot,
                
                current_period_start=timezone.now(),
                current_period_end=timezone.now() + timezone.timedelta(days=30),
            )
            self.stdout.write(self.style.SUCCESS(f"Subscription record created with price {price_snapshot}."))
        except Exception as e:
            raise CommandError(f"Failed to create subscription: {e}")

        self.stdout.write(self.style.SUCCESS(f"\nTenant '{school_name}' created successfully!"))
        self.stdout.write(f"Access it at: http://{tenant_domain}:8000")
        
        
        

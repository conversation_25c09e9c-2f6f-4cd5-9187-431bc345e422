{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\chartofaccount_list.html #}
{% extends "tenant_base.html" %}

{% load static i18n mptt_tags math_filters %} {# IMPORTANT: Load mptt_tags #}

{% block tenant_page_title %}{{ view_title|default:"Chart of Accounts" }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .coa-table .node-name {
            font-weight: 500;
        }
        .coa-table .node-level-indicator {
            display: inline-block;
            width: 1.5rem; /* For alignment of child nodes */
        }
        .table .actions-column .btn {
            padding: 0.1rem 0.4rem;
            font-size: 0.8rem;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">{{ view_title|default:"Chart of Accounts" }}</h1>
        {% if perms.accounting.add_account %}
        <a href="{% url 'accounting:account_create' %}" class="btn btn-primary btn-sm">
            <i class="bi bi-plus-circle me-1"></i> Add New Account
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}


    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">Accounts Listing</h6>
        </div>
        <div class="card-body p-0">
            {# 1. First, check if there are any accounts at all #}
            {% with account_list=accounts|default:object_list %}
            {% if account_list %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm coa-table mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 40%;">Name</th>
                                <th>Code</th>
                                <th>Type</th>
                                <th class="text-center">Active</th>
                                <th class="text-center">Control A/C</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {# 2. If there are accounts, use recursetree to loop through them #}
                            {% recursetree account_list %}
                                <tr>
                                    <td style="padding-left: {% widthratio node.get_level 1 25 %}px;">
                                        {% if not node.is_leaf_node %}
                                            <i class="bi bi-folder-fill text-warning me-1"></i>
                                        {% else %}
                                            <span class="node-level-indicator"></span>
                                        {% endif %}
                                        <a href="{% url 'accounting:account_update' node.pk %}" class="node-name">{{ node.name }}</a>
                                    </td>
                                    <td>{{ node.code }}</td>
                                    <td>{{ node.account_type.name|default:"-" }}</td>
                                    <td class="text-center">
                                        {% if node.is_active %}<i class="bi bi-check-circle-fill text-success"></i>{% else %}<i class="bi bi-x-circle-fill text-danger"></i>{% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if node.is_control_account %}<i class="bi bi-check-circle-fill text-info"></i>{% else %}<i class="bi bi-x-circle"></i>{% endif %}
                                    </td>
                                    <td class="actions-column">
                                        <a href="{% url 'accounting:account_update' node.pk %}" class="btn btn-outline-primary" title="Edit"><i class="bi bi-pencil-square"></i></a>
                                        <a href="{% url 'accounting:account_delete' node.pk %}" class="btn btn-outline-danger" title="Delete"><i class="bi bi-trash"></i></a>
                                    </td>
                                </tr>
                                
                                {# This is the magic part that renders the children of the current node #}
                                {{ children }}

                            {% endrecursetree %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                {# 3. If account_list is empty, show this message #}
                <div class="alert alert-info text-center m-4">
                    No accounts found in your Chart of Accounts.
                    {% if perms.accounting.add_account %}
                    <a href="{% url 'accounting:account_create' %}" class="alert-link">Add the first account now</a>.
                    {% endif %}
                </div>
            {% endif %}
            {% endwith %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}





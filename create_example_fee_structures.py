#!/usr/bin/env python
"""
Create example Fee Structures to populate the Fee Structure List View
Run this script to create sample fee structures with various configurations
"""

from decimal import Decimal

from django_tenants.utils import schema_context
from apps.fees.models import FeeHead, FeeStructure, FeeStructureItem
from apps.schools.models import AcademicYear, Term, SchoolClass

def create_sample_fee_structures():
    """Create comprehensive sample fee structures"""
    
    print("🏗️  Creating Sample Fee Structures...")
    print("=" * 50)
    
    # Use alpha tenant
    with schema_context('alpha'):
        
        # 1. Create Fee Heads if they don't exist
        print("\n📋 Creating Fee Heads...")
        
        fee_heads_data = [
            ("Tuition Fee", "Monthly tuition fee for academic instruction"),
            ("Library Fee", "Library access and book lending fee"),
            ("Transport Fee", "School bus transportation fee"),
            ("Sports Fee", "Sports activities and equipment fee"),
            ("Computer Lab Fee", "Computer lab usage and maintenance fee"),
            ("Examination Fee", "Examination and assessment fee"),
            ("Activity Fee", "Extra-curricular activities fee"),
            ("Uniform Fee", "School uniform and dress code fee"),
            ("Lunch Fee", "School cafeteria and meal fee"),
            ("Registration Fee", "Annual registration and enrollment fee"),
        ]
        
        fee_heads = {}
        for name, description in fee_heads_data:
            fee_head, created = FeeHead.objects.get_or_create(
                name=name,
                defaults={'description': description, 'is_active': True}
            )
            fee_heads[name] = fee_head
            print(f"  {'✅ Created' if created else '📋 Exists'}: {name}")
        
        # 2. Get Academic Years and Terms
        print("\n📅 Getting Academic Years and Terms...")
        
        academic_years = AcademicYear.objects.all().order_by('-start_date')
        if not academic_years.exists():
            print("  ❌ No Academic Years found. Please create Academic Years first.")
            return
        
        current_year = academic_years.first()
        terms = Term.objects.filter(academic_year=current_year)
        
        print(f"  📅 Using Academic Year: {current_year.name}")
        print(f"  📅 Available Terms: {list(terms.values_list('name', flat=True))}")
        
        # 3. Get School Classes
        print("\n🏫 Getting School Classes...")
        
        classes = SchoolClass.objects.all().order_by('name')
        if not classes.exists():
            print("  ❌ No School Classes found. Please create School Classes first.")
            return
        
        primary_classes = classes.filter(name__icontains='Grade')[:3]  # First 3 grades
        secondary_classes = classes.filter(name__icontains='Grade')[3:6]  # Next 3 grades
        
        print(f"  🏫 Primary Classes: {list(primary_classes.values_list('name', flat=True))}")
        print(f"  🏫 Secondary Classes: {list(secondary_classes.values_list('name', flat=True))}")
        
        # 4. Create Fee Structures
        print("\n💰 Creating Fee Structures...")
        
        fee_structures_data = [
            {
                'name': 'Primary School Monthly Fees',
                'term': terms.first() if terms.exists() else None,
                'classes': primary_classes,
                'description': 'Comprehensive monthly fee structure for primary school students (Grades 1-3)',
                'items': [
                    ('Tuition Fee', Decimal('15000.00')),
                    ('Library Fee', Decimal('500.00')),
                    ('Sports Fee', Decimal('1000.00')),
                    ('Computer Lab Fee', Decimal('800.00')),
                    ('Activity Fee', Decimal('600.00')),
                ]
            },
            {
                'name': 'Secondary School Monthly Fees',
                'term': terms.first() if terms.exists() else None,
                'classes': secondary_classes,
                'description': 'Comprehensive monthly fee structure for secondary school students (Grades 4-6)',
                'items': [
                    ('Tuition Fee', Decimal('18000.00')),
                    ('Library Fee', Decimal('750.00')),
                    ('Sports Fee', Decimal('1200.00')),
                    ('Computer Lab Fee', Decimal('1000.00')),
                    ('Activity Fee', Decimal('800.00')),
                    ('Examination Fee', Decimal('500.00')),
                ]
            },
            {
                'name': 'Transport Package - Full Year',
                'term': None,  # Full year
                'classes': classes,
                'description': 'Annual transportation fee package for all students',
                'items': [
                    ('Transport Fee', Decimal('12000.00')),
                ]
            },
            {
                'name': 'Annual Registration Package',
                'term': None,  # Full year
                'classes': classes,
                'description': 'One-time annual registration and enrollment fees',
                'items': [
                    ('Registration Fee', Decimal('5000.00')),
                    ('Uniform Fee', Decimal('3000.00')),
                ]
            },
        ]
        
        # Add second term structure if available
        if terms.count() > 1:
            second_term = terms.all()[1]
            fee_structures_data.append({
                'name': 'Primary School Monthly Fees',
                'term': second_term,
                'classes': primary_classes,
                'description': f'Monthly fee structure for primary school students - {second_term.name}',
                'items': [
                    ('Tuition Fee', Decimal('15000.00')),
                    ('Library Fee', Decimal('500.00')),
                    ('Sports Fee', Decimal('1000.00')),
                    ('Examination Fee', Decimal('1500.00')),  # Higher exam fee in second term
                ]
            })
        
        # Create the fee structures
        created_structures = []
        
        for structure_data in fee_structures_data:
            try:
                # Check if structure already exists
                existing = FeeStructure.objects.filter(
                    name=structure_data['name'],
                    academic_year=current_year,
                    term=structure_data['term']
                ).first()
                
                if existing:
                    print(f"  📋 Exists: {structure_data['name']} ({current_year.name})")
                    created_structures.append(existing)
                    continue
                
                # Create new fee structure
                fee_structure = FeeStructure.objects.create(
                    name=structure_data['name'],
                    academic_year=current_year,
                    term=structure_data['term'],
                    description=structure_data['description'],
                    is_active=True
                )
                
                # Add applicable classes
                if structure_data['classes']:
                    fee_structure.applicable_classes.set(structure_data['classes'])
                
                # Create fee structure items
                total_amount = Decimal('0.00')
                for fee_head_name, amount in structure_data['items']:
                    fee_head = fee_heads[fee_head_name]
                    FeeStructureItem.objects.create(
                        fee_structure=fee_structure,
                        fee_head=fee_head,
                        amount=amount,
                        description=f"{fee_head_name} for {structure_data['name']}"
                    )
                    total_amount += amount
                
                # Update total amount
                fee_structure.total_amount = total_amount
                fee_structure.save()
                
                created_structures.append(fee_structure)
                
                term_info = f" - {structure_data['term'].name}" if structure_data['term'] else " (Full Year)"
                print(f"  ✅ Created: {structure_data['name']}{term_info} - Total: ${total_amount:,.2f}")
                
            except Exception as e:
                print(f"  ❌ Error creating {structure_data['name']}: {e}")
        
        # 5. Summary
        print(f"\n📊 Summary:")
        print(f"  📋 Fee Heads: {FeeHead.objects.count()}")
        print(f"  💰 Fee Structures: {FeeStructure.objects.count()}")
        print(f"  📝 Fee Structure Items: {FeeStructureItem.objects.count()}")
        print(f"  ✅ Created in this run: {len([s for s in created_structures if s.created_at])}")
        
        print(f"\n🎉 Sample Fee Structures created successfully!")
        print(f"📱 Visit: http://alpha.myapp.test:8000/fees/fee-structures/ to view them")
        
        return created_structures

if __name__ == "__main__":
    create_sample_fee_structures()

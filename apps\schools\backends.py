# D:\school_fees_saas_v2\apps\schools\backends.py

from django.contrib.auth.backends import ModelBackend, BaseBackend
from django.contrib.auth.models import Permission
from django.db import connection
from django_tenants.utils import get_public_schema_name, schema_context

# Your existing imports
from .models import StaffUser # Assuming StaffUser is in the current app's models
# If ParentUser is also in this file, its imports would be here too.
# Make sure ParentUser is correctly imported for TenantParentBackend
try:
    from apps.students.models import ParentUser # ADJUST PATH IF NEEDED for TenantParentBackend
    PARENT_USER_MODEL_DEFINED = True
except ImportError:
    PARENT_USER_MODEL_DEFINED = False
    ParentUser = None

# For TenantOwnerAsStaffBackend
from django.conf import settings
from django.contrib.auth import get_user_model
PublicUserModel = get_user_model() # This gets settings.AUTH_USER_MODEL

import logging
logger_backends = logging.getLogger('project.backends') # Main logger for all backends in this file
# Specific loggers for each backend if needed for finer control (optional)
logger_toasb = logging.getLogger('project.backends.toasb')
logger_tsb = logging.getLogger('project.backends.tsb')
logger_tpb = logging.getLogger('project.backends.tpb')
logger_sumb = logging.getLogger('project.backends.sumb') # For StaffUserModelBackend

from django_tenants.utils import get_tenant_model, get_public_schema_name, schema_context

# Assuming StaffUser is in apps.schools.models
try:
    from apps.schools.models import StaffUser
except ImportError:
    StaffUser = None 

PublicUserModel = get_user_model() # Your Public User Model

import logging
# Assuming logger_toasb is already defined in your file, e.g.:
# logger_toasb = logging.getLogger("TenantOwnerAsStaffBackendLogger") 
# If not, define it:
logger_toasb = logging.getLogger(__name__) 


# # --- TenantOwnerAsStaffBackend ---

# apps/common/backends.py (or wherever TOASB is defined)

import logging
from django.contrib.auth.backends import BaseBackend # Correct base
from django.conf import settings
from django_tenants.utils import schema_context, get_public_schema_name, get_tenant_model
from django.db import connection

# --- Dynamically import models to avoid AppRegistryNotReady early ---
# These will be populated by the ready() method of an AppConfig or a top-level import
# if this backend is only used after apps are fully loaded.
# For safety in a backend, explicit imports within methods or a clear loading order is better.

StaffUser = None
PublicUserModel = None
TenantModel = None # This will be your School model

# It's good practice to initialize these once, perhaps in an AppConfig.ready()
# or ensure they are imported when this module is fully loaded.
# For this example, let's assume they will be available when methods are called.

# It's better to import them at the top level if possible,
# but ensure this file itself isn't imported too early by settings.py
try:
    from apps.schools.models import StaffUser
    from apps.users.models import User as PublicUserModel # OR settings.AUTH_USER_MODEL
    TenantModel = get_tenant_model() # This gets your School model
except ImportError as e:
    # This might happen during initial startup if apps aren't fully loaded.
    # The checks within authenticate/get_user for 'if not StaffUser:' etc. will handle this.
    logging.getLogger(__name__).warning(f"TOASB: Could not import models at module level: {e}")


logger_toasb = logging.getLogger(__name__) # Use the module's logger

class TenantOwnerAsStaffBackend(BaseBackend):
    def __init__(self, *args, **kwargs):
        # BaseBackend's __init__ doesn't take arguments.
        # super().__init__() # Not needed unless BaseBackend's __init__ actually does something critical
        logger_toasb.debug("<<<<< TenantOwnerAsStaffBackend V5 (Enhanced) INSTANTIATED >>>>>")

    def _ensure_models_loaded(self):
        """Helper to load models if they weren't at module level."""
        global StaffUser, PublicUserModel, TenantModel
        if StaffUser is None:
            from apps.schools.models import StaffUser as SUModel
            StaffUser = SUModel
        if PublicUserModel is None:
            from django.contrib.auth import get_user_model
            PublicUserModel = get_user_model()
        if TenantModel is None:
            TenantModel = get_tenant_model()
        
        if not StaffUser or not PublicUserModel or not TenantModel:
            logger_toasb.critical("TOASB: One or more critical models (StaffUser, PublicUserModel, TenantModel) could not be loaded.")
            return False
        return True

    def _ensure_staff_owner_rights(self, staff_user_instance, public_owner_instance, tenant_name):
        """Ensures the staff_user_instance has necessary rights and is active."""
        updated_fields = []
        if not staff_user_instance.is_active:
            staff_user_instance.is_active = True
            updated_fields.append('is_active')
        
        # Assuming 'is_staff' on StaffUser model controls general portal access
        if hasattr(staff_user_instance, 'is_staff') and not staff_user_instance.is_staff:
            staff_user_instance.is_staff = True
            updated_fields.append('is_staff')
        
        # Grant tenant-level superuser for full access
        if hasattr(staff_user_instance, 'is_superuser') and not staff_user_instance.is_superuser:
            staff_user_instance.is_superuser = True
            updated_fields.append('is_superuser')
        
        # Sync names if they differ (optional, but good for consistency)
        if hasattr(public_owner_instance, 'first_name') and \
            hasattr(staff_user_instance, 'first_name') and \
            staff_user_instance.first_name != public_owner_instance.first_name:
            staff_user_instance.first_name = public_owner_instance.first_name
            updated_fields.append('first_name')

        if hasattr(public_owner_instance, 'last_name') and \
            hasattr(staff_user_instance, 'last_name') and \
            staff_user_instance.last_name != public_owner_instance.last_name:
            staff_user_instance.last_name = public_owner_instance.last_name
            updated_fields.append('last_name')

        if updated_fields:
            try:
                staff_user_instance.save(update_fields=updated_fields)
                logger_toasb.info(f"  TOASB: Updated StaffUser proxy '{staff_user_instance.email}' fields {updated_fields} in tenant '{tenant_name}'.")
            except Exception as e_save:
                logger_toasb.error(f"  TOASB: Error saving updated StaffUser proxy '{staff_user_instance.email}' in tenant '{tenant_name}': {e_save}", exc_info=True)


    def authenticate(self, request, username=None, password=None, **kwargs):
        if not self._ensure_models_loaded():
            return None # Critical models not loaded

        if not request or not hasattr(request, 'tenant') or not request.tenant:
            logger_toasb.debug("  TOASB: No request or no tenant in request. Skipping.")
            return None
        
        current_tenant = request.tenant
        if current_tenant.schema_name == get_public_schema_name():
            logger_toasb.debug(f"  TOASB: In public schema ('{current_tenant.schema_name}'). Skipping.")
            return None

        current_tenant_name = getattr(current_tenant, 'name', 'UnknownTenant')
        
        # --- SCENARIO 1: User is ALREADY LOGGED IN as PublicUser and IS THE OWNER of current tenant ---
        if request.user and request.user.is_authenticated and \
            isinstance(request.user, PublicUserModel) and \
            hasattr(current_tenant, 'owner_id') and current_tenant.owner_id == request.user.pk:
            
            logger_toasb.debug(f"  TOASB Scenario 1: Authenticated PublicUser '{request.user.email}' IS owner of tenant '{current_tenant_name}'.")
            # Schema context should be set by TenantMainMiddleware for the request
            try:
                staff_user_for_owner = StaffUser.objects.get(email__iexact=request.user.email)
                self._ensure_staff_owner_rights(staff_user_for_owner, request.user, current_tenant_name) # Ensure rights
                
                if not staff_user_for_owner.is_active: # Re-check after ensure_rights (though ensure_rights should activate)
                    logger_toasb.warning(f"  TOASB Scenario 1: StaffUser '{staff_user_for_owner.email}' (owner) is INACTIVE in tenant '{current_tenant_name}'.")
                    return None 

                staff_user_for_owner.backend = f'{self.__module__}.{self.__class__.__name__}'
                logger_toasb.info(f"  TOASB Scenario 1: SUCCESS - Owner '{request.user.email}' recognized as StaffUser '{staff_user_for_owner.email}' in '{current_tenant_name}'.")
                return staff_user_for_owner
            except StaffUser.DoesNotExist:
                # Owner's StaffUser proxy doesn't exist, attempt to create it
                logger_toasb.warning(f"  TOASB Scenario 1: StaffUser for owner '{request.user.email}' NOT FOUND in tenant '{current_tenant_name}'. Attempting to create.")
                try:
                    # Use the StaffUserManager's create_user or a specific method if available
                    # For simplicity, directly creating ensuring required fields.
                    # Your StaffUser manager should handle password hashing if a new password is set.
                    # Here, password is not set as auth happened against public user.
                    created_staff_user = StaffUser.objects.create(
                        email=request.user.email,
                        first_name=getattr(request.user, 'first_name', ''),
                        last_name=getattr(request.user, 'last_name', ''),
                        is_active=True,
                        is_staff=True,      # Assuming 'is_staff' grants portal access
                        is_superuser=True,  # MAKE THE OWNER'S STAFFUSER A SUPERUSER IN TENANT
                        # date_joined will be set by auto_now_add or default
                    )
                    # If password needs to be set (e.g., if it's not nullable and has no default unusable hash)
                    # created_staff_user.set_unusable_password() # Or a random one if your model requires it
                    # created_staff_user.save()
                    
                    created_staff_user.backend = f'{self.__module__}.{self.__class__.__name__}'
                    logger_toasb.info(f"  TOASB Scenario 1: CREATED StaffUser '{created_staff_user.email}' with admin rights in tenant '{current_tenant_name}' for owner.")
                    return created_staff_user
                except Exception as e_create_staff:
                    logger_toasb.error(f"  TOASB Scenario 1: CRITICAL - Failed to CREATE StaffUser for owner '{request.user.email}' in '{current_tenant_name}': {e_create_staff}", exc_info=True)
                    return None
            except Exception as e_staff_s1:
                logger_toasb.error(f"  TOASB Scenario 1: Error getting/creating StaffUser for already authenticated owner in '{current_tenant_name}': {e_staff_s1}", exc_info=True)
                return None

        # --- SCENARIO 2: User attempts login with USERNAME AND PASSWORD ---
        login_identifier = username
        if login_identifier is None and PublicUserModel:
            login_identifier = kwargs.get(PublicUserModel.USERNAME_FIELD)
        
        if not login_identifier or not password:
            logger_toasb.debug(f"  TOASB: No username/password provided for Scenario 2 login attempt.")
            return None

        logger_toasb.debug(f"  TOASB Scenario 2: Attempting username/password auth for '{login_identifier}' in tenant '{current_tenant_name}'.")
        authenticated_public_user = None
        try:
            with schema_context(get_public_schema_name()):
                from django.db.models import Q
                public_user_lookup_q = Q(**{PublicUserModel.USERNAME_FIELD + '__iexact': login_identifier})
                user_check = PublicUserModel.objects.get(public_user_lookup_q)
            
            if user_check.check_password(password):
                authenticated_public_user = user_check
                logger_toasb.debug(f"  TOASB Scenario 2: Public credentials VALID for '{login_identifier}'.")
            else:
                logger_toasb.debug(f"  TOASB Scenario 2: Public credentials INVALID for '{login_identifier}'.")
                return None
        except PublicUserModel.DoesNotExist:
            logger_toasb.debug(f"  TOASB Scenario 2: PublicUser '{login_identifier}' NOT FOUND in public schema. This backend cannot handle this user.")
            return None
        except Exception as e_public_auth:
            logger_toasb.error(f"  TOASB Scenario 2: Error during PublicUser credential check for '{login_identifier}': {e_public_auth}", exc_info=True)
            return None

        if authenticated_public_user and hasattr(current_tenant, 'owner_id') and current_tenant.owner_id == authenticated_public_user.pk:
            logger_toasb.debug(f"  TOASB Scenario 2: Authenticated PublicUser '{authenticated_public_user.email}' IS owner of tenant '{current_tenant_name}'.")
            # Schema context should be set to the tenant by middleware for this request
            try:
                staff_user_for_owner = StaffUser.objects.get(email__iexact=authenticated_public_user.email)
                self._ensure_staff_owner_rights(staff_user_for_owner, authenticated_public_user, current_tenant_name) # Ensure rights
                
                if not staff_user_for_owner.is_active: # Re-check
                    logger_toasb.warning(f"  TOASB Scenario 2: StaffUser '{staff_user_for_owner.email}' (owner) is INACTIVE in tenant '{current_tenant_name}'.")
                    return None
                
                staff_user_for_owner.backend = f'{self.__module__}.{self.__class__.__name__}'
                logger_toasb.info(f"  TOASB Scenario 2: SUCCESS - Owner '{authenticated_public_user.email}' authenticated AS StaffUser '{staff_user_for_owner.email}' in '{current_tenant_name}'.")
                return staff_user_for_owner
            except StaffUser.DoesNotExist:
                logger_toasb.warning(f"  TOASB Scenario 2: StaffUser for owner '{authenticated_public_user.email}' NOT FOUND in tenant '{current_tenant_name}'. Attempting to create.")
                try:
                    created_staff_user = StaffUser.objects.create(
                        email=authenticated_public_user.email,
                        first_name=getattr(authenticated_public_user, 'first_name', ''),
                        last_name=getattr(authenticated_public_user, 'last_name', ''),
                        is_active=True,
                        is_staff=True,
                        is_superuser=True,
                    )
                    # created_staff_user.set_unusable_password()
                    # created_staff_user.save()

                    created_staff_user.backend = f'{self.__module__}.{self.__class__.__name__}'
                    logger_toasb.info(f"  TOASB Scenario 2: CREATED StaffUser '{created_staff_user.email}' with admin rights in tenant '{current_tenant_name}' for owner.")
                    return created_staff_user
                except Exception as e_create_staff_s2:
                    logger_toasb.error(f"  TOASB Scenario 2: CRITICAL - Failed to CREATE StaffUser for owner '{authenticated_public_user.email}' in '{current_tenant_name}': {e_create_staff_s2}", exc_info=True)
                    return None
            except Exception as e_staff_s2:
                logger_toasb.error(f"  TOASB Scenario 2: Error getting/creating StaffUser for owner in '{current_tenant_name}': {e_staff_s2}", exc_info=True)
                return None
        else:
            if authenticated_public_user:
                logger_toasb.debug(f"  TOASB Scenario 2: PublicUser '{authenticated_public_user.email}' is NOT owner of tenant '{current_tenant_name}'. This backend will not handle them.")
            return None

    def get_user(self, user_id):
        if not self._ensure_models_loaded():
            return None

        current_schema = connection.schema_name
        if current_schema == get_public_schema_name() or not current_schema:
            return None # This backend is for tenant-scoped staff users (specifically owner-as-staff)
        try:
            user = StaffUser.objects.get(pk=user_id)
            # No need to set user.backend here again, it's set during authenticate if this backend was used
            logger_toasb.debug(f"  TOASB GetUser: Retrieved StaffUser {user_id} ('{user.email}'). Schema: {current_schema}")
            return user
        except StaffUser.DoesNotExist:
            logger_toasb.debug(f"  TOASB GetUser: StaffUser {user_id} DoesNotExist in schema {current_schema}.")
            return None
        except Exception as e_get_user: # Catch broader exceptions
            logger_toasb.error(f"  TOASB GetUser: Error retrieving StaffUser {user_id} in schema {current_schema}: {e_get_user}", exc_info=True)
            return None



# # --- TenantStaffBackend (Your V3 version - NO permission methods) ---
class TenantStaffBackend(BaseBackend):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger_tsb.debug("<<<<< TenantStaffBackend V4 INSTANTIATED >>>>>")

    def authenticate(self, request, username=None, password=None, **kwargs):
        if not request or not hasattr(request, 'tenant') or not request.tenant: return None
        if connection.schema_name == get_public_schema_name(): return None
        
        current_tenant = request.tenant
        current_tenant_name = getattr(current_tenant, 'name', 'UnknownTenant')
        login_identifier = username
        if login_identifier is None: login_identifier = kwargs.get(StaffUser.USERNAME_FIELD)
        if login_identifier is None: return None
            
        try:
            user = StaffUser.objects.get(email__iexact=login_identifier)
            if user.check_password(password) and user.is_active:
                user.backend = f'{self.__module__}.{self.__class__.__name__}'
                logger_tsb.info(f"SUCCESS for StaffUser '{login_identifier}' in '{current_tenant_name}'.")
                return user
            else:
                # logger_tsb.debug(f"Invalid password or inactive StaffUser for '{login_identifier}' in '{current_tenant_name}'.")
                return None
        except StaffUser.DoesNotExist:
            # logger_tsb.debug(f"StaffUser '{login_identifier}' NOT FOUND in schema '{connection.schema_name}'.")
            return None
        except Exception as e:
            logger_tsb.error(f"Error during StaffUser auth for '{login_identifier}': {e}", exc_info=True)
            return None

    def get_user(self, user_id):
        if connection.schema_name == get_public_schema_name(): return None
        try:
            user = StaffUser.objects.get(pk=user_id)
            user.backend = f'{self.__module__}.{self.__class__.__name__}'
            # logger_tsb.debug(f"GetUser: Retrieved StaffUser {user_id} ('{user.email}'). Schema: {connection.schema_name}")
            return user
        except StaffUser.DoesNotExist:
            # logger_tsb.debug(f"GetUser: StaffUser {user_id} DoesNotExist in schema {connection.schema_name}.")
            return None
    # NO PERMISSION METHODS HERE



class TenantParentBackend(BaseBackend): # Or ModelBackend
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger_backends.debug("<<<<< TenantParentBackend V5 INSTANTIATED >>>>>") # Increment version for clarity

    def authenticate(self, request, username=None, password=None, **kwargs):
        logger_backends.debug(f"[TPB V5] Authenticate CALLED. Request: {request is not None}, Username: '{username}', Password Provided: {password is not None}")

        if not request or not hasattr(request, 'tenant') or not request.tenant:
            logger_backends.debug("[TPB V5] No request or tenant, skipping.")
            return None
        
        current_tenant_schema = connection.schema_name
        if current_tenant_schema == get_public_schema_name():
            logger_backends.debug(f"[TPB V5] In public schema ('{current_tenant_schema}'), skipping ParentUser auth.")
            return None
        
        logger_backends.debug(f"[TPB V5] Current tenant schema: '{current_tenant_schema}'")

        # Assuming ParentUser model is imported correctly
        # from .models import ParentUser # Or wherever it is

        login_identifier_from_form = username # Django's AuthenticationForm passes it as 'username'
        
        # What is ParentUser.USERNAME_FIELD set to in your model? Let's assume 'username' for now.
        # If it's 'email', the query needs to change.
        model_username_field = ParentUser.USERNAME_FIELD 
        logger_backends.debug(f"[TPB V5] ParentUser.USERNAME_FIELD is '{model_username_field}'. Login identifier from form: '{login_identifier_from_form}'")

        if not login_identifier_from_form:
            logger_backends.debug("[TPB V5] No login identifier provided in form.")
            return None

        try:
            # Query using the model's defined USERNAME_FIELD
            user = ParentUser.objects.get(**{model_username_field + '__iexact': login_identifier_from_form})
            logger_backends.debug(f"[TPB V5] Found ParentUser object: {user} (Email: {user.email}, Username: {user.username})")

            if user.check_password(password):
                logger_backends.debug(f"[TPB V5] Password check PASSED for user '{user.get_username()}'.")
                if user.is_active:
                    logger_backends.info(f"[TPB V5] Authenticate SUCCESS for ParentUser '{user.get_username()}' in tenant '{request.tenant.name}'.")
                    user.backend = f'{self.__module__}.{self.__class__.__name__}' # CRITICAL
                    return user
                else:
                    logger_backends.warning(f"[TPB V5] ParentUser '{user.get_username()}' is INACTIVE.")
                    return None # Inactive user
            else:
                logger_backends.warning(f"[TPB V5] Password check FAILED for user '{user.get_username()}'.")
                return None # Incorrect password
        except ParentUser.DoesNotExist:
            logger_backends.warning(f"[TPB V5] ParentUser with '{model_username_field}' = '{login_identifier_from_form}' DOES NOT EXIST in schema '{current_tenant_schema}'.")
            return None
        except ParentUser.MultipleObjectsReturned:
            logger_backends.error(f"[TPB V5] CRITICAL: Multiple ParentUser objects found for '{model_username_field}' = '{login_identifier_from_form}' in schema '{current_tenant_schema}'.")
            return None
        except Exception as e:
            logger_backends.error(f"[TPB V5] EXCEPTION during ParentUser authentication: {e}", exc_info=True)
            return None
            
    def get_user(self, user_id):
        # ... (your get_user method, also with logging) ...
        logger_backends.debug(f"[TPB V5] GetUser CALLED for user_id: {user_id}")
        if connection.schema_name == get_public_schema_name():
            logger_backends.debug(f"[TPB V5] GetUser: In public schema, cannot get ParentUser.")
            return None
        try:
            user = ParentUser.objects.get(pk=user_id)
            user.backend = f'{self.__module__}.{self.__class__.__name__}' # CRITICAL
            logger_backends.debug(f"[TPB V5] GetUser: Retrieved ParentUser {user_id} ('{user.get_username()}').")
            return user
        except ParentUser.DoesNotExist:
            logger_backends.debug(f"[TPB V5] GetUser: ParentUser {user_id} DoesNotExist.")
            return None
        except Exception as e:
            logger_backends.error(f"[TPB V5] GetUser EXCEPTION: {e}", exc_info=True)
            return None
        
    # --- Permissions for ParentUser (minimal) ---
    def get_all_permissions(self, user_obj, obj=None):
        if PARENT_USER_MODEL_DEFINED and isinstance(user_obj, ParentUser):
            # logger_tpb.debug(f"[TPB] get_all_permissions called for ParentUser '{user_obj.email}'. Returning empty set.")
            return set()
        return set()

    def has_perm(self, user_obj, perm, obj=None):
        if PARENT_USER_MODEL_DEFINED and isinstance(user_obj, ParentUser):
            # logger_tpb.debug(f"[TPB] has_perm called for ParentUser '{user_obj.email}' for perm '{perm}'. Returning False.")
            return False
        return False
    
    # get_group_permissions is not needed if get_all_permissions returns an empty set for parents

# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# REVISED StaffUserModelBackend
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class StaffUserModelBackend(ModelBackend): # Inherit from ModelBackend
    """
    This backend will be used by Django's permission system for StaffUser instances.
    It authenticates ONLY StaffUser types and provides permission checks for them.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger_sumb.debug("<<<<< StaffUserModelBackend V2 INSTANTIATED >>>>>")

    def authenticate(self, request, username=None, password=None, **kwargs):
        # This backend is NOT for primary login authentication of StaffUser.
        # TenantStaffBackend handles that.
        # However, if it were to be used for auth, it would look like this:
        # if username is None:
        #     username = kwargs.get(StaffUser.USERNAME_FIELD)
        # try:
        #     user = StaffUser.objects.get_by_natural_key(username)
        # except StaffUser.DoesNotExist:
        #     pass
        # else:
        #     if user.check_password(password) and self.user_can_authenticate(user):
        #         return user
        return None # Explicitly do not authenticate here for login flow

    def get_user(self, user_id):
        # This might be called if user.backend pointed here.
        try:
            user = StaffUser.objects.get(pk=user_id)
        except StaffUser.DoesNotExist:
            return None
        return user if self.user_can_authenticate(user) else None

    # --- Override permission methods to be specific to StaffUser ---
    # --- These methods are inherited from ModelBackend but we ensure they use StaffUser context ---

    def _get_user_permissions(self, user_obj):
        # Ensure we only process StaffUser instances
        if not isinstance(user_obj, StaffUser):
            # logger_sumb.debug(f"_get_user_permissions: Not a StaffUser: {type(user_obj)}")
            return Permission.objects.none()
        # logger_sumb.debug(f"_get_user_permissions: Processing StaffUser: {user_obj.email}")
        return user_obj.user_permissions.all() # From PermissionsMixin

    def _get_group_permissions(self, user_obj):
        # Ensure we only process StaffUser instances
        if not isinstance(user_obj, StaffUser):
            # logger_sumb.debug(f"_get_group_permissions: Not a StaffUser: {type(user_obj)}")
            return Permission.objects.none()
        # logger_sumb.debug(f"_get_group_permissions: Processing StaffUser: {user_obj.email}")
        # This is the key: query through StaffUser's groups
        return Permission.objects.filter(group__in=user_obj.groups.all())

    # get_all_permissions, has_perm, has_module_perms will be inherited from ModelBackend.
    # ModelBackend.get_all_permissions calls self._get_user_permissions and self._get_group_permissions.
    # By overriding these two helpers to be StaffUser-specific, the inherited get_all_permissions
    # and has_perm from ModelBackend should now work correctly for StaffUser instances
    # when *this* StaffUserModelBackend instance is the one being used by Django's auth loop.



class DebugModelBackend(ModelBackend):
    def has_perm(self, user_obj, perm, obj=None):
        print(f"--- DEBUG ModelBackend: has_perm called for user: {user_obj} (type: {type(user_obj)}), perm: {perm} ---")
        # First check if this is a StaffUser
        if hasattr(user_obj, 'is_active') and isinstance(user_obj, StaffUser):
            # For staff users, use our tenant staff backend's permission system instead
            tenant_backend = TenantStaffBackend()
            return tenant_backend.has_perm(user_obj, perm, obj)
        return super().has_perm(user_obj, perm, obj)

    def has_module_perms(self, user_obj, app_label):
        print(f"--- DEBUG ModelBackend: has_module_perms called for user: {user_obj} (type: {type(user_obj)}), app: {app_label} ---")
        # First check if this is a StaffUser
        if hasattr(user_obj, 'is_active') and isinstance(user_obj, StaffUser):
            # For staff users, use our tenant staff backend's permission system instead
            tenant_backend = TenantStaffBackend()
            return tenant_backend.has_module_perms(user_obj, app_label)
        return super().has_module_perms(user_obj, app_label)

    def get_all_permissions(self, user_obj, obj=None):
        print(f"--- DEBUG ModelBackend: get_all_permissions called for user: {user_obj} (type: {type(user_obj)}) ---")
        # First check if this is a StaffUser
        if hasattr(user_obj, 'is_active') and isinstance(user_obj, StaffUser):
            # For staff users, use our tenant staff backend's permission system instead
            tenant_backend = TenantStaffBackend()
            return tenant_backend.get_all_permissions(user_obj, obj)
        return super().get_all_permissions(user_obj, obj)

    def get_group_permissions(self, user_obj, obj=None):
        print(f"--- DEBUG ModelBackend: get_group_permissions called for user: {user_obj} (type: {type(user_obj)}) ---")
        # First check if this is a StaffUser
        if hasattr(user_obj, 'is_active') and isinstance(user_obj, StaffUser):
            # For staff users, use our tenant staff backend's permission system instead
            tenant_backend = TenantStaffBackend()
            return tenant_backend._get_group_permissions(user_obj)
        return super().get_group_permissions(user_obj, obj)


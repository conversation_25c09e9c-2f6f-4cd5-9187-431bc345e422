from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from apps.announcements.models import Announcement
from datetime import timedelta

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample announcements for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of sample announcements to create',
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # Get or create a user for the announcements
        try:
            author = User.objects.filter(is_staff=True).first()
            if not author:
                author = User.objects.filter(is_superuser=True).first()
        except:
            author = None

        sample_announcements = [
            {
                'title': 'Welcome Back to School!',
                'content': 'We are excited to welcome all students and parents back for the new academic year. Please review the updated school policies and calendar.',
                'is_sticky': True,
                'priority': 'HIGH',
                'target_all_tenant_staff': True,
                'target_all_tenant_parents': True,
            },
            {
                'title': 'Parent-Teacher Conference Schedule',
                'content': 'Parent-teacher conferences will be held next week. Please check your email for your scheduled appointment time.',
                'priority': 'MEDIUM',
                'target_all_tenant_parents': True,
                'expiry_date': timezone.now() + timedelta(days=14),
            },
            {
                'title': 'Staff Meeting - Friday 3 PM',
                'content': 'All teaching staff are required to attend the weekly staff meeting this Friday at 3:00 PM in the main conference room.',
                'priority': 'MEDIUM',
                'target_all_tenant_staff': True,
                'expiry_date': timezone.now() + timedelta(days=7),
            },
            {
                'title': 'School Sports Day Announcement',
                'content': 'Our annual sports day will be held on the 15th of next month. Students should prepare for various athletic events. More details to follow.',
                'priority': 'LOW',
                'target_all_tenant_parents': True,
                'target_all_tenant_staff': True,
            },
            {
                'title': 'Library Hours Extended',
                'content': 'The school library will now be open until 6 PM on weekdays to accommodate students who need extra study time.',
                'priority': 'LOW',
                'target_all_tenant_parents': True,
                'target_all_tenant_staff': True,
            },
            {
                'title': 'Emergency Contact Information Update',
                'content': 'Please ensure your emergency contact information is up to date. Contact the school office if you need to make any changes.',
                'priority': 'HIGH',
                'target_all_tenant_parents': True,
                'is_sticky': True,
            },
            {
                'title': 'New Curriculum Guidelines',
                'content': 'Staff members should review the new curriculum guidelines that have been uploaded to the staff portal. Implementation begins next semester.',
                'priority': 'MEDIUM',
                'target_all_tenant_staff': True,
            },
        ]

        created_count = 0
        for i in range(min(count, len(sample_announcements))):
            announcement_data = sample_announcements[i]
            
            # Check if announcement with this title already exists
            if not Announcement.objects.filter(title=announcement_data['title']).exists():
                announcement = Announcement.objects.create(
                    title=announcement_data['title'],
                    content=announcement_data['content'],
                    author=author,
                    is_published=True,
                    publish_date=timezone.now() - timedelta(hours=i),  # Stagger the publish dates
                    priority=announcement_data.get('priority', 'LOW'),
                    is_sticky=announcement_data.get('is_sticky', False),
                    target_all_tenant_staff=announcement_data.get('target_all_tenant_staff', False),
                    target_all_tenant_parents=announcement_data.get('target_all_tenant_parents', False),
                    expiry_date=announcement_data.get('expiry_date'),
                )
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created announcement: "{announcement.title}"')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Announcement already exists: "{announcement_data["title"]}"')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} sample announcements')
        )

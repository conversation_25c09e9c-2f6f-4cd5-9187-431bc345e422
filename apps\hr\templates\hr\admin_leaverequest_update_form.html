{# D:\school_fees_saas_v2\apps\hr\templates\hr\admin_leaverequest_update_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">

            <h1 class="h3 mb-4">{{ view_title }}</h1>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">Request Details</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Employee:</dt>
                        <dd class="col-sm-8">{{ leave_request.employee.get_full_name }}</dd>

                        <dt class="col-sm-4">Leave Type:</dt>
                        <dd class="col-sm-8">{{ leave_request.leave_type.name }}</dd>

                        <dt class="col-sm-4">Dates Requested:</dt>
                        <dd class="col-sm-8">{{ leave_request.start_date|date:"d M, Y" }} to {{ leave_request.end_date|date:"d M, Y" }}</dd>

                        <dt class="col-sm-4">Duration:</dt>
                        <dd class="col-sm-8">{{ leave_request.duration }} day(s)</dd>

                        <dt class="col-sm-4">Reason:</dt>
                        <dd class="col-sm-8">{{ leave_request.reason|linebreaksbr|default:"No reason provided." }}</dd>

                        <dt class="col-sm-4">Current Status:</dt>
                        <dd class="col-sm-8">
                            <span class="badge 
                                {% if leave_request.status == 'PENDING' %}bg-warning text-dark
                                {% elif leave_request.status == 'APPROVED' %}bg-success
                                {% else %}bg-danger{% endif %}">
                                {{ leave_request.get_status_display }}
                            </span>
                        </dd>
                    </dl>
                </div>
                
                {% if leave_request.status == 'PENDING' %}
                <div class="card-footer bg-light text-end">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" name="action" value="reject" class="btn btn-danger">
                            <i class="bi bi-x-circle-fill me-1"></i> Reject
                        </button>
                    </form>
                    <form method="post" class="d-inline ms-2">
                        {% csrf_token %}
                        <button type="submit" name="action" value="approve" class="btn btn-success">
                            <i class="bi bi-check-circle-fill me-1"></i> Approve
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>

            <div class="mt-4">
                <a href="{% url 'hr:admin_leaverequest_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left-circle me-1"></i> Back to All Requests
                </a>
            </div>

        </div>
    </div>
</div>
{% endblock tenant_specific_content %}




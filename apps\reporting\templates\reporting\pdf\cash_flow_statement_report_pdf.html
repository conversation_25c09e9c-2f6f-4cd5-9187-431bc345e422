{% extends "./_pdf_base.html" %}
{% load humanize %}

{% block pdf_title %}Cash Flow Statement - {{ school_profile.name|default:"School" }}{% endblock %}

{% block report_name_header %}Cash Flow Statement{% endblock %}
{% block report_period_header %}
    <p class="mb-0">Period: {{ report_data.start_date|date:"d M Y" }} to {{ report_data.end_date|date:"d M Y" }}</p>
{% endblock %}

{% block pdf_content %}
    <table>
        <tr>
            <td class="fw-bold">Cash and Cash Equivalents, Beginning of Period</td>
            <td class="text-end">{{ report_data.beginning_cash_balance|floatformat:2|intcomma }}</td>
        </tr>
        <tr><td colspan="2" style="padding: 2px;"> </td></tr>

        <tr><td colspan="2" class="fw-bold" style="background-color: #f2f2f2;">Cash Flows from Operating Activities:</td></tr>
        {% for item in report_data.operating_activities %}
            <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
        {% empty %}
            <tr><td colspan="2" style="font-style: italic;">    Detailed operating activities not yet calculated.</td></tr>
        {% endfor %}
        <tr class="fw-bold">
            <td>Net Cash from Operating Activities</td>
            <td class="text-end">{{ report_data.net_cash_operating|floatformat:2|intcomma }}</td>
        </tr>
        <tr><td colspan="2" style="padding: 2px;"> </td></tr>

        <tr><td colspan="2" class="fw-bold" style="background-color: #f2f2f2;">Cash Flows from Investing Activities:</td></tr>
         {% for item in report_data.investing_activities %}
            <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
        {% empty %}
            <tr><td colspan="2" style="font-style: italic;">    Detailed investing activities not yet calculated.</td></tr>
        {% endfor %}
        <tr class="fw-bold">
            <td>Net Cash from Investing Activities</td>
            <td class="text-end">{{ report_data.net_cash_investing|floatformat:2|intcomma }}</td>
        </tr>
        <tr><td colspan="2" style="padding: 2px;"> </td></tr>

        <tr><td colspan="2" class="fw-bold" style="background-color: #f2f2f2;">Cash Flows from Financing Activities:</td></tr>
         {% for item in report_data.financing_activities %}
            <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
        {% empty %}
            <tr><td colspan="2" style="font-style: italic;">    Detailed financing activities not yet calculated.</td></tr>
        {% endfor %}
        <tr class="fw-bold">
            <td>Net Cash from Financing Activities</td>
            <td class="text-end">{{ report_data.net_cash_financing|floatformat:2|intcomma }}</td>
        </tr>
        <tr><td colspan="2" style="padding: 2px;"> </td></tr>

        <tr class="fw-bold" style="background-color: #e9ecef;">
            <td>Net Increase / (Decrease) in Cash and Cash Equivalents</td>
            <td class="text-end">{{ report_data.net_change_in_cash|floatformat:2|intcomma }}</td>
        </tr>
        <tr><td colspan="2" style="padding: 2px;"> </td></tr>
        <tr class="fw-bold" style="font-size: 1.1em; background-color: #343a40; color: white;">
            <td>Cash and Cash Equivalents, End of Period</td>
            <td class="text-end">{{ report_data.ending_cash_balance|floatformat:2|intcomma }}</td>
        </tr>
    </table>
{% endblock %}


# D:\school_fees_saas_v2\apps\platform_management\forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model # Preferred way to get User model
from .models import PlatformSetting, SystemNotification, MaintenanceMode, PlatformAnnouncement

User = get_user_model()

class PlatformAnnouncementForm(forms.ModelForm):
    class Meta:
        model = PlatformAnnouncement
        fields = [
            'title', 
            'content', 
            'publish_date', 
            'expiry_date', 
            'is_published', 
            # 'severity'
            # 'author' is set in the view
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': _("Enter announcement content here...")}),
            'publish_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'expiry_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'is_published': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'severity': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'is_published': _("Publish this announcement immediately upon saving (if publish date is now or past)?")
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['publish_date'].input_formats = ('%Y-%m-%dT%H:%M', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M')
        self.fields['expiry_date'].input_formats = ('%Y-%m-%dT%H:%M', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M')
        self.fields['expiry_date'].required = False # Consistent with model's blank=True, null=True
        # self.fields['content'].help_text = _("Basic HTML is allowed if your display logic handles it safely.")


class PlatformSettingForm(forms.ModelForm):
    class Meta:
        model = PlatformSetting
        fields = ['setting_name', 'setting_value', 'description', 'is_active']
        widgets = {
            'setting_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _("e.g., SITE_REGISTRATION_OPEN")}),
            'setting_value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': _("e.g., True, path/to/logo.png, #FFFFFF")}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': _("Brief explanation of this setting.")}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class SystemNotificationForm(forms.ModelForm):
    target_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_staff=True, is_active=True).order_by('email'), # Example: Target only active staff/superusers
        widget=forms.SelectMultiple(attrs={'class': 'form-select select2-multiple', 'data-placeholder': _("Select specific users (optional)")}),
        required=False,
        label=_("Target Specific Platform Users")
    )

    class Meta:
        model = SystemNotification
        fields = [
            'title', 'message', 'notification_type', 
            'publish_date', 'expires_at', 'is_active', 
            'target_users'
            # 'created_by' is set in the view
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'notification_type': forms.Select(attrs={'class': 'form-select'}),
            'publish_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'expires_at': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['publish_date'].input_formats = ('%Y-%m-%dT%H:%M', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M')
        self.fields['expires_at'].input_formats = ('%Y-%m-%dT%H:%M', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M')
        self.fields['expires_at'].required = False


class MaintenanceModeForm(forms.ModelForm):
    class Meta:
        model = MaintenanceMode
        fields = ['is_enabled', 'title', 'message', 'allowed_ips', 'bypass_key', 'bypass_staff', 'site_name_override']
        widgets = {
            'is_enabled': forms.CheckboxInput(attrs={'class': 'form-check-input form-switch form-check-input-lg'}), # Using form-switch
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'allowed_ips': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': _("Enter IP addresses separated by commas or newlines")}),
            'bypass_key': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _("Optional secret key for URL bypass")}),
            'bypass_staff': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'site_name_override': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _("e.g., Our Awesome Platform")}),
        }
        help_texts = {
            'allowed_ips': _("Use for specific developer/admin IPs. One IP or CIDR per line or comma-separated."),
            'bypass_key': _("If set, accessing ?maintenance_bypass=YOUR_KEY will bypass maintenance mode."),
        }
    
    def clean_allowed_ips(self):
        data = self.cleaned_data.get('allowed_ips', '')
        # Normalize to newline-separated, removing empty lines and stripping whitespace
        ips = [ip.strip() for line in data.splitlines() for ip in line.split(',') if ip.strip()]
        # You might add IP validation here if desired, though Django's GenericIPAddressField does it on model.
        return "\n".join(sorted(list(set(ips)))) # Store unique, sorted, newline-separated
    
    

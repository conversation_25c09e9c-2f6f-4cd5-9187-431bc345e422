{% load i18n humanize %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% blocktrans with school_name=school_name %}New Invoice Issued - {{ school_name }}{% endblocktrans %}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .header img { max-height: 80px; margin-bottom: 10px; }
        .header h1 { color: {{ school_primary_color|default:'#333' }}; margin:0; font-size: 24px;}
        .content p { margin-bottom: 1em; }
        .content strong { color: {{ school_primary_color|default:'#333' }}; }
        .invoice-summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px; border: 1px solid #eee;}
        .invoice-summary h4 { margin-top:0; color: {{ school_primary_color|default:'#333' }}; }
        .invoice-items table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .invoice-items th, .invoice-items td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .invoice-items th { background-color: #f2f2f2; }
        .total-due { font-size: 1.2em; font-weight: bold; margin-top: 15px; }
        .button-container { text-align: center; margin-top: 25px; }
        .button { display: inline-block; padding: 12px 25px; background-color: {{ school_secondary_color|default:'#007bff' }}; color: white !important; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .footer { text-align: center; margin-top: 25px; font-size: 0.85em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {% if school_logo_url %}<img src="{{ school_logo_url }}" alt="{{ school_name }} Logo">{% endif %}
            <h1>{{ school_name }}</h1>
        </div>
        <div class="content">
            <p>{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}</p>
            
            <p>{% blocktrans with student_name=student_name school_name=school_name %}A new invoice has been issued for {{ student_name }} at {{ school_name }}. Please find the details below:{% endblocktrans %}</p>

            <div class="invoice-summary">
                <h4>{% trans "Invoice Details" %}</h4>
                <p><strong>{% trans "Invoice Number:" %}</strong> {{ invoice_details.invoice_number }}</p>
                <p><strong>{% trans "Issue Date:" %}</strong> {{ invoice_details.issue_date|date:"d M Y" }}</p>
                <p><strong>{% trans "Due Date:" %}</strong> {{ invoice_details.due_date|date:"d M Y" }}</p>
                
                {% if invoice_details.items %}
                <div class="invoice-items">
                    <p><strong>{% trans "Invoice Items:" %}</strong></p>
                    <table>
                        <thead><tr><th>{% trans "Description" %}</th><th>{% trans "Amount" %}</th></tr></thead>
                        <tbody>
                            {% for item in invoice_details.items %}
                            <tr>
                                <td>{{ item.description }}</td>
                                <td>{{ school_currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
                <p class="total-due"><strong>{% trans "Total Amount Due:" %} {{ school_currency_symbol|default:'$' }}{{ invoice_details.amount_due|floatformat:2|intcomma }}</strong></p>
            </div>
            
            {% if invoice_details.invoice_url or payment_portal_url %}
                <p>{% trans "You can view the full invoice and make a payment via the parent portal:" %}</p>
                <div class="button-container">
                    <a href="{{ invoice_details.invoice_url|default:payment_portal_url }}" class="button">
                        {% if invoice_details.invoice_url %}{% trans "View Invoice & Pay" %}{% else %}{% trans "Go to Parent Portal" %}{% endif %}
                    </a>
                </div>
            {% endif %}

            <p>{% blocktrans %}Please ensure payment is made by the due date. If you have any questions, please contact the school office.{% endblocktrans %}</p>
            
            <p>{% trans "Thank you," %}<br>
            {% blocktrans with school_name=school_name %}The {{ school_name }} Administration{% endblocktrans %}</p>
        </div>
        <div class="footer">
            <p>{{ school_name }}<br>
            {% if school_address %}{{ school_address }}<br>{% endif %}
            {% if school_contact_info %}{{ school_contact_info }}{% endif %}</p>
            <p>© {% now "Y" %} {{ school_name }}.</p>
        </div>
    </div>
</body>
</html>


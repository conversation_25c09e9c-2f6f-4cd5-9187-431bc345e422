# D:\school_fees_saas_v2\apps\common\models.py
from django.db import models
from django.utils import timezone # If you want timezone.now as default

class BaseModel(models.Model):
    """
    An abstract base class model that provides self-updating
    ``created_at`` and ``updated_at`` fields.
    """
    created_at = models.DateTimeField(auto_now_add=True, editable=False, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, editable=False, null=True, blank=True)
    # Optional: Add created_by and updated_by fields
    # created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL,
    #                               null=True, blank=True, related_name='%(class)s_created')
    # updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL,
    #                               null=True, blank=True, related_name='%(class)s_updated')

    class Meta:
        abstract = True # Very important! This makes it a base class not a DB table
        ordering = ['-created_at'] # Default ordering


        
        
{% extends "tenant_base.html" %}

{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">{{ page_title }}</h4>
                </div>
                <div class="card-body">
                    <p>
                        {% blocktranslate with account_name=account.name account_code=account.code %}
                        Are you sure you want to delete the account: <strong>{{ account_code|default:"N/A" }} - {{ account_name }}</strong>?
                        {% endblocktranslate %}
                    </p>
                    <p class="text-danger">{% translate "This action cannot be undone." %}</p>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'accounting:accounts_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% translate "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash-fill me-1"></i> {% translate "Yes, Delete" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}



{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

        <div class="d-flex flex-wrap justify-content-between align-items-center mb-3 gap-2">
            <div>
                <h1 class="h3 mb-0">{{ view_title }}</h1>
                <p class="text-muted mb-0">Manage and review all payroll periods for your school.</p>
            </div>
            <div class="text-nowrap">
                {# NEW "MANAGE RULES" BUTTON #}
                {% if perms.hr.view_statutorydeduction %}
                <a href="{% url 'hr:deduction_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-gear-fill me-1"></i> {% trans "Manage Rules" %}
                </a>
                {% endif %}
        
                {# YOUR EXISTING "START NEW RUN" BUTTON #}
                {% if perms.hr.manage_payroll %} {# Or a more specific 'add_payrollrun' permission #}
                <a href="{% url 'hr:payroll_process' %}" class="btn btn-primary ms-2">
                    <i class="bi bi-play-circle-fill me-1"></i> {% trans "Start New Payroll Run" %}
                </a>
                {% endif %}
            </div>
        </div>

    {% include "partials/_messages.html" %}

    {# --- TABS FOR OUTSTANDING VS COMPLETED --- #}
    <ul class="nav nav-tabs mb-3">
        <li class="nav-item">
            <a class="nav-link {% if not request.GET.view or request.GET.view == 'outstanding' %}active fw-semibold{% endif %}" 
                href="{% url 'hr:payroll_run_list' %}?view=outstanding">
                {% trans "Outstanding" %} 
                {% if outstanding_run_count > 0 %}
                <span class="badge rounded-pill bg-warning text-dark">{{ outstanding_run_count }}</span>
                {% endif %}
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {% if request.GET.view == 'completed' %}active fw-semibold{% endif %}" 
                href="{% url 'hr:payroll_run_list' %}?view=completed">
                {% trans "Completed History" %}
            </a>
        </li>
    </ul>
    {# --- END OF TABS --- #}

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                {% if request.GET.view == 'completed' %}
                    {% trans "Completed Payroll Runs" %}
                {% else %}
                    {% trans "Outstanding Payroll Runs to be Paid" %}
                {% endif %}
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Pay Period" %}</th>
                            <th>{% trans "Scheduled Payment Date" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th>{% trans "Processed By" %}</th>
                            <th>{% trans "Processed At" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {# The 'payroll_runs' variable is provided by the view #}
                        {% for run in payroll_runs %}
                            <tr>
                                <td>
                                    <a href="{{ run.get_absolute_url }}">
                                        <strong>{{ run.pay_period_start|date:"F Y" }}</strong>
                                    </a>
                                </td>
                                <td>{{ run.payment_date|date:"d M, Y" }}</td>
                                <td class="text-center">
                                    <span class="badge fs-6
                                        {% if run.status == 'PAID' %}bg-success-subtle text-success-emphasis
                                        {% elif run.status == 'PROCESSED' %}bg-info-subtle text-info-emphasis
                                        {% elif run.status == 'DRAFT' %}bg-secondary-subtle text-secondary-emphasis
                                        {% else %}bg-danger-subtle text-danger-emphasis{% endif %}">
                                        {{ run.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ run.processed_by.get_full_name|default:"-" }}</td>
                                <td>{{ run.processed_at|date:"d M, Y H:i"|default:"-" }}</td>
                                <td class="text-center">
                                    <a href="{{ run.get_absolute_url }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye-fill"></i> {% trans "View Details" %}
                                    </a>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center p-4">
                                    <div class="text-muted">
                                        {% if request.GET.view == 'completed' %}
                                            <i class="bi bi-check2-circle fs-3 d-block mb-2"></i>
                                            {% trans "No payroll runs have been marked as completed yet." %}
                                        {% else %}
                                            <i class="bi bi-emoji-sunglasses fs-3 d-block mb-2"></i>
                                            {% trans "No outstanding payroll runs found. All caught up!" %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if is_paginated %}
            <div class="card-footer bg-light">
                {% include "partials/_pagination.html" with page_obj=page_obj %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}






{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\payrollrun_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">{{ view_title }}</h1>
        <div>
            <a href="{% url 'hr:payroll_process' %}" class="btn btn-primary">
                <i class="bi bi-play-circle-fill me-1"></i> {% trans "Start New Payroll Run" %}
            </a>
        </div>
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Payroll History" %}</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Pay Period" %}</th>
                            <th>{% trans "Payment Date" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th>{% trans "Processed By" %}</th>
                            <th>{% trans "Processed At" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for run in payroll_runs %}
                            <tr>
                                <td>
                                    <a href="{{ run.get_absolute_url }}">
                                        <strong>{{ run.pay_period_start|date:"F Y" }}</strong>
                                    </a>
                                </td>
                                <td>{{ run.payment_date|date:"d M, Y" }}</td>
                                <td class="text-center">
                                    <span class="badge 
                                        {% if run.status == 'PAID' %}bg-success
                                        {% elif run.status == 'PROCESSED' %}bg-info
                                        {% elif run.status == 'DRAFT' %}bg-secondary
                                        {% else %}bg-danger{% endif %}">
                                        {{ run.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ run.processed_by.get_full_name|default:"-" }}</td>
                                <td>{{ run.processed_at|date:"d M, Y H:i"|default:"-" }}</td>
                                <td class="text-center">
                                    <a href="{{ run.get_absolute_url }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye-fill"></i> {% trans "View Details" %}
                                    </a>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center p-4 text-muted">
                                    {% trans "No payroll runs have been processed yet." %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if is_paginated %}
            <div class="card-footer bg-light">
                {% include "partials/_pagination.html" with page_obj=page_obj %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}

 {% endcomment %}

# D:\school_fees_saas_v2\apps\core\backends.py
from django.contrib.auth.backends import ModelBackend
from apps.schools.models import StaffUser # Your custom StaffUser
import logging

logger = logging.getLogger(__name__)

class StaffAwareModelBackend(ModelBackend):
    def _get_permissions(self, user_obj, obj, from_name):
        """
        Return the permissions of `user_obj` from `from_name`.
        `from_name` can be "group" or "user".
        Used by has_perm and get_all_permissions.
        """
        if isinstance(user_obj, StaffUser):
            # logger.debug(f"StaffAwareModelBackend: _get_permissions called for StaffUser '{user_obj}', from_name '{from_name}'. Skipping, should be handled by StaffUser-specific backends.")
            return set() # Effectively, ModelBackend grants no extra perms to StaffUser
        
        # logger.debug(f"StaffAwareModelBackend: _get_permissions for User '{user_obj}', from_name '{from_name}'. Proceeding with super().")
        return super()._get_permissions(user_obj, obj, from_name)

    # You might need to override other permission methods if they don't use _get_permissions
    # and directly cause issues with StaffUser.
    # Let's start with _get_permissions as it's a common choke point.

    def get_all_permissions(self, user_obj, obj=None):
        if isinstance(user_obj, StaffUser):
            # logger.debug(f"StaffAwareModelBackend: get_all_permissions for StaffUser '{user_obj}'. Returning empty set.")
            return set()
        return super().get_all_permissions(user_obj, obj)

    def has_perm(self, user_obj, perm, obj=None):
        if isinstance(user_obj, StaffUser):
            # logger.debug(f"StaffAwareModelBackend: has_perm for StaffUser '{user_obj}', perm '{perm}'. Returning False.")
            return False # ModelBackend itself grants no direct perms to StaffUser instances
        return super().has_perm(user_obj, perm, obj)

    def has_module_perms(self, user_obj, app_label):
        if isinstance(user_obj, StaffUser):
            # logger.debug(f"StaffAwareModelBackend: has_module_perms for StaffUser '{user_obj}', app '{app_label}'. Returning False.")
            return False
        return super().has_module_perms(user_obj, app_label)
        
    # authenticate and get_user from ModelBackend can remain as they are;
    # they handle the standard User model.
    
    
# D:\school_fees_saas_v2\apps\core\backends.py
from django.contrib.auth.backends import ModelBackend
from apps.schools.models import StaffUser

# Check if ParentUser is defined to avoid import errors if app is not always present
try:
    from apps.accounts.models import ParentUser
    PARENT_USER_MODEL_DEFINED = True
except ImportError:
    ParentUser = None # Define as None so isinstance checks don't raise NameError
    PARENT_USER_MODEL_DEFINED = False

class SafeModelBackend(ModelBackend): # <<<--- EXACT NAME AND INHERITANCE
    def get_user_permissions(self, user_obj, obj=None):
        if isinstance(user_obj, StaffUser) or \
        (PARENT_USER_MODEL_DEFINED and ParentUser and isinstance(user_obj, ParentUser)):
            return set() # Step aside for our custom user types
        return super().get_user_permissions(user_obj, obj)

    def get_group_permissions(self, user_obj, obj=None):
        if isinstance(user_obj, StaffUser) or \
        (PARENT_USER_MODEL_DEFINED and ParentUser and isinstance(user_obj, ParentUser)):
            return set() # Step aside for our custom user types
        return super().get_group_permissions(user_obj, obj)

    # get_all_permissions and has_perm will be inherited from ModelBackend
    # and will use the above modified methods, effectively returning empty
    # for StaffUser/ParentUser when this SafeModelBackend is consulted for them.

{% extends "tenant_base.html" %}
{% load static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
     {# Add breadcrumbs if needed #}
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger shadow">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Confirm Deletion</h5>
                </div>
                <div class="card-body p-4">
                    <p class="lead">Are you sure you want to delete the following?</p>
                    <p><strong>{{ object_to_delete }}</strong></p>
                    
                    {% if delete_message %}
                        <div class="alert alert-warning small" role="alert">
                            {{ delete_message|safe }}
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ cancel_url|default:request.META.HTTP_REFERER|default:'javascript:history.back()' }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash-fill"></i> Yes, Delete
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}
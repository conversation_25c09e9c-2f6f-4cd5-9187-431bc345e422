# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('payments', '0001_initial'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='academic_year',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payments_in_year', to='schools.academicyear'),
        ),
        migrations.AddField(
            model_name='payment',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_all_payments', to=settings.AUTH_USER_MODEL),
        ),
    ]

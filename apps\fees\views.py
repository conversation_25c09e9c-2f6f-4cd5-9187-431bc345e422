# D:\school_fees_saas_v2\apps\fees\views.py

# --- Standard Python Imports ---
import csv
import logging # Standard library logging
from decimal import Decimal
# import io # Keep if used for BytesIO with Excel/PDF, otherwise remove

# --- Django Core Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin
from django.db import transaction, IntegrityError
from django.db.models import Sum, F, Q, Count, Prefetch
from django.db.models.functions import Coalesce
from django.http import HttpResponse, Http404, JsonResponse, HttpResponseRedirect
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    ListView, CreateView, UpdateView, <PERSON>ete<PERSON>ie<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FormView
)

from django.contrib.auth.decorators import login_required # Keep this for basic auth check
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from apps.common.mixins import tenant_permission_required

# --- Third-Party App Imports (if any for this file, e.g., crispy_forms if used directly in views) ---
# from crispy_forms.helper import FormHelper # Example

# --- Your Project's App Imports ---
# Mixins
from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin

# Models from THIS app (fees)
from .models import (
    AcademicYear, Term, FeeHead, FeeStructure, FeeStructureItem,
    ConcessionType, StudentConcession, StudentFeeAllocation,
    Invoice, InvoiceDetail, InvoiceStatus, LineTypeChoices
)
# Models from OTHER apps
from apps.students.models import Student
from apps.schools.models import SchoolClass, SchoolProfile
from apps.accounting.models import Account as ChartOfAccount # Alias for clarity

# Forms from THIS app (fees)
from .forms import (
    AcademicYearForm, TermForm, FeeHeadForm, FeeStructureForm,
    FeeStructureItemInlineFormSet,
    ConcessionTypeForm, StudentConcessionForm, # StudentConcessionAssignForm seems to be missing from your forms snippet, but was in views
    StudentFeeAllocationForm,
    InvoiceFilterForm, InvoiceForm, InvoiceDetailFormSet, InvoiceDetailUpdateFormSet, InvoiceCancelForm,
    GenerateInvoicesFromStructureForm # This was FeeGenerationForm in one of your snippets
)

# Utils
from apps.common.utils import render_to_pdf, PDF_AVAILABLE
from apps.accounting.utils import create_invoice_journal_entry # If used directly, otherwise models/signals handle this

# --- Logger ---
logger = logging.getLogger(__name__) # Define logger once after imports

# ========================================
# Base View for this App (Optional)
# ========================================
class FeesBaseView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin): # Use your actual mixins
    """Base view for common fee app settings like login_url."""
    login_url = reverse_lazy('schools:staff_login') # Default login for staff in this app


# ========================================
# Academic Year CRUD
# ========================================
class AcademicYearListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = AcademicYear
    template_name = 'fees/academic_year_list.html'
    context_object_name = 'academic_years'
    permission_required = 'fees.view_academicyear' # Assuming models are in 'fees' app

    def get_queryset(self):
        # Queryset is automatically tenant-scoped if AcademicYear is a tenant model
        return AcademicYear.objects.all().order_by('-start_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Academic Years")
        return context

class AcademicYearCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = AcademicYear
    form_class = AcademicYearForm
    template_name = 'fees/academic_year_form.html' # Use a generic _form.html or specific one
    success_url = reverse_lazy('fees:academic_year_list')
    permission_required = 'fees.add_academicyear'
    success_message = _("Academic Year '%(name)s' created successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Create New Academic Year')
        context['form_mode'] = 'create'
        return context

class AcademicYearUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = AcademicYear
    form_class = AcademicYearForm
    template_name = 'fees/academic_year_form.html'
    success_url = reverse_lazy('fees:academic_year_list')
    permission_required = 'fees.change_academicyear'
    success_message = _("Academic Year '%(name)s' updated successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Update Academic Year: %(name)s') % {'name': self.object.name}
        context['form_mode'] = 'update'
        return context

class AcademicYearDeleteView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = AcademicYear
    template_name = 'fees/confirm_delete.html' # Generic confirm delete
    success_url = reverse_lazy('fees:academic_year_list')
    permission_required = 'fees.delete_academicyear'
    success_message = _("Academic Year deleted successfully.")
    # context_object_name default is 'object'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Delete Academic Year: %(name)s') % {'name': self.object.name}
        context['object_type'] = "Academic Year"
        return context
    
    def form_valid(self, form): # For SuccessMessageMixin with DeleteView
        messages.success(self.request, self.success_message)
        return super().form_valid(form)

# ========================================
# Term CRUD
# ========================================
class TermListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = Term
    template_name = 'fees/term_list.html'
    context_object_name = 'terms'
    permission_required = 'fees.view_term'

    def get_queryset(self):
        return Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Terms/Semesters")
        return context

class TermCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = Term
    form_class = TermForm
    template_name = 'fees/term_form.html' # Use a generic _form.html or specific one
    success_url = reverse_lazy('fees:term_list')
    permission_required = 'fees.add_term'
    success_message = _("Term '%(name)s' for %(academic_year)s created successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant # If TermForm needs it for AcademicYear choices
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Add New Term/Semester')
        context['form_mode'] = 'create'
        return context

class TermUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Term
    form_class = TermForm
    template_name = 'fees/term_form.html'
    success_url = reverse_lazy('fees:term_list')
    permission_required = 'fees.change_term'
    success_message = _("Term '%(name)s' updated successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Update Term: %(name)s (%(academic_year)s)') % {'name': self.object.name, 'academic_year': self.object.academic_year.name}
        context['form_mode'] = 'update'
        return context

class TermDeleteView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = Term
    template_name = 'fees/confirm_delete.html'
    success_url = reverse_lazy('fees:term_list')
    permission_required = 'fees.delete_term'
    success_message = _("Term deleted successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Delete Term: %(name)s (%(academic_year)s)') % {'name': self.object.name, 'academic_year': self.object.academic_year.name}
        context['object_type'] = "Term"
        return context

    def form_valid(self, form):
        messages.success(self.request, self.success_message)
        return super().form_valid(form)


# ========================================
# Fee Head CRUD
# ========================================
class FeeHeadListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = FeeHead
    template_name = 'fees/fee_head_list.html'
    context_object_name = 'fee_heads'
    permission_required = 'fees.view_feehead'
    def get_queryset(self):
        return FeeHead.objects.all().order_by('name') # Assumes FeeHead is tenant-specific by schema
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Fee Heads")
        return context

class FeeHeadCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = FeeHead
    form_class = FeeHeadForm
    template_name = 'fees/fee_head_form.html' # Generic or specific
    success_url = reverse_lazy('fees:fee_head_list')
    permission_required = 'fees.add_feehead'
    success_message = _("Fee Head '%(name)s' created successfully.")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Add New Fee Head')
        context['form_mode'] = 'create'
        return context

class FeeHeadUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = FeeHead
    form_class = FeeHeadForm
    template_name = 'fees/fee_head_form.html'
    success_url = reverse_lazy('fees:fee_head_list')
    permission_required = 'fees.change_feehead'
    success_message = _("Fee Head '%(name)s' updated successfully.")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Update Fee Head: %(name)s') % {'name': self.object.name}
        context['form_mode'] = 'update'
        return context



# D:\school_fees_saas_v2\apps\fees\views.py
from django.urls import reverse_lazy
from django.views.generic.edit import DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin # Or your TenantPermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from .models import FeeHead
# Import any other necessary items

class FeeHeadDeleteView(LoginRequiredMixin, SuccessMessageMixin, DeleteView):
    model = FeeHead
    template_name = 'fees/confirm_delete.html'  # This tells the view to use our new template
    success_url = reverse_lazy('fees:fee_head_list') # Redirect here after successful deletion

    def get_context_data(self, **kwargs):
        """
        Adds extra context to the template.
        """
        context = super().get_context_data(**kwargs)
        # The object to be deleted is automatically added as 'object' by DeleteView
        
        # Provide a user-friendly name for the object type
        context['object_type_name'] = "Fee Head" 
        
        # Provide a clear title for the page
        context['view_title'] = f"Delete {context['object_type_name']}"
        
        # Provide a "Cancel" URL for the button in the template
        context['cancel_url'] = self.success_url
        
        # (Optional) You can add logic here to find and list related objects
        # that might be deleted or affected, to warn the user.
        # This is more advanced but good for user experience.
        # context['related_objects'] = [...]
        
        return context
    
    def form_valid(self, form):
        """
        Adds a success message before deleting the object.
        """
        # Capture the object's name before deletion
        object_name = self.object.name

        # Delete the object first
        response = super().form_valid(form)

        # Add success message after deletion using the captured name
        success_message = f"Fee Head '{object_name}' was deleted successfully."
        messages.success(self.request, success_message)

        return response

# class FeeHeadDeleteView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
#     model = FeeHead
#     template_name = 'fees/confirm_delete.html'
#     success_url = reverse_lazy('fees:fee_head_list')
#     permission_required = 'fees.delete_feehead'
#     success_message = _("Fee Head deleted successfully.")
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = _('Delete Fee Head: %(name)s') % {'name': self.object.name}
#         context['object_type'] = "Fee Head"
#         return context
#     def form_valid(self, form):
#         messages.success(self.request, self.success_message)
#         return super().form_valid(form)

# ========================================
# Concession Type CRUD
# ========================================
class ConcessionTypeListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = ConcessionType
    template_name = 'fees/concession_type_list.html'
    context_object_name = 'concession_types'
    permission_required = 'fees.view_concessiontype'
    def get_queryset(self):
        return ConcessionType.objects.all().order_by('name') # Assumes ConcessionType is tenant-specific by schema
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Concession Types")
        return context

class ConcessionTypeCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = ConcessionType
    form_class = ConcessionTypeForm
    template_name = 'fees/concession_type_form.html' # Generic or specific
    success_url = reverse_lazy('fees:concession_type_list')
    permission_required = 'fees.add_concessiontype'
    success_message = _("Concession Type '%(name)s' created successfully.")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Add New Concession Type')
        context['form_mode'] = 'create'
        return context

class ConcessionTypeUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = ConcessionType
    form_class = ConcessionTypeForm
    template_name = 'fees/concession_type_form.html'
    success_url = reverse_lazy('fees:concession_type_list')
    permission_required = 'fees.change_concessiontype'
    success_message = _("Concession Type '%(name)s' updated successfully.")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Update Concession Type: %(name)s') % {'name': self.object.name}
        context['form_mode'] = 'update'
        return context

class ConcessionTypeDeleteView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = ConcessionType
    template_name = 'fees/confirm_delete.html'
    success_url = reverse_lazy('fees:concession_type_list')
    permission_required = 'fees.delete_concessiontype'
    success_message = _("Concession Type deleted successfully.")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Delete Concession Type: %(name)s') % {'name': self.object.name}
        context['object_type'] = "Concession Type"
        return context
    def form_valid(self, form):
        messages.success(self.request, self.success_message)
        return super().form_valid(form)


# ========================================
# Fee Structure CRUD (with Inline Formset for FeeStructureItem)
# ========================================
class FeeStructureListView(FeesBaseView, ListView):
    model = FeeStructure
    template_name = 'fees/fee_structure_list.html'
    context_object_name = 'fee_structures'
    paginate_by = 10
    permission_required = 'fees.view_feestructure'

    def get_queryset(self):
        return FeeStructure.objects.select_related(
            'academic_year', 'term'
        ).prefetch_related(
            Prefetch('items', queryset=FeeStructureItem.objects.select_related('fee_head'))
        ).order_by('-academic_year__start_date', 'term__start_date', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Fee Structures")
        return context

class FeeStructureCreateUpdateMixin: # Keep this mixin as it was good
    model = FeeStructure
    form_class = FeeStructureForm
    template_name = 'fees/fee_structure_form.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['item_formset'] = FeeStructureItemInlineFormSet(
                self.request.POST, self.request.FILES, instance=self.object, prefix='items'
            )
        else:
            context['item_formset'] = FeeStructureItemInlineFormSet(
                instance=self.object, prefix='items'
            )
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        item_formset = context['item_formset']
        
        if item_formset.is_valid():
            try:
                with transaction.atomic():
                    # For CreateView, created_by might be set here
                    if not hasattr(form.instance, 'created_by') and hasattr(self.request.user, 'is_authenticated') and self.request.user.is_authenticated:
                         if isinstance(self, CreateView): # Only for CreateView
                            # form.instance.created_by = self.request.user # This needs StaffUser or settings.AUTH_USER_MODEL
                            pass # created_by should be on model or handled by signal

                    self.object = form.save() 
                    item_formset.instance = self.object
                    item_formset.save()
                    return super().form_valid(form) 
            except IntegrityError as e:
                logger.error(f"FeeStructure Save IntegrityError: {e}", exc_info=True)
                error_msg = str(e).lower()

                # Provide more specific error messages based on the constraint violated
                if 'unique constraint' in error_msg or 'duplicate key' in error_msg:
                    if 'feestructure' in error_msg and ('name' in error_msg or 'academic_year' in error_msg or 'term' in error_msg):
                        form.add_error(None, _("A Fee Structure with this Name, Academic Year, and Term combination already exists. Please use a different name or select a different academic year/term."))
                    elif 'feestructureitem' in error_msg and ('fee_head' in error_msg or 'fee_structure' in error_msg):
                        form.add_error(None, _("You cannot add the same Fee Head multiple times to one Fee Structure. Please remove duplicate fee heads from the items section."))
                    else:
                        form.add_error(None, _("A data conflict occurred. This combination of data already exists. Please check: 1) Fee Structure name/year/term is unique, 2) No duplicate fee heads in items section."))
                else:
                    form.add_error(None, _("A database constraint was violated. Please check your input data."))

                return self.form_invalid(form)
            except Exception as e: 
                logger.error(f"FeeStructure Save Error: {e}", exc_info=True)
                form.add_error(None, _("An unexpected error occurred while saving."))
                return self.form_invalid(form)
        else:
            logger.warning(f"FeeStructure Items Formset errors: {item_formset.errors} {item_formset.non_form_errors()}")
            form.add_error(None, _("Please correct the errors in the fee items section below."))
            return self.form_invalid(form)

class FeeStructureCreateView(FeesBaseView, SuccessMessageMixin, FeeStructureCreateUpdateMixin, CreateView):
    permission_required = 'fees.add_feestructure'
    success_url = reverse_lazy('fees:fee_structure_list') 
    success_message = _("Fee Structure '%(name)s' created successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Create New Fee Structure')
        context['form_mode'] = "create"
        return context

class FeeStructureUpdateView(FeesBaseView, SuccessMessageMixin, FeeStructureCreateUpdateMixin, UpdateView):
    permission_required = 'fees.change_feestructure'
    success_url = reverse_lazy('fees:fee_structure_list')
    success_message = _("Fee Structure '%(name)s' updated successfully.")
    context_object_name = 'fee_structure'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Update Fee Structure: %(name)s') % {'name': self.object.name}
        context['form_mode'] = "update"
        return context

class FeeStructureDeleteView(FeesBaseView, SuccessMessageMixin, DeleteView):
    model = FeeStructure
    template_name = 'fees/confirm_delete.html'
    permission_required = 'fees.delete_feestructure'
    success_url = reverse_lazy('fees:fee_structure_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _('Delete Fee Structure: %(name)s') % {'name': self.object.name}
        context['object_type'] = "Fee Structure"
        return context
    def form_valid(self, form):
        success_message = _("Fee Structure '%(name)s' deleted successfully.") % {'name': self.object.name}
        messages.success(self.request, success_message)
        return super().form_valid(form)


# ==================================================
# Student Fee Allocation & Concession Assignment
# ==================================================
class StudentFeeAllocationCreateView(FeesBaseView, SuccessMessageMixin, CreateView):
    model = StudentFeeAllocation
    form_class = StudentFeeAllocationForm
    template_name = 'fees/student_fee_allocation_form.html'
    permission_required = 'fees.add_studentfeeallocation'
    success_message = _("Fee structure allocated to '%(student)s' successfully.")

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        self.student = get_object_or_404(Student, pk=self.kwargs.get('student_pk'))

    def get_initial(self):
        initial = super().get_initial()
        if self.student: initial['student'] = self.student.pk
        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if self.student: kwargs['student_instance'] = self.student
        return kwargs

    def form_valid(self, form):
        if not form.instance.student_id and self.student:
            form.instance.student = self.student
        form.instance.created_by = self.request.user
        try:
            return super().form_valid(form)
        except IntegrityError:
            messages.error(self.request, _("This fee structure is already allocated to this student for the selected academic year/term."))
            return self.form_invalid(form)

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.student.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['student'] = self.student
        context['view_title'] = _('Allocate Fee Structure to %(student_name)s') % {'student_name': self.student.get_full_name()}
        context['form_mode'] = 'create'
        return context

class StudentConcessionAssignView(FeesBaseView, SuccessMessageMixin, CreateView):
    model = StudentConcession
    form_class = StudentConcessionForm
    template_name = 'fees/student_concession_form.html'
    permission_required = 'fees.add_studentconcession'
    
    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        self.student = get_object_or_404(Student, pk=self.kwargs.get('student_pk'))

    def get_initial(self):
        initial = super().get_initial()
        if self.student: initial['student'] = self.student.pk
        if self.request.user.is_authenticated: initial['granted_by'] = self.request.user
        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        if self.student: kwargs['student_instance'] = self.student
        return kwargs
    
    def form_valid(self, form):
        if not form.instance.student_id and self.student:
            form.instance.student = self.student
        if not form.instance.granted_by_id and self.request.user.is_authenticated:
            form.instance.granted_by = self.request.user
        try:
            self.object = form.save()
            success_msg = _("Concession '%(concession_type)s' assigned to student '%(student)s' successfully.") % {
                'concession_type': self.object.concession_type.name,
                'student': self.student.get_full_name()
            }
            messages.success(self.request, success_msg)
            return redirect(self.get_success_url())
        except IntegrityError:
            messages.error(self.request, _("This concession type may already be assigned to this student for the selected academic year/term."))
            return self.form_invalid(form)

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.student.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['student'] = self.student
        context['view_title'] = _('Assign Concession to %(student_name)s') % {'student_name': self.student.get_full_name()}
        context['form_mode'] = 'create'
        return context
    
    
# ========================================
# Invoice CRUD & Actions
# ========================================

# D:\school_fees_saas_v2\apps\fees\views.py
from django.views.generic import ListView
from .models import Invoice
from .filters import InvoiceFilter # Import the filter
from apps.common.mixins import TenantPermissionRequiredMixin # Assuming this is your mixin

class InvoiceListView(TenantPermissionRequiredMixin, ListView):
    model = Invoice
    template_name = 'fees/invoice_list.html'
    context_object_name = 'invoices'
    paginate_by = 50
    permission_required = 'fees.view_invoice'

    def get_queryset(self):
        """
        Base queryset with all necessary pre-fetching for performance.
        """
        queryset = super().get_queryset().select_related(
            'student', 'academic_year', 'term'
        ).prefetch_related(
            'details', # Prefetch the details themselves
            # THIS IS THE NEW, CRITICAL LINE FOR YOUR PAYMENT DISPLAY REQUEST
            'allocations__payment__payment_method' 
        )
        
        # Apply filtering using the FilterSet
        self.filter = InvoiceFilter(self.request.GET, queryset=queryset)

        # Return the filtered queryset for pagination and rendering
        return self.filter.qs.order_by('-issue_date', 'student__last_name')

    def get_context_data(self, **kwargs):
        """
        Adds the filter form to the context.
        """
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Invoices")
        # Add the filter form instance to the context so the template can render it
        context['filter_form'] = self.filter.form
        return context

    def get(self, request, *args, **kwargs):
        """
        Handle GET requests including export functionality.
        """
        # Check for export parameter
        export_type = request.GET.get('export')
        if export_type in ['csv', 'excel', 'pdf']:
            return self.handle_export(request, export_type)

        # Normal GET request
        return super().get(request, *args, **kwargs)

    def handle_export(self, request, export_type):
        """
        Handle export requests for invoices.
        """
        # Get the filtered queryset for export
        queryset = super().get_queryset().select_related(
            'student', 'academic_year', 'term'
        ).prefetch_related('details', 'allocations__payment__payment_method')

        # Apply filtering
        filter_obj = InvoiceFilter(request.GET, queryset=queryset)
        filtered_queryset = filter_obj.qs.order_by('-issue_date', 'student__last_name')

        if export_type == 'csv':
            return self.export_to_csv(filtered_queryset, request)
        elif export_type == 'excel':
            return self.export_to_excel(filtered_queryset, request)
        elif export_type == 'pdf':
            return self.export_to_pdf(filtered_queryset, request)

        # Fallback to normal view
        return super().get(request, *args, **kwargs)

    def export_to_csv(self, queryset, request):
        """
        Export invoices to CSV format.
        """
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="invoices_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        headers = [
            'Invoice Number', 'Student Name', 'Admission Number', 'Class',
            'Academic Year', 'Term', 'Issue Date', 'Due Date', 'Status',
            'Total Amount', 'Amount Paid', 'Balance Due'
        ]
        writer.writerow(headers)

        for invoice in queryset:
            writer.writerow([
                invoice.invoice_number,
                invoice.student.get_full_name() if invoice.student else 'N/A',
                invoice.student.admission_number if invoice.student else 'N/A',
                invoice.student.current_class.name if invoice.student and invoice.student.current_class else 'N/A',
                invoice.academic_year.name if invoice.academic_year else 'N/A',
                invoice.term.name if invoice.term else 'N/A',
                invoice.issue_date.strftime('%Y-%m-%d') if invoice.issue_date else '',
                invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                invoice.get_status_display(),
                f"{invoice.total_amount:.2f}",
                f"{invoice.amount_paid:.2f}",
                f"{invoice.balance_due:.2f}"
            ])

        return response

    def export_to_excel(self, queryset, request):
        """
        Export invoices to Excel format.
        """
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils import get_column_letter
        except ImportError:
            messages.error(request, _("Excel export requires openpyxl package."))
            return redirect(request.path)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Invoices"

        # Headers
        headers = [
            'Invoice Number', 'Student Name', 'Admission Number', 'Class',
            'Academic Year', 'Term', 'Issue Date', 'Due Date', 'Status',
            'Total Amount', 'Amount Paid', 'Balance Due'
        ]

        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Data rows
        for row_num, invoice in enumerate(queryset, 2):
            ws.cell(row=row_num, column=1, value=invoice.invoice_number)
            ws.cell(row=row_num, column=2, value=invoice.student.get_full_name() if invoice.student else 'N/A')
            ws.cell(row=row_num, column=3, value=invoice.student.admission_number if invoice.student else 'N/A')
            ws.cell(row=row_num, column=4, value=invoice.student.current_class.name if invoice.student and invoice.student.current_class else 'N/A')
            ws.cell(row=row_num, column=5, value=invoice.academic_year.name if invoice.academic_year else 'N/A')
            ws.cell(row=row_num, column=6, value=invoice.term.name if invoice.term else 'N/A')
            ws.cell(row=row_num, column=7, value=invoice.issue_date.strftime('%Y-%m-%d') if invoice.issue_date else '')
            ws.cell(row=row_num, column=8, value=invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '')
            ws.cell(row=row_num, column=9, value=invoice.get_status_display())
            ws.cell(row=row_num, column=10, value=float(invoice.total_amount))
            ws.cell(row=row_num, column=11, value=float(invoice.amount_paid))
            ws.cell(row=row_num, column=12, value=float(invoice.balance_due))

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="invoices_{timezone.now().strftime("%Y%m%d")}.xlsx"'

        wb.save(response)
        return response

    def export_to_pdf(self, queryset, request):
        """
        Export invoices to PDF format.
        """
        try:
            from apps.common.utils import render_to_pdf
        except ImportError:
            messages.error(request, _("PDF export functionality is not available."))
            return redirect(request.path)

        # Prepare context for PDF template
        context = {
            'invoices': queryset,
            'report_title': _("Invoice List Report"),
            'report_generated_at': timezone.now(),
            'school_profile': getattr(request, 'school_profile', None),
            'filter_applied': bool(request.GET),
            'total_invoices': queryset.count(),
            'total_amount': queryset.aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00'),
            'total_paid': queryset.aggregate(total=Sum('amount_paid'))['total'] or Decimal('0.00'),
        }

        # Calculate balance due
        context['total_balance'] = context['total_amount'] - context['total_paid']

        # Render PDF
        pdf = render_to_pdf('fees/invoice_list_pdf.html', context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"invoice_list_{timezone.now().strftime('%Y%m%d')}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            messages.error(request, _("Sorry, we could not generate the PDF report."))
            return redirect(request.path)

# class InvoiceListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
#     model = Invoice
#     template_name = 'fees/invoice_list.html'
#     context_object_name = 'invoices'
#     paginate_by = 50  # Increased from 20 to 50 for better visibility
#     permission_required = 'fees.view_invoice'

#     def get_queryset(self):
#         queryset = Invoice.objects.select_related(
#             'student', 'academic_year', 'term' #, 'fee_structure_used'
#         ).prefetch_related(
#             'details__fee_head', 'details__concession_type' # Added concession_type from InvoiceDetail
#         ).all().order_by('-issue_date', 'student__last_name') # Default order

#         self.form = InvoiceFilterForm(self.request.GET, tenant=self.request.tenant) # Pass tenant
#         if self.form.is_valid():
#             cd = self.form.cleaned_data
#             if cd.get('student'): queryset = queryset.filter(student=cd['student'])
#             if cd.get('status'): queryset = queryset.filter(status=cd['status'])
#             if cd.get('academic_year'): queryset = queryset.filter(academic_year=cd['academic_year'])
#             if cd.get('issue_date_from'): queryset = queryset.filter(issue_date__gte=cd['issue_date_from'])
#             if cd.get('issue_date_to'): queryset = queryset.filter(issue_date__lte=cd['issue_date_to'])
#             if cd.get('invoice_number_query'): queryset = queryset.filter(invoice_number__icontains=cd['invoice_number_query'])
#         return queryset

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = _("Manage Invoices")
#         context['filter_form'] = self.form # self.form is set in get_queryset
#         # context['status_choices'] = InvoiceStatus.choices # Form handles choices
#         return context

class InvoiceUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, UpdateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'fees/invoice_form.html'
    permission_required = 'fees.change_invoice'
    context_object_name = 'invoice'

    def get_queryset(self): # Only allow editing DRAFT invoices
        return super().get_queryset().filter(status=InvoiceStatus.DRAFT)

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to check if invoice can be edited before processing"""
        try:
            # Check if invoice exists and is editable
            invoice = Invoice.objects.get(pk=kwargs['pk'])
            if invoice.status != InvoiceStatus.DRAFT:
                messages.error(request,
                    _("Invoice '%(invoice_number)s' cannot be edited because it has status '%(status)s'. Only DRAFT invoices can be edited.") % {
                        'invoice_number': invoice.invoice_number_display,
                        'status': invoice.get_status_display()
                    })
                return redirect('fees:invoice_detail', pk=invoice.pk)
        except Invoice.DoesNotExist:
            # Let the normal flow handle the 404
            pass

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Update Draft Invoice: %(invoice_number)s") % {'invoice_number': self.object.invoice_number}
        context['form_mode'] = "update"

        # Only create formset if it's not already provided in context (e.g., from form_invalid)
        if 'item_formset' not in context:
            if self.request.POST:
                context['item_formset'] = InvoiceDetailUpdateFormSet(self.request.POST, instance=self.object, prefix='details')
            else:
                context['item_formset'] = InvoiceDetailUpdateFormSet(instance=self.object, prefix='details')
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        item_formset = context['item_formset']

        if item_formset.is_valid():
            with transaction.atomic():
                self.object = form.save()
                item_formset.instance = self.object
                item_formset.save()

                self.object.recalculate_totals_from_details()

                # Check if this is a preview action
                action = self.request.POST.get('action', 'save')
                if action == 'preview':
                    messages.success(self.request, _("Invoice '%(invoice_number)s' updated successfully. Showing preview below.") % {'invoice_number': self.object.invoice_number})
                else:
                    messages.success(self.request, _("Invoice '%(invoice_number)s' updated successfully.") % {'invoice_number': self.object.invoice_number})

                return redirect(self.get_success_url())
        else:
                # COMPREHENSIVE ERROR LOGGING FOR DEBUGGING
                logger.error("=" * 80)
                logger.error("INVOICE UPDATE FORMSET VALIDATION FAILED")
                logger.error("=" * 80)
                logger.error(f"Invoice ID: {self.object.pk}")
                logger.error(f"Invoice Number: {self.object.invoice_number_display}")
                logger.error(f"Formset type: {type(item_formset)}")
                logger.error(f"Formset is_bound: {item_formset.is_bound}")
                logger.error(f"Management form valid: {item_formset.management_form.is_valid()}")

                if item_formset.management_form.errors:
                    logger.error(f"Management form errors: {item_formset.management_form.errors}")

                logger.error(f"Total forms: {item_formset.total_form_count()}")
                logger.error(f"Initial forms: {item_formset.initial_form_count()}")

                # Log formset-level errors
                if item_formset.non_form_errors():
                    logger.error(f"Non-form errors: {item_formset.non_form_errors()}")

                # Log each form's errors and data
                for i, form in enumerate(item_formset.forms):
                    logger.error(f"\n--- FORM {i} ---")
                    logger.error(f"Form {i} is_bound: {form.is_bound}")
                    logger.error(f"Form {i} is_valid: {form.is_valid()}")

                    if form.errors:
                        logger.error(f"Form {i} errors: {form.errors}")

                    # Log form data
                    if hasattr(form, 'data') and form.data:
                        form_data = {}
                        for field_name in form.fields:
                            field_key = f"{form.prefix}-{field_name}"
                            if field_key in form.data:
                                form_data[field_name] = form.data[field_key]
                        logger.error(f"Form {i} data: {form_data}")

                    # Log cleaned data if available
                    try:
                        if hasattr(form, 'cleaned_data'):
                            logger.error(f"Form {i} cleaned_data: {form.cleaned_data}")
                    except:
                        logger.error(f"Form {i} cleaned_data: Not available")

                logger.error("=" * 80)

                # Collect specific error messages to show to user
                error_messages = []
                for i, form_item in enumerate(item_formset.forms):
                    if form_item.errors:
                        for field_name, errors in form_item.errors.items():
                            for error in errors:
                                error_messages.append(f"Line {i+1} - {field_name}: {error}")

                if error_messages:
                    # Show specific errors to help user
                    error_summary = "Please correct the following errors in line items: " + "; ".join(error_messages[:3])
                    if len(error_messages) > 3:
                        error_summary += f" (and {len(error_messages) - 3} more errors)"
                    messages.error(self.request, error_summary)
                else:
                    messages.error(self.request, _("Please correct errors in the invoice line items."))

                return self.form_invalid(form)

    def form_invalid(self, form):
        """Override to ensure formset is properly included in context when form validation fails"""
        # Get the formset with POST data to preserve user input
        item_formset = InvoiceDetailUpdateFormSet(self.request.POST, instance=self.object, prefix='details')

        # Add the formset to the context so it's available in the template
        context = self.get_context_data(form=form)
        context['item_formset'] = item_formset

        return self.render_to_response(context)

    def get_success_url(self):
        return reverse('fees:invoice_detail', kwargs={'pk': self.object.pk})

class InvoiceDetailView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, DetailView):
    model = Invoice
    template_name = 'fees/invoice_detail.html'
    context_object_name = 'invoice'
    permission_required = 'fees.view_invoice'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'student', 'academic_year', 'term', 'created_by', # 'fee_structure_used',
            'student__current_class', 'student__current_section'
        ).prefetch_related(
            Prefetch('details', queryset=InvoiceDetail.objects.select_related('fee_head', 'concession_type')),
            # Prefetch('payments_made', queryset=Payment.objects.filter(status='COMPLETED').select_related('payment_method')) # Assuming Payment has FK to Invoice
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = self.object
        context['view_title'] = _("Invoice Details: %(invoice_number)s") % {'invoice_number': invoice.invoice_number}
        
        # Separate details into charges and concessions for display
        context['charge_items'] = invoice.details.filter(line_type=LineTypeChoices.FEE_ITEM)
        context['concession_lines'] = invoice.details.filter(line_type=LineTypeChoices.CONCESSION)
        
        # Calculate displayed totals based on current state
        context['display_subtotal'] = invoice.subtotal_amount
        context['display_total_concessions'] = invoice.total_concession_amount
        context['display_net_billable'] = invoice.net_billable_amount
        context['display_amount_paid'] = invoice.amount_paid
        context['display_balance_due'] = invoice.balance_due

        # Get related payments (This needs Payment model to have FK to Invoice, or through PaymentAllocation)
        # For now, assuming PaymentAllocation links Payment to Invoice
        # payment_allocations = PaymentAllocation.objects.filter(invoice=invoice).select_related('payment', 'payment__payment_method')
        # context['related_payments'] = [pa.payment for pa in payment_allocations]
        
        context['school_profile'] = SchoolProfile.objects.first() # Assuming one profile per tenant
        return context


from .models import Invoice, InvoiceStatus # Make sure InvoiceStatus is imported

class InvoiceDraftDeleteView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, DeleteView):
    model = Invoice
    template_name = 'fees/invoice_confirm_delete_draft.html'
    context_object_name = 'invoice'
    permission_required = 'fees.delete_invoice' # <<< USE STANDARD DELETE PERMISSION
    success_url = reverse_lazy('fees:invoice_list')
    
    view_title = _("Delete Draft Invoice") 
    view_icon = "bi-trash3-fill"

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        if obj.status != InvoiceStatus.DRAFT: # Ensure InvoiceStatus.DRAFT is correct
            logger.warning(f"User {self.request.user.email} attempt to delete non-draft invoice PK {obj.pk} (Status: {obj.status}). Access denied via status check.")
            raise Http404(_("Only DRAFT invoices can be deleted through this action. This invoice is '%(status)s'.") % {'status': obj.get_status_display()})
        return obj

    def form_valid(self, form):
        invoice_pk = self.object.pk # Get PK before deletion
        invoice_number_display = self.object.invoice_number_display
        student_name = self.object.student.get_full_name() if self.object.student else "N/A"
        tenant_name = self.request.tenant.name
        
        # Log the action
        try:
            from apps.portal_admin.utils import create_admin_log_entry
            create_admin_log_entry(
                user=self.request.user,
                tenant=self.request.tenant,
                action_type="DELETE_DRAFT_INVOICE",
                model_name="Invoice",
                object_id=invoice_pk, # Use stored PK
                object_repr=f"Draft Invoice {invoice_number_display} for {student_name}",
                details=f"Draft invoice deleted by {self.request.user.email}."
            )
        except Exception as e:
            logger.error(f"Error creating admin log for draft invoice deletion (PK {invoice_pk}): {e}")
            
        response = super().form_valid(form) # This deletes the object
        messages.success(self.request, _("Draft Invoice '%(number)s' has been successfully deleted.") % {'number': invoice_number_display})
        logger.info(f"User {self.request.user.email} DELETED DRAFT Invoice PK {invoice_pk}, Number: {invoice_number_display} for tenant '{tenant_name}'.")
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        context['success_url'] = self.get_success_url()
        context['fallback_url'] = self.get_success_url()
        
        context['view_title'] = self.view_title
        context['view_icon'] = self.view_icon
        context['breadcrumb'] = [
            {"name": _("Fees Management"), "url": reverse_lazy('fees:invoice_list')},
            {"name": _("Invoices"), "url": reverse_lazy('fees:invoice_list')},
            {"name": self.object.invoice_number_display, "url": self.object.get_absolute_url()},
            {"name": _("Confirm Delete Draft"), "url": ""},
        ]
        return context


# --- Action Views for Invoices ---
@login_required(login_url=reverse_lazy('schools:staff_login'))
@tenant_permission_required('fees.change_invoice')
def issue_invoice_view(request, pk):
    invoice = get_object_or_404(Invoice, pk=pk)
    tenant = request.tenant

    if invoice.status != InvoiceStatus.DRAFT:
        messages.warning(request, _("Only DRAFT invoices can be issued."))
        return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))

    try:
        with transaction.atomic():
            # 1. Generate Invoice Number if not already set
            if not invoice.invoice_number:
                # Generate invoice number using the utility function
                from .utils import generate_invoice_number
                invoice.invoice_number = generate_invoice_number(tenant)

            # 2. Update Status
            invoice.status = InvoiceStatus.SENT
            invoice.issue_date = timezone.now().date() # Update issue date to now
            
            # 3. Update amounts and final status (in case details were changed in draft)
            invoice.recalculate_totals_from_details() # This will also check for overdue based on new issue_date
            
            # 4. Create Journal Entry
            # from apps.accounting.utils import create_invoice_journal_entry # Import at top
            # ar_account = ChartOfAccount.objects.get(code=settings.ACCOUNTING_AR_CODE) # Get from settings
            # income_lines_data = [] # Prepare data for JE from invoice.details
            # for detail in invoice.details.filter(line_type=LineTypeChoices.FEE_ITEM).select_related('fee_head__income_account_link'):
            #     if detail.fee_head and detail.fee_head.income_account_link:
            #         income_lines_data.append({
            #             'account': detail.fee_head.income_account_link,
            #             'amount': detail.amount, # Positive amount for income
            #             'type': 'CR'
            #         })
            # # Handle concession lines for JE (e.g., debit a discount/concession expense account or contra-revenue)
            # if invoice.total_concession_amount > 0:
            #     # discount_account = ChartOfAccount.objects.get(code=settings.ACCOUNTING_DISCOUNT_GIVEN_CODE)
            #     # income_lines_data.append({'account': discount_account, 'amount': invoice.total_concession_amount, 'type': 'DR'})
            #     pass # Placeholder for concession JE logic

            # je = create_invoice_journal_entry(
            #     tenant=tenant, user=request.user, invoice=invoice, date=invoice.issue_date,
            #     ar_account=ar_account, income_lines_data=income_lines_data, 
            #     # concession_amount=invoice.total_concession_amount, concession_account=discount_account,
            #     narration=f"Invoice {invoice.invoice_number} issued to {invoice.student.get_full_name()}."
            # )
            # invoice.journal_entry = je # Link JE to invoice
            
            invoice.save() # Save all changes
            
            logger.info(f"Invoice {invoice.invoice_number} (PK: {invoice.pk}) ISSUED by {request.user.email}.")
            messages.success(request, _("Invoice %(invoice_number)s has been issued successfully.") % {'invoice_number': invoice.invoice_number})
            
            # TODO: Trigger email notification to parent (Celery task)

    except ChartOfAccount.DoesNotExist:
        messages.error(request, _("Accounting configuration error (A/R or Income account not found). Invoice not issued."))
    except Exception as e:
        messages.error(request, _("An error occurred while issuing the invoice: %(error)s") % {'error': str(e)})
        logger.error(f"Error issuing invoice {invoice.pk}: {e}", exc_info=True)

    return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))


@login_required(login_url=reverse_lazy('schools:staff_login'))
@tenant_permission_required('fees.change_invoice')
def void_invoice_view(request, pk):
    invoice = get_object_or_404(Invoice, pk=pk)
    
    if request.method == 'POST':
        form = InvoiceCancelForm(request.POST) # Using InvoiceCancelForm
        if form.is_valid():
            cancellation_reason = form.cleaned_data['cancellation_reason']
            try:
                with transaction.atomic():
                    invoice.status = InvoiceStatus.VOID
                    invoice.internal_notes = (invoice.internal_notes or "") + \
                                            f"\n--- VOIDED on {timezone.now().strftime('%Y-%m-%d %H:%M')} by {request.user.email} ---\nReason: {cancellation_reason}"
                    # invoice.amount_paid = Decimal('0.00') # Optional: Reset amount_paid? Depends on accounting.
                    # invoice.balance_due is a property, will reflect change in amount_paid
                    
                    # TODO: Create Reversing Journal Entry for the original invoice's JE
                    # if invoice.journal_entry:
                    #     create_reversing_journal_entry(invoice.journal_entry, request.user, f"Reversal for voided invoice {invoice.invoice_number}")
                    #     invoice.journal_entry = None # Unlink or link to reversal JE
                    
                    invoice.save()
                    logger.info(f"Invoice {invoice.invoice_number} (PK: {invoice.pk}) VOIDED by {request.user.email}. Reason: {cancellation_reason}")
                    messages.success(request, _("Invoice %(invoice_number)s has been voided.") % {'invoice_number': invoice.invoice_number})
                    return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))
            except Exception as e:
                messages.error(request, _("An error occurred while voiding the invoice: %(error)s") % {'error': str(e)})
                logger.error(f"Error voiding invoice {invoice.pk}: {e}", exc_info=True)
                # Form will re-render with errors if any, or just redirect
        # else: form is invalid, re-render template
    else:
        form = InvoiceCancelForm()

    context = {
        'invoice': invoice,
        'form': form,
        'view_title': _("Void Invoice: %(invoice_number)s") % {'invoice_number': invoice.invoice_number}
    }
    return render(request, 'fees/invoice_void_confirm.html', context) # New template needed


# ========================================
# Invoice Generation (from Fee Structure) - View
# This is a more complex view involving selection and bulk processing.
# ========================================
class GenerateInvoicesFromStructureView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, FormView):
    form_class = GenerateInvoicesFromStructureForm # Your form from forms.py
    template_name = 'fees/generate_structure_invoices.html' # Use existing template
    permission_required = 'fees.add_invoice' # Or a specific 'fees.generate_bulk_invoices'
    success_url = reverse_lazy('fees:invoice_list') # Redirect after generation

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant # Pass tenant if form needs to scope FeeStructure choices
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Generate Invoices from Fee Structure")
        return context

    @transaction.atomic # Ensure all or nothing for invoice generation batch
    def form_valid(self, form):
        fee_structure = form.cleaned_data['fee_structure']
        issue_date = form.cleaned_data['issue_date']
        due_date = form.cleaned_data['due_date']
        # Term is now part of the fee_structure itself, or the allocation
        # If generating for a specific term not implied by structure, form needs a term field
        
        # Period description from the form (user input)
        user_period_description = form.cleaned_data.get('period_description', '')

        tenant = self.request.tenant
        current_user = self.request.user

        logger.info(f"Attempting to generate invoices from FeeStructure '{fee_structure.name}' (PK: {fee_structure.pk}) "
                    f"for AY '{fee_structure.academic_year.name}', Term '{fee_structure.term.name if fee_structure.term else 'Annual'}'. "
                    f"Issue: {issue_date}, Due: {due_date}, PeriodDesc: '{user_period_description}'.")

        # --- Pre-flight checks for accounting setup ---
        try:
            ar_account_code = getattr(settings, 'ACCOUNTING_ACCOUNTS_RECEIVABLE_CODE', 'AR_DEFAULT')
            ar_account = ChartOfAccount.objects.get(code=ar_account_code)
            # Optional: check for discount account if you handle discounts via JE during invoice issue
            # discount_account_code = getattr(settings, 'ACCOUNTING_DISCOUNT_GIVEN_CODE', 'DISCOUNT_DEFAULT')
            # discount_account = ChartOfAccount.objects.get(code=discount_account_code)
        except ChartOfAccount.DoesNotExist:
            messages.error(self.request, _("Critical accounting configuration missing (e.g., Accounts Receivable account). Please contact support."))
            return self.form_invalid(form)
        except AttributeError: # If settings codes are missing
            messages.error(self.request, _("Accounting codes not defined in settings. Cannot generate invoices."))
            return self.form_invalid(form)
        
        # Check if all fee heads in the structure are linked to income accounts
        unlinked_items = FeeStructureItem.objects.filter(fee_structure=fee_structure, fee_head__income_account_link__isnull=True).select_related('fee_head')
        if unlinked_items.exists():
            missing_links = ", ".join([f"'{item.fee_head.name}'" for item in unlinked_items])
            messages.error(self.request, _(f"Cannot generate invoices: The following fee heads in the structure '{fee_structure.name}' are not linked to an income account: {missing_links}."))
            return self.form_invalid(form)

        # Find students allocated to this fee structure for the structure's academic year and term
        allocations_qs = StudentFeeAllocation.objects.filter(
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            # term=fee_structure.term, # Matching term is crucial
            student__is_active=True,
            is_active=True # Allocation itself is active
        ).select_related('student', 'term').prefetch_related(
            Prefetch('student__applied_concessions', 
                     queryset=StudentConcession.objects.filter(
                         academic_year=fee_structure.academic_year,
                         # Q(term=fee_structure.term) | Q(term__isnull=True) # Concessions for this term or whole year
                     ).select_related('concession_type'))
        )
        # Filter allocations by term if the fee structure has a specific term
        if fee_structure.term:
            allocations_qs = allocations_qs.filter(term=fee_structure.term)
        else: # Structure is annual, allocations might be annual or termly.
              # This logic needs refinement if structure is annual but invoices are per term.
              # For now, assume if structure is annual, we generate one annual invoice per allocation.
            allocations_qs = allocations_qs.filter(term__isnull=True)


        if not allocations_qs.exists():
            messages.warning(self.request, _("No active student allocations found for this fee structure and period to generate invoices."))
            return self.form_invalid(form)

        created_count = 0
        skipped_count = 0
        error_count = 0
        
        # Construct the final period description
        term_name_for_desc = fee_structure.term.name if fee_structure.term else _("Annual")
        final_period_description = f"{fee_structure.name} - {term_name_for_desc}"
        if user_period_description: # Append user's description if provided
            final_period_description += f" ({user_period_description})"


        for allocation in allocations_qs:
            student = allocation.student
            
            # --- Duplicate Invoice Check ---
            # Check if an invoice already exists for this student, structure, AY, and term
            # The period_description can also be part of uniqueness to allow re-billing for different cycles within a term (e.g. monthly)
            existing_invoice_q = Q(student=student, academic_year=fee_structure.academic_year, status__in=[InvoiceStatus.DRAFT, InvoiceStatus.SENT, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.PAID, InvoiceStatus.OVERDUE])
            if fee_structure.term:
                existing_invoice_q &= Q(term=fee_structure.term)
            else:
                existing_invoice_q &= Q(term__isnull=True)
            
            # To allow for different billing cycles (e.g. monthly tuition from an annual structure),
            # the period_description becomes very important for uniqueness.
            # For simplicity, let's assume one invoice per fee_structure-term allocation for now.
            # If you use `period_description` in uniqueness, you might need to rethink how it's formed.
            # existing_invoice_q &= Q(period_description__iexact=final_period_description) # Stricter check

            if Invoice.objects.filter(existing_invoice_q).exists():
                logger.info(f"Skipping invoice for {student.get_full_name()} and structure '{fee_structure.name}': Invoice already exists for this period.")
                skipped_count += 1
                continue

            # --- Prepare Invoice Data ---
            invoice_details_to_create = []
            invoice_subtotal = Decimal('0.00')

            for item in fee_structure.items.all(): # items is related_name from FeeStructureItem
                invoice_details_to_create.append(
                    InvoiceDetail(
                        # invoice will be set after Invoice object is created
                        line_type=LineTypeChoices.FEE_ITEM,
                        fee_head=item.fee_head,
                        description=item.description or item.fee_head.name, # Use item desc if available
                        quantity=Decimal('1.00'), # Assuming quantity 1 for fee structure items
                        unit_price=item.amount,
                        amount=item.amount # Model's save will verify based on type
                    )
                )
                invoice_subtotal += item.amount
            
            # --- Calculate and Apply Concessions for this Student ---
            invoice_total_concession = Decimal('0.00')
            # Iterate through student's active concessions for THIS academic year and term (or year-round)
            concessions_for_student = student.applied_concessions.filter(
                academic_year=fee_structure.academic_year,
                concession_type__is_active=True
            ).filter(
                Q(term=fee_structure.term) | Q(term__isnull=True) # For this term or year-round
            )

            for student_concession_instance in concessions_for_student:
                concession_type_obj = student_concession_instance.concession_type
                # For simplicity, assume concession applies to the invoice_subtotal
                # More complex: apply to specific fee_heads if StudentConcession links to FeeHead
                discount_val = concession_type_obj.calculate_discount_on_amount(invoice_subtotal) # Pass current subtotal
                
                if discount_val > Decimal('0.00'):
                    invoice_details_to_create.append(
                        InvoiceDetail(
                            line_type=LineTypeChoices.CONCESSION,
                            concession_type=concession_type_obj,
                            description=concession_type_obj.name,
                            quantity=Decimal('1.00'),
                            unit_price=discount_val, # Store the calculated discount as unit price
                            amount=-discount_val # Amount is negative for concessions
                        )
                    )
                    invoice_total_concession += discount_val
            
            # Ensure concession doesn't exceed subtotal
            invoice_total_concession = min(invoice_subtotal, invoice_total_concession)

            # --- Create Invoice and Details in Transaction ---
            try:
                new_invoice = Invoice(
                    student=student,
                    academic_year=fee_structure.academic_year,
                    term=fee_structure.term, # This comes from the structure itself
                    issue_date=issue_date,
                    due_date=due_date,
                    status=InvoiceStatus.DRAFT, # Create as Draft first
                    created_by=current_user,
                    period_description=final_period_description, # Use the constructed description
                    subtotal_amount=invoice_subtotal,
                    total_concession_amount=invoice_total_concession,
                    amount_paid=Decimal('0.00') # Initially zero
                )
                # Invoice number will be generated automatically in save() method
                new_invoice.save() # Save to get PK

                for detail_item in invoice_details_to_create:
                    detail_item.invoice = new_invoice # Link to the created invoice
                    # detail_item.save() # Save individually - model save recalculates amount
                InvoiceDetail.objects.bulk_create(invoice_details_to_create) # More efficient

                # Final update of amounts and status on invoice after details are saved
                new_invoice.recalculate_totals_from_details() # Recalculate based on actual saved details

                created_count += 1
                logger.info(f"Generated DRAFT Invoice {new_invoice.invoice_number} for {student.get_full_name()}")

            except Exception as e_inv_create:
                logger.error(f"Error creating invoice for student {student.pk} from structure {fee_structure.pk}: {e_inv_create}", exc_info=True)
                messages.error(self.request, _("Error generating invoice for %(student_name)s: %(error)s") % {'student_name': student.get_full_name(), 'error': str(e_inv_create)})
                error_count +=1

        if created_count > 0:
            messages.success(self.request, _("%(count)d draft invoices generated successfully.") % {'count': created_count})
        if skipped_count > 0:
            messages.info(self.request, _("%(count)d invoices were skipped as they already exist for the period.") % {'count': skipped_count})
        if error_count > 0:
            messages.warning(self.request, _("%(count)d invoices could not be generated due to errors.") % {'count': error_count})
        if created_count == 0 and skipped_count == 0 and error_count == 0:
            messages.info(self.request, _("No new invoices were generated. All applicable students might have already been invoiced for this structure and period."))

        return HttpResponseRedirect(self.get_success_url())



# D:\school_fees_saas_v2\apps\fees\views.py

import logging
from decimal import Decimal
from django.utils import timezone
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, reverse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

# --- Import Decorators for Security ---
from django.contrib.auth.decorators import login_required
from apps.common.decorators import staff_permission_required # Assuming you have a custom decorator

# --- Import Models and Utilities ---
from .models import Invoice, InvoiceDetail, LineTypeChoices
from apps.schools.models import SchoolProfile
from apps.common.utils import render_to_pdf, PDF_AVAILABLE

logger = logging.getLogger(__name__)

# --- Using Decorators for a Cleaner View ---
@login_required(login_url=reverse_lazy('schools:staff_login'))
@staff_permission_required('fees.view_invoice') # Your custom decorator for staff perms
def invoice_pdf_view(request, pk):
    """
    Generates and serves a PDF for a single invoice.
    Protected by login and permission decorators.
    """
    # 1. --- Check for PDF Library ---
    if not PDF_AVAILABLE:
        logger.error("PDF generation library (xhtml2pdf) is not available.")
        messages.error(request, _("PDF generation service is currently unavailable."))
        # Redirect to the HTML detail view as a fallback
        return redirect(reverse('fees:invoice_detail', kwargs={'pk': pk}))

    # 2. --- Fetch Data Efficiently ---
    try:
        invoice = get_object_or_404(
            Invoice.objects.select_related(
                'student__current_class', 'academic_year', 'term', 'created_by'
            ).prefetch_related(
                Prefetch('details', queryset=InvoiceDetail.objects.select_related('fee_head', 'concession_type'))
            ), 
            pk=pk
        )
        # Fetch the single SchoolProfile for the current tenant
        school_profile = SchoolProfile.objects.first()
        if not school_profile:
            logger.warning(f"No SchoolProfile found for tenant {request.tenant.name} when generating invoice PDF {pk}.")
    
    except Invoice.DoesNotExist:
        # This case is handled by get_object_or_404, but an explicit catch is safe
        messages.error(request, _("The requested invoice could not be found."))
        return redirect(reverse('fees:invoice_list'))
    except Exception as e:
        logger.error(f"Error fetching data for invoice PDF {pk}: {e}", exc_info=True)
        messages.error(request, _("An error occurred while retrieving invoice details."))
        return redirect(reverse('fees:invoice_list'))
        
    # 3. --- Prepare Context for Template ---
    # Your calculation logic is good. Let's ensure it's robust.
    charge_items = [item for item in invoice.details.all() if item.line_type == LineTypeChoices.FEE_ITEM]
    concession_lines = [item for item in invoice.details.all() if item.line_type == LineTypeChoices.CONCESSION]

    subtotal = sum(item.amount for item in charge_items) or Decimal('0.00')
    total_concessions = sum(abs(item.amount) for item in concession_lines) or Decimal('0.00')
    
    context = {
        'invoice': invoice,
        'school_profile': school_profile,
        'charge_items': charge_items,
        'concession_lines': concession_lines,
        'tenant': request.tenant,
        # Pass calculated totals to the template
        'display_subtotal': subtotal,
        'display_total_concessions': total_concessions,
        'display_net_billable': invoice.total_amount, # Use the model's value
        'display_amount_paid': invoice.amount_paid,
        'display_balance_due': invoice.balance_due,
    }

    # 4. --- Render PDF ---
    pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)

    if pdf:
        response = HttpResponse(pdf, content_type='application/pdf')
        filename = f"Invoice-{invoice.invoice_number_display or f'Draft-{invoice.pk}'}.pdf"
        
        # Your logic for download/print is great
        if request.GET.get('download'):
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
        else:
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            
        return response

    logger.error(f"PDF generation failed for invoice {invoice.pk}. render_to_pdf returned None.")
    messages.error(request, _("Could not generate the PDF document at this time."))
    return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))

# # --- PDF View (Direct PDF generation) ---
# def invoice_pdf_view(request, pk): # pk is invoice_pk
#     # Ensure user is authenticated and has permission
#     if not (request.user.is_authenticated and hasattr(request.user, 'has_perm') and request.user.has_perm('fees.view_invoice')):
#         # Handle permission denied - redirect or 403
#         # This is basic; your mixins handle it better for CBVs
#         messages.error(request, "You do not have permission to view this invoice.")
#         return redirect(reverse_lazy('schools:dashboard')) # Or appropriate redirect

#     invoice = get_object_or_404(
#         Invoice.objects.select_related(
#             'student', 'student__current_class', 'student__current_section', 
#             'academic_year', 'term', 'created_by'
#         ).prefetch_related(
#             Prefetch('details', queryset=InvoiceDetail.objects.select_related('fee_head', 'concession_type').order_by('id'))
#         ), 
#         pk=pk
#     )
#     # SchoolProfile should be fetched from current tenant schema
#     school_profile = None
#     if hasattr(request, 'tenant') and request.tenant:
#         try:
#             # SchoolProfile is now tenant-scoped, exists in tenant schemas
#             school_profile = SchoolProfile.objects.first()
#             logger.info(f"Retrieved SchoolProfile for tenant {request.tenant.name} when generating invoice PDF for INV {invoice.pk}")
#         except Exception as e:
#             logger.warning(f"Error fetching SchoolProfile for tenant {request.tenant.name}: {e}")


#     # Calculate display amounts for the PDF template
#     charge_items = invoice.details.filter(line_type=LineTypeChoices.FEE_ITEM)
#     concession_lines = invoice.details.filter(line_type=LineTypeChoices.CONCESSION)

#     # Calculate totals
#     subtotal = sum(item.amount for item in charge_items)
#     total_concessions = sum(abs(item.amount) for item in concession_lines)
#     net_billable = subtotal - total_concessions
#     amount_paid = invoice.amount_paid or 0
#     balance_due = net_billable - amount_paid

#     context = {
#         'invoice': invoice,
#         'school_profile': school_profile,
#         'charge_items': charge_items,
#         'concession_lines': concession_lines,
#         'current_datetime': timezone.now(), # For "Generated on"
#         'request': request, # For full URL in PDF if needed
#         'tenant': request.tenant if hasattr(request, 'tenant') else None, # For template compatibility
#         # Add display variables for the template
#         'display_subtotal': subtotal,
#         'display_total_concessions': total_concessions,
#         'display_net_billable': net_billable,
#         'display_amount_paid': amount_paid,
#         'display_balance_due': balance_due,
#     }

#     # Check if PDF generation is available
#     if not PDF_AVAILABLE:
#         logger.error("PDF generation library (xhtml2pdf) is not available")
#         messages.error(request, _("PDF generation is not available on this system."))
#         return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))

#     pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)

#     if pdf:
#         response = HttpResponse(pdf, content_type='application/pdf')
#         filename = f"Invoice-{invoice.invoice_number_display or f'Draft-{invoice.pk}'}.pdf"

#         # Handle different PDF options based on query parameters
#         if request.GET.get('download'):
#             # Force download
#             response['Content-Disposition'] = f'attachment; filename="{filename}"'
#         elif request.GET.get('print'):
#             # Inline with print-friendly headers
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             # Add JavaScript to trigger print dialog
#             response['X-Print-PDF'] = 'true'
#         else:
#             # Default: inline for viewing
#             response['Content-Disposition'] = f'inline; filename="{filename}"'

#         return response

#     logger.error(f"PDF generation failed for invoice {invoice.pk}")
#     messages.error(request, _("Could not generate PDF at this time."))
#     return redirect(reverse('fees:invoice_detail', kwargs={'pk': invoice.pk}))


@require_http_methods(["POST"])
@login_required
def email_invoice_pdf(request, pk):
    """Email invoice PDF to parent/student"""
    from django.http import JsonResponse
    from django.core.mail import EmailMessage
    from django.template.loader import render_to_string
    from django.conf import settings
    import io

    try:
        # Get the invoice
        invoice = get_object_or_404(
            Invoice.objects.select_related(
                'student', 'academic_year', 'term', 'created_by',
                'student__current_class', 'student__current_section'
            ).prefetch_related(
                Prefetch('details', queryset=InvoiceDetail.objects.select_related('fee_head', 'concession_type')),
            ),
            pk=pk
        )

        # Check permissions
        if not request.user.has_perm('fees.view_invoice'):
            return JsonResponse({'success': False, 'message': 'Permission denied.'})

        # Get student's parent email
        parent_emails = []
        if hasattr(invoice.student, 'parents') and invoice.student.parents.exists():
            for parent in invoice.student.parents.all():
                if hasattr(parent, 'email') and parent.email:
                    parent_emails.append(parent.email)

        # Fallback to student email if available
        if not parent_emails and hasattr(invoice.student, 'email') and invoice.student.email:
            parent_emails.append(invoice.student.email)

        if not parent_emails:
            return JsonResponse({
                'success': False,
                'message': 'No email address found for this student or their parents.'
            })

        # Generate PDF
        school_profile = None
        if hasattr(request, 'tenant'):
            from apps.schools.models import SchoolProfile
            # SchoolProfile is now tenant-scoped, no need to filter by school
            school_profile = SchoolProfile.objects.first()

        # Calculate display amounts for the PDF template
        charge_items = invoice.details.filter(line_type=LineTypeChoices.FEE_ITEM)
        concession_lines = invoice.details.filter(line_type=LineTypeChoices.CONCESSION)

        # Calculate totals
        subtotal = sum(item.amount for item in charge_items)
        total_concessions = sum(abs(item.amount) for item in concession_lines)
        net_billable = subtotal - total_concessions
        amount_paid = invoice.amount_paid or 0
        balance_due = net_billable - amount_paid

        context = {
            'invoice': invoice,
            'school_profile': school_profile,
            'charge_items': charge_items,
            'concession_lines': concession_lines,
            'current_datetime': timezone.now(),
            'request': request,
            'tenant': request.tenant if hasattr(request, 'tenant') else None,
            # Add display variables for the template
            'display_subtotal': subtotal,
            'display_total_concessions': total_concessions,
            'display_net_billable': net_billable,
            'display_amount_paid': amount_paid,
            'display_balance_due': balance_due,
        }

        if not PDF_AVAILABLE:
            return JsonResponse({
                'success': False,
                'message': 'PDF generation is not available on this system.'
            })

        pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)
        if not pdf:
            return JsonResponse({
                'success': False,
                'message': 'Failed to generate PDF. Please try again.'
            })

        # Prepare email
        school_name = school_profile.school_name_on_reports if school_profile and school_profile.school_name_on_reports else (
            request.tenant.name if hasattr(request, 'tenant') else 'School'
        )

        subject = f"Invoice #{invoice.invoice_number_display} - {school_name}"

        # Email body
        email_context = {
            'invoice': invoice,
            'school_name': school_name,
            'student_name': invoice.student.full_name,
        }

        message = render_to_string('fees/emails/invoice_pdf_email.txt', email_context)

        # Create email
        email = EmailMessage(
            subject=subject,
            body=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=parent_emails,
        )

        # Attach PDF
        filename = f"Invoice-{invoice.invoice_number_display or f'Draft-{invoice.pk}'}.pdf"
        email.attach(filename, pdf, 'application/pdf')

        # Send email
        email.send()

        return JsonResponse({
            'success': True,
            'message': f'Invoice PDF sent successfully to {", ".join(parent_emails)}'
        })

    except Exception as e:
        logger.error(f"Error emailing invoice PDF {pk}: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while sending the email. Please try again.'
        })


@require_http_methods(["POST"])
@login_required
def send_invoice_reminder(request, pk):
    """Send payment reminder email to parent/student"""
    from django.http import JsonResponse
    from django.core.mail import EmailMessage
    from django.template.loader import render_to_string
    from django.conf import settings

    try:
        invoice = Invoice.objects.select_related(
            'student', 'academic_year', 'term'
        ).get(pk=pk)

        # Check permissions
        if not request.user.has_perm('fees.view_invoice'):
            return JsonResponse({'success': False, 'message': 'Permission denied.'})

        # Check if invoice is eligible for reminders
        if invoice.status == InvoiceStatus.DRAFT:
            return JsonResponse({
                'success': False,
                'message': 'Cannot send reminder for draft invoices. Please issue the invoice first.'
            })

        if invoice.status == InvoiceStatus.PAID or invoice.balance_due <= 0:
            return JsonResponse({
                'success': False,
                'message': 'This invoice is already paid in full.'
            })

        # Get student's parent email
        parent_emails = []
        if hasattr(invoice.student, 'parents') and invoice.student.parents.exists():
            for parent in invoice.student.parents.all():
                if parent.email:
                    parent_emails.append(parent.email)

        # Fallback to student email if available
        if not parent_emails and hasattr(invoice.student, 'email') and invoice.student.email:
            parent_emails.append(invoice.student.email)

        if not parent_emails:
            return JsonResponse({
                'success': False,
                'message': 'No email address found for this student or their parents.'
            })

        # Get school profile for email details
        school_profile = None
        try:
            school_profile = SchoolProfile.objects.first()
        except:
            pass

        school_name = school_profile.school_name_on_reports if school_profile and school_profile.school_name_on_reports else (
            request.tenant.name if hasattr(request, 'tenant') else 'School'
        )

        subject = f"Payment Reminder - Invoice #{invoice.invoice_number_display} - {school_name}"

        # Email body for reminder
        email_context = {
            'invoice': invoice,
            'school_name': school_name,
            'student_name': invoice.student.full_name,
            'balance_due': invoice.balance_due,
            'due_date': invoice.due_date,
            'is_overdue': invoice.is_overdue,
        }

        html_body = render_to_string('fees/emails/payment_reminder_email.html', email_context)
        text_body = render_to_string('fees/emails/payment_reminder_email.txt', email_context)

        # Create and send email
        email = EmailMessage(
            subject=subject,
            body=text_body,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=parent_emails,
        )
        email.attach_alternative(html_body, "text/html")
        email.send()

        return JsonResponse({
            'success': True,
            'message': f'Payment reminder sent successfully to {", ".join(parent_emails)}'
        })

    except Exception as e:
        logger.error(f"Error sending payment reminder for invoice {pk}: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while sending the reminder. Please try again.'
        })





# apps/fees/views.py
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.template.loader import get_template
from django.views import View # Or DetailView if inheriting
# from xhtml2pdf import pisa # Assuming you have this utility function elsewhere
from apps.common.utils import render_to_pdf # Using your utility
from .models import Invoice
from apps.schools.models import SchoolProfile # For logo and address

# Assuming StaffLoginRequiredMixin and TenantPermissionRequiredMixin are appropriate
from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin
from apps.common.utils import PDF_AVAILABLE, render_to_pdf


class GenerateInvoicePDFView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, View): # Or inherit from DetailView if pk is in URL
    permission_required = 'fees.view_invoice' # Or a specific PDF permission

    def dispatch(self, request, *args, **kwargs):
        logger.info(f"GenerateInvoicePDFView: Dispatch CALLED. User: {request.user}, Auth: {request.user.is_authenticated}")
        return super().dispatch(request, *args, **kwargs)
    
    
    def get(self, request, *args, **kwargs):
        logger.info(f"GenerateInvoicePDFView: GET method CALLED. User: {request.user}")
        invoice_pk = kwargs.get('pk') # Assuming PK is passed in URL
        try:
            invoice = Invoice.objects.select_related(
                'student', 'academic_year', 'term', 
                'student__current_class', 'student__current_section'
            ).prefetch_related(
                'details', 
                'details__fee_head', 
                'details__concession_type'
            ).get(pk=invoice_pk) # Ensure invoice belongs to current tenant's student
        except Invoice.DoesNotExist:
            return HttpResponse("Invoice not found or access denied.", status=404)


        # Get SchoolProfile from current tenant schema
        school_profile = None
        try:
            # SchoolProfile is now tenant-scoped (exists in tenant schemas)
            # No need to filter by school since each tenant schema has its own SchoolProfile
            school_profile = SchoolProfile.objects.first()
            logger.info(f"Retrieved SchoolProfile for tenant {request.tenant.name if hasattr(request, 'tenant') else 'unknown'}")
        except Exception as e:
            logger.error(f"Error fetching SchoolProfile: {e}")
            school_profile = None
        
        
        # # Get the school profile for the current tenant
        # school_profile = SchoolProfile.objects.filter(school_id=request.tenant.id).first()
        # # OR if SchoolProfile has a OneToOne directly to School(Tenant):
        # # school_profile = getattr(request.tenant, 'schoolprofile', None) 

        # if not school_profile:
        #     # Handle case where school profile is missing, maybe use tenant name as fallback
        #     # For PDF, it's better to have it. You could log a warning.
        #     logger.warning(f"SchoolProfile not found for tenant {request.tenant.name} when generating PDF for invoice {invoice.invoice_number}")


        context = {
            'invoice': invoice,
            'school_profile': school_profile,
            'tenant': request.tenant, # Pass tenant for currency or other details
            'charge_items': invoice.details.filter(line_type=LineTypeChoices.FEE_ITEM),
            'concession_lines': invoice.details.filter(line_type=LineTypeChoices.CONCESSION),
            # You already calculate these in the model or InvoiceDetailView, reuse that logic or pass them
            'display_subtotal': invoice.subtotal_amount,
            'display_total_concessions': invoice.total_concession_amount,
            'display_net_billable': invoice.net_billable_amount,
            'display_amount_paid': invoice.amount_paid,
            'display_balance_due': invoice.balance_due,
        }
        
        # Check if PDF generation is available
        if not PDF_AVAILABLE:
            return HttpResponse("PDF generation is not available on this system.", status=500)

        # Generate PDF using the professional template
        pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)

        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"Invoice-{invoice.invoice_number or invoice.pk}.pdf"
            # To force download:
            # response['Content-Disposition'] = f'attachment; filename="{filename}"'
            # To display in browser:
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        
        return HttpResponse("Error generating PDF.", status=500)
    
    
    


from django.urls import reverse_lazy
from django.views.generic.edit import CreateView
from django.contrib.auth.mixins import LoginRequiredMixin # For security

from .models import Invoice, InvoiceStatus
from .forms import ManualInvoiceForm

# ... any other views you have ...


class InvoiceCreateView(LoginRequiredMixin, CreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'fees/invoice_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create Invoice"
        context['form_mode'] = "create"

        if self.request.POST:
            context['item_formset'] = InvoiceDetailFormSet(self.request.POST, prefix='details')
        else:
            # Pre-fill student if passed in GET param
            student_pk = self.request.GET.get('student_pk')
            initial_data = {}
            if student_pk:
                try:
                    student = Student.objects.get(pk=student_pk)
                    initial_data['student'] = student
                except Student.DoesNotExist:
                    pass

            if initial_data:
                context['form'] = InvoiceForm(initial=initial_data)

            context['item_formset'] = InvoiceDetailFormSet(prefix='details')

        return context

    def form_valid(self, form):
        context = self.get_context_data()
        item_formset = context['item_formset']

        with transaction.atomic():
            # Set basic fields
            form.instance.created_by = self.request.user
            form.instance.status = InvoiceStatus.DRAFT

            # Save invoice first
            self.object = form.save()

            if item_formset.is_valid():
                item_formset.instance = self.object
                item_formset.save()

                # Update totals
                self.object.recalculate_totals_from_details()

                # Check if this is a preview action
                action = self.request.POST.get('action', 'save')
                if action == 'preview':
                    messages.success(self.request, _("Invoice saved successfully. Showing preview below."))
                else:
                    messages.success(self.request, _("Invoice created successfully as Draft."))

                return redirect('fees:invoice_detail', pk=self.object.pk)
            else:
                # COMPREHENSIVE ERROR LOGGING FOR DEBUGGING
                logger.error("=" * 80)
                logger.error("INVOICE FORMSET VALIDATION FAILED")
                logger.error("=" * 80)
                logger.error(f"Formset type: {type(item_formset)}")
                logger.error(f"Formset is_bound: {item_formset.is_bound}")
                logger.error(f"Management form valid: {item_formset.management_form.is_valid()}")

                if item_formset.management_form.errors:
                    logger.error(f"Management form errors: {item_formset.management_form.errors}")

                logger.error(f"Total forms: {item_formset.total_form_count()}")
                logger.error(f"Initial forms: {item_formset.initial_form_count()}")

                # Log formset-level errors
                if item_formset.non_form_errors():
                    logger.error(f"Non-form errors: {item_formset.non_form_errors()}")

                # Log each form's errors and data
                for i, form_item in enumerate(item_formset.forms):
                    logger.error(f"\n--- FORM {i} ---")
                    logger.error(f"Form {i} is_bound: {form_item.is_bound}")
                    logger.error(f"Form {i} is_valid: {form_item.is_valid()}")

                    if form_item.errors:
                        logger.error(f"Form {i} errors: {form_item.errors}")

                    # Log form data
                    if hasattr(form_item, 'data') and form_item.data:
                        form_data = {}
                        for field_name in form_item.fields:
                            field_key = f"{form_item.prefix}-{field_name}"
                            if field_key in form_item.data:
                                form_data[field_name] = form_item.data[field_key]
                        logger.error(f"Form {i} data: {form_data}")

                    # Log cleaned data if available
                    try:
                        if hasattr(form_item, 'cleaned_data'):
                            logger.error(f"Form {i} cleaned_data: {form_item.cleaned_data}")
                    except:
                        logger.error(f"Form {i} cleaned_data: Not available")

                logger.error("=" * 80)

                # Collect specific error messages to show to user
                error_messages = []
                for i, form_item in enumerate(item_formset.forms):
                    if form_item.errors:
                        for field_name, errors in form_item.errors.items():
                            for error in errors:
                                error_messages.append(f"Line {i+1} - {field_name}: {error}")

                if error_messages:
                    # Show specific errors to help user
                    error_summary = "Please correct the following errors in line items: " + "; ".join(error_messages[:3])
                    if len(error_messages) > 3:
                        error_summary += f" (and {len(error_messages) - 3} more errors)"
                    form.add_error(None, error_summary)
                else:
                    form.add_error(None, _("Please correct errors in the invoice line items."))

        return self.form_invalid(form)


@csrf_exempt
def ajax_get_terms_for_year(request):
    """
    AJAX endpoint to get terms for a specific academic year.
    Used by the fee structure form to dynamically populate term dropdown.
    """
    # Skip authentication check for now to match working state

    academic_year_id = request.GET.get('academic_year_id')

    if not academic_year_id:
        return JsonResponse({'terms': []})

    try:
        from apps.schools.models import Term
        terms = Term.objects.filter(
            academic_year_id=academic_year_id
        ).order_by('start_date').values('id', 'name')

        return JsonResponse({'terms': list(terms)})
    except Exception as e:
        logger.error(f"Error fetching terms for academic year {academic_year_id}: {e}")
        return JsonResponse({'terms': []}, status=500)

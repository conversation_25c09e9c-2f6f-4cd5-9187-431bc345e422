#!/usr/bin/env python
"""
Fix migration dependency issue between payments.0003_initial and fees.0003_initial
"""
import os
import sys
import django
from django.db import connection
from django_tenants.utils import get_tenant_model, get_public_schema_name

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def fix_migration_order():
    """Fix the migration order issue"""
    print("🔧 Fixing migration dependency issue...")
    
    TenantModel = get_tenant_model()
    tenants = TenantModel.objects.exclude(schema_name=get_public_schema_name())
    
    for tenant in tenants:
        print(f"\n--- Processing tenant: {tenant.schema_name} ---")
        
        # Switch to tenant schema
        connection.set_tenant(tenant)
        
        with connection.cursor() as cursor:
            # Check current migration state
            cursor.execute("""
                SELECT app, name, applied
                FROM django_migrations
                WHERE (app = 'fees' AND name = '0003_initial') 
                   OR (app = 'payments' AND name = '0003_initial')
                ORDER BY applied;
            """)
            results = cursor.fetchall()
            
            if len(results) < 2:
                print(f"  ⚠️  Not all migrations found for {tenant.schema_name}")
                continue
                
            print(f"  Current migration order:")
            for row in results:
                print(f"    {row[0]}.{row[1]} - {row[2]}")
            
            # Check if payments.0003_initial comes before fees.0003_initial
            migration_order = [(row[0], row[1], row[2]) for row in results]
            
            payments_idx = None
            fees_idx = None
            
            for i, (app, name, applied) in enumerate(migration_order):
                if app == 'payments' and name == '0003_initial':
                    payments_idx = i
                elif app == 'fees' and name == '0003_initial':
                    fees_idx = i
            
            if payments_idx is not None and fees_idx is not None:
                if payments_idx < fees_idx:
                    print(f"  ❌ Issue found: payments.0003_initial applied before fees.0003_initial")
                    
                    # Get the payments timestamp
                    payments_timestamp = migration_order[payments_idx][2]
                    
                    # Update fees.0003_initial to have an earlier timestamp
                    cursor.execute("""
                        UPDATE django_migrations 
                        SET applied = %s - INTERVAL '1 minute'
                        WHERE app = 'fees' AND name = '0003_initial'
                    """, [payments_timestamp])
                    
                    print(f"  ✅ Fixed: Updated fees.0003_initial timestamp to be earlier")
                    
                    # Verify the fix
                    cursor.execute("""
                        SELECT app, name, applied
                        FROM django_migrations
                        WHERE (app = 'fees' AND name = '0003_initial') 
                           OR (app = 'payments' AND name = '0003_initial')
                        ORDER BY applied;
                    """)
                    results = cursor.fetchall()
                    
                    print(f"  Updated migration order:")
                    for row in results:
                        print(f"    {row[0]}.{row[1]} - {row[2]}")
                else:
                    print(f"  ✅ Migration order is correct")
            else:
                print(f"  ⚠️  Could not find both migrations")

if __name__ == "__main__":
    try:
        fix_migration_order()
        print("\n🎉 Migration dependency fix completed!")
        print("\nNow you can run: python manage.py migrate_schemas")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

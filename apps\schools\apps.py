# apps/schools/apps.py
from django.apps import AppConfig

class SchoolsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.schools'

    # def ready(self):
    #     import apps.schools.signals # If you put receivers in signals.py
    #     # Or if receivers are in models.py:
    #     # from . import models # This implicitly connects @receiver decorated functions
        
        
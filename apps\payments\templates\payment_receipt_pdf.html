{# D:\school_fees_saas_v2\apps\payments\templates\payments\pdf\payment_receipt_pdf.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize %}

{% block pdf_title %}Payment Receipt #{{ payment.id }} - {{ school_profile.school_name_on_reports|default:tenant.name }}{% endblock %}

{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 55%; padding-right: 10mm; vertical-align: top;">
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 16mm; max-width: 45mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0 0 2mm 0; font-size: 14pt; color: #2c3e50; font-weight:bold;">
                    {{ school_profile.school_name_on_reports|default:tenant.name }}
                </h2>
                <div style="font-size: 8pt; color: #444; line-height: 1.25;">
                    {% if school_profile.address_line_1 %}{{ school_profile.address_line_1 }}<br>{% endif %}
                    {% if school_profile.city %}{{ school_profile.city }}, {% endif %}{% if school_profile.state_province %}{{ school_profile.state_province }}{% endif %} {% if school_profile.postal_code %}{{ school_profile.postal_code }}{% endif %}
                </div>
            </td>
            <td style="width: 45%; text-align: right; vertical-align: top;">
                <h1 style="margin:0 0 8px 0; font-size:22pt; color: #28a745; text-transform: uppercase;">PAYMENT RECEIPT</h1>
                <div style="font-size: 9pt; text-align: left; display: inline-block;">
                    <p style="margin:0.5mm 0;"><strong>Receipt No:</strong> P-{{ payment.id }}</p>
                    <p style="margin:0.5mm 0;"><strong>Payment Date:</strong> {{ payment.payment_date|date:"F d, Y" }}</p>
                    {% if payment.reference_number %}<p style="margin:0.5mm 0;"><strong>Reference:</strong> {{ payment.reference_number }}</p>{% endif %}
                </div>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 1.5mm; margin-bottom: 1.5mm; border: none; border-top: 0.5px solid #333;">
{% endblock pdf_header_content %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .receipt-summary-box { padding: 3mm; font-size: 10pt; line-height: 1.4; border: 1px solid #ccc; background-color: #f8f9fa; margin-bottom: 7mm; }
        .items-table { table-layout: fixed; width: 100%; }
        .items-table thead th { background-color: #6c757d; color: white; font-size: 9pt; font-weight: bold; padding: 2mm 3mm; text-align: left; }
        .items-table tbody td { padding: 2.5mm 3mm; border-bottom: 0.5px solid #e9ecef; font-size: 9pt; }
        .thank-you-section { margin-top: 10mm; text-align: center; font-size: 10pt; color: #495057; }
    </style>
{% endblock %}

{% block pdf_main_content %}
    <div class="receipt-summary-box">
        <table style="width: 100%;">
            <tr>
                <td style="width: 50%; vertical-align: top;">
                    <strong>Received From:</strong><br>
                    {% if payment.parent_payer %}
                        {{ payment.parent_payer.get_full_name }} (Parent)<br>
                    {% endif %}
                    {{ payment.student.get_full_name }}<br>
                    Adm. No: {{ payment.student.admission_number|default:"N/A" }}
                </td>
                <td style="width: 50%; text-align: right; vertical-align: top;">
                    <strong>Amount Received:</strong>
                    <h2 style="margin: 2mm 0; color: #1e7e34; font-size: 18pt;">
                        {{ school_profile.currency_symbol|default:'$' }}{{ payment.amount|floatformat:2|intcomma }}
                    </h2>
                    <strong>Payment Method:</strong> {{ payment.payment_method.name|default:"N/A" }}
                </td>
            </tr>
        </table>
    </div>

    <h3 class="section-title">Payment Allocation Details</h3>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Invoice Number</th>
                <th class="text-right" style="width: 50%;">Amount Allocated</th>
            </tr>
        </thead>
        <tbody>
            {% for allocation in allocations %}
            <tr>
                <td>{{ allocation.invoice.invoice_number_display|default:"N/A" }} (Issued: {{ allocation.invoice.issue_date|date:"d M, Y" }})</td>
                <td class="text-right">{{ school_profile.currency_symbol|default:'$' }}{{ allocation.allocated_amount|floatformat:2|intcomma }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="2" class="text-center text-muted">This payment has not yet been allocated to any invoices.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    {% if payment.notes %}
    <div style="margin-top: 7mm; padding: 3mm; background-color: #f0f0f0; font-size: 8pt; border-left: 2px solid #ccc;">
        <strong>Notes:</strong><br>
        {{ payment.notes|linebreaksbr }}
    </div>
    {% endif %}

    <div class="thank-you-section">
        <p>Thank you for your prompt payment!</p>
    </div>
{% endblock pdf_main_content %}

{% block pdf_footer_content %}
    <p>{{ school_profile.school_name_on_reports|default:tenant.name }} - Generated: {% now "F d, Y H:i" %}</p>
    <p class="page-number"></p>
{% endblock pdf_footer_content %}




# D:\school_fees_saas_V2\apps\portal_admin\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, FormView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.contrib.auth.models import Group, Permission

from django.contrib.contenttypes.models import ContentType # <<< --- ADD THIS IMPORT ---

# from django.contrib.contenttypes.models import ContentType # Not directly used here after consolidation
from django.db.models import Count, Q
from django.apps import apps as django_apps
from datetime import timedelta # For ActivityLogListView date filtering
from collections import defaultdict
from django.conf import settings
import logging
from .forms import TenantGroupForm, GroupPermissionAssignmentForm

logger = logging.getLogger(__name__)

# App-specific imports
from .models import AdminActivityLog # For ACTION_ constants and ActivityLogListView
from .forms import (
    TenantGroupForm,
    GroupPermissionAssignmentForm,
    ActivityLogFilterForm # Assuming this form is defined in forms.py
)
from .utils import create_admin_log_entry # Your refined audit log utility

from django.contrib.auth.models import Group

import logging
logger = logging.getLogger(__name__) # Standard logger for the current module


# --- Group (Role) CRUD ---

class TenantGroupListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'auth.view_group'
    model = Group
    template_name = 'portal_admin/group_list.html'
    context_object_name = 'groups'

    def get_queryset(self):
        try:
            # Use 'staffuser' which is the related_query_name from StaffUser.groups
            queryset = Group.objects.annotate(
                user_count=Count('staffuser')
            ).prefetch_related('staffuser_set').order_by('name')
            return queryset
        except Exception as e:
            # Log this error properly in a real scenario
            print(f"--- TenantGroupListView: ERROR during annotation: {e} ---")
            return Group.objects.all().order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Roles (Groups)"

        # Add staff members to each group for display in template
        groups_with_staff = []
        for group in context['groups']:
            group.staff_members = group.staffuser_set.all()[:5]  # Limit to first 5 for display
            groups_with_staff.append(group)
        context['groups'] = groups_with_staff

        return context
    
    
    
class TenantGroupDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    model = Group # We are displaying details of a Django Group
    template_name = 'portal_admin/group_detail.html' # You'll need to create this template
    context_object_name = 'group_obj' # Name to use in the template for the group instance
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'auth.view_group' # Only users who can view groups can see details

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Role Details: {self.object.name}"
        # You can add more context here, e.g., list of users in this group
        # context['users_in_group'] = self.object.user_set.all() # Default related_name
        # For StaffUser, it would be:
        # context['staff_in_group'] = self.object.staff_users_in_role.select_related('hr_profile').order_by('last_name', 'first_name')
        
        try:
            # self.object is the Group instance
            # Use 'staffuser_set' which is the correct related_name from StaffUser.groups
            staff_users_queryset = self.object.staffuser_set.all() # Get all StaffUser instances in the group

            # If 'hr_profile' is the related_name from EmployeeProfile.user (OneToOneField to StaffUser)
            # use prefetch_related.
            # This assumes your EmployeeProfile model has:
            #   user = models.OneToOneField(StaffUser, ..., related_name='hr_profile')
            context['staff_in_group'] = staff_users_queryset.prefetch_related('hr_profile').order_by('last_name', 'first_name')

            # If 'hr_profile' was meant to be a direct field on StaffUser that links TO EmployeeProfile,
            # and that field is actually named differently (e.g., 'employee_profile_link'), then
            # select_related('employee_profile_link') would be used.
            # But the error suggests 'hr_profile' is not a direct forward link.

        except Exception as e: # Catching a broader exception for safety during debugging
            logger.error(f"Error fetching or prefetching staff_in_group with hr_profile: {e}", exc_info=True)
            # Fallback if prefetching fails or if 'hr_profile' isn't set up as expected
            context['staff_in_group'] = self.object.staffuser_set.order_by('last_name', 'first_name')
        # For ParentUser, it would be (using the related_name you set on ParentUser.groups):
        # context['parents_in_group'] = self.object.parentuser_set_groups.all()
        return context
    
    
    

class TenantGroupCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'auth.add_group'
    model = Group
    form_class = TenantGroupForm
    template_name = 'portal_admin/group_form.html'
    success_url = reverse_lazy('portal_admin:group_list')
    success_message = "Role/Group '%(name)s' created successfully."

    def form_valid(self, form):
        response = super().form_valid(form)
        create_admin_log_entry(
            request=self.request,
            action_type=AdminActivityLog.ACTION_CREATE, # Use constant from AdminActivityLog model
            target_object=self.object,
            description=f"Role/Group '{self.object.name}' created."
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Role/Group"
        return context

class TenantGroupUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'auth.change_group'
    model = Group
    form_class = TenantGroupForm
    template_name = 'portal_admin/group_form.html'
    success_url = reverse_lazy('portal_admin:group_list')
    success_message = "Role/Group '%(name)s' updated successfully."
    context_object_name = 'group_obj'

    def form_valid(self, form):
        original_name = self.object.name # Get name before potential change
        response = super().form_valid(form)
        
        changed_fields_desc = []
        if form.changed_data:
            for field_name in form.changed_data:
                changed_fields_desc.append(f"{field_name}")
        
        details_msg = f"Role/Group '{self.object.name}' (originally '{original_name}') updated."
        if changed_fields_desc:
            details_msg += f" Changed fields: {', '.join(changed_fields_desc)}."

        create_admin_log_entry(
            request=self.request,
            action_type=AdminActivityLog.ACTION_UPDATE, # Use constant
            target_object=self.object,
            description=details_msg
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Update Role/Group: {self.object.name}"
        return context

class TenantGroupDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView): # Removed SuccessMessageMixin for manual handling
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'auth.delete_group'
    model = Group
    template_name = 'portal_admin/group_confirm_delete.html'
    success_url = reverse_lazy('portal_admin:group_list')
    context_object_name = 'group_obj'

    def form_valid(self, form):
        group_to_delete_name = self.object.name
        group_pk_for_log = self.object.pk

        if self.object.staffuser_groups.exists() or self.object.user_set.exists():
            messages.error(self.request, f"Role/Group '{group_to_delete_name}' cannot be deleted as it is assigned to users.")
            create_admin_log_entry(
                request=self.request,
                action_type=AdminActivityLog.ACTION_DELETE, # Consider a specific ACTION_DELETE_FAILED if defined
                target_object_description=f"Role/Group '{group_to_delete_name}' (ID: {group_pk_for_log})",
                description=f"Attempted to delete Role/Group '{group_to_delete_name}' but failed: Group is assigned to users."
            )
            return redirect(self.success_url)
        
        response = super().form_valid(form) # Object is deleted here
        
        create_admin_log_entry(
            request=self.request,
            action_type=AdminActivityLog.ACTION_DELETE, # Use constant
            target_object_description=f"Role/Group '{group_to_delete_name}' (ID: {group_pk_for_log})",
            description=f"Role/Group '{group_to_delete_name}' successfully deleted."
        )
        messages.success(self.request, f"Role/Group '{group_to_delete_name}' deleted successfully.")
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Confirm Delete Role/Group"
        return context



class AssignPermissionsToGroupView(LoginRequiredMixin, PermissionRequiredMixin, FormView):
    login_url = reverse_lazy('schools:staff_login')
    # Ensure user needs permission to change groups to access this
    permission_required = ('auth.change_group', 'auth.view_permission', 'auth.view_group') 
    form_class = GroupPermissionAssignmentForm
    template_name = 'portal_admin/assign_permissions_to_group_NEWTEST.html' # Using your NEWTEST name

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        self.group = get_object_or_404(Group, pk=self.kwargs['group_pk'])

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['group_instance'] = self.group # Pass group to form for initial values
        return kwargs
    
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs) # This will add 'form' to the context
        context['view_title'] = f"Assign Permissions to Role: {self.group.name}"
        context['group_obj'] = self.group # For display in the template (e.g., showing group name)

        # Define app labels for permissions relevant to tenant roles
        app_labels_for_tenant_perms = [
            'accounting', 'announcements', 'communication', 'fees', 'finance', 
            'hr', 'payments', 'portal_admin', 'reporting', 'schools', 'students',
            # Add any other custom tenant apps that have models with permissions
        ]
        # Define app labels to exclude (core Django, utility apps, public apps)
        excluded_app_labels = [
            'admin', 'auth', 'contenttypes', 'sessions', 'django_celery_beat', 
            'django_tenants', 'public_site', 'subscriptions', 'users', # Assuming 'users' is your public user app
        ]

        # Get ContentType objects for relevant apps, excluding specified ones
        relevant_content_types = ContentType.objects.filter(
            app_label__in=app_labels_for_tenant_perms
        ).exclude(
            app_label__in=excluded_app_labels
        )
        
        # Fetch all permissions for these content types, ordered for consistent grouping
        all_permissions_qs = Permission.objects.filter(
            content_type__in=relevant_content_types
        ).select_related('content_type').order_by(
            'content_type__app_label', 
            'content_type__model', 
            'name'
        )
        
        # Group permissions by "App Verbose Name | Model Verbose Name"
        local_grouped_permissions = defaultdict(list) 
        for perm in all_permissions_qs:
            try:
                app_config = django_apps.get_app_config(perm.content_type.app_label)
                app_label_verbose = app_config.verbose_name.title()
            except LookupError: 
                app_label_verbose = perm.content_type.app_label.title()

            model_class = perm.content_type.model_class()
            model_name_verbose = model_class._meta.verbose_name.title() if model_class \
                                else perm.content_type.model.replace("_", " ").title()
            
            group_key = f"{app_label_verbose} | {model_name_verbose}"
            
            local_grouped_permissions[group_key].append({
                'id': perm.id,
                'codename': perm.codename,
                'name': perm.name, # Already verbose, e.g., "Can add student"
                'app_label': perm.content_type.app_label,
                'model_name': perm.content_type.model 
            })
        
        # Add the sorted grouped permissions to the context for the template
        context['grouped_perms_display_data'] = dict(sorted(local_grouped_permissions.items()))
        
        # Add the set of currently assigned permission IDs for this group
        context['assigned_permission_ids'] = set(self.group.permissions.values_list('id', flat=True))

        # --- CONSOLE DEBUG OUTPUT ---
        print("\n--- AssignPermissionsToGroupView CONTEXT DEBUG (get_context_data) ---")
        print(f"Tenant: {self.request.tenant.name if self.request.tenant else 'N/A'}")
        print(f"Editing Group: {context['group_obj'].name} (PK: {context['group_obj'].pk})")
        print(f"Count of Assigned Permission IDs for this group: {len(context['assigned_permission_ids'])}")
        # print(f"Assigned Permission IDs: {context['assigned_permission_ids']}") # Can be very long
        print(f"Number of Display Groups (Cards): {len(context['grouped_perms_display_data'])}")
        
        form_instance = context.get('form')
        if form_instance and hasattr(form_instance, 'fields') and 'permissions' in form_instance.fields:
            print(f"Total Permission Choices in Form: {len(form_instance.fields['permissions'].choices)}")
            # The 'initial' attribute of the form field itself shows what will be pre-selected
            initial_perms_in_form = form_instance.fields['permissions'].initial
            print(f"Form Field 'permissions' Initial Data Count: {len(initial_perms_in_form) if initial_perms_in_form else 0}")
        else:
            logger.warning("Form or form.permissions field not found in context for AssignPermissionsToGroupView.")
        print("--- END AssignPermissionsToGroupView CONTEXT DEBUG ---\n")
        
        return context

    def form_valid(self, form):
        selected_permissions = form.cleaned_data['permissions']
        
        current_permission_pks = set(self.group.permissions.values_list('pk', flat=True))
        new_permission_pks = set(p.pk for p in selected_permissions)
        added_pks = new_permission_pks - current_permission_pks
        removed_pks = current_permission_pks - new_permission_pks

        self.group.permissions.set(selected_permissions)

        perm_details_parts = []
        if added_pks:
            added_perms_names = list(Permission.objects.filter(pk__in=added_pks).values_list('name', flat=True))
            perm_details_parts.append(f"Added: {', '.join(added_perms_names)}")
        if removed_pks:
            removed_perms_names = list(Permission.objects.filter(pk__in=removed_pks).values_list('name', flat=True))
            perm_details_parts.append(f"Removed: {', '.join(removed_perms_names)}")
        
        log_description = f"Permissions updated for Role/Group '{self.group.name}'."
        if perm_details_parts:
            log_description += " Changes: " + " | ".join(perm_details_parts) + "."
        else:
            log_description += " No changes were made to permissions."

        # Call the utility function correctly
        create_admin_log_entry(
            request=self.request,  # Pass the full request object
            action_type=AdminActivityLog.ACTION_PERMISSION_CHANGE, # Use class constant
            target_object=self.group,
            description=log_description
            # target_object_description can be omitted if str(self.group) is sufficient for your utility
        )
        
        messages.success(self.request, f"Permissions successfully updated for role '{self.group.name}'.")
        return redirect(self.get_success_url())

    def get_success_url(self):
        try:
            return reverse('portal_admin:group_detail', kwargs={'pk': self.group.pk}) 
        except NoReverseMatch: 
            logger.warning(f"NoReverseMatch for portal_admin:group_detail with pk={self.group.pk}. Falling back to group_list.")
            return reverse('portal_admin:group_list')

        

# --- Activity Log View ---
class ActivityLogListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = AdminActivityLog
    template_name = 'portal_admin/activity_log_list.html'
    context_object_name = 'logs'
    paginate_by = 25
    permission_required = 'portal_admin.view_adminactivitylog' # Ensure this permission exists
    login_url = reverse_lazy('schools:staff_login') # Explicitly set login_url
    # raise_exception = True # Consider using this or a custom 403 handler

    def get_queryset(self):
        queryset = super().get_queryset().select_related('staff_user', 'user', 'target_content_type') # Optimize
        self.filter_form = ActivityLogFilterForm(self.request.GET, request=self.request) # Pass request to form if needed

        if self.filter_form.is_valid():
            action_type = self.filter_form.cleaned_data.get('action_type')
            staff_user_instance = self.filter_form.cleaned_data.get('staff_user') # Form field should return instance or None
            # public_user_id = self.filter_form.cleaned_data.get('public_user') # If form has this field
            start_date = self.filter_form.cleaned_data.get('start_date')
            end_date = self.filter_form.cleaned_data.get('end_date')
            search_term = self.filter_form.cleaned_data.get('search_term')

            if action_type:
                queryset = queryset.filter(action_type=action_type)
            if staff_user_instance: # Filter by staff user instance
                queryset = queryset.filter(staff_user=staff_user_instance)
            if start_date:
                queryset = queryset.filter(timestamp__date__gte=start_date) # Use __date for date objects
            if end_date:
                queryset = queryset.filter(timestamp__date__lte=end_date) # Use __date for date objects
            if search_term:
                queryset = queryset.filter(
                    Q(description__icontains=search_term) |
                    Q(target_object_description__icontains=search_term) |
                    Q(ip_address__icontains=search_term) |
                    Q(staff_user__email__icontains=search_term) |
                    Q(staff_user__first_name__icontains=search_term) |
                    Q(staff_user__last_name__icontains=search_term) |
                    Q(user__username__icontains=search_term) # If logging public users
                )
        return queryset.order_by('-timestamp') # Ensure consistent ordering

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "System Activity Log"
        # Ensure filter_form is always in context, even if not initialized in get_queryset (though it should be)
        context['filter_form'] = getattr(self, 'filter_form', ActivityLogFilterForm(request=self.request))
        
        # Breadcrumb: Ensure 'portal_admin:dashboard' URL exists or remove this part
        # context['breadcrumb'] = [
        #     {"name": "Tenant Admin", "url": reverse_lazy('portal_admin:dashboard')},
        #     {"name": "Activity Log", "url": None},
        # ]
        
        query_params = self.request.GET.copy()
        if 'page' in query_params:
            del query_params['page']
        context['current_filters'] = query_params.urlencode()
        return context
    
    

# D:\school_fees_saas_v2\apps\portal_admin\views.py
import logging
from django.views.generic import FormView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import redirect, render # For the GET request handling if needed

from .forms import StaffRoleAssignmentForm
from apps.schools.models import StaffUser # For initial data if a staff member is selected

logger = logging.getLogger(__name__)

class AssignStaffRolesView(LoginRequiredMixin, PermissionRequiredMixin, FormView):
    template_name = "portal_admin/assign_staff_roles_form.html"
    form_class = StaffRoleAssignmentForm
    permission_required = 'portal_admin.manage_staff_roles' # From your Meta.permissions
    login_url = reverse_lazy('schools:staff_login')
    success_url = reverse_lazy('portal_admin:assign_staff_roles') # Redirect back to the same page

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant # Pass tenant if form needs it (not strictly now, but good practice)
        return kwargs

    def get_initial(self):
        """
        If a staff_member_id is passed in GET params, pre-select them.
        This is useful for a "load roles for this staff" feature.
        """
        initial = super().get_initial()
        staff_member_id = self.request.GET.get('staff_member_id')
        if staff_member_id:
            try:
                # No need to fetch the full object here, just pass the ID.
                # The form's __init__ will handle fetching and setting initial roles.
                initial['staff_member'] = int(staff_member_id)
            except (ValueError, TypeError):
                logger.warning(f"Invalid staff_member_id '{staff_member_id}' in GET params.")
            except StaffUser.DoesNotExist: # Should not happen if just passing ID
                logger.warning(f"StaffUser with id '{staff_member_id}' not found for initial form data.")
        return initial

    def form_valid(self, form):
        staff_member = form.save_assignments()
        if staff_member:
            messages.success(self.request, f"Role assignments updated successfully for {staff_member.get_full_name() or staff_member.email}.")
        else:
            messages.error(self.request, "Could not save role assignments. Staff member not found or invalid.")
        # Redirect to the same page, potentially with the staff member pre-selected
        redirect_url = self.get_success_url()
        if staff_member:
            redirect_url += f'?staff_member_id={staff_member.pk}' # Add param to reload with selection
        return redirect(redirect_url)

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Assign Staff to Roles"
        
        # If a staff member is selected in the form (either initially or after POST),
        # show their name for clarity.
        selected_staff_id_in_form = None
        if self.request.method == 'POST': # If form was submitted
            if 'staff_member' in self.request.POST and self.request.POST['staff_member']:
                selected_staff_id_in_form = self.request.POST['staff_member']
        elif 'staff_member' in context.get('form', {}).initial : # If form is loaded via GET with initial
            selected_staff_id_in_form = context['form'].initial['staff_member']
        
        if selected_staff_id_in_form:
            try:
                context['selected_staff_member_obj'] = StaffUser.objects.get(pk=selected_staff_id_in_form)
            except (StaffUser.DoesNotExist, ValueError, TypeError):
                context['selected_staff_member_obj'] = None
                
        return context
# Generated by Django 5.1.9 on 2025-07-04 21:18

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0003_alter_subscriptionplan_options_subscription_ended_at_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='subscriptionplan',
            options={'ordering': ['display_order', 'price_monthly'], 'verbose_name': 'Subscription Plan', 'verbose_name_plural': 'Subscription Plans'},
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='price_at_subscription',
        ),
        migrations.AlterField(
            model_name='subscription',
            name='cancel_at_period_end',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='cancelled_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='current_period_end',
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='current_period_start',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='ended_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='pg_customer_id',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='pg_subscription_id',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='subscriptions.subscriptionplan'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending Setup'), ('TRIALING', 'Trialing'), ('ACTIVE', 'Active'), ('PAST_DUE', 'Past Due'), ('CANCELLED', 'Cancelled'), ('ENDED', 'Ended')], db_index=True, default='PENDING', max_length=25),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='display_order',
            field=models.PositiveIntegerField(default=0, help_text='Order for display on pricing page (0=first).'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='features',
            field=models.ManyToManyField(blank=True, related_name='plans', to='subscriptions.feature'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='max_staff',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of active staff users allowed. Leave blank for unlimited.', null=True),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='max_students',
            field=models.PositiveIntegerField(default=50, help_text='The maximum number of active students allowed for this plan.'),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='price_annually',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Price per year if billed annually.', max_digits=10),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='trial_period_days',
            field=models.PositiveIntegerField(default=0, help_text='Number of trial days for this plan (0 for no trial).'),
        ),
    ]

{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\checkout_cancelled.html #}
{% extends "public_base.html" %}
{% load static i18n %}

{% block public_page_title %}{% trans "Subscription Cancelled" %}{% endblock %}

{% block content %}
<div class="container py-5 text-center">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <i class="bi bi-x-circle display-1 text-warning mb-3"></i>
            <h1 class="display-5">{% trans "Subscription Process Cancelled" %}</h1>
            {% if messages %}
                {% for message in messages %}
                    <p class="lead {% if message.tags %}alert alert-{{ message.tags }}{% endif %}">{{ message }}</p>
                {% endfor %}
            {% else %}
                <p class="lead">{% trans "You have cancelled the subscription process." %}</p>
            {% endif %}
            <p>{% trans "Your subscription has not been activated. You can choose a plan and try again at any time." %}</p>
            <a href="{% url 'subscriptions:pricing_page' %}" class="btn btn-primary btn-lg mt-3">{% trans "View Plans & Pricing" %}</a>
        </div>
    </div>
</div>
{% endblock %}



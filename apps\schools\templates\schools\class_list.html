{# D:\school_fees_saas_v2\apps\schools\templates\schools\class_list.html #}
{% extends "tenant_base.html" %}

{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Manage Classes" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title }}</h1>
        <a href="{% url 'schools:class_create' %}" class="btn btn-primary">Add New Class/Grade</a>
    </div>

    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    {% if classes %}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>Class/Grade Name</th>
                            <th>Description</th>
                            <th style="width: 220px;">Actions</th> {# Wider for more buttons #}
                        </tr>
                    </thead>
                    <tbody>
                        {% for class_obj in classes %}
                        <tr>
                            <td>{{ class_obj.name }}</td>
                            <td>{{ class_obj.description|truncatewords:15|default:"-" }}</td>
                            <td>
                                <a href="{% url 'schools:section_list' class_obj.pk %}" class="btn btn-info btn-sm" title="Manage Sections">Sections ({{ class_obj.sections.count }})</a>
                                <a href="{% url 'schools:class_update' class_obj.pk %}" class="btn btn-secondary btn-sm" title="Edit Class">Edit</a>
                                <form action="{% url 'schools:class_delete' class_obj.pk %}" method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete class \'{{ class_obj.name|escapejs }}\'? This might affect associated sections and students.');">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete Class">Del</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">No classes/grades have been added yet.</div>
    {% endif %}

    {# Pagination (reuse from other list views if needed) #}
    {% include "partials/_pagination.html" %}


    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock content %}



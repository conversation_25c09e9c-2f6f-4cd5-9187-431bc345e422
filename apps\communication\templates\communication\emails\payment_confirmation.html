{% load i18n humanize %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% blocktrans with school_name=school_name %}Payment Confirmation - {{ school_name }}{% endblocktrans %}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .header img { max-height: 80px; margin-bottom: 10px; }
        .header h1 { color: {{ school_primary_color|default:'#333' }}; margin:0; font-size: 24px;}
        .content p { margin-bottom: 1em; }
        .content strong { color: {{ school_primary_color|default:'#333' }}; }
        .payment-summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px; border: 1px solid #eee;}
        .payment-summary p { margin-bottom: 0.5em; }
        .button-container { text-align: center; margin-top: 25px; }
        .button { display: inline-block; padding: 12px 25px; background-color: {{ school_secondary_color|default:'#007bff' }}; color: white !important; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .footer { text-align: center; margin-top: 25px; font-size: 0.85em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {% if school_logo_url %}<img src="{{ school_logo_url }}" alt="{{ school_name }} Logo">{% endif %}
            <h1>{{ school_name }}</h1>
        </div>
        <div class="content">
            <p>{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}</p>
            
            <p>{% blocktrans with school_name=school_name %}Thank you for your payment. We have successfully received it. Please find the details below:{% endblocktrans %}</p>

            <div class="payment-summary">
                <h4>{% trans "Payment Summary" %}</h4>
                <p><strong>{% trans "Payment ID/Receipt No.:" %}</strong> {{ payment_details.id|default:"N/A" }}</p>
                <p><strong>{% trans "Payment Date:" %}</strong> {{ payment_details.date|date:"d M Y, P" }}</p>
                <p><strong>{% trans "Amount Paid:" %}</strong> {{ school_currency_symbol|default:'$' }}{{ payment_details.amount|floatformat:2|intcomma }}</p>
                <p><strong>{% trans "Payment Method:" %}</strong> {{ payment_details.method|default:"N/A" }}</p>
                <p><strong>{% trans "Status:" %}</strong> {{ payment_details.status|default:"Processed" }}</p>
                {% if payment_details.paid_for_student %}
                    <p><strong>{% trans "For Student:" %}</strong> {{ payment_details.paid_for_student }}</p>
                {% endif %}
                {% if payment_details.invoices_covered %}
                    <p><strong>{% trans "Applied to Invoice(s):" %}</strong> {{ payment_details.invoices_covered|join:", " }}</p>
                {% endif %}
            </div>
            
            {% if payment_portal_url %}
                <p>{% blocktrans %}You can view your updated balance and payment history in the parent portal:{% endblocktrans %}</p>
                <div class="button-container">
                    <a href="{{ payment_portal_url }}" class="button">{% trans "View Payment History" %}</a>
                </div>
            {% endif %}

            <p>{% blocktrans %}If you have any questions regarding this payment, please do not hesitate to contact the school office.{% endblocktrans %}</p>
            
            <p>{% trans "Sincerely," %}<br>
            {% blocktrans with school_name=school_name %}The {{ school_name }} Accounts Team{% endblocktrans %}</p>
        </div>
        <div class="footer">
            <p>{{ school_name }}<br>
            {% if school_address %}{{ school_address }}<br>{% endif %}
            {% if school_contact_info %}{{ school_contact_info }}{% endif %}</p>
            <p>© {% now "Y" %} {{ school_name }}.</p>
        </div>
    </div>
</body>
</html>


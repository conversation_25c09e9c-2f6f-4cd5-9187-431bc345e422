{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_confirm_reverse.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Reversal") }}{% endblock tenant_page_title %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">{{ view_title }}</h4>
                </div>
                <div class="card-body">
                    <p class="lead">
                        {% blocktrans with je_id=original_journal_entry.pk|stringformat:"05d" desc=original_journal_entry.description %}
                        Are you sure you want to reverse Journal Entry <strong>JE-{{ je_id }}</strong>: "<em>{{ desc }}</em>"?
                        {% endblocktrans %}
                    </p>
                    <p>
                        {% trans "This will create a new journal entry with opposite debit/credit amounts. The original entry will remain for audit purposes." %}
                    </p>
                    <hr>
                    <h5>{% trans "Original Entry Summary:" %}</h5>
                    <dl class="row">
                        <dt class="col-sm-4">{% trans "Date" %}:</dt><dd class="col-sm-8">{{ original_journal_entry.date|date:"d M Y" }}</dd>
                        <dt class="col-sm-4">{% trans "Total Debits" %}:</dt><dd class="col-sm-8">{{ school_profile.currency_symbol|default:"$" }}{{ original_journal_entry.total_debits|floatformat:2|intcomma }}</dd>
                        <dt class="col-sm-4">{% trans "Total Credits" %}:</dt><dd class="col-sm-8">{{ school_profile.currency_symbol|default:"$" }}{{ original_journal_entry.total_credits|floatformat:2|intcomma }}</dd>
                    </dl>
                    <hr>
                    
                    <form method="post" action="{% url 'accounting:journalentry_reverse_action' pk=original_journal_entry.pk %}">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.reversal_date.id_for_label }}" class="form-label">{{ form.reversal_date.label }}</label>
                            {{ form.reversal_date }}
                            {% for error in form.reversal_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.reversal_description.id_for_label }}" class="form-label">{{ form.reversal_description.label }}</label>
                            {{ form.reversal_description }}
                            <small class="form-text text-muted">{{ form.reversal_description.help_text }}</small>
                            {% for error in form.reversal_description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <a href="{% url 'accounting:journalentry_detail' pk=original_journal_entry.pk %}" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-arrow-repeat me-1"></i> {% trans "Yes, Create Reversing Entry" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}




# apps/tenants/templatetags/tenant_extras.py

from django import template
from django.conf import settings

register = template.Library()

@register.simple_tag
def get_base_domain():
    """
    A simple template tag to retrieve the base hostname from settings.
    Usage in template: {% get_base_domain %}
    """
    # Using APP_HOSTNAME is recommended as it should be clean (no port).
    # If it's not defined, fall back to TENANT_BASE_DOMAIN and strip the port.
    hostname = getattr(settings, 'APP_HOSTNAME', None)
    if not hostname:
        base_domain = getattr(settings, 'TENANT_BASE_DOMAIN', 'myapp.test:8000')
        hostname = base_domain.split(':')[0]
    return hostname

# --- ALTERNATIVE: If you prefer to use it as a filter ---
# A filter is less logical here since it doesn't modify an input,
# but if you absolutely want to use the `|` syntax, you can do this.
# Note: The input `value` will be unused.

@register.filter
def get_base_domain_filter(value):
    """
    A template filter to retrieve the base domain.
    The input value is ignored.
    Usage in template: {{ ''|get_base_domain_filter }}
    """
    return getattr(settings, 'APP_HOSTNAME', 'myapp.test')



@register.filter(name='split')
def split_string(value, key):
    """
    Splits a string by the given key.
    Usage: {{ some_string|split:":" }}
    Returns a list.
    """
    return value.split(key)


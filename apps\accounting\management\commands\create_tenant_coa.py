# apps/accounting/management/commands/create_tenant_coa.py
from django.core.management.base import BaseCommand, CommandError
from django_tenants.utils import schema_context, get_tenant_model
from django.db import connection # For logging current schema
import logging

# Use the same logger name as your CoA signal for consistent output
# OR create a new one specific to this command.
# Assuming you used 'project.tenant_signals' for the CoA signal logic:
command_logger = logging.getLogger('project.tenant_signals') 
# If your CoA signal functions are in apps.tenants.signals:
from apps.tenants.signals import _fetch_account_types_from_public_schema, _create_accounts_in_tenant_schema
# Or, if the helper functions are directly in accounting/signals.py or another utils file:
# from apps.accounting.utils_coa import _fetch_account_types_from_public_schema, _create_accounts_in_tenant_schema 


Tenant = get_tenant_model()

class Command(BaseCommand):
    help = 'Creates the starter Chart of Accounts for a specified tenant schema.'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name of the tenant.')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        
        try:
            tenant = Tenant.objects.get(schema_name=schema_name)
        except Tenant.DoesNotExist:
            raise CommandError(f'Tenant with schema_name "{schema_name}" does not exist.')

        self.stdout.write(self.style.SUCCESS(f"Processing CoA creation for tenant '{tenant.name}' (Schema: {schema_name})"))
        command_logger.info(f"MANUAL_COA_COMMAND: --- Initiating CoA creation for tenant '{schema_name}' ---")

        # --- Replicate the core logic of your CoA signal ---
        # 1. Fetch AccountTypes from public schema
        command_logger.info(f"MANUAL_COA_COMMAND: Fetching AccountTypes...")
        account_type_map = _fetch_account_types_from_public_schema() # Call your existing helper

        if not account_type_map:
            command_logger.error(f"MANUAL_COA_COMMAND: Failed to fetch AccountTypes. Aborting CoA creation for '{schema_name}'.")
            self.stderr.write(self.style.ERROR(f"Failed to fetch AccountTypes. CoA not created for '{schema_name}'."))
            return

        # 2. Create accounts in the tenant's schema
        command_logger.info(f"MANUAL_COA_COMMAND: Creating accounts in tenant schema '{schema_name}'...")
        
        # _create_accounts_in_tenant_schema already handles schema_context internally
        _create_accounts_in_tenant_schema(tenant, account_type_map) # Call your existing helper
        
        # The _create_accounts_in_tenant_schema function should have its own success/failure logging.
        # We can add a final message here.
        # To verify, you might query the count of accounts.
        with schema_context(tenant.schema_name):
            from apps.accounting.models import Account
            # If your Account model has a direct FK to tenant:
            # coa_count = Account.objects.filter(tenant=tenant).count()
            # If Account model is purely tenant-schema specific (no direct tenant FK, relies on schema):
            coa_count = Account.objects.count() 
        
        command_logger.info(f"MANUAL_COA_COMMAND: --- CoA creation process completed for '{schema_name}'. Accounts created/verified: {coa_count} ---")
        self.stdout.write(self.style.SUCCESS(f"Successfully processed CoA for tenant '{schema_name}'. Accounts found: {coa_count}."))
        self.stdout.write(self.style.WARNING("Please check the logs for detailed status of individual account creations."))
        
        
        
        
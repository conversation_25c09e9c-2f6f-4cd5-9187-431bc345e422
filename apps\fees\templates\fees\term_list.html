{# D:\school_fees_saas_v2\apps\fees\templates\fees\term_list.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Manage Terms" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Manage Terms / Semesters" }}</h1>
        {% if perms.fees.add_term %}
            <a href="{% url 'fees:term_create' %}" class="btn btn-primary">
                <i class="bi bi-calendar-plus-fill me-2"></i>Add New Term
            </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    {% if terms %}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm data-table">
                    <thead class="table-light">
                        <tr>
                            <th>Term Name</th>
                            <th>Academic Year</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th class="text-center">Is Active?</th>
                            <th style="width: 130px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for term_obj in terms %} {# Changed loop variable #}
                        <tr>
                            <td>{{ term_obj.name }}</td>
                            <td>{{ term_obj.academic_year.name }}</td>
                            <td>{{ term_obj.start_date|date:"Y-m-d" }}</td>
                            <td>{{ term_obj.end_date|date:"Y-m-d" }}</td>
                            <td class="text-center">
                                {% if term_obj.is_active %}
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                {% else %}
                                    <i class="bi bi-x-circle-fill text-secondary"></i>
                                {% endif %}
                            </td>
                            <td class="action-buttons">
                                {% if perms.fees.change_term %}
                                <a href="{% url 'fees:term_update' term_obj.pk %}" class="btn btn-secondary btn-sm" title="Edit Term"><i class="bi bi-pencil-fill"></i></a>
                                {% endif %}
                                {% if perms.fees.delete_term %}
                                <form action="{% url 'fees:term_delete' term_obj.pk %}" method="post" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete Term" onclick="return confirm('Are you sure you want to delete term \'{{ term_obj.name|escapejs }}\' for academic year \'{{ term_obj.academic_year.name|escapejs }}\'?');"><i class="bi bi-trash-fill"></i></button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info mt-3">
        No terms or semesters found.
        {% if perms.fees.add_term %}
            <a href="{% url 'fees:term_create' %}" class="alert-link ms-2">Add the first one?</a>
        {% endif %}
    </div>
    {% endif %}

    {% include "partials/_pagination.html" %}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left-circle me-2"></i>Back to Dashboard</a>
    </div>
</div>
{% endblock %}
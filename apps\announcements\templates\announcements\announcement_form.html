{# D:\school_fees_saas_v2\apps\announcements\templates\announcements\announcement_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %} {# Assuming you use widget_tweaks #}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Link to Select2 CSS if you use it for target_staff_groups #}
    {# <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" /> #}
    {# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" /> #}

    <style>
        /* Premium Announcement Form Design */
        .premium-card {
            border: none;
            border-radius: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            max-width: 900px;
            margin: 2rem auto;
        }

        .premium-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #20c997, #17a2b8, #6f42c1, #e83e8c, #fd7e14, #20c997);
            background-size: 300% 100%;
            animation: rainbow-border 3s ease-in-out infinite;
            z-index: 10;
        }

        @keyframes rainbow-border {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .premium-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            color: white;
            padding: 2rem;
            margin: -1.5rem -1.5rem 2rem -1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-radius: 1rem 1rem 0 0;
        }

        .card-title::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shine 2s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Floating Labels */
        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem 0.75rem 0.5rem 0.75rem;
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: #333333;
        }

        .form-floating > textarea.form-control {
            height: auto;
            min-height: calc(6rem + 2px);
            padding: 1.5rem 0.75rem 0.5rem 0.75rem;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #20c997;
            box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
            transform: translateY(-2px);
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
        }

        .form-floating > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1.25rem 0.75rem;
            pointer-events: none;
            border: 2px solid transparent;
            transform-origin: 0 0;
            transition: all 0.3s ease;
            color: #20c997;
            font-weight: 500;
            z-index: 2;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label,
        .form-floating > .form-select ~ label {
            opacity: 1;
            transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
            color: #20c997;
            background: transparent;
            padding: 0;
            border-radius: 0;
        }

        .icon-input {
            color: #20c997;
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }

        /* Premium Buttons */
        .btn-premium {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
        }

        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);
            color: white;
        }

        .btn-secondary-premium {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Form Switch Styling */
        .form-check-input:checked {
            background-color: #20c997;
            border-color: #20c997;
        }

        .form-check-input:focus {
            border-color: #20c997;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
        }

        /* Form validation */
        .invalid-feedback {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Breadcrumb styling */
        .pagetitle h1 {
            color: #20c997;
            font-weight: 600;
        }

        .breadcrumb-item a {
            color: #20c997;
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: #17a2b8;
            text-decoration: underline;
        }

        /* Form section specific centering */
        .announcement-form-section {
            padding: 2rem 0;
        }

        .announcement-form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .premium-card {
                margin: 1rem;
                max-width: none;
            }

            .card-title {
                padding: 1.5rem;
                margin: -1rem -1rem 1.5rem -1rem;
            }

            .form-floating > .form-control,
            .form-floating > .form-select {
                height: calc(3rem + 2px);
                font-size: 0.9rem;
            }

            .form-floating > label {
                padding: 1.25rem 0.75rem;
                font-size: 0.9rem;
            }
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'announcements:announcement_list' %}">{% trans "Announcements" %}</a></li>
            <li class="breadcrumb-item active">{{ view_title }}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section announcement-form-section">
    <div class="announcement-form-container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card shadow-sm premium-card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-bullhorn me-2"></i>
                        {% if form_mode == "create" %}
                            {% trans "Create New Announcement" %}
                        {% else %}
                            {% trans "Edit Announcement:" %} {{ object.title|truncatechars:50 }}
                        {% endif %}
                    </h5>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Hidden fields that are auto-set by the form #}
                        {{ form.tenant.as_hidden }}
                        {{ form.author.as_hidden }}
                        {{ form.is_global.as_hidden }}
                        {{ form.target_global_audience_type.as_hidden }}

                        {{ form.non_field_errors }}

                        <div class="row g-3">
                            <div class="col-md-12">
                                <div class="form-floating">
                                    {% render_field form.title class+="form-control" placeholder="Enter announcement title" %}
                                    <label for="{{ form.title.id_for_label }}">
                                        <i class="fas fa-heading icon-input"></i>{{ form.title.label }}
                                    </label>
                                </div>
                                {{ form.title.errors }}
                                {% if form.title.help_text %}<small class="form-text text-muted">{{ form.title.help_text }}</small>{% endif %}
                            </div>

                            <div class="col-md-12">
                                <div class="form-floating">
                                    {% render_field form.content class+="form-control" rows="8" placeholder="Enter announcement content" %}
                                    <label for="{{ form.content.id_for_label }}">
                                        <i class="fas fa-align-left icon-input"></i>{{ form.content.label }}
                                    </label>
                                </div>
                                {{ form.content.errors }}
                                {% if form.content.help_text %}<small class="form-text text-muted">{{ form.content.help_text }}</small>{% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {% render_field form.publish_date class+="form-control" type="datetime-local" placeholder="Select publish date" %}
                                    <label for="{{ form.publish_date.id_for_label }}">
                                        <i class="fas fa-calendar-plus icon-input"></i>{{ form.publish_date.label }}
                                    </label>
                                </div>
                                {{ form.publish_date.errors }}
                                {% if form.publish_date.help_text %}<small class="form-text text-muted">{{ form.publish_date.help_text }}</small>{% endif %}
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    {% render_field form.expiry_date class+="form-control" type="datetime-local" placeholder="Select expiry date" %}
                                    <label for="{{ form.expiry_date.id_for_label }}">
                                        <i class="fas fa-calendar-times icon-input"></i>{{ form.expiry_date.label }}
                                    </label>
                                </div>
                                {{ form.expiry_date.errors }}
                                {% if form.expiry_date.help_text %}<small class="form-text text-muted">{{ form.expiry_date.help_text }}</small>{% endif %}
                            </div>

                            <fieldset class="mt-4">
                                <legend class="h6">{% trans "Audience Targeting" %}</legend>
                                <div class="form-check mb-2">
                                    {% render_field form.target_all_staff class+="form-check-input" %}
                                    <label for="{{ form.target_all_staff.id_for_label }}" class="form-check-label">{{ form.target_all_staff.label }}</label>
                                    {% if form.target_all_staff.help_text %}<small class="form-text text-muted d-block">{{ form.target_all_staff.help_text }}</small>{% endif %}
                                    {{ form.target_all_staff.errors }}
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">{{ form.target_tenant_staff_groups.label }}</label>
                                    <div id="id_{{ form.target_tenant_staff_groups.name }}_wrapper" class="checkbox-group-wrapper p-2 border rounded" style="max-height: 200px; overflow-y: auto;">
                                        {% for choice_value, choice_label in form.target_tenant_staff_groups.field.choices %}
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                    name="{{ form.target_tenant_staff_groups.html_name }}" 
                                                    value="{{ choice_value }}" 
                                                    id="id_{{ form.target_tenant_staff_groups.html_name }}_{{ forloop.counter }}"
                                                    {% if choice_value|stringformat:"s" in form.target_tenant_staff_groups.value|default_if_none:""|join:", "|stringformat:"s" %}checked{% endif %}>
                                                <label class="form-check-label" for="id_{{ form.target_tenant_staff_groups.html_name }}_{{ forloop.counter }}">
                                                    {{ choice_label }}
                                                </label>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted small">{% trans "No staff groups available to select." %}</p>
                                        {% endfor %}
                                    </div>
                                    {{ form.target_tenant_staff_groups.errors }}
                                    {% if form.target_tenant_staff_groups.help_text %}<small class="form-text text-muted d-block mt-1">{{ form.target_tenant_staff_groups.help_text }}</small>{% endif %}
                                </div>

                                <div class="form-check">
                                    {% render_field form.target_all_parents class+="form-check-input" %}
                                    <label for="{{ form.target_all_parents.id_for_label }}" class="form-check-label">{{ form.target_all_parents.label }}</label>
                                    {% if form.target_all_parents.help_text %}<small class="form-text text-muted d-block">{{ form.target_all_parents.help_text }}</small>{% endif %}
                                    {{ form.target_all_parents.errors }}
                                </div>
                            </fieldset>

                            <fieldset class="mt-3">
                                <legend class="h6">{% trans "Status" %}</legend>
                                <div class="form-check form-switch mb-2">
                                    {% render_field form.is_published class+="form-check-input" %}
                                    <label for="{{ form.is_published.id_for_label }}" class="form-check-label">{{ form.is_published.label }}</label>
                                    {% if form.is_published.help_text %}<small class="form-text text-muted d-block">{{ form.is_published.help_text }}</small>{% endif %}
                                    {{ form.is_published.errors }}
                                </div>
                                <div class="form-check form-switch">
                                    {% render_field form.is_sticky class+="form-check-input" %}
                                    <label for="{{ form.is_sticky.id_for_label }}" class="form-check-label">{{ form.is_sticky.label }}</label>
                                    {% if form.is_sticky.help_text %}<small class="form-text text-muted d-block">{{ form.is_sticky.help_text }}</small>{% endif %}
                                    {{ form.is_sticky.errors }}
                                </div>
                            </fieldset>
                            
                            {% if object and object.author %}
                            <div class="col-12 mt-3">
                                <p class="text-muted small"><em>{% trans "Authored by:" %} {{ object.author.get_full_name|default:object.author.email }}</em></p>
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                            <button type="submit" class="btn-premium">
                                <i class="fas fa-save me-2"></i>
                                {% if form_mode == "create" %}{% trans "Create Announcement" %}{% else %}{% trans "Save Changes" %}{% endif %}
                            </button>
                            <a href="{% url 'tenant_announcements:announcement_list' %}" class="btn-secondary-premium">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to List" %}
                            </a>
                        </div>
                    </form>
                </div> <!-- card-body -->
            </div> <!-- card -->
        </div> <!-- col-12 -->
    </div> <!-- row -->
</div> <!-- announcement-form-container -->
</section>
{% endblock tenant_specific_content %}


{% block page_specific_js %}
{# In announcement_form.html, block extra_tenant_css #}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

{# In announcement_form.html, block page_specific_js #}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() { // Ensure jQuery is loaded if using $
    // Or use vanilla JS: document.addEventListener('DOMContentLoaded', function() { ... });
    $('.select2-multiple').select2({
        theme: "bootstrap-5",
        width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
        placeholder: $(this).data('placeholder') || "Select groups...", // Add a default placeholder
        closeOnSelect: false,
    });

    // Form submission handling
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');

    if (form && submitButton) {
        form.addEventListener('submit', function(e) {
            // Prevent double submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitButton.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>{% if form_mode == "create" %}Create Announcement{% else %}Save Changes{% endif %}';
                submitButton.style.background = 'linear-gradient(135deg, #20c997 0%, #17a2b8 100%)';
            }, 10000);
        });
    }
});
</script>

{% endblock %}

{% comment %} {% block page_specific_js %}
    {{ block.super }}
    {# Link to Select2 JS if you use it #}
    {# <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        $('.select2-multiple').select2({
            theme: "bootstrap-5",
            width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
            placeholder: $(this).data('placeholder'),
            closeOnSelect: false, // Keep open for multiple selections
        });
    });
    </script> #}
{% endblock %} {% endcomment %}



# D:\school_fees_saas_v2\apps\schools\forms.py
from django import forms
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm, UserChangeForm

from django.contrib.auth.models import Group

from django.db import transaction
from django.utils import timezone

from .models import Staff<PERSON>ser, SchoolProfile, SchoolClass, Section

from .models import GENDER_CHOICES, EMPLOYMENT_TYPE_CHOICES, MARITAL_STATUS_CHOICES

from django.db.models import Q

from django.utils.translation import gettext_lazy as _

from apps.portal_admin.models import AdminActivityLog

from django.conf import settings # For settings.ACCOUNTING_..._CODES     # For Q objects in limit_choices_to

from apps.accounting.models import Account,AccountType 

from apps.accounting import models as accounting_models 

from .models import Term, AcademicYear 

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
#  
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# apps/schools/forms.py
class StaffLoginForm(AuthenticationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = "Staff Email"
        self.fields['username'].widget = forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your staff email address',
                'id': 'floatingStaffEmail',
                'autofocus': True,
                'autocomplete': 'email',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter the email address associated with your staff account'
            }
        )
        self.fields['password'].label = "Password"
        self.fields['password'].widget = forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your secure password',
                'id': 'floatingStaffPassword',
                'autocomplete': 'current-password',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter your staff account password'
            }
        )



# D:\school_fees_saas_v2\apps\schools\forms.py
from django import forms
from django.conf import settings # For accessing ACCOUNTING_..._TYPE_CODE settings
from django.utils.translation import gettext_lazy as _ # If using for labels/help_texts here

# Assuming these models are correctly defined and imported
from .models import SchoolProfile
from apps.accounting.models import Account # CRITICAL IMPORT


# apps/schools/forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
# from django.conf import settings # Not strictly needed if coa_field_map directly uses Q objects
from django.db.models import Q

# Ensure correct model imports from their canonical locations
from .models import SchoolProfile 
from apps.accounting.models import Account, AccountType 
from apps.schools.models import AcademicYear # Assuming AcademicYear is for current_academic_year field

class SchoolProfileForm(forms.ModelForm):
    class Meta:
        model = SchoolProfile
        # fields = '__all__' # Includes all fields from SchoolProfile model

        exclude = ['school']
        
        # Define widgets for all fields you want to customize
        # This ensures consistency and allows for specific attributes like 'select2-coa'
        widgets = {
            # General Info
            'school_name_override': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'school_motto': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'city': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'state_province': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'country_name': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': '******-123-4567'}),
            'school_email': forms.EmailInput(attrs={'class': 'form-control form-control-sm'}),
            'logo': forms.ClearableFileInput(attrs={'class': 'form-control form-control-sm'}),
            
            # Financial Settings
            'school_name_on_reports': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'currency_symbol': forms.TextInput(attrs={'class': 'form-control form-control-sm', 'maxlength': '5'}),
            'financial_year_start_month': forms.Select(attrs={'class': 'form-select form-select-sm'}), 
            'default_due_days': forms.NumberInput(attrs={'class': 'form-control form-control-sm', 'min': '0'}),
            'current_academic_year': forms.Select(attrs={'class': 'form-select form-select-sm'}),

            # Default CoA Accounts
            'default_accounts_receivable_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            'default_cash_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            'default_bank_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            'default_fee_income_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            'default_discount_given_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            'default_expense_coa': forms.Select(attrs={'class': 'form-select form-select-sm select2-coa'}),
            # Add any other default_..._coa fields here if they exist on SchoolProfile
        }
        
        labels = {
            'school_name_override': _("School Name (Display Override)"),
            'school_motto': _("School Motto"),
            'school_email': _("School Contact Email"),
            'phone_number': _("School Contact Phone"),
            'country_name': _("Country"),
            'school_name_on_reports': _("School Name (for Reports)"),
            'financial_year_start_month': _("Financial Year Start Month"),
            'default_due_days': _("Default Invoice Due Days"),
            'current_academic_year': _("Current Academic Year"),
            'default_accounts_receivable_coa': _("Default Accounts Receivable (CoA)"),
            'default_cash_coa': _("Default Cash (CoA)"),
            'default_bank_coa': _("Default Bank (CoA)"),
            'default_fee_income_coa': _("Default Fee Income (CoA)"),
            'default_discount_given_coa': _("Default Discount Given (CoA)"),
            'default_expense_coa': _("Default General Expense (CoA)"),
        }
        
        help_texts = {
            'currency_symbol': _("e.g., $, £, €, ₹. This is for display purposes."),
            'school_name_override': _("Optional. Use if you want a display name different from the registered tenant name."),
            'financial_year_start_month': _("Select the month your school's financial reporting year begins."),
            'current_academic_year': _("Set the current academic session for operations like fee allocation."),
            'default_accounts_receivable_coa': _("Asset account for tracking money owed by students/parents."),
            'default_fee_income_coa': _("Revenue account for crediting fee income."),
        }


    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) 
        super().__init__(*args, **kwargs)

        print("--- SchoolProfileForm __init__ ---")

        # Populate queryset for current_academic_year
        if 'current_academic_year' in self.fields:
            try:
                # Based on your AcademicYear model having 'is_active' (from previous model version you confirmed)
                # If it's 'is_current' now, use that.
                # Let's assume it's 'is_active' on AcademicYear model for this example.
                # If your AcademicYear model actually uses 'is_current', change 'is_active=True' to 'is_current=True'
                self.fields['current_academic_year'].queryset = AcademicYear.objects.filter(
                    is_active=True # Or is_current=True if that's the field name on AcademicYear
                ).order_by('-start_date', 'name')
                self.fields['current_academic_year'].empty_label = _("Select Current Academic Year")
                print(f"  Queryset count for current_academic_year: {self.fields['current_academic_year'].queryset.count()}")
            except FieldError as e: # Catch FieldError if 'is_active' or 'is_current' is wrong
                print(f"!!! FieldError setting queryset for 'current_academic_year': {e} - Check field name on AcademicYear model (is_active vs is_current)!!!")
            except NameError: 
                print("!!! NameError: AcademicYear model not imported correctly for 'current_academic_year' field !!!")
            except Exception as e:
                print(f"!!! Exception setting queryset for 'current_academic_year': {e}")
        else:
            print("!!! Field 'current_academic_year' NOT FOUND in self.fields !!!")

        # CoA Field QuerySet Filtering
        coa_field_map = {
            'default_accounts_receivable_coa': Q(account_type__classification='ASSET'),
            'default_cash_coa': Q(account_type__classification='ASSET', account_type__name__iexact='Cash'),
            'default_bank_coa': Q(account_type__classification='ASSET', account_type__name__iexact='Bank'),
            'default_fee_income_coa': Q(account_type__classification='REVENUE'), 
            'default_discount_given_coa': Q(account_type__classification='EXPENSE'),
            'default_expense_coa': Q(account_type__classification='EXPENSE'),
        }

        for field_name, q_object_for_field in coa_field_map.items():
            if field_name in self.fields:
                print(f"Processing CoA field: {field_name} with Q: {q_object_for_field}")
                try:
                    final_q_object = q_object_for_field & Q(is_active=True) # Filter for active Accounts
                    
                    self.fields[field_name].queryset = Account.objects.filter(
                        final_q_object
                    ).select_related('account_type').order_by('code', 'name')
                    
                    print(f"  Queryset count for {field_name}: {self.fields[field_name].queryset.count()}")
                    
                    model_field = self.Meta.model._meta.get_field(field_name)
                    if model_field.blank or model_field.null:
                        self.fields[field_name].required = False
                    self.fields[field_name].empty_label = _("--------- (Select Account) ---------")
                
                except FieldError as e:
                    print(f"!!! FieldError for {field_name} with Q {q_object_for_field}: {e}")
                except Exception as e:
                    print(f"!!! Other Exception for {field_name} with Q {q_object_for_field}: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"!!! Field '{field_name}' from coa_field_map NOT FOUND in self.fields !!!")
        
        print("--- SchoolProfileForm __init__ END ---")        
        
        

from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.db import transaction
from django.utils import timezone

# Assuming StaffUser model is in the same app's models.py
# If not, adjust the import path: from apps.schools.models import StaffUser
from .models import StaffUser

# Define CHOICES here or import them
GENDER_CHOICES = [
    ('', '---------'), # Optional: Add a blank choice
    ('Male', 'Male'),
    ('Female', 'Female'),
    ('Other', 'Other'),
    ('Prefer_not_to_say', 'Prefer not to say'),
]
MARITAL_STATUS_CHOICES = [
    ('', '---------'),
    ('Single', 'Single'),
    ('Married', 'Married'),
    ('Divorced', 'Divorced'),
    ('Widowed', 'Widowed'),
    ('Other', 'Other'),
]
EMPLOYMENT_TYPE_CHOICES = [
    ('', '---------'),
    ('FullTime', 'Full-Time'),
    ('PartTime', 'Part-Time'),
    ('Contract', 'Contract'),
    ('Permanent', 'Permanent'),
    ('Intern', 'Intern'),
    ('Volunteer', 'Volunteer'),
]



# forms.py (e.g., apps/schools/forms.py)

from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.db import transaction
from django.utils import timezone

# Import StaffUser and CHOICES from your models file
from .models import StaffUser, GENDER_CHOICES, MARITAL_STATUS_CHOICES, EMPLOYMENT_TYPE_CHOICES


# --- StaffUserCreationForm ---
class StaffUserCreationForm(UserCreationForm):
    """
    Form for creating new StaffUser instances, including all profile fields.
    """
    # Explicitly defined fields to match the updated StaffUser model
    middle_name = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional'}))
    employee_id = forms.CharField(max_length=50, required=False, help_text="School-specific unique Employee ID (can be optional).", widget=forms.TextInput(attrs={'class': 'form-control'}))
    designation = forms.CharField(max_length=100, required=False, help_text="Job title or role.", widget=forms.TextInput(attrs={'class': 'form-control'}))
    department = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Academics'}))
    employment_type = forms.ChoiceField(choices=EMPLOYMENT_TYPE_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    date_hired = forms.DateField(widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}), required=False, label="Date Hired")
    
    gender = forms.ChoiceField(choices=GENDER_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    date_of_birth = forms.DateField(widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}), required=False)
    marital_status = forms.ChoiceField(choices=MARITAL_STATUS_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    
    phone_number_primary = forms.CharField(max_length=30, required=False, label="Primary Phone", widget=forms.TextInput(attrs={'class': 'form-control'}))
    phone_number_alternate = forms.CharField(max_length=30, required=False, label="Alternate Phone", widget=forms.TextInput(attrs={'class': 'form-control'}))
    
    address_line1 = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    address_line2 = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    city = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    state_province = forms.CharField(max_length=100, required=False, label="State/Province", widget=forms.TextInput(attrs={'class': 'form-control'}))
    postal_code = forms.CharField(max_length=20, required=False, label="Postal/Zip Code", widget=forms.TextInput(attrs={'class': 'form-control'}))
    country = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))

    photo = forms.ImageField(required=False, label="Profile Photo", widget=forms.ClearableFileInput(attrs={'class': 'form-control'}))
    notes = forms.CharField(widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}), required=False)

    class Meta(UserCreationForm.Meta):
        model = StaffUser
        fields = ( # All fields intended for the creation form
            "email", "first_name", "last_name", "middle_name",
            "employee_id", "designation", "department", "employment_type", "date_hired",
            "gender", "date_of_birth", "marital_status",
            "phone_number_primary", "phone_number_alternate",
            "address_line1", "address_line2", "city", "state_province", "postal_code", "country",
            "photo", "notes"
            # Password fields are handled by UserCreationForm
            # is_active, is_staff are usually set by defaults or post-creation logic in view
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure first_name and last_name are required (model already enforces this via blank=False)
        self.fields['first_name'].widget.attrs.update({'placeholder': 'Enter first name'})
        self.fields['last_name'].widget.attrs.update({'placeholder': 'Enter last name'})
        # Add other initializations or widget attrs if needed

    @transaction.atomic
    def save(self, commit=True):
        print("--- StaffUserCreationForm: save() called ---")
        # print(f"Cleaned data: {self.cleaned_data}") # Can be very verbose
        
        user = super().save(commit=False) # Handles fields in Meta and password
        # ModelForm machinery populates instance from cleaned_data for fields in Meta.fields.

        # Optional: Log some key fields from the user object before final save
        print(f"User object pre-commit: Email='{user.email}', Name='{user.first_name} {user.last_name}', EmpID='{user.employee_id}'")

        if commit:
            user.save() # This saves the StaffUser instance with all its fields
            print(f"User {user.email} (PK: {user.pk}) CREATED and saved to database.")
        else:
            print("User object prepared but NOT YET committed to database (commit=False).")
        return user


# --- StaffUserChangeForm ---
class StaffUserChangeForm(UserChangeForm):
    """
    Form for updating existing StaffUser instances.
    """
    # Re-declare fields from StaffUser that you want on the change form,
    # especially if you want different widgets, labels, or required status than the model implies.
    middle_name = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional'}))
    employee_id = forms.CharField(max_length=50, required=False, help_text="School-specific unique Employee ID.", widget=forms.TextInput(attrs={'class': 'form-control'}))
    designation = forms.CharField(max_length=100, required=False, help_text="Job title or role.", widget=forms.TextInput(attrs={'class': 'form-control'}))
    department = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Academics'}))
    employment_type = forms.ChoiceField(choices=EMPLOYMENT_TYPE_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    date_hired = forms.DateField(widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}), required=False, label="Date Hired")
    date_left = forms.DateField(widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}), required=False, label="Date Left School")
    
    gender = forms.ChoiceField(choices=GENDER_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    date_of_birth = forms.DateField(widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}), required=False)
    marital_status = forms.ChoiceField(choices=MARITAL_STATUS_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select'}))
    
    phone_number_primary = forms.CharField(max_length=30, required=False, label="Primary Phone", widget=forms.TextInput(attrs={'class': 'form-control'}))
    phone_number_alternate = forms.CharField(max_length=30, required=False, label="Alternate Phone", widget=forms.TextInput(attrs={'class': 'form-control'}))
    
    address_line1 = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    address_line2 = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    city = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    state_province = forms.CharField(max_length=100, required=False, label="State/Province", widget=forms.TextInput(attrs={'class': 'form-control'}))
    postal_code = forms.CharField(max_length=20, required=False, label="Postal/Zip Code", widget=forms.TextInput(attrs={'class': 'form-control'}))
    country = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))

    photo = forms.ImageField(required=False, label="Profile Photo", widget=forms.ClearableFileInput(attrs={'class': 'form-control'}))
    notes = forms.CharField(widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}), required=False)
    
    is_active = forms.BooleanField(required=False, label="Active Status", widget=forms.CheckboxInput(attrs={'class': 'form-check-input mt-0 me-1'}))
    is_staff = forms.BooleanField(required=False, label="Django Admin Access", help_text="Can this user access the Django admin site?", widget=forms.CheckboxInput(attrs={'class': 'form-check-input mt-0 me-1'}))
    # is_superuser should not be editable by tenant admins typically.

    class Meta(UserChangeForm.Meta):
        model = StaffUser
        fields = ( # All fields intended for the edit form
            "email", "first_name", "last_name", "middle_name",
            "employee_id", "designation", "department", "employment_type", "date_hired", "date_left",
            "gender", "date_of_birth", "marital_status",
            "phone_number_primary", "phone_number_alternate",
            "address_line1", "address_line2", "city", "state_province", "postal_code", "country",
            "photo", "notes",
            "is_active", "is_staff"
            # Password is handled by UserChangeForm's separate mechanism (links)
            # Groups/permissions are usually handled in a separate role assignment view.
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'email' in self.fields:
            self.fields['email'].widget.attrs['readonly'] = True # Don't allow email change on edit
        # Make first_name and last_name required (model already enforces this)

    @transaction.atomic
    def save(self, commit=True):
        print("--- StaffUserChangeForm: save() called ---")
        # print(f"Cleaned data: {self.cleaned_data}")
        print(f"Changed data fields: {self.changed_data}")
        
        user = super().save(commit=False) # Handles fields in Meta

        # Explicit handling for ClearableFileInput if 'photo-clear' was checked
        if 'photo' in self.changed_data and self.cleaned_data.get('photo') is False:
            user.photo = None # Clear the photo
            print("Photo field explicitly cleared.")
        # If a new photo was uploaded, super().save() should handle it as it's in Meta.fields.

        # Optional: Log some key fields from the user object before final save
        print(f"User object pre-commit: Email='{user.email}', Name='{user.first_name} {user.last_name}', EmpID='{user.employee_id}'")
        print(f"Photo on model pre-commit: {user.photo}")

        if commit:
            user.save()
            print(f"User {user.email} (PK: {user.pk}) UPDATED and saved to database.")
        else:
            print("User object modified but NOT YET committed to database (commit=False).")
        return user



# --- SchoolClass Form ---
class SchoolClassForm(forms.ModelForm):
    class Meta:
        model = SchoolClass
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Grade 1, Class X, Senior KG'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Optional: Brief description of the class/grade (e.g., Advanced mathematics focus, Arts specialization, etc.)'
            }),
        }
        labels = {
            'name': 'Class/Grade Name',
            'description': 'Description',
        }
        help_texts = {
            'name': 'Enter the name of the class or grade level (e.g., Grade 1, Form IV A, Senior KG)',
            'description': 'Optional: Provide additional details about this class/grade such as its focus area, special programs, or any other relevant information.',
        }



# --- Section Form ---
class SectionForm(forms.ModelForm):
    # Dynamically set queryset for school_class in the view if needed (e.g. to filter by current tenant)
    # For now, it will show all SchoolClass instances in the current tenant's schema.
    school_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.all().order_by('name'), # Queryset can be refined in view
        widget=forms.Select(attrs={'class': 'form-select'}),
        empty_label="-- Select Class/Grade --" # Optional
    )

    
    class Meta:
        model = Section
        fields = ['name'] # ONLY 'name'
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Section A, Blue Group'}),
        }
    # class Meta:
    #     model = Section
    #     fields = ['name', 'school_class'] # school_class is populated via ModelChoiceField
    #     widgets = {
    #         'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., A, Blue, Rose'}),
    #         # 'school_class' widget defined above
    #     }
        labels = {
            'name': 'Section Name',
            'school_class': 'Associated Class/Grade'
        }

    def __init__(self, *args, **kwargs):
        # If you pass a 'school_class_instance' kwarg from the view,
        # you can pre-select it or limit choices.
        # Example:
        # school_class_instance = kwargs.pop('school_class_instance', None)
        super().__init__(*args, **kwargs)
        # if school_class_instance:
        #     self.fields['school_class'].queryset = SchoolClass.objects.filter(pk=school_class_instance.pk)
        #     self.fields['school_class'].initial = school_class_instance
        #     self.fields['school_class'].disabled = True # Make it read-only if creating section FOR a class
        # Or, if just creating general sections:
        self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name')
        


# --- StaffRoleAssignForm ---
class StaffRoleAssignForm(forms.Form):
    roles = forms.ModelMultipleChoiceField(
        queryset=Group.objects.none(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input individual-checkbox-role'}), # Class for individual inputs
        required=False,
        label="Assign Roles / Groups"
    )
    # No __init__ or save needed if view handles population and saving



# --- SchoolClassForm ---
class SchoolClassForm(forms.ModelForm):
    class Meta:
        model = SchoolClass
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }



# --- SectionForm ---
class SectionForm(forms.ModelForm):
    class Meta:
        model = Section
        # school_class is set in the view, so exclude or make it hidden if needed
        fields = ['name'] # Add 'school_class' if it's a ModelChoiceField here
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
        }



class ParentLoginForm(AuthenticationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = "Parent Email"
        self.fields['username'].widget = forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your parent email address',
                'id': 'floatingParentEmail',
                'autofocus': True,
                'autocomplete': 'email',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter the email address associated with your parent account'
            }
        )
        self.fields['password'].label = "Password"
        self.fields['password'].widget = forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Enter your secure password',
                'id': 'floatingParentPassword',
                'autocomplete': 'current-password',
                'data-bs-toggle': 'tooltip',
                'title': 'Enter your parent account password'
            }
        )
        


# D:\school_fees_saas_v2\apps\schools\forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from .models import AcademicSetting, AcademicYear # Assuming both in schools.models

class AcademicSettingForm(forms.ModelForm):
    class Meta:
        model = AcademicSetting
        fields = [
            'current_academic_year',
            'grading_system',
            # Add other fields from your AcademicSetting model here
        ]
        widgets = {
            'current_academic_year': forms.Select(attrs={'class': 'form-select'}),
            'grading_system': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'current_academic_year': _("Current Active Academic Year"),
            'grading_system': _("School's Grading System"),
        }
        help_texts = {
            'current_academic_year': _("Select the academic year that is currently in session. Student enrollments and fee calculations will primarily use this."),
            'grading_system': _("Choose the main grading methodology employed by your institution."),
        }

    def __init__(self, *args, **kwargs):
        # tenant = kwargs.pop('tenant', None) # If you need to pass tenant for some reason
        super().__init__(*args, **kwargs)

        if 'current_academic_year' in self.fields:
            # Populate choices for current_academic_year
            # This queryset should ideally be filtered by the current tenant if AcademicYear was shared,
            # but since AcademicYear is likely a TENANT_APP model, .all() is fine within tenant context.
            self.fields['current_academic_year'].queryset = AcademicYear.objects.filter(is_active=True).order_by('-start_date')
            self.fields['current_academic_year'].empty_label = _("Select Current Academic Year")


from django import forms
from django.core.exceptions import ValidationError
from .models import AcademicYear # , Term, SchoolClass, Section, StaffUser, SchoolProfile
# ... (your existing forms like StaffUserCreationForm, StaffUserChangeForm) ...

class AcademicYearForm(forms.ModelForm):
    class Meta:
        model = AcademicYear
        fields = ['name', 'start_date', 'end_date', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 2024-2025'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        help_texts = {
            'is_active': 'Only one academic year can be active at a time. Setting this will deactivate others.',
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        # Optional: Add specific name validation if needed beyond model's unique=True
        return name

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        is_active_form = cleaned_data.get("is_active")

        if start_date and end_date and start_date >= end_date:
            self.add_error('end_date', "End date must be after start date.")

        # Check for uniqueness of 'is_active' if this instance is being set to active
        # The model's clean method also handles this, but form validation is good for user feedback
        if is_active_form and self.instance.pk: # Check only if updating an existing instance
            if AcademicYear.objects.filter(is_active=True).exclude(pk=self.instance.pk).exists():
                self.add_error('is_active', "Another academic year is already active. Please deactivate it first.")
        elif is_active_form and not self.instance.pk: # Check if creating a new active year
            if AcademicYear.objects.filter(is_active=True).exists():
                self.add_error('is_active', "Another academic year is already active. Please deactivate it first.")
        
        return cleaned_data
    

from django import forms
from django.core.exceptions import ValidationError
from .models import AcademicYear, Term # Add Term
# ... (your existing AcademicYearForm, StaffUser forms etc.) ...

class TermForm(forms.ModelForm):
    class Meta:
        model = Term
        fields = ['name', 'academic_year', 'start_date', 'end_date'] #, 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Term 1, Semester II'}),
            'academic_year': forms.Select(attrs={'class': 'form-select'}), # Or ModelChoiceField for better control
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            # 'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}), # Added widget
        }
        # labels = { # Optional: Add a label for is_active
        #     'is_active': _("Is Active?"),
        # }
        
        help_texts = {
            'academic_year': 'Select the academic year this term belongs to.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Optionally, order academic_year queryset if it's a ModelChoiceField or for the default widget
        if 'academic_year' in self.fields:
            self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date', 'name')
            self.fields['academic_year'].empty_label = "Select Academic Year"


    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        academic_year = cleaned_data.get("academic_year")

        # Validate start_date < end_date (also handled by model's clean, but good for form feedback)
        if start_date and end_date and start_date >= end_date:
            self.add_error('end_date', "End date must be after start date.")

        # Validate term dates are within the selected academic year's dates
        if start_date and end_date and academic_year:
            if not (academic_year.start_date <= start_date <= academic_year.end_date and \
                    academic_year.start_date <= end_date <= academic_year.end_date):
                self.add_error(None, # Non-field error for overall date range issue
                            f"Term dates must fall within the selected academic year's range "
                            f"({academic_year.start_date.strftime('%d %b %Y')} - "
                            f"{academic_year.end_date.strftime('%d %b %Y')}).")
            
            # Check for overlapping terms within the same academic year (if not creating new)
            # Model's unique_together handles ('academic_year', 'name')
            # Overlapping date check is more complex and might be better handled in model or service layer if strict
            # For simplicity, we'll rely on the date range check above and model's unique_together for now.

        return cleaned_data
    
    
    
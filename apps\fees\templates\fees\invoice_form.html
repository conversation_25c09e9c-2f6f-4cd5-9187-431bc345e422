{% extends "tenant_base.html" %}
{% load static humanize %}

{% block title %}{{ view_title|default:"Invoice Form" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    <style>
        body {
            background-color: #f8f9fa;
        }
        .invoice-form-card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .invoice-form-card .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            padding: 2rem 2rem 1.5rem;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem 0.75rem 0.5rem 0.75rem;
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: #333333;
        }
        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #20c997;
            box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
            transform: translateY(-2px);
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
        }

        .form-floating > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1.25rem 0.75rem;
            pointer-events: none;
            border: 2px solid transparent;
            transform-origin: 0 0;
            transition: all 0.3s ease;
            color: #20c997;
            font-weight: 500;
            z-index: 2;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label,
        .form-floating > .form-select ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
            color: #20c997;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .field-icon {
            color: #20c997;
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }

        /* Compact field styling */
        .col-lg-1 .form-floating > .form-control,
        .col-lg-1 .form-floating > .form-select {
            font-size: 0.9rem;
        }

        .col-lg-2 .form-floating > .form-control,
        .col-lg-2 .form-floating > .form-select {
            font-size: 0.95rem;
        }

        /* Help text styling */
        .help-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
            font-style: italic;
        }

        /* Responsive adjustments */
        @media (max-width: 991.98px) {
            .form-floating > .form-control,
            .form-floating > .form-select {
                height: calc(3rem + 2px);
                font-size: 0.9rem;
            }

            .form-floating > label {
                padding: 1rem 0.75rem;
                font-size: 0.9rem;
            }
        }
        .fieldset-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }
        .formset-item-container {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
        }
        .formset-item-container:hover {
            border-color: #007bff;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
        }
        .formset-item-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 0.75rem 1.25rem;
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .formset-item-header h6 {
            margin-bottom: 0;
            font-weight: 600;
            color: white;
        }
        .formset-item-body {
            padding: 1.5rem;
        }
        .select2-container .select2-selection--single {
            height: calc(3.5rem + 2px) !important;
            border-radius: 0.5rem !important;
            border: 2px solid #e9ecef !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 3.5rem !important;
            padding-left: 0.75rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 3.5rem !important;
        }
        .select2-container--default.select2-container--focus .select2-selection--single {
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }
        .btn-primary {
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.3);
        }
        .btn-success {
            border-radius: 0.5rem;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-danger {
            border-radius: 0.5rem;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .errorlist {
            color: var(--bs-danger);
            font-size: 0.875em;
            list-style: none;
            padding-left: 0;
            margin-top: .25rem;
        }
        .invalid-feedback.d-block {
            display: block !important;
            width: 100%;
            margin-top: .25rem;
            font-size: .875em;
            color: var(--bs-danger);
        }
        @media (max-width: 768px) {
            .invoice-form-card .card-header {
                padding: 1.5rem 1rem 1rem;
            }
            .invoice-icon {
                font-size: 2.5rem !important;
            }
        }
        /* Specific styling for form field wrappers (adjust if your structure is different) */
        .field-wrapper { /* Add this class to divs wrapping label+input+errors */
            margin-bottom: 1rem; /* Standard Bootstrap spacing */
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
    {% include "partials/_breadcrumb.html" %}

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <div class="card invoice-form-card">
                <div class="card-header text-white text-center">
                    <div class="invoice-icon mb-3">
                        <i class="bi bi-receipt-cutoff" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 class="mb-2 fw-bold">{{ view_title|default:"Invoice Management" }}</h2>
                    <p class="mb-0 opacity-75">Create and manage student invoices with detailed billing information</p>
                </div>
                <div class="card-body p-lg-5 p-4">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="invoiceForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form.media }}

                        <div class="fieldset-header mb-4">
                            {% if object %}
                                <h5><i class="bi bi-pencil-square me-2"></i>Update Invoice Details</h5>
                            {% else %}
                                <h5><i class="bi bi-file-earmark-text me-2"></i>Invoice Information</h5>
                            {% endif %}
                        </div>

                        {% if object and form.errors %}
                            <div class="alert alert-warning mb-4">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Please correct the errors below to update this invoice.
                            </div>
                        {% endif %}

                        <!-- Hidden Fields -->
                        {% for field in form %}
                            {% if field.is_hidden %}
                                {{ field }}
                            {% endif %}
                        {% endfor %}

                        <!-- Compact Single Row Layout -->
                        <div class="row g-3 mb-4">
                            {% if form.student %}
                                <div class="col-lg-3 col-md-6">
                                    <div class="form-floating">
                                        {{ form.student }}
                                        <label for="{{ form.student.id_for_label }}">
                                            <i class="bi bi-person field-icon"></i>{{ form.student.label }}
                                        </label>
                                    </div>
                                    {% if form.student.help_text %}<div class="help-text">{{ form.student.help_text|safe }}</div>{% endif %}
                                    {% for error in form.student.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}

                            {% if form.academic_year %}
                                <div class="col-lg-2 col-md-6">
                                    <div class="form-floating">
                                        {{ form.academic_year }}
                                        <label for="{{ form.academic_year.id_for_label }}">
                                            <i class="bi bi-calendar-range field-icon"></i>Academic Year
                                        </label>
                                    </div>
                                    {% if form.academic_year.help_text %}<div class="help-text">{{ form.academic_year.help_text|safe }}</div>{% endif %}
                                    {% for error in form.academic_year.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}

                            {% if form.term %}
                                <div class="col-lg-2 col-md-4">
                                    <div class="form-floating">
                                        {{ form.term }}
                                        <label for="{{ form.term.id_for_label }}">
                                            <i class="bi bi-calendar-week field-icon"></i>Term
                                        </label>
                                    </div>
                                    {% if form.term.help_text %}<div class="help-text">{{ form.term.help_text|safe }}</div>{% endif %}
                                    {% for error in form.term.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}

                            {% if form.issue_date %}
                                <div class="col-lg-2 col-md-4">
                                    <div class="form-floating">
                                        {{ form.issue_date }}
                                        <label for="{{ form.issue_date.id_for_label }}">
                                            <i class="bi bi-calendar-plus field-icon"></i>Issue Date
                                        </label>
                                    </div>
                                    {% if form.issue_date.help_text %}<div class="help-text">{{ form.issue_date.help_text|safe }}</div>{% endif %}
                                    {% for error in form.issue_date.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}

                            {% if form.due_date %}
                                <div class="col-lg-2 col-md-4">
                                    <div class="form-floating">
                                        {{ form.due_date }}
                                        <label for="{{ form.due_date.id_for_label }}">
                                            <i class="bi bi-calendar-event field-icon"></i>Due Date
                                        </label>
                                    </div>
                                    {% if form.due_date.help_text %}<div class="help-text">{{ form.due_date.help_text|safe }}</div>{% endif %}
                                    {% for error in form.due_date.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}

                            {% if form.status %}
                                <div class="col-lg-1 col-md-6">
                                    <div class="form-floating">
                                        {{ form.status }}
                                        <label for="{{ form.status.id_for_label }}">
                                            <i class="bi bi-flag field-icon"></i>Status
                                        </label>
                                    </div>
                                    {% if form.status.help_text %}<div class="help-text">{{ form.status.help_text|safe }}</div>{% endif %}
                                    {% for error in form.status.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Invoice Number Field (if exists) -->
                        {% if form.invoice_number %}
                            <div class="row g-3 mb-4">
                                <div class="col-lg-3 col-md-6">
                                    <div class="form-floating">
                                        {{ form.invoice_number }}
                                        <label for="{{ form.invoice_number.id_for_label }}">
                                            <i class="bi bi-hash field-icon"></i>Invoice Number
                                        </label>
                                    </div>
                                    {% if form.invoice_number.help_text %}<div class="help-text">{{ form.invoice_number.help_text|safe }}</div>{% endif %}
                                    {% for error in form.invoice_number.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Notes Section -->
                        {% if form.notes_to_parent or form.internal_notes %}
                            <div class="row g-3 mb-4">
                                {% if form.notes_to_parent %}
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            {{ form.notes_to_parent }}
                                            <label for="{{ form.notes_to_parent.id_for_label }}">
                                                <i class="bi bi-chat-text field-icon"></i>Notes to Parent
                                            </label>
                                        </div>
                                        {% if form.notes_to_parent.help_text %}<div class="help-text">{{ form.notes_to_parent.help_text|safe }}</div>{% endif %}
                                        {% for error in form.notes_to_parent.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                    </div>
                                {% endif %}

                                {% if form.internal_notes %}
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            {{ form.internal_notes }}
                                            <label for="{{ form.internal_notes.id_for_label }}">
                                                <i class="bi bi-sticky field-icon"></i>Internal Notes
                                            </label>
                                        </div>
                                        {% if form.internal_notes.help_text %}<div class="help-text">{{ form.internal_notes.help_text|safe }}</div>{% endif %}
                                        {% for error in form.internal_notes.errors %}<div class="invalid-feedback d-block mt-1">{{ error }}</div>{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="row">
                                <div class="col-12">
                                    {% for error in form.non_field_errors %}<div class="alert alert-danger py-2">{{ error }}</div>{% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        {% if object and not object.is_editable %}
                            <div class="alert alert-warning mb-4">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>This invoice is not in a draft state. Main details and items may be locked or restricted.
                            </div>
                        {% endif %}

                        <div class="fieldset-header mt-5 mb-4">
                            <h5><i class="bi bi-list-ol me-2"></i>Invoice Line Items</h5>
                        </div>
                            {% if not object or object.is_editable %}
                            <button type="button" id="add-invoice-item-form" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-plus-lg me-1"></i> Add Item
                            </button>
                            {% endif %}
                        </div>

                        {{ item_formset.management_form }}
                        {% if item_formset.non_form_errors %}
                            <div class="alert alert-danger">
                                {% for error in item_formset.non_form_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <div id="invoice-items-formset-container">
                            {% for item_form in item_formset.forms %}
                                <div class="formset-item-container" id="{{ item_form.prefix }}-container">
                                    <div class="formset-item-header">
                                        <h6>Item #<span class="item-number">{{ forloop.counter }}</span></h6>
                                        {% if item_formset.can_delete and not object or object.is_editable %}
                                            {% if forloop.counter0 >= item_formset.initial_form_count or not item_form.instance.pk %}
                                                <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"></button>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    <div class="formset-item-body">
                                        {% if item_form.non_field_errors %}
                                            <div class="alert alert-danger py-2">
                                                {% for error in item_form.non_field_errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        {% for hidden_field in item_form.hidden_fields %}{{ hidden_field }}{% endfor %}
                                        <div class="row g-3">
                                            {# Render each field with its errors #}
                                            <div class="col-md-3 field-wrapper field-line_type">
                                                {{ item_form.line_type.label_tag }}
                                                {{ item_form.line_type }}
                                                {% for error in item_form.line_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-fee_head">
                                                {{ item_form.fee_head.label_tag }}
                                                {{ item_form.fee_head }}
                                                {% for error in item_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-concession_type" style="display:none;">
                                                {{ item_form.concession_type.label_tag }}
                                                {{ item_form.concession_type }}
                                                {% for error in item_form.concession_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-3 field-wrapper field-description">
                                                {{ item_form.description.label_tag }}
                                                {{ item_form.description }}
                                                {% for error in item_form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-2 field-wrapper field-quantity">
                                                {{ item_form.quantity.label_tag }}
                                                {{ item_form.quantity }}
                                                {% for error in item_form.quantity.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                            <div class="col-md-2 field-wrapper field-unit_price">
                                                {{ item_form.unit_price.label_tag }}
                                                {{ item_form.unit_price }}
                                                {% for error in item_form.unit_price.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                            </div>
                                        </div>
                                        {% if item_formset.can_delete and item_form.instance.pk and not object or object.is_editable %}
                                            <div class="col-12 text-end mt-2">
                                                <div class="form-check form-check-inline">
                                                    {{ item_form.DELETE }}
                                                    <label for="{{ item_form.DELETE.id_for_label }}" class="form-check-label text-danger">
                                                        {{ item_form.DELETE.label|default:"Mark for Deletion" }}
                                                    </label>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        {# Hidden template for new formset items (empty_form) #}
                        <div id="empty-invoice-item-form-template" style="display: none;">
                            <div class="formset-item-container" id="item-__prefix__">
                                <div class="formset-item-header">
                                    <h6>Item #<span class="item-number">__prefix_display__</span></h6>
                                    <button type="button" class="btn-close remove-invoice-item-form" aria-label="Remove item" title="Remove this item"></button>
                                </div>
                                <div class="formset-item-body">
                                    {% for hidden_field in item_formset.empty_form.hidden_fields %}{{ hidden_field }}{% endfor %}
                                    <div class="row g-3">
                                        <div class="col-md-3 field-wrapper field-line_type">
                                            {{ item_formset.empty_form.line_type.label_tag }}
                                            {{ item_formset.empty_form.line_type }}
                                            {% comment %} Errors won't typically be on the empty form template, but doesn't hurt {% endcomment %}
                                            {% for error in item_formset.empty_form.line_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-fee_head">
                                            {{ item_formset.empty_form.fee_head.label_tag }}
                                            {{ item_formset.empty_form.fee_head }}
                                            {% for error in item_formset.empty_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-concession_type" style="display:none;">
                                            {{ item_formset.empty_form.concession_type.label_tag }}
                                            {{ item_formset.empty_form.concession_type }}
                                            {% for error in item_formset.empty_form.concession_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-3 field-wrapper field-description">
                                            {{ item_formset.empty_form.description.label_tag }}
                                            {{ item_formset.empty_form.description }}
                                            {% for error in item_formset.empty_form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-2 field-wrapper field-quantity">
                                            {{ item_formset.empty_form.quantity.label_tag }}
                                            {{ item_formset.empty_form.quantity }}
                                            {% for error in item_formset.empty_form.quantity.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                        <div class="col-md-2 field-wrapper field-unit_price">
                                            {{ item_formset.empty_form.unit_price.label_tag }}
                                            {{ item_formset.empty_form.unit_price }}
                                            {% for error in item_formset.empty_form.unit_price.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-end gap-2">
                            {% if object and object.pk %}
                                <a href="{{ object.get_absolute_url }}" class="btn btn-outline-secondary"><i class="bi bi-x-circle me-1"></i>Cancel</a>
                            {% else %}
                                <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary"><i class="bi bi-x-circle me-1"></i>Cancel</a>
                            {% endif %}
                            {% if not object or object.is_editable %}
                            <button type="submit" name="action" value="preview" class="btn btn-outline-primary px-4">
                                <i class="bi bi-eye me-1"></i>
                                {% if object %}Preview Changes{% else %}Save & Preview{% endif %}
                            </button>
                            <button type="submit" name="action" value="save" class="btn btn-success px-4">
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% if object %}Update{% else %}Save{% endif %} Invoice
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script> {# Updated jQuery to latest stable #}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('invoice-items-formset-container');
    const addButton = document.getElementById('add-invoice-item-form');
    const emptyFormTemplateHTML = document.getElementById('empty-invoice-item-form-template').innerHTML;
    const totalFormsInput = document.querySelector('input[name$="-TOTAL_FORMS"]'); // e.g., details-TOTAL_FORMS
    const initialFormsInput = document.querySelector('input[name$="-INITIAL_FORMS"]'); // e.g., details-INITIAL_FORMS
    
    let initialFormsCount = initialFormsInput ? parseInt(initialFormsInput.value) : 0;
    let currentFormCount = totalFormsInput ? parseInt(totalFormsInput.value) : initialFormsCount;

    // This class should wrap each field's label, input, help_text, and error display.
    // Use the actual Bootstrap column classes that wrap the form fields
    const formFieldWrapperClass = '[class*="col-"]';

    function toggleInvoiceLineFields(formRow) {
        if (!formRow) return;
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]');

        const getColumnWrapper = (fieldNameSuffix) => {
            const fieldElement = formRow.querySelector(`[name$="-${fieldNameSuffix}"]`);
            // Assuming the direct parent with class 'field-wrapper' is the one to hide/show.
            return fieldElement ? fieldElement.closest(formFieldWrapperClass) : null;
        };

        const feeHeadWrapper = getColumnWrapper('fee_head');
        const concessionTypeWrapper = getColumnWrapper('concession_type');

        if (!lineTypeSelector) return;

        const selectedType = lineTypeSelector.value;

        const setupField = (wrapper, show, required = false) => {
            if (wrapper) {
                wrapper.style.display = show ? '' : 'none';
                const inputElement = wrapper.querySelector('input, select, textarea');
                if (inputElement) {
                    inputElement.required = required;
                    if (!show) { // Optionally clear value if hidden
                        if (inputElement.tagName === 'SELECT') inputElement.value = '';
                        // else inputElement.value = ''; // For inputs/textareas if needed
                    }
                }
            }
        };
        
        if (selectedType === 'FEE_ITEM') {
            setupField(feeHeadWrapper, true, true); // Fee head is visible and required
            setupField(concessionTypeWrapper, false, false); // Concession type is hidden and not required
        } else if (selectedType === 'CONCESSION_ITEM') {
            setupField(feeHeadWrapper, false, false); // Fee head is hidden and not required
            setupField(concessionTypeWrapper, true, true); // Concession type is visible and required
        } else { // Default state for new/empty rows (which will be 'FEE_ITEM' due to JS default)
            setupField(feeHeadWrapper, true, true);
            setupField(concessionTypeWrapper, false, false);
        }
    }

    function initializeFormRowEventListeners(formRow) {
        if (!formRow) return;
        const lineTypeSelector = formRow.querySelector('select[name$="-line_type"]');
        if (lineTypeSelector) {
            lineTypeSelector.addEventListener('change', function() {
                toggleInvoiceLineFields(formRow);
            });
        }
        toggleInvoiceLineFields(formRow); // Call once to set initial state
    }

    function updateAllItemNumbersAndRemoveButtons() {
        const items = formsetContainer.querySelectorAll('.formset-item-container');
        items.forEach((item, index) => {
            const numberSpan = item.querySelector('.item-number');
            if (numberSpan) {
                numberSpan.textContent = index + 1;
            }
            const removeButton = item.querySelector('.remove-invoice-item-form');
            if (removeButton) {
                // Show remove 'X' button only for forms that are NOT initial forms,
                // OR if they are initial forms but don't have a PK (i.e., truly empty initial extra forms)
                const isInitialPersistedForm = index < initialFormsCount && item.querySelector('input[name$="-id"][value]') ;
                if (isInitialPersistedForm) {
                     removeButton.style.display = 'none'; // Hide for initial forms that have data
                } else {
                    removeButton.style.display = 'block';
                }
            }
        });
    }
    
    function initializeSelect2ForElement(elementContext) {
        if (!elementContext || typeof $ === 'undefined' || !$.fn.select2) return;

        // Initialize Select2 for all select fields with select2-field class or specific selectors
        $(elementContext).find('select.select2-field, select[data-control="select2"], select.fee-head-selector, select.applies-to-selector, select.line-type-selector').each(function() {
            if (!$(this).data('select2')) { // Initialize only if not already initialized
                $(this).select2({
                    theme: "bootstrap-5",
                    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                    placeholder: $(this).data('placeholder') || "Select an option",
                    allowClear: $(this).find('option[value=""]').length > 0, // Allow clear if there's an empty option
                    // dropdownParent: $(this).closest('.modal').length ? $(this).closest('.modal') : $(document.body) // If inside a modal
                });
            }
        });

        // Also initialize main form fields specifically (for invoice form)
        $('#{{ form.student.id_for_label }}, #{{ form.academic_year.id_for_label }}, #{{ form.term.id_for_label }}').each(function() {
            if (!$(this).data('select2')) {
                $(this).select2({
                    theme: "bootstrap-5",
                    width: '100%',
                    placeholder: "Select an option",
                    allowClear: true
                });
            }
        });
    }

    if (addButton) {
        addButton.addEventListener('click', function() {
            if (!totalFormsInput) return;
            const prefix = totalFormsInput.name.split('-')[0]; // e.g., 'details'
            let formIdx = parseInt(totalFormsInput.value);

            const newFormHtml = emptyFormTemplateHTML.replace(/__prefix__/g, formIdx).replace(/__prefix_display__/g, formIdx + 1);
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            const newFormElement = tempDiv.firstElementChild;

            formsetContainer.appendChild(newFormElement);
            
            const newLineTypeSelect = newFormElement.querySelector('select[name$="-line_type"]');
            if (newLineTypeSelect) {
                newLineTypeSelect.value = 'FEE_ITEM'; // Explicitly set to FEE_ITEM for new rows
            }

            initializeSelect2ForElement(newFormElement);
            initializeFormRowEventListeners(newFormElement); 

            totalFormsInput.value = formIdx + 1;
            // currentFormCount = parseInt(totalFormsInput.value); // Update if you use currentFormCount elsewhere
            updateAllItemNumbersAndRemoveButtons();
        });
    }

    formsetContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-invoice-item-form')) {
            event.preventDefault();
            const itemToRemove = event.target.closest('.formset-item-container');
            const deleteCheckbox = itemToRemove.querySelector('input[type="checkbox"][name$="-DELETE"]');

            if (deleteCheckbox && deleteCheckbox.name.indexOf('__prefix__') === -1) { 
                deleteCheckbox.checked = true;
                itemToRemove.style.display = 'none';
            } else {
                itemToRemove.remove();
                // Note: TOTAL_FORMS should ideally be decremented and subsequent forms re-indexed
                // if removing a non-persisted form. Many libraries handle this.
                // For manual handling, it's complex. Simplest is to let server ignore empty forms.
            }
            updateAllItemNumbersAndRemoveButtons();
        }
    });

    // Initialize for existing forms on page load
    document.querySelectorAll('#invoice-items-formset-container .formset-item-container').forEach(formRow => {
        initializeSelect2ForElement(formRow);
        initializeFormRowEventListeners(formRow);
    });
    initializeSelect2ForElement(document.getElementById('invoiceForm')); // For main form selects

    updateAllItemNumbersAndRemoveButtons(); // Initial update
});
</script>
{% endblock %}




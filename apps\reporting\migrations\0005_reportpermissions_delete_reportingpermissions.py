# Generated by Django 5.1.9 on 2025-07-05 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reporting', '0004_alter_reportingpermissions_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'Reporting Permission Set',
                'verbose_name_plural': 'Reporting Permission Sets',
                'permissions': [('view_collection_report', 'Can view Collection Report'), ('view_outstanding_fees_report', 'Can view Outstanding Fees Report'), ('view_student_ledger_report', 'Can view Student Ledger Report'), ('view_payment_summary_report', 'Can view Payment Summary Report'), ('view_trial_balance_report', 'Can view Trial Balance Report'), ('view_income_statement_report', 'Can view Income Statement (P&L)'), ('view_income_expense_report', 'Can view Income & Expense Report'), ('view_balance_sheet_report', 'Can view Balance Sheet Report'), ('view_cash_flow_statement_report', 'Can view Cash Flow Statement Report'), ('view_cash_flow_statement', 'Can view Cash Flow Statement'), ('view_budget_variance_report', 'Can view Budget Variance Report'), ('view_expense_report', 'Can view Expense Report'), ('view_fee_projection_report', 'Can view Fee Projection Report'), ('view_report_dashboard', 'Can view the main reports dashboard page')],
                'managed': False,
                'default_permissions': (),
            },
        ),
        migrations.DeleteModel(
            name='ReportingPermissions',
        ),
    ]

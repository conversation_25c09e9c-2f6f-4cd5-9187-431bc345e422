from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_tenant_model
import logging

# Adjust these imports based on your actual model structure
# from schools.models import PublicUser, StaffUser, ParentUser
# from tenant.models import Client  # or whatever your tenant model is called

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Diagnose and fix user associations across tenants'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Specific email to diagnose/fix'
        )
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specific tenant schema to work with'
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Actually fix issues (dry-run by default)'
        )
        parser.add_argument(
            '--create-staff',
            action='store_true',
            help='Create missing StaffUser associations'
        )
        parser.add_argument(
            '--create-parent',
            action='store_true',
            help='Create missing ParentUser associations'
        )
        parser.add_argument(
            '--list-all',
            action='store_true',
            help='List all user associations across tenants'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Verbose output'
        )

    def handle(self, *args, **options):
        self.verbosity = options.get('verbosity', 1)
        self.verbose = options.get('verbose', False)
        self.dry_run = not options.get('fix', False)
        
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made. Use --fix to apply changes.')
            )
        
        try:
            if options.get('list_all'):
                self.list_all_associations()
            elif options.get('email'):
                self.diagnose_user(
                    email=options['email'],
                    tenant_schema=options.get('tenant'),
                    create_staff=options.get('create_staff', False),
                    create_parent=options.get('create_parent', False)
                )
            else:
                self.find_orphaned_users()
                
        except Exception as e:
            logger.exception("Command failed")
            raise CommandError(f'Command failed: {str(e)}')

    def diagnose_user(self, email, tenant_schema=None, create_staff=False, create_parent=False):
        """Diagnose a specific user's associations"""
        self.stdout.write(f"\n=== DIAGNOSING USER: {email} ===")
        
        # Check if PublicUser exists
        try:
            # Replace with your actual PublicUser model
            from your_app.models import PublicUser
            public_user = PublicUser.objects.get(email=email)
            self.stdout.write(
                self.style.SUCCESS(f"✓ PublicUser found: {public_user.email} (ID: {public_user.id})")
            )
        except PublicUser.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"✗ PublicUser with email '{email}' does not exist")
            )
            return
        
        # Get all tenants or specific tenant
        Tenant = get_tenant_model()
        if tenant_schema:
            tenants = Tenant.objects.filter(schema_name=tenant_schema)
            if not tenants.exists():
                self.stdout.write(
                    self.style.ERROR(f"✗ Tenant '{tenant_schema}' not found")
                )
                return
        else:
            tenants = Tenant.objects.exclude(schema_name='public')
        
        for tenant in tenants:
            self.stdout.write(f"\n--- TENANT: {tenant.schema_name} ({tenant.name}) ---")
            
            with schema_context(tenant.schema_name):
                self.check_tenant_associations(
                    public_user, tenant, create_staff, create_parent
                )

    def check_tenant_associations(self, public_user, tenant, create_staff=False, create_parent=False):
        """Check user associations within a specific tenant"""
        from your_app.models import StaffUser, ParentUser  # Adjust imports
        
        # Check StaffUser association
        try:
            staff_user = StaffUser.objects.get(public_user=public_user)
            self.stdout.write(
                self.style.SUCCESS(f"  ✓ StaffUser found (ID: {staff_user.id})")
            )
            if self.verbose:
                self.stdout.write(f"      Created: {staff_user.created_at}")
                # Add more staff-specific details as needed
        except StaffUser.DoesNotExist:
            self.stdout.write(
                self.style.WARNING(f"  ✗ StaffUser missing for {public_user.email}")
            )
            if create_staff:
                self.create_staff_user(public_user, tenant)
        
        # Check ParentUser association
        try:
            parent_user = ParentUser.objects.get(email=public_user.email)
            self.stdout.write(
                self.style.SUCCESS(f"  ✓ ParentUser found (ID: {parent_user.id})")
            )
        except ParentUser.DoesNotExist:
            self.stdout.write(
                self.style.WARNING(f"  ✗ ParentUser missing for {public_user.email}")
            )
            if create_parent:
                self.create_parent_user(public_user, tenant)

    def create_staff_user(self, public_user, tenant):
        """Create StaffUser association"""
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING(f"    [DRY RUN] Would create StaffUser for {public_user.email}")
            )
            return
        
        try:
            with transaction.atomic():
                from your_app.models import StaffUser
                
                staff_user = StaffUser.objects.create(
                    public_user=public_user,
                    # Add default values for required fields
                    # first_name=public_user.first_name or '',
                    # last_name=public_user.last_name or '',
                    # is_active=True,
                )
                self.stdout.write(
                    self.style.SUCCESS(f"    ✓ Created StaffUser (ID: {staff_user.id})")
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"    ✗ Failed to create StaffUser: {str(e)}")
            )

    def create_parent_user(self, public_user, tenant):
        """Create ParentUser association"""
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING(f"    [DRY RUN] Would create ParentUser for {public_user.email}")
            )
            return
        
        try:
            with transaction.atomic():
                from your_app.models import ParentUser
                
                parent_user = ParentUser.objects.create(
                    email=public_user.email,
                    # Add default values for required fields
                    # first_name=public_user.first_name or '',
                    # last_name=public_user.last_name or '',
                    # is_active=True,
                )
                self.stdout.write(
                    self.style.SUCCESS(f"    ✓ Created ParentUser (ID: {parent_user.id})")
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"    ✗ Failed to create ParentUser: {str(e)}")
            )

    def find_orphaned_users(self):
        """Find users with missing associations across all tenants"""
        self.stdout.write("\n=== FINDING ORPHANED USERS ===")
        
        from your_app.models import PublicUser
        Tenant = get_tenant_model()
        
        public_users = PublicUser.objects.all()
        tenants = Tenant.objects.exclude(schema_name='public')
        
        orphaned_count = 0
        
        for tenant in tenants:
            self.stdout.write(f"\n--- Checking Tenant: {tenant.schema_name} ---")
            
            with schema_context(tenant.schema_name):
                from your_app.models import StaffUser, ParentUser
                
                # Find PublicUsers without StaffUser associations
                staff_user_emails = set(
                    StaffUser.objects.values_list('public_user__email', flat=True)
                )
                parent_user_emails = set(
                    ParentUser.objects.values_list('email', flat=True)
                )
                
                for public_user in public_users:
                    issues = []
                    
                    if public_user.email not in staff_user_emails:
                        issues.append("Missing StaffUser")
                    
                    if public_user.email not in parent_user_emails:
                        issues.append("Missing ParentUser")
                    
                    if issues:
                        orphaned_count += 1
                        self.stdout.write(
                            self.style.WARNING(
                                f"  {public_user.email}: {', '.join(issues)}"
                            )
                        )
        
        if orphaned_count == 0:
            self.stdout.write(
                self.style.SUCCESS("✓ No orphaned users found!")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"Found {orphaned_count} orphaned user associations")
            )

    def list_all_associations(self):
        """List all user associations across tenants"""
        self.stdout.write("\n=== ALL USER ASSOCIATIONS ===")
        
        from your_app.models import PublicUser
        Tenant = get_tenant_model()
        
        tenants = Tenant.objects.exclude(schema_name='public')
        
        for tenant in tenants:
            self.stdout.write(f"\n--- TENANT: {tenant.schema_name} ({tenant.name}) ---")
            
            with schema_context(tenant.schema_name):
                from your_app.models import StaffUser, ParentUser
                
                staff_users = StaffUser.objects.select_related('public_user').all()
                parent_users = ParentUser.objects.all()
                
                self.stdout.write(f"Staff Users ({staff_users.count()}):")
                for staff_user in staff_users:
                    self.stdout.write(f"  - {staff_user.public_user.email} (ID: {staff_user.id})")
                
                self.stdout.write(f"Parent Users ({parent_users.count()}):")
                for parent_user in parent_users:
                    self.stdout.write(f"  - {parent_user.email} (ID: {parent_user.id})")


# Example usage commands:
"""
# Diagnose specific user
python manage.py fix_user_associations --email <EMAIL> --tenant zharatest --verbose

# Fix specific user (create missing StaffUser)
python manage.py fix_user_associations --email <EMAIL> --tenant zharatest --create-staff --fix

# Find all orphaned users
python manage.py fix_user_associations

# List all associations
python manage.py fix_user_associations --list-all

# Dry run for specific user with all fixes
python manage.py fix_user_associations --email <EMAIL> --create-staff --create-parent
"""



from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import EventCategory, SchoolEvent, EventAttendee


@admin.register(EventCategory)
class EventCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'color_display', 'icon_display', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['is_active']
    ordering = ['name']

    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; border-radius: 3px;">{}</span>',
            obj.color,
            obj.color
        )
    color_display.short_description = _('Color')

    def icon_display(self, obj):
        return format_html('<i class="{}"></i> {}', obj.icon, obj.icon)
    icon_display.short_description = _('Icon')


class EventAttendeeInline(admin.TabularInline):
    model = EventAttendee
    extra = 0
    readonly_fields = ['rsvp_date']
    fields = ['user', 'rsvp_status', 'rsvp_date', 'notes']


@admin.register(SchoolEvent)
class SchoolEventAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'event_type', 'start_date', 'end_date', 
        'priority_display', 'is_public', 'is_active', 'created_by'
    ]
    list_filter = [
        'event_type', 'priority', 'is_public', 'is_active', 
        'visible_to_parents', 'visible_to_staff', 'visible_to_students',
        'start_date', 'category', 'created_at'
    ]
    search_fields = ['title', 'description', 'location']
    list_editable = ['is_public', 'is_active']
    date_hierarchy = 'start_date'
    ordering = ['-start_date', '-created_at']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'description', 'category', 'event_type', 'priority')
        }),
        (_('Date & Time'), {
            'fields': (
                ('start_date', 'end_date'),
                ('start_time', 'end_time'),
                'is_all_day'
            )
        }),
        (_('Location'), {
            'fields': ('location', 'venue_details'),
            'classes': ('collapse',)
        }),
        (_('Recurrence'), {
            'fields': ('recurrence', 'recurrence_end_date'),
            'classes': ('collapse',)
        }),
        (_('Visibility & Permissions'), {
            'fields': (
                'is_public',
                ('visible_to_parents', 'visible_to_staff', 'visible_to_students')
            )
        }),
        (_('RSVP & Attendance'), {
            'fields': (
                'requires_rsvp', 'max_attendees',
                ('contact_person', 'contact_email', 'contact_phone')
            ),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_by', 'is_active'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ['created_at', 'updated_at']
    inlines = [EventAttendeeInline]
    
    def priority_display(self, obj):
        colors = {
            'LOW': '#28a745',
            'MEDIUM': '#ffc107', 
            'HIGH': '#fd7e14',
            'URGENT': '#dc3545'
        }
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">{}</span>',
            colors.get(obj.priority, '#6c757d'),
            obj.get_priority_display()
        )
    priority_display.short_description = _('Priority')

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new event
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'category', 'created_by'
        ).prefetch_related('attendees')


@admin.register(EventAttendee)
class EventAttendeeAdmin(admin.ModelAdmin):
    list_display = ['event', 'user', 'rsvp_status', 'rsvp_date']
    list_filter = ['rsvp_status', 'rsvp_date', 'event__event_type']
    search_fields = ['event__title', 'user__email', 'user__first_name', 'user__last_name']
    date_hierarchy = 'rsvp_date'
    ordering = ['-rsvp_date']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('event', 'user')

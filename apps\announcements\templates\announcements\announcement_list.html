{# D:\school_fees_saas_v2\apps\announcements\templates\announcements\announcement_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Announcements") }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Add any list-specific styles if needed #}
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0">{{ view_title|default:_("School Announcements") }}</h2>
        {# Add permission check if you have one for creating announcements #}
        {% if perms.announcements.add_announcement %} 
        <a href="{% url 'tenant_announcements:create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle-fill me-1"></i> {% trans "New Announcement" %}
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-body">
            {% if announcements %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Title" %}</th>
                                <th>{% trans "Audience" %}</th>
                                <th>{% trans "Author" %}</th>
                                <th>{% trans "Published" %}</th>
                                <th>{% trans "Expires" %}</th>
                                <th class="text-center">{% trans "Sticky" %}</th>
                                <th class="text-center">{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for announcement in announcements %}
                            <tr>
                                <td>
                                    <a href="{% url 'tenant_announcements:announcement_detail' pk=announcement.pk %}">{{ announcement.title }}</a>
                                </td>
                                <td><small>{{ announcement.get_audience_display }}</small></td>
                                <td><small>{{ announcement.author.get_full_name|default:announcement.author.email|default:"N/A" }}</small></td>
                                <td><small>{{ announcement.publish_date|date:"d M Y, P" }}</small></td>
                                <td><small>{{ announcement.expiry_date|date:"d M Y, P"|default:"Never" }}</small></td>
                                <td class="text-center">
                                    {% if announcement.is_sticky %}<i class="bi bi-pin-angle-fill text-warning"></i>{% else %}<i class="bi bi-pin-angle text-muted"></i>{% endif %}
                                </td>
                                <td class="text-center">
                                    {% if announcement.is_published and announcement.is_currently_visible %}
                                        <span class="badge bg-success">{% trans "Visible" %}</span>
                                    {% elif announcement.is_published and not announcement.is_currently_visible %}
                                        <span class="badge bg-secondary">{% trans "Scheduled/Expired" %}</span>
                                    {% else %}
                                        <span class="badge bg-warning text-dark">{% trans "Draft" %}</span>
                                    {% endif %}
                                </td>
                                <td class="actions-column">
                                    <a href="{% url 'tenant_announcements:announcement_detail' pk=announcement.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View' %}"><i class="bi bi-eye"></i></a>
                                    {% if perms.announcements.change_announcement %} {# Add perm check #}
                                    <a href="{% url 'tenant_announcements:update' pk=announcement.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit' %}"><i class="bi bi-pencil-square"></i></a>
                                    {% endif %}
                                    {% if perms.announcements.delete_announcement %} {# Add perm check #}
                                    <a href="{% url 'tenant_announcements:delete' pk=announcement.pk %}" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}"><i class="bi bi-trash"></i></a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" %} {# If you have a pagination partial #}
            {% else %}
                <p class="text-center text-muted p-3">{% trans "No announcements found." %}</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}







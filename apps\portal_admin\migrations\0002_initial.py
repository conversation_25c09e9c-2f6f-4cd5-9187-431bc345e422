# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('portal_admin', '0001_initial'),
        ('schools', '0001_initial'),
        ('tenants', '0003_alter_school_is_active_alter_school_slug'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='adminactivitylog',
            name='staff_user',
            field=models.ForeignKey(blank=True, help_text='The tenant staff user who performed the action.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='staff_activity_logs', to='schools.staffuser'),
        ),
        migrations.AddField(
            model_name='adminactivitylog',
            name='target_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype', verbose_name='target model type'),
        ),
        migrations.AddField(
            model_name='adminactivitylog',
            name='tenant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='tenants.school', verbose_name='Associated School/Tenant'),
        ),
        migrations.AddField(
            model_name='adminactivitylog',
            name='user',
            field=models.ForeignKey(blank=True, help_text='The public admin user (platform owner/admin) who performed the action.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='public_admin_activity_logs', to=settings.AUTH_USER_MODEL),
        ),
    ]

# apps/hr/services.py

import decimal
from .models import TaxBracket, StatutoryDeduction

# The standard Python Decimal type is perfect for financial calculations
Decimal = decimal.Decimal

def calculate_progressive_tax(annual_gross_salary: Decimal) -> Decimal:
    """
    Calculates the total annual income tax based on a progressive bracket system.
    This version matches the user's specific TaxBracket model.
    """
    taxable_income = annual_gross_salary
    
    # Find the single bracket where the annual salary fits.
    correct_bracket = TaxBracket.objects.filter(
        is_active=True,
        from_amount__lte=taxable_income,
    ).filter(
        Q(to_amount__gte=taxable_income) | Q(to_amount__isnull=True)
    ).first()

    if not correct_bracket:
        return Decimal('0.00') # No tax if income is below the lowest bracket

    # The formula is now: (Taxable Income * Rate) - Flat Deduction
    # This is a common way tax is calculated in some regions.
    rate_as_decimal = correct_bracket.rate_percent / Decimal('100')
    total_tax = (taxable_income * rate_as_decimal) - correct_bracket.deduction_amount
    
    # Ensure tax is not negative
    return max(total_tax, Decimal('0.00'))


# def calculate_progressive_tax(annual_gross_salary: Decimal) -> Decimal:
#     """
#     Calculates the total annual income tax based on a progressive bracket system.
    
#     Args:
#         annual_gross_salary: The total gross salary for the year.

#     Returns:
#         The total annual tax amount.
#     """
#     taxable_income = annual_gross_salary
#     total_tax = Decimal('0.00')

#     # Fetch all tax brackets, ordered by income level
#     brackets = TaxBracket.objects.order_by('min_annual_income')

#     if not brackets.exists():
#         # If no brackets are defined, return zero tax
#         return total_tax

#     for bracket in brackets:
#         # Check if the income falls into this bracket
#         if taxable_income > bracket.min_annual_income:
#             # The upper bound for calculation in this bracket
#             # If max_annual_income is None, it's the top bracket, so use all remaining income.
#             upper_bound = bracket.max_annual_income or taxable_income
            
#             # The amount of income to be taxed *in this bracket*
#             income_in_bracket = min(taxable_income, upper_bound) - bracket.min_annual_income
            
#             # Calculate tax for this portion of the income
#             tax_for_bracket = (income_in_bracket * (bracket.rate / Decimal('100'))) + bracket.base_tax_amount
            
#             # The logic here assumes a "slab" system where the rate applies to the income
#             # *within* the slab. A different system (like a flat rate on all income
#             # once a threshold is met) would require different logic. This is a common method.
#             # For simplicity in this example, we'll apply a common interpretation:
#             # Find the single correct bracket and apply its rules.

#             # Let's use a simpler, more common tax calculation method for payroll systems:
#             # Find the single bracket the total income falls into and apply its formula.
#             # This is often how payroll software interprets tax tables for simplicity.
#             pass # We will replace this logic below.

#     # --- A More Standard Payroll Calculation Method ---
#     # Find the single bracket where the annual salary fits.
#     # This is a common simplification for payroll, though less precise than a true slab calculation.
#     # It assumes the rate and base amount are for the *entire* salary once it hits that bracket.

#     correct_bracket = TaxBracket.objects.filter(
#         min_annual_income__lte=taxable_income,
#     ).filter(
#         models.Q(max_annual_income__gte=taxable_income) | models.Q(max_annual_income__isnull=True)
#     ).first()

#     if not correct_bracket:
#         return Decimal('0.00') # No tax if income is below the lowest bracket

#     # Calculate tax based on the found bracket's formula
#     # This formula assumes the rate applies to the amount *over* the minimum.
#     # Example: "25% of income over $50,000, plus a base of $5,000"
#     income_over_min = taxable_income - correct_bracket.min_annual_income
#     rate_tax = income_over_min * (correct_bracket.rate / Decimal('100'))
#     total_tax = correct_bracket.base_tax_amount + rate_tax
    
#     return max(total_tax, Decimal('0.00')) # Ensure tax is not negative


def calculate_statutory_deductions(monthly_gross_salary: Decimal) -> list:
    """
    Calculates all active statutory deductions.
    
    Returns:
        A list of dictionaries, each representing a deduction.
        Example: [{'name': 'Pension Fund', 'payslip_label': 'Pension', 'amount': '250.00'}]
    """
    deductions_list = []
    active_deductions = StatutoryDeduction.objects.filter(is_active=True)

    for deduction in active_deductions:
        amount = monthly_gross_salary * (deduction.employee_contribution_rate / Decimal('100'))
        deductions_list.append({
            'name': deduction.name,
            'payslip_label': deduction.payslip_label or deduction.name, # Fallback to full name
            'amount': str(amount.quantize(Decimal('0.01')))
        })

    return deductions_list

# def calculate_statutory_deductions(monthly_gross_salary: Decimal) -> dict:
#     """
#     Calculates all active statutory deductions based on a percentage of gross salary.
    
#     Args:
#         monthly_gross_salary: The gross salary for the month.

#     Returns:
#         A dictionary of deduction names and their calculated amounts.
#         Example: {'Pension': Decimal('250.00'), 'Social Security': Decimal('120.50')}
#     """
#     deductions_dict = {}
#     active_deductions = StatutoryDeduction.objects.filter(is_active=True)

#     for deduction in active_deductions:
#         amount = monthly_gross_salary * (deduction.employee_contribution_rate / Decimal('100'))
#         deductions_dict[deduction.name] = amount.quantize(Decimal('0.01')) # Round to 2 decimal places

#     return deductions_dict


class PayrollCalculationService:
    """
    Orchestrates the entire calculation for a single payslip.
    """
    def __init__(self, monthly_gross_salary: Decimal, one_off_adjustments: Decimal = Decimal('0.00')):
        self.monthly_gross = monthly_gross_salary
        self.annual_gross = monthly_gross_salary * 12
        self.adjustments = one_off_adjustments
        
        # This will hold the results
        self.results = {}

    
    def calculate(self):
        # 1. Calculate Tax
        annual_tax = calculate_progressive_tax(self.annual_gross)
        monthly_tax = (annual_tax / 12).quantize(Decimal('0.01'))
        
        # 2. Calculate Other Statutory Deductions
        other_deductions_list = calculate_statutory_deductions(self.monthly_gross)
        
        # 3. Sum up all deductions
        # Convert string amounts back to Decimal for summing
        total_other_deductions = sum(Decimal(d['amount']) for d in other_deductions_list)
        total_deductions = monthly_tax + total_other_deductions

        # 4. Calculate Net Pay
        net_pay = self.monthly_gross - total_deductions + self.adjustments

        # 5. Store all results
        self.results = {
            'gross_salary': self.monthly_gross,
            'tax_deducted': monthly_tax,
            'other_deductions': other_deductions_list, # THIS MUST BE A LIST
            'total_deductions': total_deductions,
            'adjustments': self.adjustments,
            'net_pay': net_pay,
        }
        
        return self.results
    
    
    # def calculate(self):
    #     # 1. Calculate Tax
    #     annual_tax = calculate_progressive_tax(self.annual_gross)
    #     monthly_tax = (annual_tax / 12).quantize(Decimal('0.01'))
        
    #     # 2. Calculate Other Statutory Deductions
    #     other_deductions_dict = calculate_statutory_deductions(self.monthly_gross)
        
    #     # 3. Sum up all deductions
    #     total_other_deductions = sum(other_deductions_dict.values())
    #     total_deductions = monthly_tax + total_other_deductions

    #     # 4. Calculate Net Pay
    #     # Note: one-off adjustments can be positive (bonus) or negative (deduction)
    #     net_pay = self.monthly_gross - total_deductions + self.adjustments

    #     # 5. Store all results
    #     self.results = {
    #         'gross_salary': self.monthly_gross,
    #         'tax_deducted': monthly_tax,
    #         'other_deductions': {k: str(v) for k, v in other_deductions_dict.items()}, # Convert Decimals to strings for JSON
    #         'total_deductions': total_deductions,
    #         'adjustments': self.adjustments,
    #         'net_pay': net_pay,
    #     }
        
    #     return self.results
    
    
    
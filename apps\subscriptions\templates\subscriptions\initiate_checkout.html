{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\initiate_checkout.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize widget_tweaks %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Subscription") }}{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'subscriptions:subscription_details' %}">{% trans "My Subscription" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'subscriptions:select_plan' %}">{% trans "Select Plan" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Confirm" %}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-7">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "Confirm Your Subscription Choice" %}</h5>
                </div>
                <div class="card-body p-4">
                    <h4 class="card-title text-center">{{ plan.name }}</h4>
                    <p class="text-center fs-3 fw-bold">
                        {{ school_currency_symbol|default:'$' }}{{ display_price|floatformat:2|intcomma }}
                        <span class="fs-6 text-muted">/ {{ display_cycle_name }}</span>
                    </p>

                    <p class="text-center text-muted mb-4">{{ plan.description|default:"" }}</p>
                    
                    <h5>{% trans "Features Included:" %}</h5>
                    <ul class="list-group list-group-flush mb-4">
                        {% for feature in plan.features.all %}
                            <li class="list-group-item"><i class="bi bi-check-circle text-success me-2"></i>{{ feature.name }}</li>
                        {% empty %}
                            <li class="list-group-item">{% trans "Standard platform features." %}</li>
                        {% endfor %}
                    </ul>

                    <form method="post">
                        {% csrf_token %}
                        {% if form.non_field_errors %}<div class="alert alert-danger">{{ form.non_field_errors }}</div>{% endif %}
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ form.billing_cycle.label }}</label>
                            {{ form.billing_cycle.errors }}
                            <div>
                            {% for radio in form.billing_cycle %}
                                <div class="form-check form-check-inline">
                                    {{ radio.tag }}
                                    <label for="{{ radio.id_for_label }}" class="form-check-label">{{ radio.choice_label }}</label>
                                </div>
                            {% endfor %}
                            </div>
                        </div>

                        <hr>
                        <p class="text-muted small">
                            {% if plan.trial_period_days > 0 %}
                                {% blocktrans with days=plan.trial_period_days %}
                                By confirming, you will start your {{ days }}-day free trial. No payment is required now.
                                You can cancel anytime during the trial.
                                {% endblocktrans %}
                            {% else %}
                                {% trans "By confirming, you agree to subscribe to this plan. You will be redirected to our secure payment gateway to complete the process." %}
                            {% endif %}
                        </p>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                {% if plan.trial_period_days > 0 %}
                                    <i class="bi bi-play-circle-fill me-1"></i> {% trans "Start Free Trial" %}
                                {% else %}
                                    <i class="bi bi-credit-card-fill me-1"></i> {% trans "Proceed to Payment Simulation" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'subscriptions:select_plan' %}" class="btn btn-outline-secondary">{% trans "Change Plan or Cycle" %}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}



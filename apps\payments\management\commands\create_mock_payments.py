# D:\school_fees_saas_v2\apps\payments\management\commands\create_mock_payments.py

import random
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django_tenants.utils import schema_context

# --- Import necessary models and the new utility function ---
try:
    from apps.tenants.models import School
    from apps.fees.models import Invoice
    from apps.payments.models import PaymentMethod
    from apps.payments.utils import record_payment
except ImportError:
    raise CommandError("Could not import necessary models. Ensure apps are installed and configured.")


class Command(BaseCommand):
    help = 'Creates mock payments for outstanding invoices for a specific tenant.'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name of the tenant to process.')
        parser.add_argument(
            '--percentage',
            type=int,
            default=100,
            help='The percentage of the outstanding balance to pay (e.g., 75 for 75%%). Defaults to 100.'
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=10,
            help='The maximum number of invoices to process. Defaults to 10.'
        )

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        percentage_to_pay = options['percentage']
        limit = options['limit']

        try:
            tenant = School.objects.get(schema_name=schema_name)
        except School.DoesNotExist:
            raise CommandError(f'Tenant with schema name "{schema_name}" does not exist.')

        with schema_context(tenant.schema_name):
            self.stdout.write(self.style.SUCCESS(f'--- Processing tenant: {tenant.name} ({tenant.schema_name}) ---'))

            # Get a default payment method to use for the mock payments
            # Ensure at least one payment method exists for this tenant!
            payment_method = PaymentMethod.objects.filter(is_active=True).first()
            if not payment_method:
                raise CommandError(f'No active payment methods found for tenant "{schema_name}". Please create one first.')
            self.stdout.write(f"Using payment method: '{payment_method.name}'")

            # Find outstanding invoices
            outstanding_statuses = [
                    'SENT',
                    'PARTIALLY_PAID',
                    'OVERDUE'
                ]
            outstanding_invoices = Invoice.objects.filter(status__in=outstanding_statuses).order_by('due_date')[:limit]
            
            if not outstanding_invoices.exists():
                self.stdout.write(self.style.WARNING('No outstanding invoices found to pay.'))
                return

            self.stdout.write(f'Found {outstanding_invoices.count()} outstanding invoices to process (limit was {limit}).')

            created_count = 0
            for invoice in outstanding_invoices:
                balance_due = invoice.total_amount - invoice.amount_paid
                
                if balance_due <= 0:
                    self.stdout.write(f"Skipping Invoice #{invoice.invoice_number} as its balance is already zero or less.")
                    continue
                
                amount_to_pay = (balance_due * Decimal(percentage_to_pay / 100)).quantize(Decimal('0.01'))

                self.stdout.write(
                    f"Processing Invoice #{invoice.invoice_number} for {invoice.student.full_name}..."
                    f" Balance Due: {balance_due:.2f}, Paying: {amount_to_pay:.2f} ({percentage_to_pay}%)"
                )

                # Call the utility function to record the payment
                payment_obj = record_payment(
                    invoice=invoice,
                    amount_paid=amount_to_pay,
                    payment_method=payment_method,
                    notes=f"Mock payment generated by management command.",
                    reference_number=f"MOCK-{invoice.pk}-{random.randint(1000, 9999)}",
                    parent_payer=invoice.student.parents.first() # Get the first linked parent as payer
                )

                if payment_obj:
                    self.stdout.write(self.style.SUCCESS(f'  -> Successfully created Payment #{payment_obj.pk}. Invoice status is now: {invoice.get_status_display()}'))
                    created_count += 1
                else:
                    self.stdout.write(self.style.ERROR(f'  -> FAILED to create payment for Invoice #{invoice.invoice_number}. Check error logs.'))

            self.stdout.write(self.style.SUCCESS(f'--- Finished. Created {created_count} mock payments. ---'))
            
            
            
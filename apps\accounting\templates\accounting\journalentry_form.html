{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'libs/select2/css/select2.min.css' %}">
    <link rel="stylesheet" href="{% static 'libs/select2-bootstrap-5-theme/select2-bootstrap-5-theme.min.css' %}">
    <style>
        .formset-row td {
            vertical-align: top;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }
        .formset-row .form-control-sm, 
        .formset-row .form-select-sm,
        .formset-row .select2-container .select2-selection--single {
            font-size: 0.875rem;
            height: calc(1.5em + .5rem + 2px);
        }
        .formset-row .select2-container .select2-selection--single .select2-selection__rendered {
            line-height: calc(1.5em + .5rem);
        }
        .formset-row .jel-debit, .formset-row .jel-credit {
            min-width: 100px;
            text-align: right;
        }
        .delete-form-checkbox input[type="checkbox"] { /* More specific selector for checkbox itself */
            margin-top: 0.3rem; 
        }
        #lines-table tfoot td {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
        .form-label {
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
    </style>
{% endblock extra_tenant_css %}

{% block tenant_specific_content %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <a href="{% if object %}{% url 'accounting:journalentry_detail' pk=object.pk %}{% else %}{% url 'accounting:journalentry_list' %}{% endif %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> {% if object %}{% trans "Back to Details" %}{% else %}{% trans "Back to Journal Entries" %}{% endif %}
        </a>
    </div>

    <form method="post" id="journalEntryForm" novalidate>
        {% csrf_token %}
        
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Header" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label|default:_("Entry Date") }}</label>
                        {{ form.date }}
                        {% for error in form.date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label|default:_("Description / Narration") }}</label>
                        {{ form.description }}
                        {% for error in form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="{{ form.reference_number.id_for_label }}" class="form-label">{{ form.reference_number.label|default:_("Reference Number") }}</label>
                        {{ form.reference_number }}
                        {% for error in form.reference_number.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                </div>
                {% for error in form.non_field_errors %}<div class="alert alert-danger mt-2">{{ error }}</div>{% endfor %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Lines" %}</h5>
            </div>
            <div class="card-body">
                {{ line_formset.management_form }}
                {% if line_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in line_formset.non_form_errors %}
                            {{ error|escape }}<br>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="table-responsive">
                    <table class="table table-sm" id="lines-table">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 35%;">{% trans "Account" %}</th>
                                <th style="width: 17%;" class="text-end">{% trans "Debit" %}</th>
                                <th style="width: 17%;" class="text-end">{% trans "Credit" %}</th>
                                <th style="width: 26%;">{% trans "Line Description" %}</th>
                                {% if line_formset.can_delete %}<th class="text-center align-middle">{% trans "Del?" %}</th>{% endif %}
                            </tr>
                        </thead>
                        <tbody class="formset-lines-container">
                            {% for form_line in line_formset %}
                            <tr class="formset-row {% if form_line.errors %}table-danger{% endif %}" id="{{ line_formset.prefix }}-{{ forloop.counter0 }}">
                                <td>
                                    {{ form_line.id }}
                                    {{ form_line.account }}
                                    {% for error in form_line.account.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.debit_amount }}
                                    {% for error in form_line.debit_amount.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.credit_amount }}
                                    {% for error in form_line.credit_amount.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.description }}
                                    {% for error in form_line.description.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                {% if line_formset.can_delete %}
                                <td class="text-center align-middle delete-form-checkbox">
                                    {% if form_line.instance.pk or line_formset.can_delete_extra %}
                                        {{ form_line.DELETE }}
                                    {% endif %}
                                </td>
                                {% endif %}
                            </tr>
                            {% if form_line.non_field_errors %}
                                <tr><td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="p-0">
                                    <div class="alert alert-danger py-1 px-2 small mb-0 border-0 rounded-0">
                                    {% for error in form_line.non_field_errors %}{{ error|escape }}{% endfor %}
                                    </div>
                                </td></tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td class="text-end fw-bold">{% trans "Totals:" %}</td>
                                <td class="text-end fw-bold" id="total-debit">0.00</td>
                                <td class="text-end fw-bold" id="total-credit">0.00</td>
                                <td class="text-end fw-bold" colspan="{% if line_formset.can_delete %}2{% else %}1{% endif %}" id="balance-status"></td>
                            </tr>
                            <tr>
                                <td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="text-end pt-3">
                                    <button type="button" class="btn btn-sm btn-outline-success add-formset-row">
                                        <i class="bi bi-plus-circle"></i> {% trans "Add Line" %}
                                    </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-success btn-lg">
                <i class="bi bi-save-fill me-1"></i> 
                {% if object %}{% trans "Save Draft Changes" %}{% else %}{% trans "Save Draft Journal Entry" %}{% endif %}
            </button>
            <a href="{% if object %}{% url 'accounting:journalentry_detail' pk=object.pk %}{% else %}{% url 'accounting:journalentry_list' %}{% endif %}" class="btn btn-outline-secondary ms-2">{% trans "Cancel" %}</a>
        </div>
    </form>

    <template id="formset-empty-form-template">
        {% with form_line=line_formset.empty_form %}
        {# This content will be cloned by JS. The id of the <tr> will be set by JS. #}
        <tr class="formset-row"> 
            <td>
                {{ form_line.id }} {# Renders <input type="hidden" name="lines-__prefix__-id" id="id_lines-__prefix__-id"> #}
                {{ form_line.account }} {# Renders select and other widget parts for account #}
            </td>
            <td>{{ form_line.debit_amount }}</td>
            <td>{{ form_line.credit_amount }}</td>
            <td>{{ form_line.description }}</td>
            {% if line_formset.can_delete and line_formset.can_delete_extra %}
            <td class="text-center align-middle delete-form-checkbox">
                {{ form_line.DELETE }} {# Renders <input type="checkbox" name="lines-__prefix__-DELETE" id="id_lines-__prefix__-DELETE"> #}
            </td>
            {% endif %}
        </tr>
        {% endwith %}
    </template>

{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
    {{ block.super }}
    <script src="{% static 'libs/select2/js/select2.full.min.js' %}"></script> {# Use .full.min.js for all features if needed #}
    <script src="https://cdn.jsdelivr.net/npm/decimal.js@10.4.3/decimal.min.js"></script> 
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const formsetPrefix = '{{ line_formset.prefix }}';

        function initializeSelect2(element) {
            if (!element || !jQuery.fn.select2) return; // Check if jQuery and select2 are loaded
            $(element).select2({
                theme: 'bootstrap-5',
                width: $(element).data('width') ? $(element).data('width') : $(element).hasClass('w-100') ? '100%' : 'style',
                placeholder: "{% trans 'Select an account' %}",
                allowClear: true
            });
        }

        function rebindEventListeners(rowElem) {
            rowElem.querySelectorAll('.jel-debit, .jel-credit').forEach(function(input) {
                input.removeEventListener('input', updateTotalsAndBalance);
                input.removeEventListener('change', updateTotalsAndBalance);
                input.addEventListener('input', updateTotalsAndBalance);
                input.addEventListener('change', updateTotalsAndBalance);
            });
            const deleteCheckbox = rowElem.querySelector('input[type="checkbox"][name$="-DELETE"]');
            if (deleteCheckbox) {
                deleteCheckbox.removeEventListener('change', handleDeleteCheckboxChange);
                deleteCheckbox.addEventListener('change', handleDeleteCheckboxChange);
            }
        }
        
        function handleDeleteCheckboxChange(event) {
            updateTotalsAndBalance();
            const row = event.target.closest('.formset-row');
            if (!row) return;
            row.style.opacity = event.target.checked ? 0.5 : 1;
            row.querySelectorAll('input:not([type="checkbox"]), select, textarea').forEach(el => {
                el.disabled = event.target.checked;
                if (event.target.checked && el.classList.contains('is-invalid')) {
                    el.classList.remove('is-invalid');
                    let errorMsg = el.nextElementSibling;
                    while(errorMsg && (errorMsg.classList.contains('invalid-feedback') || errorMsg.classList.contains('text-danger'))) {
                        errorMsg.textContent = ''; // Clear specific field errors visually
                        errorMsg = errorMsg.nextElementSibling;
                    }
                }
            });
            // If unchecking, re-enable. Any validation errors will be re-applied on server side if still present.
        }

        document.querySelectorAll('.formset-lines-container .formset-row').forEach(function(row) {
            initializeSelect2(row.querySelector('.account-select')); // Make sure your select has this class
            rebindEventListeners(row);
        });

        function updateTotalsAndBalance() {
            let totalDebit = new Decimal(0);
            let totalCredit = new Decimal(0);
            document.querySelectorAll('.formset-lines-container .formset-row').forEach(function(row) {
                const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
                if (deleteCheckbox && deleteCheckbox.checked) {
                    return; 
                }
                const debitInput = row.querySelector('.jel-debit');
                const creditInput = row.querySelector('.jel-credit');
                if (debitInput && debitInput.value) {
                    try { totalDebit = totalDebit.plus(new Decimal(debitInput.value || 0)); } catch (e) { /* ignore invalid input for live sum */ }
                }
                if (creditInput && creditInput.value) {
                     try { totalCredit = totalCredit.plus(new Decimal(creditInput.value || 0)); } catch (e) { /* ignore invalid input for live sum */ }
                }
            });
            const debitCell = document.getElementById('total-debit');
            const creditCell = document.getElementById('total-credit');
            const balanceCell = document.getElementById('balance-status');

            if(debitCell) debitCell.textContent = totalDebit.toFixed(2);
            if(creditCell) creditCell.textContent = totalCredit.toFixed(2);

            if (balanceCell) {
                if (totalDebit.equals(totalCredit)) {
                    balanceCell.innerHTML = '<span class="badge bg-success fs-6">{% trans "Balanced" %}</span>';
                } else {
                    const diff = totalDebit.minus(totalCredit).abs().toFixed(2);
                    const currencySymbol = "{{ school_profile.currency_symbol|default:'$'|escapejs }}"; // Get currency symbol safely
                    balanceCell.innerHTML = `<span class="badge bg-danger fs-6">{% trans "Unbalanced by" %} ${currencySymbol}${diff}</span>`;
                }
            }
        }
        
        const addRowButton = document.querySelector('.add-formset-row');
        const formsetContainer = document.querySelector('.formset-lines-container');
        const totalFormsInput = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);
        const emptyFormTemplate = document.getElementById('formset-empty-form-template');

        if (addRowButton && formsetContainer && totalFormsInput && emptyFormTemplate) {
            addRowButton.addEventListener('click', function() {
                let formIdx = parseInt(totalFormsInput.value);
                
                const newRow = emptyFormTemplate.content.firstElementChild.cloneNode(true);
                newRow.id = `${formsetPrefix}-${formIdx}`; // Set ID for the new <tr> itself

                newRow.querySelectorAll('input, select, textarea').forEach(function(input) {
                    const nameAttr = input.getAttribute('name');
                    const idAttr = input.getAttribute('id');
                    if (nameAttr) input.setAttribute('name', nameAttr.replace(/__prefix__/g, formIdx));
                    if (idAttr) input.setAttribute('id', idAttr.replace(/__prefix__/g, formIdx));
                    
                    // Clear values for new row, except for hidden id and the delete checkbox
                    if (input.type !== 'hidden' && input.getAttribute('name') && !input.getAttribute('name').endsWith('-DELETE')) {
                        input.value = '';
                        if (input.classList.contains('jel-debit') || input.classList.contains('jel-credit')) {
                            input.value = '0.00';
                        }
                        if (input.tagName === 'SELECT') {
                            input.selectedIndex = 0; 
                            // If using Select2, you might need to trigger a change or update for it to reflect this
                            if ($(input).data('select2')) { $(input).val(null).trigger('change'); }
                        }
                    }
                });
                
                formsetContainer.appendChild(newRow);
                totalFormsInput.value = formIdx + 1;

                initializeSelect2(newRow.querySelector('.account-select')); // Make sure selects have 'account-select' class
                rebindEventListeners(newRow);
                
                const firstFocusable = newRow.querySelector('.account-select, input:not([type="hidden"])');
                if (firstFocusable && $(firstFocusable).data('select2')) { $(firstFocusable).select2('open');} 
                else if (firstFocusable) { firstFocusable.focus(); }
                
                updateTotalsAndBalance();
            });
        }
        updateTotalsAndBalance(); // Initial call
    });
    </script>
{% endblock extra_tenant_js %}


































{% comment %} {# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n crispy_forms_tags %} {# Remove crispy_forms_tags if not using Crispy Forms #}

{% block tenant_page_title %}{{ view_title|default:_("Create Journal Entry") }}{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'libs/select2/css/select2.min.css' %}"> {# For Select2 if you use it on accounts #}
    <link rel="stylesheet" href="{% static 'libs/select2-bootstrap-5-theme/select2-bootstrap-5-theme.min.css' %}">
    <style>
        .formset-row td {
            vertical-align: top; /* Align content to top for multi-line errors */
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }
        .formset-row .form-control-sm, 
        .formset-row .form-select-sm,
        .formset-row .select2-container .select2-selection--single {
            font-size: 0.875rem; /* Bootstrap's default sm size */
            height: calc(1.5em + .5rem + 2px); /* Adjust height for select2 */
        }
        .formset-row .select2-container .select2-selection--single .select2-selection__rendered {
            line-height: calc(1.5em + .5rem); /* Vertically center text in select2 */
        }
        .formset-row .jel-debit, .formset-row .jel-credit {
            min-width: 100px; /* Ensure number inputs have some width */
        }
        .delete-form-checkbox {
            margin-top: 0.5rem;
        }
        #lines-table tfoot td {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
    </style>
{% endblock extra_tenant_css %}

{% block tenant_specific_content %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <a href="{% url 'accounting:journalentry_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-list-ul me-1"></i> {% trans "Back to Journal Entries" %}
        </a>
    </div>

    <form method="post" id="journalEntryForm" novalidate>
        {% csrf_token %}
        
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Header" %}</h5>
            </div>
            <div class="card-body">
                {# Use crispy forms for main form if installed, otherwise render manually #}
                {% comment %} {% if form|is_crispy %}
                    {{ form|crispy }}
                {% else %} ################
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                            {{ form.date }}
                            {% for error in form.date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% for error in form.description.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.reference_number.id_for_label }}" class="form-label">{{ form.reference_number.label }}</label>
                            {{ form.reference_number }}
                            {% for error in form.reference_number.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                    </div>
                {% comment %} {% endif %} ##################
                {% for error in form.non_field_errors %}<div class="alert alert-danger">{{ error }}</div>{% endfor %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Lines" %}</h5>
            </div>
            <div class="card-body">
                {{ line_formset.management_form }}
                {% if line_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in line_formset.non_form_errors %}
                            {{ error|escape }}<br> {# Use escape for safety #}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="table-responsive">
                    <table class="table table-sm" id="lines-table">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 35%;">{% trans "Account" %}</th>
                                <th style="width: 17%;" class="text-end">{% trans "Debit" %}</th>
                                <th style="width: 17%;" class="text-end">{% trans "Credit" %}</th>
                                <th style="width: 26%;">{% trans "Line Description" %}</th>
                                {% if line_formset.can_delete %}<th class="text-center">{% trans "Del?" %}</th>{% endif %}
                            </tr>
                        </thead>
                        <tbody class="formset-lines-container">
                            {% for form_line in line_formset %}
                            <tr class="formset-row {% if form_line.errors %}table-danger{% endif %}" id="{{ line_formset.prefix }}-{{ forloop.counter0 }}">
                                <td>
                                    {{ form_line.id }} {# Hidden ID field for the form line #}
                                    {{ form_line.account }}
                                    {% for error in form_line.account.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.debit_amount }}
                                    {% for error in form_line.debit_amount.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.credit_amount }}
                                    {% for error in form_line.credit_amount.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.description }}
                                    {% for error in form_line.description.errors %}<div class="text-danger small mt-1">{{ error|escape }}</div>{% endfor %}
                                </td>
                                {% if line_formset.can_delete %}
                                <td class="text-center">
                                    {% if form_line.instance.pk or line_formset.can_delete_extra %}
                                        {{ form_line.DELETE }}
                                    {% endif %}
                                </td>
                                {% endif %}
                            </tr>
                            {% if form_line.non_field_errors %}
                                <tr><td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="p-0">
                                    <div class="alert alert-danger py-1 px-2 small mb-0 border-0 rounded-0">
                                    {% for error in form_line.non_field_errors %}{{ error|escape }}{% endfor %}
                                    </div>
                                </td></tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td class="text-end fw-bold">{% trans "Totals:" %}</td>
                                <td class="text-end fw-bold" id="total-debit">0.00</td>
                                <td class="text-end fw-bold" id="total-credit">0.00</td>
                                <td class="text-end fw-bold" colspan="{% if line_formset.can_delete %}2{% else %}1{% endif %}" id="balance-status"></td>
                            </tr>
                            <tr>
                                <td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="text-end pt-3">
                                    <button type="button" class="btn btn-sm btn-outline-success add-formset-row">
                                        <i class="bi bi-plus-circle"></i> {% trans "Add Line" %}
                                    </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-success btn-lg">
                <i class="bi bi-save-fill me-1"></i> 
                {% if object %}{% trans "Save Draft Changes" %}{% else %}{% trans "Save Draft Journal Entry" %}{% endif %}
            </button>
            <a href="{% url 'accounting:journalentry_list' %}" class="btn btn-outline-secondary ms-2">{% trans "Cancel" %}</a>
        </div>
    </form>

    {# Hidden template for new formset rows (for more robust JS add row) #}
    <template id="formset-empty-form-template">
        {% with form_line=line_formset.empty_form %}
        <tr class="formset-row" id="{{ line_formset.prefix }}-__prefix__">
            <td>
                {{ form_line.id }}
                {{ form_line.account }}
            </td>
            <td>{{ form_line.debit_amount }}</td>
            <td>{{ form_line.credit_amount }}</td>
            <td>{{ form_line.description }}</td>
            {% if line_formset.can_delete %}
            <td class="text-center">
                {% if line_formset.can_delete_extra %}{{ form_line.DELETE }}{% endif %}
            </td>
            {% endif %}
        </tr>
        {% endwith %}
    </template>

    {% endblock tenant_specific_content %}

    {% block extra_tenant_js %}
    {{ block.super }}
    <script src="{% static 'libs/select2/js/select2.min.js' %}"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        function initializeSelect2(element) {
            $(element).select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: "{% trans 'Select an account' %}",
                allowClear: true
            });
        }

        // Initialize Select2 for existing account selects
        document.querySelectorAll('.formset-row .account-select').forEach(function(select) {
            initializeSelect2(select);
        });

        function updateTotalsAndBalance() {
            let totalDebit = Decimal(0);
            let totalCredit = Decimal(0);
            document.querySelectorAll('.formset-lines-container .formset-row').forEach(function(row) {
                const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
                if (deleteCheckbox && deleteCheckbox.checked) {
                    return; // Skip deleted rows
                }

                const debitInput = row.querySelector('.jel-debit');
                const creditInput = row.querySelector('.jel-credit');
                if (debitInput && debitInput.value) {
                    totalDebit = totalDebit.plus(Decimal(debitInput.value || 0));
                }
                if (creditInput && creditInput.value) {
                    totalCredit = totalCredit.plus(Decimal(creditInput.value || 0));
                }
            });
            const debitCell = document.getElementById('total-debit');
            const creditCell = document.getElementById('total-credit');
            const balanceCell = document.getElementById('balance-status');

            debitCell.textContent = totalDebit.toFixed(2);
            creditCell.textContent = totalCredit.toFixed(2);

            if (totalDebit.equals(totalCredit)) {
                balanceCell.innerHTML = '<span class="badge bg-success">{% trans "Balanced" %}</span>';
            } else {
                const diff = totalDebit.minus(totalCredit).abs().toFixed(2);
                balanceCell.innerHTML = `<span class="badge bg-danger">{% trans "Unbalanced by" %} ${diff}</span>`;
            }
        }

        // Attach event listeners to debit/credit inputs for live totals
        document.querySelector('.formset-lines-container').addEventListener('input', function(event) {
            if (event.target.classList.contains('jel-debit') || event.target.classList.contains('jel-credit')) {
                updateTotalsAndBalance();
            }
        });
        // Also for change event for number input spinners
         document.querySelector('.formset-lines-container').addEventListener('change', function(event) {
            if (event.target.classList.contains('jel-debit') || event.target.classList.contains('jel-credit')) {
                updateTotalsAndBalance();
            }
        });


        // Add new formset row using the empty_form template
        const addRowButton = document.querySelector('.add-formset-row');
        const formsetContainer = document.querySelector('.formset-lines-container');
        const totalFormsInput = document.getElementById('id_{{ line_formset.prefix }}-TOTAL_FORMS');
        const emptyFormTemplate = document.getElementById('formset-empty-form-template');

        if (addRowButton && formsetContainer && totalFormsInput && emptyFormTemplate) {
            addRowButton.addEventListener('click', function() {
                let formIdx = parseInt(totalFormsInput.value);
                let newFormHtml = emptyFormTemplate.innerHTML.replace(/__prefix__/g, formIdx);
                
                // Create a temporary div to parse the HTML string into DOM elements
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newFormHtml.trim(); // trim to avoid text nodes
                const newRow = tempDiv.firstChild; // Get the actual <tr> element

                formsetContainer.appendChild(newRow);
                totalFormsInput.value = formIdx + 1;

                // Initialize Select2 on the new row's account select
                const newAccountSelect = newRow.querySelector('.account-select');
                if (newAccountSelect) {
                    initializeSelect2(newAccountSelect);
                }
                // Re-attach event listeners for totals calculation to new inputs
                newRow.querySelectorAll('.jel-debit, .jel-credit').forEach(function(input) {
                    input.addEventListener('input', updateTotalsAndBalance);
                    input.addEventListener('change', updateTotalsAndBalance);
                });
                // Focus on the first input of the new row
                if (newAccountSelect) $(newAccountSelect).select2('open'); // Open select2
                else if (newRow.querySelector('input, select')) newRow.querySelector('input, select').focus();
                
                updateTotalsAndBalance(); // Recalculate after adding
            });
        }
         // Handle "DELETE" checkbox changes to update totals
        formsetContainer.addEventListener('change', function(event) {
            if (event.target.matches('input[type="checkbox"][name$="-DELETE"]')) {
                updateTotalsAndBalance();
                // Optionally, visually strike through the row or hide it
                event.target.closest('.formset-row').style.opacity = event.target.checked ? 0.5 : 1;
                event.target.closest('.formset-row').querySelectorAll('input:not([type="checkbox"]), select, textarea').forEach(el => {
                    el.disabled = event.target.checked;
                });
            }
        });

        // Initial calculation on page load
        updateTotalsAndBalance();
    });
    </script>
    {# Include Decimal.js if not already globally available #}
    <script src="https://cdn.jsdelivr.net/npm/decimal.js@10.4.3/decimal.min.js"></script> 
    {% endblock extra_tenant_js %} {% endcomment %}

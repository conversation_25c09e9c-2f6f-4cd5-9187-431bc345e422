{# D:\school_fees_saas_v2\apps\schools\templates\schools\dashboard.html #}
{% extends "tenant_base.html" %}
{% load static humanize i18n %}

{% block tenant_page_title %}{{ view_title|default:"Dashboard" }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .dashboard-stat-card {
            transition: transform .2s ease-out, box-shadow .2s ease-out;
            border-left-width: 4px;
        }
        .dashboard-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .quick-action-card .list-group-item {
            font-size: 1rem;
        }
        .announcement-summary a {
            text-decoration: none;
        }
        .announcement-summary a:hover .card-title {
            color: var(--bs-primary);
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid"> {# Changed to container-fluid for dashboard to allow more width if needed #}
    
    <div class="pagetitle mb-3">
    <h1>{{ view_title }}</h1>
    <nav>
            <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Home</a></li>
            <li class="breadcrumb-item active">Dashboard</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    {# Row 1: Stats Cards #}
    <div class="row">
        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card dashboard-stat-card border-primary h-100 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="stat-icon text-primary mb-3"><i class="bi bi-people-fill fs-1"></i></div>
                    <div class="stat-value">{{ active_student_count|default:"0" }}</div>
                    <div class="stat-label">{% trans "Active Students" %}</div>
                </div>
                <a href="{% url 'students:student_list' %}" class="card-footer text-primary text-decoration-none">
                    View Details <i class="bi bi-arrow-right-circle"></i>
                </a>
            </div>
        </div>

        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card dashboard-stat-card border-success h-100 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="stat-icon text-success mb-3"><i class="bi bi-receipt-cutoff fs-1"></i></div>
                    <div class="stat-value">{{ outstanding_invoice_count|default:"0" }}</div>
                    <div class="stat-label">{% trans "Outstanding Invoices" %}</div>
                </div>
                <a href="{% url 'fees:invoice_list' %}?status=SENT&status=PARTIALLY_PAID&status=OVERDUE" class="card-footer text-success text-decoration-none">
                    Manage Invoices <i class="bi bi-arrow-right-circle"></i>
                </a>
            </div>
        </div>

        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card dashboard-stat-card border-info h-100 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="stat-icon text-info mb-3"><i class="bi bi-cash-stack fs-1"></i></div>
                    <div class="stat-value">0</div> {# Placeholder for Total Collections Today #}
                    <div class="stat-label">{% trans "Collections Today" %} (TBD)</div>
                </div>
                <a href="#" class="card-footer text-info text-decoration-none disabled">
                    View Report <i class="bi bi-arrow-right-circle"></i>
                </a>
            </div>
        </div>
        
        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card dashboard-stat-card border-warning h-100 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="stat-icon text-warning mb-3"><i class="bi bi-person-check-fill fs-1"></i></div>
                    <div class="stat-value">0</div> {# Placeholder for Active Staff #}
                    <div class="stat-label">{% trans "Active Staff" %} (TBD)</div>
                </div>
                <a href="{% url 'schools:staff_list' %}" class="card-footer text-warning text-decoration-none">
                    Manage Staff <i class="bi bi-arrow-right-circle"></i>
                </a>
            </div>
        </div>
    </div>

    {# Row 2: Announcements and Quick Actions/Other Info #}
    <div class="row">
        <div class="col-lg-7 mb-4">
            {% include "announcements/widgets/announcement_widget.html" with announcements=announcements show_manage_link=True show_view_all_link=True show_create_link=True %}
        </div>

        <div class="col-lg-5 mb-4">
            <div class="card shadow-sm h-100 quick-action-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-lightning-charge-fill me-2 text-success"></i>{% trans "Quick Actions" %}</h5>
                </div>


                <div class="list-group list-group-flush">
                    {% if perms.students.add_student %} {# Check permission using the 'perms' object from context #}
                        <a href="{% url 'students:student_create' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-person-plus-fill me-2"></i>{% trans "Add New Student" %}
                        </a>
                    {% endif %}
                    {% if perms.fees.add_invoice %}
                        <a href="{% url 'fees:invoice_create' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-receipt me-2"></i>{% trans "Create New Invoice" %}
                        </a>
                    {% endif %}
                    {% if perms.payments.add_payment %}
                        <a href="{% url 'payments:record_payment' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-credit-card me-2"></i>{% trans "Record Payment" %}
                        </a>
                    {% endif %}
                    {% if perms.schools.add_staffuser %}
                        <a href="{% url 'schools:staff_create' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-person-badge me-2"></i>{% trans "Add New Staff" %}
                        </a>
                    {% endif %}

                    {# Calendar Management #}
                    <a href="{% url 'school_calendar:calendar' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-event me-2"></i>{% trans "School Calendar" %}
                    </a>
                    <a href="{% url 'school_calendar:admin_event_create' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-plus me-2"></i>{% trans "Create Event" %}
                    </a>
                    <a href="{% url 'school_calendar:admin_event_list' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-gear me-2"></i>{% trans "Manage Events" %}
                    </a>
                    {% if not perms.students.add_student and not perms.fees.add_invoice and not perms.payments.add_payment and not perms.schools.add_staffuser %}
                        <div class="list-group-item">
                            <p class="text-muted mb-0">{% trans "No quick actions available based on your permissions." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {# Row 3: Calendar Overview #}
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-event me-2"></i>{% trans "Upcoming School Events" %}
                    </h5>
                    <div>
                        <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-light btn-sm me-2">
                            <i class="bi bi-plus-circle me-1"></i>{% trans "Add Event" %}
                        </a>
                        <a href="{% url 'school_calendar:calendar' %}" class="btn btn-outline-light btn-sm">
                            <i class="bi bi-calendar me-1"></i>{% trans "View Calendar" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div id="upcoming-events">
                                {# This will be populated by JavaScript or server-side #}
                                <p class="text-muted text-center py-3">
                                    <i class="bi bi-calendar-x display-4 mb-2"></i><br>
                                    {% trans "Loading upcoming events..." %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">{% trans "Quick Calendar Actions" %}</h6>
                                <div class="d-grid gap-2">
                                    <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-calendar-plus me-1"></i>{% trans "Create Event" %}
                                    </a>
                                    <a href="{% url 'school_calendar:admin_event_list' %}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-list me-1"></i>{% trans "Manage Events" %}
                                    </a>
                                    <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-eye me-1"></i>{% trans "View All Events" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# Add more rows for charts, task lists, etc. as needed #}

</div>
{% endblock tenant_specific_content %}



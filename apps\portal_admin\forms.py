# D:\school_fees_saas_V2\apps\portal_admin\forms.py

from django import forms
from django.contrib.auth.models import Group, Permission
# from django.contrib.contenttypes.models import ContentType # Not directly used in forms

# Import your models from the current app
from .models import AdminActivityLog
# Import models from other apps
from apps.schools.models import StaffUser


class TenantGroupForm(forms.ModelForm):
    """ Form for creating/updating a Group (Role) within a tenant. """
    class Meta:
        model = Group
        fields = ['name'] # Only manage the name
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'e.g., Accountant, HR Manager'}),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        # Group.name is unique globally. Django's ModelForm validation will handle this.
        # If you need tenant-specific uniqueness for group names (meaning two tenants can have
        # a group named "Teachers"), then Group model itself isn't suitable unless extended
        # or you manage this uniqueness constraint differently.
        # For now, relying on Django's default unique=True for Group.name.
        # The existing unique check might be redundant or specific to a different logic.
        # If relying on default, this clean_name might not be strictly needed for uniqueness.
        
        # Example if you wanted to ensure the name isn't 'Admin' (just an example)
        # if name and name.lower() == 'admin':
        #     raise forms.ValidationError("The name 'Admin' is reserved.")
        return name



# apps/portal_admin/forms.py
class GroupPermissionAssignmentForm(forms.Form):
    permissions = forms.ModelMultipleChoiceField(
        queryset=Permission.objects.all().select_related('content_type').order_by('content_type__app_label', 'name'),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Assign Permissions"
    )

    def __init__(self, *args, **kwargs):
        self.group_instance = kwargs.pop('group_instance', None)
        print(f"--- FORM INIT: Group instance received: {self.group_instance} (PK: {self.group_instance.pk if self.group_instance else 'None'}) ---")
        super().__init__(*args, **kwargs)

        if self.group_instance:
            print(f"--- FORM INIT: Attempting to get permissions for group '{self.group_instance.name}' (PK: {self.group_instance.pk}) ---")
            current_group_permissions = self.group_instance.permissions.all()
            print(f"--- FORM INIT: self.group_instance.permissions.all() count: {current_group_permissions.count()} ---")
            # for p in current_group_permissions[:5]:
            #     print(f"    Initial perm: {p.codename}")
            self.fields['permissions'].initial = current_group_permissions
            print(f"--- FORM INIT: self.fields['permissions'].initial set with {current_group_permissions.count()} permissions ---")
        else:
            print("--- FORM INIT: No group_instance provided. ---")
            self.fields['permissions'].initial = Permission.objects.none()
            print("--- FORM INIT: self.fields['permissions'].initial set to Permission.objects.none() ---")
            
#         # choices/widget of ModelMultipleChoiceField here in a complex way.


class ActivityLogFilterForm(forms.Form):
    action_type = forms.ChoiceField(
        # CORRECTED LINE: Use ACTION_TYPES_CHOICES from your AdminActivityLog model
        choices=[('', 'All Actions')] + AdminActivityLog.ACTION_TYPES_CHOICES,
        required=False,
        label="Action Type",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    staff_user = forms.ModelChoiceField(
        queryset=StaffUser.objects.none(), # Default empty queryset
        required=False,
        label="Staff Member",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    start_date = forms.DateField(
        required=False,
        label="Start Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm', 'placeholder': 'YYYY-MM-DD'})
    )
    end_date = forms.DateField(
        required=False,
        label="End Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm', 'placeholder': 'YYYY-MM-DD'})
    )
    search_term = forms.CharField(
        required=False,
        label="Search Logs",
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Search text...'})
    )

    def __init__(self, *args, **kwargs):
        # The request object can be passed from the view if needed for dynamic querysets
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

        # Populate staff_user queryset.
        # Since StaffUser is a tenant model, its default manager should automatically
        # filter by the current tenant's schema when the form is used within a
        # tenant-aware view (like ActivityLogListView).
        self.fields['staff_user'].queryset = StaffUser.objects.filter(is_active=True).order_by('email')
        # If StaffUser had a direct FK to the tenant (e.g., `school` field) and you had the tenant instance:
        # if self.request and hasattr(self.request, 'tenant'):
        #     self.fields['staff_user'].queryset = StaffUser.objects.filter(
        #         school=self.request.tenant, is_active=True
        #     ).order_by('email')
        # else:
        #     self.fields['staff_user'].queryset = StaffUser.objects.none() # Fallback
        


from django import forms
from django.contrib.auth.models import Group
from apps.schools.models import StaffUser

class StaffRoleAssignmentForm(forms.Form):
    staff_member = forms.ModelChoiceField(
        queryset=StaffUser.objects.filter(is_active=True).order_by('last_name', 'first_name'),
        label="Select Staff Member",
        empty_label="--- Select a Staff Member ---",
        required=True
    )
    
    roles = forms.ModelMultipleChoiceField(
        queryset=Group.objects.all().order_by('name'),
        widget=forms.CheckboxSelectMultiple, # This is key for rendering as checkboxes
        required=False,
        label="Assign Roles"
    )
    def __init__(self, *args, **kwargs):
        # You can still accept the 'tenant' kwarg in case you need it
        # for other logic, but you don't need to use it for this query.
        # Popping it is still good practice to prevent errors with super().__init__.
        tenant = kwargs.pop('tenant', None) 
        
        super().__init__(*args, **kwargs)
        
        # The query is automatically scoped to the current tenant's schema
        # by the django-tenants middleware. No extra filter is needed.
        self.fields['staff_member'].queryset = StaffUser.objects.filter(
            is_active=True
        ).order_by('last_name', 'first_name')

        # The rest of your __init__ logic for setting initial values...
        if 'staff_member' in self.initial:
            staff_id = self.initial.get('staff_member')
            try:
                staff_user = StaffUser.objects.get(pk=staff_id)
                self.fields['roles'].initial = staff_user.groups.all()
            except StaffUser.DoesNotExist:
                pass
            
                 
    def save_assignments(self):
        """
        Custom method to save the form's data.
        This will be called from the view's form_valid method.
        """
        staff_member = self.cleaned_data.get('staff_member')
        assigned_roles = self.cleaned_data.get('roles')

        if staff_member:
            # .set() is the perfect method for M2M relationships. It handles
            # adding the new, and removing the old, in one go.
            staff_member.groups.set(assigned_roles)
            return staff_member
        return None

# # D:\school_fees_saas_v2\apps\portal_admin\forms.py
# from django import forms
# from django.contrib.auth.models import Group
# from apps.schools.models import StaffUser # Assuming your StaffUser model path

# class StaffRoleAssignmentForm(forms.Form):
#     staff_member = forms.ModelChoiceField(
#         queryset=StaffUser.objects.filter(is_active=True, is_staff=True).order_by('last_name', 'first_name'),
#         label="Select Staff Member",
#         widget=forms.Select(attrs={'class': 'form-select form-select-lg mb-3', 'id': 'id_staff_member_select'}),
#         empty_label="--- Select a Staff Member ---"
#     )
#     roles = forms.ModelMultipleChoiceField(
#         queryset=Group.objects.all().order_by('name'),
#         widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
#         label="Assign to Roles",
#         required=False # A staff member might belong to no roles
#     )

#     def __init__(self, *args, **kwargs):
#         self.tenant = kwargs.pop('tenant', None) # Allow passing tenant if needed for querysets
#         super().__init__(*args, **kwargs)

#         if self.tenant:
#             # If you need to filter StaffUser or Group based on tenant-specific logic in the future
#             # For now, StaffUser is already tenant-specific by schema, and Group is also tenant-specific
#             pass

#         # If a staff member is pre-selected (e.g., when the form is reloaded after selection),
#         # populate the initial roles.
#         if 'staff_member' in self.initial and self.initial['staff_member']:
#             try:
#                 staff_id = self.initial['staff_member']
#                 if isinstance(staff_id, StaffUser): # If initial is already a StaffUser instance
#                     staff_id = staff_id.pk
#                 selected_staff = StaffUser.objects.get(pk=staff_id)
#                 self.fields['roles'].initial = selected_staff.groups.all()
#             except StaffUser.DoesNotExist:
#                 pass # Staff member not found, roles will be empty
#             except ValueError: # staff_id might not be a valid PK
#                 pass

#     def clean_staff_member(self):
#         staff = self.cleaned_data.get('staff_member')
#         if not staff:
#             raise forms.ValidationError("Please select a staff member.")
#         return staff
        
#     def save_assignments(self):
#         staff_member = self.cleaned_data.get('staff_member')
#         assigned_roles = self.cleaned_data.get('roles')

#         if staff_member:
#             # .set() is efficient: it removes from groups not in assigned_roles
#             # and adds to groups in assigned_roles that they weren't in.
#             staff_member.groups.set(assigned_roles) 
#             return staff_member
#         return None    
    
    


# apps/parent_portal/forms.py
from django import forms
from apps.students.models import ParentUser # Correct import

class ParentProfileUpdateForm(forms.ModelForm):
    class Meta:
        model = ParentUser
        fields = [
            'first_name', 
            'last_name', 
            'phone_number', 
            'address_line1',  # New
            'address_line2',  # New
            'city',           # New
            'state_province', # New (if added to model)
            'postal_code',    # New (if added to model)
            'country',        # New (if added to model)
            'profile_picture' # New
            # 'email', # Usually read-only or handled separately
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'state_province': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            'profile_picture': forms.ClearableFileInput(attrs={'class': 'form-control'}), # Use ClearableFileInput for ImageFields
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Optional: Make email read-only if you decide to include it
        if 'email' in self.fields:
            self.fields['email'].disabled = True 
            self.fields['email'].widget.attrs['class'] = 'form-control'
            self.fields['email'].help_text = "Email cannot be changed."
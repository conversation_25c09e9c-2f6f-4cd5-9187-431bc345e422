from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta, time
from apps.school_calendar.models import EventCategory, SchoolEvent
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample calendar events and categories'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample calendar data...'))
        
        # Create event categories
        categories_data = [
            {
                'name': 'Academic',
                'color': '#007bff',
                'icon': 'bi-book',
                'description': 'Academic events and activities'
            },
            {
                'name': 'Sports',
                'color': '#28a745',
                'icon': 'bi-trophy',
                'description': 'Sports and athletic events'
            },
            {
                'name': 'Cultural',
                'color': '#ffc107',
                'icon': 'bi-palette',
                'description': 'Cultural and artistic events'
            },
            {
                'name': 'Parent Events',
                'color': '#17a2b8',
                'icon': 'bi-people',
                'description': 'Events for parents and families'
            },
            {
                'name': 'Holidays',
                'color': '#dc3545',
                'icon': 'bi-calendar-heart',
                'description': 'School holidays and breaks'
            },
            {
                'name': 'Examinations',
                'color': '#6f42c1',
                'icon': 'bi-pencil-square',
                'description': 'Exams and assessments'
            }
        ]
        
        categories = {}
        for cat_data in categories_data:
            category, created = EventCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'Created category: {category.name}')
        
        # Get or create a staff user for event creation
        try:
            staff_user = User.objects.filter(is_staff=True).first()
            if not staff_user:
                staff_user = User.objects.filter(is_superuser=True).first()
        except:
            staff_user = None
        
        # Create sample events
        today = timezone.now().date()
        events_data = [
            {
                'title': 'Parent-Teacher Conference',
                'description': 'Meet with your child\'s teachers to discuss academic progress.',
                'category': categories['Parent Events'],
                'event_type': 'PARENT',
                'priority': 'HIGH',
                'start_date': today + timedelta(days=7),
                'end_date': today + timedelta(days=7),
                'start_time': time(9, 0),
                'end_time': time(17, 0),
                'location': 'School Main Hall',
                'requires_rsvp': True,
                'max_attendees': 200,
                'contact_person': 'Ms. Johnson',
                'contact_email': '<EMAIL>',
                'visible_to_parents': True,
                'visible_to_staff': True,
            },
            {
                'title': 'Annual Sports Day',
                'description': 'Join us for our annual sports competition featuring various athletic events.',
                'category': categories['Sports'],
                'event_type': 'SPORTS',
                'priority': 'HIGH',
                'start_date': today + timedelta(days=14),
                'end_date': today + timedelta(days=14),
                'start_time': time(8, 0),
                'end_time': time(16, 0),
                'location': 'School Sports Ground',
                'venue_details': 'Bring sunscreen and water bottles. Refreshments will be available.',
                'requires_rsvp': True,
                'visible_to_parents': True,
                'visible_to_staff': True,
                'visible_to_students': True,
            },
            {
                'title': 'Science Fair',
                'description': 'Students showcase their science projects and experiments.',
                'category': categories['Academic'],
                'event_type': 'ACADEMIC',
                'priority': 'MEDIUM',
                'start_date': today + timedelta(days=21),
                'end_date': today + timedelta(days=21),
                'start_time': time(10, 0),
                'end_time': time(15, 0),
                'location': 'Science Laboratory',
                'visible_to_parents': True,
                'visible_to_staff': True,
                'visible_to_students': True,
            },
            {
                'title': 'Winter Break',
                'description': 'School closed for winter holidays.',
                'category': categories['Holidays'],
                'event_type': 'HOLIDAY',
                'priority': 'LOW',
                'start_date': today + timedelta(days=30),
                'end_date': today + timedelta(days=44),
                'is_all_day': True,
                'visible_to_parents': True,
                'visible_to_staff': True,
                'visible_to_students': True,
            },
            {
                'title': 'Mid-Term Examinations',
                'description': 'Mid-term examinations for all grades.',
                'category': categories['Examinations'],
                'event_type': 'EXAM',
                'priority': 'URGENT',
                'start_date': today + timedelta(days=35),
                'end_date': today + timedelta(days=39),
                'is_all_day': True,
                'location': 'Various Classrooms',
                'visible_to_parents': True,
                'visible_to_staff': True,
                'visible_to_students': True,
            },
            {
                'title': 'Cultural Night',
                'description': 'An evening of music, dance, and cultural performances by our students.',
                'category': categories['Cultural'],
                'event_type': 'CULTURAL',
                'priority': 'MEDIUM',
                'start_date': today + timedelta(days=28),
                'end_date': today + timedelta(days=28),
                'start_time': time(18, 0),
                'end_time': time(21, 0),
                'location': 'School Auditorium',
                'venue_details': 'Formal attire recommended. Light refreshments will be served.',
                'requires_rsvp': True,
                'max_attendees': 300,
                'contact_person': 'Mr. Davis',
                'contact_email': '<EMAIL>',
                'visible_to_parents': True,
                'visible_to_staff': True,
                'visible_to_students': True,
            },
            {
                'title': 'Staff Development Day',
                'description': 'Professional development workshop for teaching staff.',
                'category': categories['Academic'],
                'event_type': 'STAFF',
                'priority': 'MEDIUM',
                'start_date': today + timedelta(days=10),
                'end_date': today + timedelta(days=10),
                'start_time': time(8, 30),
                'end_time': time(16, 30),
                'location': 'Conference Room A',
                'visible_to_staff': True,
                'visible_to_parents': False,
                'visible_to_students': False,
            }
        ]
        
        for event_data in events_data:
            event, created = SchoolEvent.objects.get_or_create(
                title=event_data['title'],
                start_date=event_data['start_date'],
                defaults={**event_data, 'created_by': staff_user}
            )
            if created:
                self.stdout.write(f'Created event: {event.title}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(categories_data)} categories and {len(events_data)} events!'
            )
        )

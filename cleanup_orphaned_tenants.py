#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import psycopg
from django.conf import settings

print("🧹 Cleaning up orphaned tenant records...")

db_settings = settings.DATABASES['default']
conn = psycopg.connect(
    host=db_settings['HOST'],
    port=db_settings['PORT'],
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD']
)
conn.autocommit = True
cur = conn.cursor()

# Set search path to public
cur.execute('SET search_path TO public;')

# Get existing schemas
cur.execute("""
    SELECT schema_name 
    FROM information_schema.schemata 
    WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'public', 'pg_toast')
    ORDER BY schema_name
""")
existing_schemas = [row[0] for row in cur.fetchall()]
print(f"Existing schemas: {existing_schemas}")

# Get all tenant records
cur.execute('SELECT id, schema_name, name FROM tenants_school ORDER BY schema_name;')
tenants = cur.fetchall()

# Find orphaned tenants
orphaned_tenants = []
for tenant_id, schema_name, name in tenants:
    if schema_name not in existing_schemas:
        orphaned_tenants.append((tenant_id, schema_name, name))

if not orphaned_tenants:
    print("✅ No orphaned tenant records found")
    conn.close()
    exit()

print(f"Found {len(orphaned_tenants)} orphaned tenant records:")
for tenant_id, schema_name, name in orphaned_tenants:
    print(f"  ID: {tenant_id}, Schema: {schema_name}, Name: {name}")

print("\n🗑️ Deleting orphaned tenant records and their dependencies...")

for tenant_id, schema_name, name in orphaned_tenants:
    try:
        print(f"  Processing tenant: {schema_name} (ID: {tenant_id})")
        
        # Delete domain records first
        cur.execute('DELETE FROM tenants_domain WHERE tenant_id = %s', (tenant_id,))
        domains_deleted = cur.rowcount
        if domains_deleted > 0:
            print(f"    ✅ Deleted {domains_deleted} domain record(s)")
        
        # Delete subscription records
        cur.execute('DELETE FROM subscriptions_subscription WHERE school_id = %s', (tenant_id,))
        subs_deleted = cur.rowcount
        if subs_deleted > 0:
            print(f"    ✅ Deleted {subs_deleted} subscription record(s)")
        
        # Delete the tenant record
        cur.execute('DELETE FROM tenants_school WHERE id = %s', (tenant_id,))
        print(f"    ✅ Deleted tenant record: {schema_name}")
        
    except Exception as e:
        print(f"    ❌ Error deleting tenant {schema_name}: {e}")

print("\n🎉 Cleanup completed!")

# Verify cleanup
cur.execute('SELECT id, schema_name, name FROM tenants_school ORDER BY schema_name;')
remaining_tenants = cur.fetchall()
print(f"\nRemaining tenant records: {len(remaining_tenants)}")
for tenant_id, schema_name, name in remaining_tenants:
    print(f"  ID: {tenant_id}, Schema: {schema_name}, Name: {name}")

conn.close()
print("\nYou can now run: python manage.py migrate")

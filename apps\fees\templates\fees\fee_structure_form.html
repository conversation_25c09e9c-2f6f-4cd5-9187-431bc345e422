{# templates/fees/fee_structure_form.html - Premium UI Design #}
{% extends "tenant_base.html" %}

{% load static %}

{% block title %}
    {{ view_title|default:"Manage Fee Structure" }}
{% endblock %}

{% block content %}
<style>
    /* Premium Fee Structure Form Design */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        max-width: 1200px;
        margin: 2rem auto;
        position: relative;
    }

    .premium-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #20c997, #17a2b8, #6f42c1, #e83e8c, #fd7e14, #20c997);
        background-size: 300% 100%;
        animation: rainbow-border 3s ease-in-out infinite;
        z-index: 10;
    }

    @keyframes rainbow-border {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-header {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .premium-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shine 2s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .premium-header i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .premium-body {
        padding: 3rem 2rem;
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
    }

    .premium-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(32, 201, 151, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(23, 162, 184, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* Floating Labels */
    .form-floating {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem 0.75rem 0.5rem 0.75rem;
        background: #ffffff !important;
        background-color: #ffffff !important;
        background-image: none !important;
        transition: all 0.3s ease;
        font-size: 1rem;
        color: #333333;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #20c997;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
        transform: translateY(-2px);
        background: #ffffff !important;
        background-color: #ffffff !important;
        background-image: none !important;
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1.25rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #20c997;
        font-weight: 500;
        z-index: 2;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 1;
        transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
        color: #20c997;
        background: transparent;
        padding: 0;
        border-radius: 0;
    }

    .icon-input {
        color: #20c997;
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }

    /* Section Headers */
    .fieldset-header {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 1rem;
        margin: 2rem 0 1.5rem 0;
        box-shadow: 0 4px 15px rgba(32, 201, 151, 0.2);
        position: relative;
        overflow: hidden;
    }

    .fieldset-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shimmer 2s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .fieldset-header h4 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .fieldset-header i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    /* Premium Buttons */
    .btn-premium {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
    }

    .btn-premium:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);
        color: white;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Form validation */
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .premium-header {
            padding: 2rem 1rem;
        }

        .premium-body {
            padding: 2rem 1rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3rem + 2px);
            font-size: 0.9rem;
        }

        .form-floating > label {
            padding: 1.25rem 0.75rem;
            font-size: 0.9rem;
        }
    }
</style>

<div class="container-fluid">
    <div class="premium-card">
        <!-- Premium Header -->
        <div class="premium-header">
            <i class="fas fa-money-bill-wave"></i>
            <h3 class="mb-2 fw-bold">{{ view_title|default:"Fee Structure Management" }}</h3>
            <p class="mb-0 opacity-75">
                Create and manage comprehensive fee structures for different academic periods and student groups
            </p>
        </div>

        <!-- Form Body -->
        <div class="premium-body">
            {# Include messages partial or render directly #}
            {% if messages %}
                <ul class="messages" style="list-style: none; padding: 0;">
                    {% for message in messages %}
                        <li class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %}" role="alert">{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}

            <form method="post" novalidate id="feeStructureForm">
                {% csrf_token %}

                {# --- Main Structure Details --- #}
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-info-circle"></i>Structure Details
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.name }}
                            <label for="{{ form.name.id_for_label }}">
                                <i class="fas fa-tag icon-input"></i>{{ form.name.label }}
                            </label>
                        </div>
                        {% if form.name.help_text %}<div class="form-text text-muted">{{ form.name.help_text }}</div>{% endif %}
                        {% if form.name.errors %}<div class="invalid-feedback d-block">{{ form.name.errors|striptags }}</div>{% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.academic_year }}
                            <label for="{{ form.academic_year.id_for_label }}">
                                <i class="fas fa-calendar-alt icon-input"></i>{{ form.academic_year.label }}
                            </label>
                        </div>
                        {% if form.academic_year.errors %}<div class="invalid-feedback d-block">{{ form.academic_year.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.term }}
                            <label for="{{ form.term.id_for_label }}">
                                <i class="fas fa-calendar-week icon-input"></i>{{ form.term.label }}
                            </label>
                        </div>
                        {% if form.term.help_text %}<div class="form-text text-muted">{{ form.term.help_text }}</div>{% endif %}
                        {% if form.term.errors %}<div class="invalid-feedback d-block">{{ form.term.errors|striptags }}</div>{% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.description }}
                            <label for="{{ form.description.id_for_label }}">
                                <i class="fas fa-align-left icon-input"></i>{{ form.description.label }}
                            </label>
                        </div>
                        {% if form.description.errors %}<div class="invalid-feedback d-block">{{ form.description.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 d-flex align-items-center">
                        <div class="form-check form-switch">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                <i class="fas fa-toggle-on icon-input"></i>{{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}<div class="invalid-feedback d-block">{{ form.is_active.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <!-- Applicable Classes Section -->
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-users"></i>Applicable Classes
                    </h4>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="premium-classes-container">
                            {{ form.applicable_classes }}
                            {% if form.applicable_classes.help_text %}<div class="form-text text-muted mt-2">{{ form.applicable_classes.help_text }}</div>{% endif %}
                            {% if form.applicable_classes.errors %}<div class="invalid-feedback d-block">{{ form.applicable_classes.errors|striptags }}</div>{% endif %}
                        </div>
                    </div>
                </div>

                {% for error in form.non_field_errors %}
                    <div class="alert alert-danger">{{ error }}</div>
                {% endfor %}

                {# --- Fee Items Formset --- #}
                <div class="fieldset-header">
                    <h4>
                        <i class="fas fa-list-ul"></i>Fee Items
                    </h4>
                </div>

                {# Formset Management Form - CRITICAL for processing #}
                {{ item_formset.management_form }}

                {# Display Formset-level errors (e.g., min_num validation) #}
                {% for error in item_formset.non_form_errors %}
                    <div class="alert alert-danger">{{ error }}</div>
                {% endfor %}

                <div id="fee-items-formset-container">
                    {% for item_form in item_formset %}
                        <div class="fee-item-row premium-item-card p-4 mb-3" id="{{ item_form.prefix }}-row">
                            {{ item_form.id }} {# Hidden field for existing item's PK #}
                            {% if item_form.non_field_errors %}
                                <div class="alert alert-danger p-2">{{ item_form.non_field_errors }}</div>
                            {% endif %}

                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        {{ item_form.fee_head }}
                                        <label for="{{ item_form.fee_head.id_for_label }}">
                                            <i class="fas fa-money-bill icon-input"></i>{{ item_form.fee_head.label }}
                                        </label>
                                    </div>
                                    {% if item_form.fee_head.errors %}<div class="invalid-feedback d-block">{{ item_form.fee_head.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-3">
                                    <div class="form-floating">
                                        {{ item_form.amount }}
                                        <label for="{{ item_form.amount.id_for_label }}">
                                            <i class="fas fa-dollar-sign icon-input"></i>Amount
                                        </label>
                                    </div>
                                    {% if item_form.amount.errors %}<div class="invalid-feedback d-block">{{ item_form.amount.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-3">
                                    <div class="form-floating">
                                        {{ item_form.description }}
                                        <label for="{{ item_form.description.id_for_label }}">
                                            <i class="fas fa-edit icon-input"></i>Description
                                        </label>
                                    </div>
                                    {% if item_form.description.errors %}<div class="invalid-feedback d-block">{{ item_form.description.errors|striptags }}</div>{% endif %}
                                </div>
                                <div class="col-md-1 d-flex align-items-center">
                                    <div class="form-check form-switch">
                                        {{ item_form.is_optional }}
                                        <label class="form-check-label" for="{{ item_form.is_optional.id_for_label }}">
                                            <i class="fas fa-question-circle icon-input"></i>Optional
                                        </label>
                                    </div>
                                    {% if item_form.is_optional.errors %}<div class="invalid-feedback d-block">{{ item_form.is_optional.errors|striptags }}</div>{% endif %}
                                </div>
                                {# Delete checkbox for existing items #}
                                <div class="col-md-1 d-flex align-items-center justify-content-center">
                                    {% if item_formset.can_delete and item_form.instance.pk %}
                                        <div class="form-check">
                                            {{ item_form.DELETE }}
                                            <label class="form-check-label text-danger" for="{{ item_form.DELETE.id_for_label }}" title="Delete this item">
                                                <i class="fas fa-trash"></i>
                                            </label>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                {# The "Add" button for JavaScript to use #}
                <div class="text-center mb-4">
                    <button type="button" id="add-item-form" class="btn-secondary-premium">
                        <i class="fas fa-plus me-2"></i>Add Another Fee Item
                    </button>
                </div>

                {# --- Final Submit Buttons --- #}
                <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                    <button type="submit" class="btn-premium">
                        <i class="fas fa-save me-2"></i>{% if object %}Update{% else %}Save{% endif %} Fee Structure
                    </button>
                    <a href="{% url 'fees:fee_structure_list' %}" class="btn-secondary-premium">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Premium Item Card Styling */
    .premium-item-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .premium-item-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    }

    .premium-item-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(32, 201, 151, 0.15);
        border-color: #20c997;
    }

    /* Form Switch Styling */
    .form-check-input:checked {
        background-color: #20c997;
        border-color: #20c997;
    }

    .form-check-input:focus {
        border-color: #20c997;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
    }

    /* Premium Classes Container */
    .premium-classes-container {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .premium-classes-container select {
        border: 2px solid #e9ecef;
        border-radius: 0.75rem;
        padding: 0.75rem;
        background: white;
        transition: all 0.3s ease;
    }

    .premium-classes-container select:focus {
        border-color: #20c997;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
    }
</style>

{# Hidden empty form template for JavaScript to clone #}
<div id="empty-item-form-template" style="display: none;">
    <div class="fee-item-row premium-item-card p-3 mb-3" id="items-__prefix__-row">
        {{ item_formset.empty_form.id }}
        <div class="row g-3">
            <div class="col-md-3">
                <div class="form-floating">
                    {{ item_formset.empty_form.fee_head }}
                    <label for="{{ item_formset.empty_form.fee_head.id_for_label }}">
                        <i class="fas fa-money-bill icon-input"></i>Fee Head
                    </label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating">
                    {{ item_formset.empty_form.amount }}
                    <label for="{{ item_formset.empty_form.amount.id_for_label }}">
                        <i class="fas fa-dollar-sign icon-input"></i>Amount
                    </label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating">
                    {{ item_formset.empty_form.description }}
                    <label for="{{ item_formset.empty_form.description.id_for_label }}">
                        <i class="fas fa-edit icon-input"></i>Description
                    </label>
                </div>
            </div>
            <div class="col-md-2 d-flex align-items-center">
                <div class="form-check form-switch">
                    {{ item_formset.empty_form.is_optional }}
                    <label class="form-check-label" for="{{ item_formset.empty_form.is_optional.id_for_label }}">
                        Optional
                    </label>
                </div>
            </div>
            {# Newly added forms can just be removed from the DOM #}
            <div class="col-md-1 d-flex align-items-center justify-content-center">
                <button type="button" class="btn btn-danger btn-sm remove-item-form" title="Remove this item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Selectors for formset functionality
    const formsetContainer = document.getElementById('fee-items-formset-container');
    const addButton = document.getElementById('add-item-form');
    const emptyFormTemplateDiv = document.getElementById('empty-item-form-template');
    // Be more specific with the total forms input selector
    const totalFormsInput = document.querySelector('input[id="id_items-TOTAL_FORMS"]');

    // Check if all elements exist before adding listeners
    if (formsetContainer && addButton && emptyFormTemplateDiv && totalFormsInput) {
        console.log("All formset elements found. Attaching 'Add' listener.");

        addButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Get current form count from the management form
            let formCount = parseInt(totalFormsInput.value);

            // Create new form HTML by replacing the prefix placeholder
            const newFormHtml = emptyFormTemplateDiv.innerHTML.replace(/__prefix__/g, formCount);

            // Append new form to container
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            formsetContainer.appendChild(tempDiv.firstElementChild);

            // Increment the form count in the management form
            totalFormsInput.value = formCount + 1;

            // Add validation for the new form
            attachFeeHeadValidation();
        });

        // Function to validate duplicate fee heads
        function attachFeeHeadValidation() {
            const feeHeadSelects = document.querySelectorAll('select[name*="fee_head"]');
            feeHeadSelects.forEach(select => {
                select.addEventListener('change', function() {
                    validateDuplicateFeeHeads();
                });
            });
        }

        function validateDuplicateFeeHeads() {
            const feeHeadSelects = document.querySelectorAll('select[name*="fee_head"]');
            const selectedValues = [];
            const duplicates = [];

            feeHeadSelects.forEach(select => {
                if (select.value && select.value !== '') {
                    if (selectedValues.includes(select.value)) {
                        duplicates.push(select.value);
                    } else {
                        selectedValues.push(select.value);
                    }
                }
            });

            // Remove previous error messages
            document.querySelectorAll('.duplicate-fee-head-error').forEach(el => el.remove());

            if (duplicates.length > 0) {
                // Add error messages to duplicate selects
                feeHeadSelects.forEach(select => {
                    if (duplicates.includes(select.value)) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'text-danger duplicate-fee-head-error';
                        errorDiv.textContent = 'This fee head is already selected. Each fee head can only be used once.';
                        select.parentNode.appendChild(errorDiv);
                    }
                });
                return false;
            }
            return true;
        }

        // Attach validation to existing forms
        attachFeeHeadValidation();

        // Add event listener for removing dynamically added forms
        formsetContainer.addEventListener('click', function(e) {
            // Check if a 'remove' button was clicked inside the container
            if (e.target && e.target.classList.contains('remove-item-form')) {
                e.preventDefault();
                // Find the closest parent row and remove it
                const formRowToRemove = e.target.closest('.fee-item-row');
                if (formRowToRemove) {
                    formRowToRemove.remove();
                    // After removal, re-index the remaining forms
                    updateFormsetIndexes();
                }
            }
        });

    } else {
        console.error("Could not find all necessary formset elements. 'Add' functionality disabled.");
        console.log("formsetContainer:", formsetContainer);
        console.log("addButton:", addButton);
        console.log("emptyFormTemplateDiv:", emptyFormTemplateDiv);
        console.log("totalFormsInput:", totalFormsInput);
    }

    // Add form submission validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateDuplicateFeeHeads()) {
                e.preventDefault();
                alert('Please fix the duplicate fee head errors before submitting.');
                return false;
            }
        });
    }

    // Function to re-index form prefixes after a dynamic removal
    function updateFormsetIndexes() {
        const formRows = formsetContainer.querySelectorAll('.fee-item-row');
        // Update the management form's total count
        totalFormsInput.value = formRows.length;

        formRows.forEach((formRow, index) => {
            // Update all inputs, selects, labels within this form row
            formRow.querySelectorAll('input, select, textarea, label').forEach(el => {
                ['id', 'name', 'for'].forEach(attr => {
                    if (el.hasAttribute(attr)) {
                        // Regex to find 'items-X-' and replace X with the new index
                        const newAttrValue = el.getAttribute(attr).replace(/items-\d+-/g, `items-${index}-`);
                        el.setAttribute(attr, newAttrValue);
                    }
                });
            });
            // Update the row ID itself if needed
            formRow.id = `items-${index}-row`;
        });
    }

    // AJAX functionality for term population based on academic year
    const academicYearSelect = document.getElementById('id_academic_year');
    const termSelect = document.getElementById('id_term');

    if (academicYearSelect && termSelect) {
        academicYearSelect.addEventListener('change', function() {
            const academicYearId = this.value;

            // Clear current term options except the default
            termSelect.innerHTML = '<option value="">--- (Applies to Full Academic Year) ---</option>';

            if (academicYearId) {
                // Make AJAX request to get terms for this academic year
                fetch(`/tenant-fees/ajax/terms-for-year/?academic_year_id=${academicYearId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.terms && Array.isArray(data.terms)) {
                        data.terms.forEach(term => {
                            const option = document.createElement('option');
                            option.value = term.id;
                            option.textContent = term.name;
                            termSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching terms:', error);
                });
            }
        });
    }
});
</script>
{% endblock %}






{# templates/fees/fee_structure_form.html (REVISED & CONSOLIDATED) #}
{% extends "tenant_base.html" %}

{% load static %} {# Not used currently, but good practice #}

{% block title %}
    {{ view_title|default:"Manage Fee Structure" }}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h4>{{ view_title|default:"Fee Structure Management" }}</h4>
        </div>
        <div class="card-body">
            {# Include messages partial or render directly #}
            {% if messages %}
                <ul class="messages" style="list-style: none; padding: 0;">
                    {% for message in messages %}
                        <li class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %}" role="alert">{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}

            <form method="post" novalidate>
                {% csrf_token %}

                {# --- Main Structure Details --- #}
                <fieldset>
                    <legend>Structure Details</legend>

                    {# Render main form fields explicitly for better control #}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.help_text %}<div class="form-text">{{ form.name.help_text }}</div>{% endif %}
                            {% if form.name.errors %}<div class="text-danger">{{ form.name.errors|striptags }}</div>{% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
                            {{ form.academic_year }}
                            {% if form.academic_year.errors %}<div class="text-danger">{{ form.academic_year.errors|striptags }}</div>{% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.term.id_for_label }}" class="form-label">{{ form.term.label }}</label>
                            {{ form.term }}
                            {% if form.term.help_text %}<div class="form-text">{{ form.term.help_text }}</div>{% endif %}
                            {% if form.term.errors %}<div class="text-danger">{{ form.term.errors|striptags }}</div>{% endif %}
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.applicable_classes.id_for_label }}" class="form-label">{{ form.applicable_classes.label }}</label>
                            {{ form.applicable_classes }}
                            {% if form.applicable_classes.help_text %}<div class="form-text">{{ form.applicable_classes.help_text }}</div>{% endif %}
                            {% if form.applicable_classes.errors %}<div class="text-danger">{{ form.applicable_classes.errors|striptags }}</div>{% endif %}
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}<div class="text-danger">{{ form.description.errors|striptags }}</div>{% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}<div class="text-danger">{{ form.is_active.errors|striptags }}</div>{% endif %}
                        </div>
                    </div>

                    {% for error in form.non_field_errors %}
                        <div class="alert alert-danger">{{ error }}</div>
                    {% endfor %}
                </fieldset>

                {# --- Fee Items Formset --- #}
                <fieldset class="mt-4">
                    <legend>Fee Items</legend>

                    {# Formset Management Form - CRITICAL for processing #}
                    {{ item_formset.management_form }}

                    {# Display Formset-level errors (e.g., min_num validation) #}
                    {% for error in item_formset.non_form_errors %}
                        <div class="alert alert-danger">{{ error }}</div>
                    {% endfor %}

                    <div id="fee-items-formset-container">
                        {% for item_form in item_formset %}
                            <div class="fee-item-row border p-3 mb-3" id="{{ item_form.prefix }}-row">
                                {{ item_form.id }} {# Hidden field for existing item's PK #}
                                {% if item_form.non_field_errors %}
                                    <div class="alert alert-danger p-2">{{ item_form.non_field_errors }}</div>
                                {% endif %}

                                <div class="row align-items-center">
                                    <div class="col-md-3 mb-2">
                                        <label for="{{ item_form.fee_head.id_for_label }}" class="form-label">{{ item_form.fee_head.label }}</label>
                                        {{ item_form.fee_head }}
                                        {% if item_form.fee_head.errors %}<div class="text-danger small">{{ item_form.fee_head.errors|striptags }}</div>{% endif %}
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label for="{{ item_form.amount.id_for_label }}" class="form-label">{{ item_form.amount.label }}</label>
                                        {{ item_form.amount }}
                                        {% if item_form.amount.errors %}<div class="text-danger small">{{ item_form.amount.errors|striptags }}</div>{% endif %}
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <label for="{{ item_form.description.id_for_label }}" class="form-label">{{ item_form.description.label }}</label>
                                        {{ item_form.description }}
                                        {% if item_form.description.errors %}<div class="text-danger small">{{ item_form.description.errors|striptags }}</div>{% endif %}
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <div class="form-check mt-4">
                                            {{ item_form.is_optional }}
                                            <label class="form-check-label" for="{{ item_form.is_optional.id_for_label }}">
                                                {{ item_form.is_optional.label }}
                                            </label>
                                        </div>
                                        {% if item_form.is_optional.errors %}<div class="text-danger small">{{ item_form.is_optional.errors|striptags }}</div>{% endif %}
                                    </div>
                                    {# Delete checkbox for existing items #}
                                    <div class="col-md-1 mb-2 pt-4">
                                        {% if item_formset.can_delete and item_form.instance.pk %}
                                            <div class="form-check">
                                                {{ item_form.DELETE }}
                                                <label class="form-check-label text-danger" for="{{ item_form.DELETE.id_for_label }}">
                                                    {{ item_form.DELETE.label }}
                                                </label>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    {# The "Add" button for JavaScript to use #}
                    <div class="mb-3">
                        <button type="button" id="add-item-form" class="btn btn-success btn-sm">
                            Add Another Fee Item
                        </button>
                    </div>
                </fieldset>

                {# --- Final Submit Buttons --- #}
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        {% if object %}Update{% else %}Save{% endif %} Fee Structure
                    </button>
                    <a href="{% url 'fees:fee_structure_list' %}" class="btn btn-secondary ms-2">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

{# Hidden empty form template for JavaScript to clone #}
<div id="empty-item-form-template" style="display: none;">
    <div class="fee-item-row border p-3 mb-3" id="items-__prefix__-row">
        {{ item_formset.empty_form.id }}
        <div class="row align-items-center">
            <div class="col-md-3 mb-2">
                <label for="{{ item_formset.empty_form.fee_head.id_for_label }}" class="form-label">{{ item_formset.empty_form.fee_head.label }}</label>
                {{ item_formset.empty_form.fee_head }}
            </div>
            <div class="col-md-2 mb-2">
                <label for="{{ item_formset.empty_form.amount.id_for_label }}" class="form-label">{{ item_formset.empty_form.amount.label }}</label>
                {{ item_formset.empty_form.amount }}
            </div>
            <div class="col-md-4 mb-2">
                <label for="{{ item_formset.empty_form.description.id_for_label }}" class="form-label">{{ item_formset.empty_form.description.label }}</label>
                {{ item_formset.empty_form.description }}
            </div>
            <div class="col-md-2 mb-2">
                <div class="form-check mt-4">
                    {{ item_formset.empty_form.is_optional }}
                    <label class="form-check-label" for="{{ item_formset.empty_form.is_optional.id_for_label }}">
                        {{ item_formset.empty_form.is_optional.label }}
                    </label>
                </div>
            </div>
            {# Newly added forms can just be removed from the DOM #}
            <div class="col-md-1 mb-2 pt-4">
                <button type="button" class="btn btn-danger btn-sm remove-item-form">Remove</button>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Selectors for formset functionality
    const formsetContainer = document.getElementById('fee-items-formset-container');
    const addButton = document.getElementById('add-item-form');
    const emptyFormTemplateDiv = document.getElementById('empty-item-form-template');
    // Be more specific with the total forms input selector
    const totalFormsInput = document.querySelector('input[id="id_items-TOTAL_FORMS"]');

    // Check if all elements exist before adding listeners
    if (formsetContainer && addButton && emptyFormTemplateDiv && totalFormsInput) {
        console.log("All formset elements found. Attaching 'Add' listener.");

        addButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Get current form count from the management form
            let formCount = parseInt(totalFormsInput.value);

            // Create new form HTML by replacing the prefix placeholder
            const newFormHtml = emptyFormTemplateDiv.innerHTML.replace(/__prefix__/g, formCount);

            // Append new form to container
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newFormHtml;
            formsetContainer.appendChild(tempDiv.firstElementChild);

            // Increment the form count in the management form
            totalFormsInput.value = formCount + 1;
        });

        // Add event listener for removing dynamically added forms
        formsetContainer.addEventListener('click', function(e) {
            // Check if a 'remove' button was clicked inside the container
            if (e.target && e.target.classList.contains('remove-item-form')) {
                e.preventDefault();
                // Find the closest parent row and remove it
                const formRowToRemove = e.target.closest('.fee-item-row');
                if (formRowToRemove) {
                    formRowToRemove.remove();
                    // After removal, re-index the remaining forms
                    updateFormsetIndexes();
                }
            }
        });

    } else {
        console.error("Could not find all necessary formset elements. 'Add' functionality disabled.");
        console.log("formsetContainer:", formsetContainer);
        console.log("addButton:", addButton);
        console.log("emptyFormTemplateDiv:", emptyFormTemplateDiv);
        console.log("totalFormsInput:", totalFormsInput);
    }

    // Function to re-index form prefixes after a dynamic removal
    function updateFormsetIndexes() {
        const formRows = formsetContainer.querySelectorAll('.fee-item-row');
        // Update the management form's total count
        totalFormsInput.value = formRows.length;

        formRows.forEach((formRow, index) => {
            // Update all inputs, selects, labels within this form row
            formRow.querySelectorAll('input, select, textarea, label').forEach(el => {
                ['id', 'name', 'for'].forEach(attr => {
                    if (el.hasAttribute(attr)) {
                        // Regex to find 'items-X-' and replace X with the new index
                        const newAttrValue = el.getAttribute(attr).replace(/items-\d+-/g, `items-${index}-`);
                        el.setAttribute(attr, newAttrValue);
                    }
                });
            });
            // Update the row ID itself if needed
            formRow.id = `items-${index}-row`;
        });
    }

    // AJAX functionality for term population based on academic year
    const academicYearSelect = document.getElementById('id_academic_year');
    const termSelect = document.getElementById('id_term');

    if (academicYearSelect && termSelect) {
        academicYearSelect.addEventListener('change', function() {
            const academicYearId = this.value;

            // Clear current term options except the default
            termSelect.innerHTML = '<option value="">--- (Applies to Full Academic Year) ---</option>';

            if (academicYearId) {
                // Make AJAX request to get terms for this academic year
                fetch(`/tenant-fees/ajax/terms-for-year/?academic_year_id=${academicYearId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.terms && Array.isArray(data.terms)) {
                        data.terms.forEach(term => {
                            const option = document.createElement('option');
                            option.value = term.id;
                            option.textContent = term.name;
                            termSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching terms:', error);
                });
            }
        });
    }
});
</script>
{% endblock %}






{# D:\school_fees_saas_v2\apps\students\templates\students\student_detail.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize i18n %} {# Added i18n here #}

{% block tenant_page_title %}{{ view_title|default:"Student Details" }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }} {# Ensures tenant_base.html CSS (like tenant_form_styles) is included #}
    <link rel="stylesheet" href="{% static 'css/student_detail_styles.css' %}"> {# Optional: for highly specific styles #}
    <style>
        /* Minor adjustments if needed */
        .profile-pic-lg {
            width: 150px; 
            height: 150px; 
            object-fit: cover; 
            border: 3px solid #dee2e6; /* Slightly softer border */
        }
        .dl-horizontal dt { /* For definition lists if you want them side-by-side on larger screens */
            /* white-space: normal; */
            /* text-align: right; */
        }
        .tab-content {
            min-height: 250px; /* Ensure tab content area has some min height */
        }
        .card-header h5 {
            font-size: 1.1rem; /* Slightly smaller card headers */
        }
    </style>
{% endblock %}

{% block tenant_specific_content %} {# This block replaces the default content in tenant_base.html #}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <div class="pagetitle">
            <h1>{{ student.get_full_name|default:"Student Profile" }}</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">{% trans "Students" %}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ student.get_full_name|truncatechars:30 }}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if perms.students.change_student %} {# Assuming standard Django permissions #}
            <a href="{% url 'students:student_update' student.pk %}" class="btn btn-primary btn-sm me-2">
                <i class="bi bi-pencil-square"></i> {% trans "Edit Student" %}
            </a>
            {% endif %}
            {% if perms.students.delete_student %}
            <a href="{% url 'students:student_delete' student.pk %}" class="btn btn-danger btn-sm">
                <i class="bi bi-trash"></i> {% trans "Delete Student" %}
            </a>
            {% endif %}
        </div>
    </div>

    {% include "partials/_messages.html" %} {# Ensure this partial exists and is styled #}

    <div class="row">
        {# Left Column: Profile Summary & Linked Parents #}
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light text-dark"> {# Changed header style for profile card #}
                    <h5 class="mb-0 py-1">{% trans "Profile Summary" %}</h5>
                </div>
                <div class="card-body text-center pt-3">
                    {% if student.photo %}
                        <img src="{{ student.photo.url }}" alt="Photo of {{ student.get_full_name }}" class="img-fluid rounded-circle mb-3 profile-pic-lg">
                    {% else %}
                        <div class="text-center text-secondary mb-3" style="font-size: 8rem; line-height: 1;">
                            <i class="bi bi-person-circle"></i>
                        </div>
                    {% endif %}
                    <h4>{{ student.get_full_name }}</h4>
                    <p class="text-muted mb-1">{% trans "Admission No" %}: {{ student.admission_number }}</p>
                    <p class="text-muted mb-1">{% trans "Class" %}: {{ student.current_class.name|default:"N/A" }}{% if student.current_section %} - {{ student.current_section.name }}{% endif %}</p>
                    <p class="text-muted mb-2">{% trans "Roll No" %}: {{ student.roll_number|default:"N/A" }}</p>
                    <span class="badge fs-6 {% if student.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                        {% if student.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                        {% endif %}
                    </span>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 py-1">{% trans "Linked Parents/Guardians" %}</h5>
                    {% if perms.students.change_student %} {# Or 'students.manage_parent_links' #}
                    <a href="{% url 'students:student_manage_parents' student.pk %}" class="btn btn-sm btn-outline-primary py-0 px-2" title="{% trans 'Manage linked parents' %}">
                        <i class="bi bi-link-45deg"></i> {% trans "Manage" %}
                    </a>
                    {% endif %}
                </div>
                {% if linked_parents %}
                    <div class="list-group list-group-flush">
                        {% for parent_user_instance in linked_parents %}
                            <div class="list-group-item">
                                <div>
                                    <strong>{{ parent_user_instance.get_full_name|default:parent_user_instance.email }}</strong>
                                </div>
                                <small class="text-muted">
                                    {{ parent_user_instance.email }}
                                    {% if parent_user_instance.phone_number %}| {{ parent_user_instance.phone_number }}{% endif %}
                                </small>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="card-body">
                        <p class="text-muted small">{% trans "No parents/guardians are currently linked." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        {# Right Column: Detailed Information Tabs #}
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light text-dark"> {# Changed header style #}
                    <h5 class="mb-0 py-1">{% trans "Detailed Information" %}</h5>
                </div>
                <div class="card-body pt-2">
                    <ul class="nav nav-tabs nav-fill mb-3" id="studentDetailTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">{% trans "Personal" %}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="guardian-tab" data-bs-toggle="tab" data_bs_target="#guardian" type="button" role="tab" aria-controls="guardian" aria-selected="false">{% trans "Guardian(s)" %}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">{% trans "Contact" %}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="other-tab" data-bs-toggle="tab" data-bs-target="#other" type="button" role="tab" aria-controls="other" aria-selected="false">{% trans "Other Info" %}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab" aria-controls="fees" aria-selected="false">{% trans "Fees & Billing" %}</button>
                        </li>
                    </ul>
                    <div class="tab-content p-2" id="studentDetailTabsContent">
                        <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                            <dl class="row">
                                <dt class="col-sm-4">{% trans "Date of Birth" %}:</dt><dd class="col-sm-8">{{ student.date_of_birth|date:"F d, Y"|default:"N/A" }}</dd>
                                <dt class="col-sm-4">{% trans "Age" %}:</dt><dd class="col-sm-8">{% if student.calculated_age is not None %}{{ student.calculated_age }} {% trans "years" %}{% else %}N/A{% endif %}</dd>
                                <dt class="col-sm-4">{% trans "Gender" %}:</dt><dd class="col-sm-8">{{ student.get_gender_display|default:"N/A" }}</dd>
                                <dt class="col-sm-4">{% trans "Date of Admission" %}:</dt><dd class="col-sm-8">{{ student.date_of_admission|date:"F d, Y"|default:"N/A" }}</dd>
                                <dt class="col-sm-4">{% trans "Blood Group" %}:</dt><dd class="col-sm-8">{{ student.get_blood_group_display|default:"N/A" }}</dd>
                            </dl>
                        </div>
                        <div class="tab-pane fade" id="guardian" role="tabpanel" aria-labelledby="guardian-tab">
                            <h6>{% trans "Primary Guardian / Parent 1" %}</h6>
                            <dl class="row">
                                <dt class="col-sm-4">{% trans "Full Name" %}:</dt><dd class="col-sm-8">{{ student.guardian1_full_name|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Relationship" %}:</dt><dd class="col-sm-8">{{ student.guardian1_relationship|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Phone" %}:</dt><dd class="col-sm-8">{{ student.guardian1_phone|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Email" %}:</dt><dd class="col-sm-8">{{ student.guardian1_email|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Occupation" %}:</dt><dd class="col-sm-8">{{ student.guardian1_occupation|default:"-" }}</dd>
                            </dl>
                            {% if student.guardian2_full_name %}
                            <hr>
                            <h6>{% trans "Secondary Guardian / Parent 2" %}</h6>
                            <dl class="row">
                                <dt class="col-sm-4">{% trans "Full Name" %}:</dt><dd class="col-sm-8">{{ student.guardian2_full_name|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Relationship" %}:</dt><dd class="col-sm-8">{{ student.guardian2_relationship|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Phone" %}:</dt><dd class="col-sm-8">{{ student.guardian2_phone|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Email" %}:</dt><dd class="col-sm-8">{{ student.guardian2_email|default:"-" }}</dd>
                            </dl>
                            {% endif %}
                        </div>
                        <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                            <h6>{% trans "Student's Contact" %}</h6>
                            <dl class="row">
                                <dt class="col-sm-4">{% trans "Email" %}:</dt><dd class="col-sm-8">{{ student.student_email|default:"-" }}</dd>
                                <dt class="col-sm-4">{% trans "Phone" %}:</dt><dd class="col-sm-8">{{ student.student_phone|default:"-" }}</dd>
                            </dl>
                            <hr>
                            <h6>{% trans "Residential Address" %}</h6>
                            <address class="mb-0">
                                {% if student.address_line1 %}{{ student.address_line1 }}<br>{% endif %}
                                {% if student.address_line2 %}{{ student.address_line2 }}<br>{% endif %}
                                {% if student.city %}{{ student.city }}{% endif %}{% if student.city and student.state_province %}, {% endif %}{% if student.state_province %}{{ student.state_province }}{% endif %} {% if student.postal_code %}{{ student.postal_code }}{% endif %}<br>
                                {% if student.country %}{{ student.get_country_display|default:student.country }}{% endif %}
                                {% if not student.address_line1 and not student.city and not student.country %}-{% endif %}
                            </address>
                        </div>
                        <div class="tab-pane fade" id="other" role="tabpanel" aria-labelledby="other-tab"> {# Renamed from medical for broader scope #}
                            <dl class="row">
                                <dt class="col-sm-4">{% trans "Allergies" %}:</dt><dd class="col-sm-8">{{ student.allergies|linebreaksbr|default:"None specified" }}</dd>
                                <dt class="col-sm-4">{% trans "Medical Conditions" %}:</dt><dd class="col-sm-8">{{ student.medical_conditions|linebreaksbr|default:"None specified" }}</dd>
                                <dt class="col-sm-4">{% trans "Previous School" %}:</dt><dd class="col-sm-8">{{ student.previous_school|default:"N/A" }}</dd>
                                <dt class="col-sm-4">{% trans "General Notes" %}:</dt><dd class="col-sm-8">{{ student.notes|linebreaksbr|default:"None" }}</dd>
                            </dl>
                        </div>
                        <div class="tab-pane fade" id="fees" role="tabpanel" aria-labelledby="fees-tab">
                            <p class="text-muted">{% trans "Details of assigned fee structures and terms will appear here." %}</p>
                            <p class="text-muted">{% trans "A summary of recent invoices will appear here." %}</p>
                            <button class="btn btn-outline-primary btn-sm" disabled>{% trans "View All Invoices (TBD)" %}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# Removed the separate footer actions with delete button, moved edit/delete to card headers #}
</div>
{% endblock tenant_specific_content %}































{% comment %} {# D:\school_fees_saas_v2\apps\students\templates\students\student_detail.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Student Details" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title }}</h1>
        <div>
            {% if perms.students.change_student %}
            <a href="{% url 'students:student_update' student.pk %}" class="btn btn-primary btn-sm">
                <i class="bi bi-pencil-square"></i> Edit Student
            </a>
            {% endif %}
        </div>
    </div>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">Manage Students</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ student.full_name }} ({{ student.admission_number }})</li>
        </ol>
    </nav>

    {% include "partials/_messages.html" %}

    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Student Profile</h5>
                </div>
                <div class="card-body text-center">
                    {% if student.photo %}
                        <img src="{{ student.photo.url }}" alt="Photo of {{ student.full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #ddd;">
                    {% else %}
                        <div class="text-center text-muted mb-3" style="font-size: 5rem;">
                            <i class="bi bi-person-circle"></i>
                        </div>
                    {% endif %}
                    <h4>{{ student.full_name }}</h4>
                    <p class="text-muted mb-1">Admission No: {{ student.admission_number }}</p>
                    <p class="text-muted mb-1">Class: {{ student.current_class.name|default:"N/A" }} {% if student.current_section %}- {{ student.current_section.name }}{% endif %}</p>
                    <p class="text-muted mb-0">Roll No: {{ student.roll_number|default:"N/A" }}</p>
                    <hr>
                    <span class="badge bg-{% if student.status == 'ACTIVE' %}success{% elif student.status == 'INACTIVE' %}secondary{% elif student.status == 'GRADUATED' %}info{% else %}warning{% endif %} fs-6">
                        {{ student.get_status_display }}
                    </span>
                </div>
            </div>
        </div>

        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Detailed Information</h5>
                </div>
                <div class="card-body">
                    {# Using tabs for better organization #}
                    <ul class="nav nav-tabs" id="studentDetailTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">Personal</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="guardian-tab" data-bs-toggle="tab" data-bs-target="#guardian" type="button" role="tab" aria-controls="guardian" aria-selected="false">Guardian</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">Contact & Address</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="medical-tab" data-bs-toggle="tab" data-bs-target="#medical" type="button" role="tab" aria-controls="medical" aria-selected="false">Medical/Other</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab" aria-controls="fees" aria-selected="false">Fees & Concessions</button>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="studentDetailTabsContent">
                        <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                            <dl class="row">
                                <dt class="col-sm-4">Date of Birth:</dt><dd class="col-sm-8">{{ student.date_of_birth|date:"F d, Y"|default:"N/A" }}</dd>
                                <dt class="col-sm-4">Gender:</dt><dd class="col-sm-8">{{ student.get_gender_display|default:"N/A" }}</dd>
                                <dt class="col-sm-4">Date of Admission:</dt><dd class="col-sm-8">{{ student.date_of_admission|date:"F d, Y" }}</dd>
                            </dl>
                        </div>
                        <div class="tab-pane fade" id="guardian" role="tabpanel" aria-labelledby="guardian-tab">
                            <h6>Guardian 1</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Full Name:</dt><dd class="col-sm-8">{{ student.guardian1_full_name|default:"-" }}</dd>
                                <dt class="col-sm-4">Relationship:</dt><dd class="col-sm-8">{{ student.guardian1_relationship|default:"-" }}</dd>
                                <dt class="col-sm-4">Phone:</dt><dd class="col-sm-8">{{ student.guardian1_phone|default:"-" }}</dd>
                                <dt class="col-sm-4">Email:</dt><dd class="col-sm-8">{{ student.guardian1_email|default:"-" }}</dd>
                                <dt class="col-sm-4">Occupation:</dt><dd class="col-sm-8">{{ student.guardian1_occupation|default:"-" }}</dd>
                            </dl>
                            <hr>
                            <h6>Guardian 2 (Optional)</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Full Name:</dt><dd class="col-sm-8">{{ student.guardian2_full_name|default:"-" }}</dd>
                                <dt class="col-sm-4">Relationship:</dt><dd class="col-sm-8">{{ student.guardian2_relationship|default:"-" }}</dd>
                                <dt class="col-sm-4">Phone:</dt><dd class="col-sm-8">{{ student.guardian2_phone|default:"-" }}</dd>
                                <dt class="col-sm-4">Email:</dt><dd class="col-sm-8">{{ student.guardian2_email|default:"-" }}</dd>
                            </dl>
                        </div>
                        <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                            <h6>Student Contact</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Email:</dt><dd class="col-sm-8">{{ student.student_email|default:"-" }}</dd>
                                <dt class="col-sm-4">Phone:</dt><dd class="col-sm-8">{{ student.student_phone|default:"-" }}</dd>
                            </dl>
                            <hr>
                            <h6>Address</h6>
                            <address>
                                {{ student.address_line1|default:"" }}<br>
                                {% if student.address_line2 %}{{ student.address_line2 }}<br>{% endif %}
                                {{ student.city|default:"" }}{% if student.city and student.state_province %}, {% endif %}{{ student.state_province|default:"" }} {{ student.postal_code|default:"" }}<br>
                                {{ student.country|default:"" }}
                            </address>
                        </div>
                        <div class="tab-pane fade" id="medical" role="tabpanel" aria-labelledby="medical-tab">
                            <dl class="row">
                                <dt class="col-sm-4">Blood Group:</dt><dd class="col-sm-8">{{ student.get_blood_group_display|default:"N/A" }}</dd>
                                <dt class="col-sm-4">Allergies:</dt><dd class="col-sm-8">{{ student.allergies|linebreaksbr|default:"None specified" }}</dd>
                                <dt class="col-sm-4">Medical Conditions:</dt><dd class="col-sm-8">{{ student.medical_conditions|linebreaksbr|default:"None specified" }}</dd>
                                <dt class="col-sm-4">Previous School:</dt><dd class="col-sm-8">{{ student.previous_school|default:"N/A" }}</dd>
                                <dt class="col-sm-4">Notes:</dt><dd class="col-sm-8">{{ student.notes|linebreaksbr|default:"None" }}</dd>
                            </dl>
                        </div>
                        <div class="tab-pane fade" id="fees" role="tabpanel" aria-labelledby="fees-tab">
                            <h5 class="mt-3">Assigned Fee Structures</h5>
                            {# Table for allocations - Placeholder #}
                            <p class="text-muted">Fee allocations will be listed here. <a href="#">Assign Fee Structure (TMP)</a></p>

                            <h5 class="mt-4">Assigned Concessions</h5>
                            {# Table for concessions - Placeholder #}
                            <p class="text-muted">Concessions will be listed here. <a href="#">Assign Concession (TMP)</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer-actions mt-3">
        <a href="{% url 'students:student_list' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left-circle me-2"></i>Back to Student List</a>
        {% if perms.students.delete_student %}
        <form action="{% url 'students:student_delete' student.pk %}" method="post" style="display: inline; margin-left: 10px;">
            {% csrf_token %}
            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete student \'{{ student.full_name|escapejs }}\'? This will remove all associated records.');"><i class="bi bi-trash-fill me-2"></i>Delete Student</button>
        </form>
        {% endif %}
    </div>
</div>
{% endblock content %}
 {% endcomment %}

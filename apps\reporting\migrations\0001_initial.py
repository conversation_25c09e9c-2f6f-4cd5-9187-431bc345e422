# Generated by Django 5.1.9 on 2025-06-18 20:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ReportingPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'Reporting Permission Set',
                'verbose_name_plural': 'Reporting Permission Sets',
                'permissions': [('view_collection_report', 'Can view Collection Report'), ('view_outstanding_fees_report', 'Can view Outstanding Fees Report'), ('view_student_ledger_report', 'Can view Student Ledger Report'), ('view_payment_summary_report', 'Can view Payment Summary Report'), ('view_trial_balance_report', 'Can view Trial Balance Report'), ('view_income_statement_report', 'Can view Income Statement (P&L)'), ('view_balance_sheet_report', 'Can view Balance Sheet Report'), ('view_cash_flow_statement_report', 'Can view Cash Flow Statement Report'), ('view_budget_variance_report', 'Can view Budget Variance Report'), ('view_expense_report', 'Can view Expense Report'), ('view_fee_projection_report', 'Can view Fee Projection Report')],
                'managed': False,
                'default_permissions': (),
            },
        ),
    ]

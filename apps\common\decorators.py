# D:\school_fees_saas_v2\apps\common\decorators.py

from functools import wraps
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

# Safe import of StaffUser model
try:
    from apps.schools.models import StaffUser
except ImportError:
    StaffUser = None

def staff_permission_required(permission):
    """
    A decorator to check if a user is an authenticated staff member
    and has a specific permission.
    
    Args:
        permission (str): The permission codename to check (e.g., 'fees.view_invoice').
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            user_to_check = getattr(request, 'effective_tenant_user', request.user)

            # 1. Check if user is authenticated and is a StaffUser instance
            if not (user_to_check.is_authenticated and StaffUser and isinstance(user_to_check, StaffUser)):
                messages.error(request, _("You must be logged in as staff to view this page."))
                # You might want to redirect to a staff-specific login page
                # from django.urls import reverse_lazy
                # return redirect(reverse_lazy('schools:staff_login'))
                raise PermissionDenied

            # 2. Check for the specific permission
            if not user_to_check.has_perm(permission):
                messages.error(request, _("You do not have permission to perform this action."))
                # Or redirect to a 'permission-denied' page or the dashboard
                raise PermissionDenied
            
            # If both checks pass, execute the original view function
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator



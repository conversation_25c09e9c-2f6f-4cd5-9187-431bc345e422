{# D:\school_fees_saas_v2\apps\reporting\templates\reporting\fee_projection_report.html #}
{% extends "tenant_base.html" %}
{% load static humanize %}

{% comment %} {% block tenant_page_title %}{{ view_title|default:"Fee Projection Report" }}{% endblock %} {% endcomment %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# Add any specific CSS for this report if needed #}
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-graph-up-arrow me-2" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Fee Projection Filters" %}

{% comment %} <div class="container-fluid">
    <div class="pagetitle mb-3">
        <h1><i class="bi bi-graph-up-arrow me-2"></i>{{ view_title }}</h1> {% endcomment %}
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item">Reports</li>
                <li class="breadcrumb-item active">{{ view_title }}</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ report_description|default:"Details of projected fee income." }}</h5>
        </div>
        <div class="card-body mt-3">
            {% if error_message %}
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>{{ error_message }}
                </div>
            {% endif %}

            {% if current_academic_year %}
                <div class="alert alert-info mb-3" role="alert">
                    <strong>Academic Year:</strong> {{ current_academic_year.name }}
                    <span class="ms-3"><strong>Total Active Students:</strong> {{ total_active_students|intcomma }}</span>
                    {% if total_projected %}
                        <span class="ms-3"><strong>Total Projected Income:</strong> ${{ total_projected|floatformat:2|intcomma }}</span>
                    {% endif %}
                </div>
            {% endif %}

            {% if projected_items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Class/Category</th>
                                <th class="text-end">Projected Amount</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in projected_items %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td class="text-end fw-bold">${{ item.amount|floatformat:2|intcomma }}</td>
                                <td class="text-muted">{{ item.notes }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th>Total Projected Income</th>
                                <th class="text-end fw-bold">${{ total_projected|floatformat:2|intcomma }}</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>No projection data available. This may be due to:
                    <ul class="mb-0 mt-2">
                        <li>No active students enrolled</li>
                        <li>No fee structures configured</li>
                        <li>No current academic year set</li>
                    </ul>
                </div>
            {% endif %}
        </div>
        <div class="card-footer text-muted small">
            Report generated on: {% now "jS F Y, P" %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



{# templates/portal_admin/group_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .role-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border: 2px solid #28a745;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .role-info h6 {
            color: #155724;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .role-info p {
            color: #155724;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Role Management") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'portal_admin:group_list' %}">{% trans "Roles" %}</a></li>
            <li class="breadcrumb-item active">{{ view_title|default:_("Form") }}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-people-fill me-2"></i>
                        {% if object %}
                            {% trans "Update Role" %}
                        {% else %}
                            {% trans "Create New Role" %}
                        {% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Information Section -->
                    <div class="role-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>{% trans "Role Management" %}</h6>
                        <p>{% trans "Roles define groups of permissions that can be assigned to staff members. Create meaningful role names like 'Accountant', 'HR Manager', or 'Teacher'." %}</p>
                    </div>

                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        <!-- Role Name Field -->
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control{% if form.name.errors %} is-invalid{% endif %}"
                                   id="{{ form.name.id_for_label }}"
                                   name="{{ form.name.name }}"
                                   value="{{ form.name.value|default:'' }}"
                                   placeholder="{% trans 'Enter role name' %}"
                                   required>
                            <label for="{{ form.name.id_for_label }}">
                                <i class="bi bi-tag-fill icon-input"></i>{% trans "Role Name" %}
                            </label>
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'portal_admin:group_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}{% trans "Update Role" %}{% else %}{% trans "Create Role" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_tenant_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form elements
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');

        // Auto-focus name input
        if (nameInput) {
            nameInput.focus();
        }

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Enhanced input interactions
        if (nameInput) {
            nameInput.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            nameInput.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Real-time validation
            nameInput.addEventListener('input', function() {
                const value = this.value.trim();

                // Remove invalid class if user is typing
                if (this.classList.contains('is-invalid') && value.length > 0) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }

                // Basic validation
                if (value.length < 2) {
                    this.setCustomValidity('{% trans "Role name must be at least 2 characters long" %}');
                } else if (value.length > 50) {
                    this.setCustomValidity('{% trans "Role name must be less than 50 characters" %}');
                } else {
                    this.setCustomValidity('');
                }
            });

            // Character counter
            const maxLength = 50;
            const counter = document.createElement('div');
            counter.className = 'form-text text-end';
            counter.style.fontSize = '0.8rem';

            function updateCounter() {
                const remaining = maxLength - nameInput.value.length;
                counter.textContent = `${nameInput.value.length}/${maxLength} characters`;
                counter.style.color = remaining < 10 ? '#dc3545' : '#6c757d';
            }

            nameInput.addEventListener('input', updateCounter);
            nameInput.parentElement.appendChild(counter);
            updateCounter();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    });
    </script>
{% endblock %}



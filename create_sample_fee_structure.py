#!/usr/bin/env python
"""
Script to create sample fee structure data for testing
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_tenants.utils import schema_context
from apps.fees.models import FeeHead, FeeStructure, FeeStructureItem
from apps.schools.models import AcademicYear, Term, School
from decimal import Decimal

def create_sample_data():
    try:
        # Get alpha tenant
        alpha = School.objects.get(schema_name='alpha')
        print(f"Working with tenant: {alpha.name}")

        # Create sample data in alpha tenant
        with schema_context('alpha'):
            # Create fee heads if they don't exist
            tuition_fee, created = FeeHead.objects.get_or_create(
                name="Tuition Fee",
                defaults={'description': 'Monthly tuition fee'}
            )
            print(f"Tuition Fee: {'Created' if created else 'Exists'} - ID: {tuition_fee.id}")
            
            library_fee, created = FeeHead.objects.get_or_create(
                name="Library Fee", 
                defaults={'description': 'Library access fee'}
            )
            print(f"Library Fee: {'Created' if created else 'Exists'} - ID: {library_fee.id}")
            
            transport_fee, created = FeeHead.objects.get_or_create(
                name="Transport Fee",
                defaults={'description': 'School bus transportation fee'}
            )
            print(f"Transport Fee: {'Created' if created else 'Exists'} - ID: {transport_fee.id}")
            
            # Get academic year and term
            academic_year = AcademicYear.objects.filter(is_active=True).first()
            if not academic_year:
                print("No active academic year found!")
                return
            
            term = Term.objects.filter(academic_year=academic_year).first()
            print(f"Using Academic Year: {academic_year.name}")
            if term:
                print(f"Using Term: {term.name}")
            
            # Create sample fee structure - avoid constraint issues
            try:
                fee_structure = FeeStructure.objects.get(
                    name="Grade 1-5 Monthly Fees",
                    academic_year=academic_year
                )
                created = False
                print(f"Fee Structure: Exists - ID: {fee_structure.id}")
            except FeeStructure.DoesNotExist:
                # Create manually to avoid constraint issues
                fee_structure = FeeStructure(
                    name="Grade 1-5 Monthly Fees",
                    academic_year=academic_year,
                    description='Standard monthly fee structure for primary grades',
                    is_active=True
                )
                if term:
                    fee_structure.term = term
                fee_structure.save()
                created = True
                print(f"Fee Structure: Created - ID: {fee_structure.id}")
            print(f"Fee Structure: {'Created' if created else 'Exists'} - ID: {fee_structure.id}")
            
            # Create fee structure items
            items_data = [
                (tuition_fee, Decimal('15000.00'), 'Monthly tuition fee', False),
                (library_fee, Decimal('500.00'), 'Library access and books', False),
                (transport_fee, Decimal('2000.00'), 'School bus service', True),
            ]
            
            for fee_head, amount, description, is_optional in items_data:
                item, created = FeeStructureItem.objects.get_or_create(
                    fee_structure=fee_structure,
                    fee_head=fee_head,
                    defaults={
                        'amount': amount,
                        'description': description,
                        'is_optional': is_optional
                    }
                )
                print(f"Fee Item {fee_head.name}: {'Created' if created else 'Exists'} - Amount: {amount}")
            
            print("\n✅ Sample fee structure created successfully!")
            print(f"Fee Structure ID: {fee_structure.id}")
            print(f"Total Items: {fee_structure.items.count()}")
            
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_sample_data()

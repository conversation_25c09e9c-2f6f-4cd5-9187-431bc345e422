{# apps/accounting/templates/accounting/general_ledger.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize i18n %}

{% block title %}{% trans "General Ledger" %}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        .ledger-table th, .ledger-table td {
            white-space: nowrap;
            font-size: 0.9rem;
        }
        .text-balance {
            font-weight: 600;
        }
        .filter-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .account-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        .je-link {
            color: #0d6efd;
            text-decoration: none;
            font-weight: 500;
        }
        .je-link:hover {
            text-decoration: underline;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .export-buttons {
            gap: 0.5rem;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">
            <i class="bi bi-journal-text me-2"></i>{% trans "General Ledger" %}
        </h1>
        <div class="d-flex export-buttons">
            {% if transactions %}
            <button class="btn btn-outline-success btn-sm" onclick="exportToCSV()">
                <i class="bi bi-file-earmark-spreadsheet me-1"></i>{% trans "Export CSV" %}
            </button>
            <button class="btn btn-outline-danger btn-sm" onclick="exportToPDF()">
                <i class="bi bi-file-earmark-pdf me-1"></i>{% trans "Export PDF" %}
            </button>
            {% endif %}
        </div>
    </div>

    {# Filter Form #}
    <div class="card filter-card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-funnel me-2"></i>{% trans "Filter Options" %}
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ filter_form.account_type.id_for_label }}" class="form-label">{{ filter_form.account_type.label }}</label>
                    {{ filter_form.account_type }}
                </div>
                <div class="col-md-3">
                    <label for="{{ filter_form.account.id_for_label }}" class="form-label">{{ filter_form.account.label }}</label>
                    {{ filter_form.account }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.start_date.id_for_label }}" class="form-label">{{ filter_form.start_date.label }}</label>
                    {{ filter_form.start_date }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.end_date.id_for_label }}" class="form-label">{{ filter_form.end_date.label }}</label>
                    {{ filter_form.end_date }}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <div class="form-check me-3">
                        {{ filter_form.show_only_posted }}
                        <label class="form-check-label" for="{{ filter_form.show_only_posted.id_for_label }}">
                            {{ filter_form.show_only_posted.label }}
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>{% trans "Filter" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    {# Summary Information #}
    {% if transactions %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h5 class="card-title">{% trans "Total Transactions" %}</h5>
                    <h3 class="mb-0">{{ transactions|length|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h5 class="card-title">{% trans "Accounts Involved" %}</h5>
                    <h3 class="mb-0">{{ filtered_accounts_count|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h5 class="card-title">{% trans "Total Debits" %}</h5>
                    <h3 class="mb-0">{{ school_profile.currency_symbol|default:"$" }}{{ total_debits|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h5 class="card-title">{% trans "Total Credits" %}</h5>
                    <h3 class="mb-0">{{ school_profile.currency_symbol|default:"$" }}{{ total_credits|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
    </div>

    {# Period Information #}
    {% if date_range_display %}
    <div class="alert alert-info">
        <i class="bi bi-calendar-range me-2"></i>
        <strong>{% trans "Reporting Period" %}:</strong> {{ date_range_display }}
    </div>
    {% endif %}
    {% endif %}

    {# General Ledger Table #}
    {% if transactions %}
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                {% trans "General Ledger Transactions" %}
                <span class="badge bg-primary ms-2">{{ transactions|length }} {% trans "entries" %}</span>
            </h5>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover ledger-table mb-0" id="generalLedgerTable">
                <thead class="table-light">
                    <tr>
                        <th>{% trans "Date" %}</th>
                        <th>{% trans "JE ID" %}</th>
                        <th>{% trans "Account Code" %}</th>
                        <th>{% trans "Account Name" %}</th>
                        <th>{% trans "Account Type" %}</th>
                        <th>{% trans "Description" %}</th>
                        <th class="text-end">{% trans "Debit" %}</th>
                        <th class="text-end">{% trans "Credit" %}</th>
                        <th class="text-center">{% trans "Entry Type" %}</th>
                        <th class="text-center">{% trans "Status" %}</th>
                        <th>{% trans "Created By" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tx in transactions %}
                    <tr>
                        <td>{{ tx.date|date:"d M Y" }}</td>
                        <td>
                            <a href="{% url 'accounting:journalentry_detail' pk=tx.je_id %}" 
                               class="je-link" 
                               title="View {{ tx.je_number }}">
                                {{ tx.je_number }}
                            </a>
                        </td>
                        <td class="account-code">{{ tx.account_code }}</td>
                        <td>{{ tx.account_name|truncatechars:30 }}</td>
                        <td>
                            <span class="badge bg-secondary rounded-pill">{{ tx.account_type }}</span>
                        </td>
                        <td>{{ tx.description|truncatechars:40 }}</td>
                        <td class="text-end {% if tx.debit > 0 %}text-success fw-bold{% endif %}">
                            {% if tx.debit > 0 %}
                                {{ school_profile.currency_symbol|default:"$" }}{{ tx.debit|floatformat:2|intcomma }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end {% if tx.credit > 0 %}text-danger fw-bold{% endif %}">
                            {% if tx.credit > 0 %}
                                {{ school_profile.currency_symbol|default:"$" }}{{ tx.credit|floatformat:2|intcomma }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge bg-info rounded-pill">{{ tx.entry_type }}</span>
                        </td>
                        <td class="text-center">
                            <span class="badge status-badge 
                                {% if tx.status == 'Posted' %}bg-success
                                {% elif tx.status == 'Draft' %}bg-warning
                                {% else %}bg-secondary{% endif %} rounded-pill">
                                {{ tx.status }}
                            </span>
                        </td>
                        <td>{{ tx.created_by|truncatechars:20 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-light">
                    <tr class="fw-bold">
                        <td colspan="6" class="text-end">{% trans "TOTALS:" %}</td>
                        <td class="text-end text-success">
                            {{ school_profile.currency_symbol|default:"$" }}{{ total_debits|floatformat:2|intcomma }}
                        </td>
                        <td class="text-end text-danger">
                            {{ school_profile.currency_symbol|default:"$" }}{{ total_credits|floatformat:2|intcomma }}
                        </td>
                        <td colspan="3"></td>
                    </tr>
                    <tr class="fw-bold">
                        <td colspan="6" class="text-end">{% trans "DIFFERENCE:" %}</td>
                        <td colspan="2" class="text-center
                            {% if is_balanced %}text-success
                            {% else %}text-warning{% endif %}">
                            {{ school_profile.currency_symbol|default:"$" }}{{ balance_difference|floatformat:2|intcomma }}
                            {% if is_balanced %}
                                <i class="bi bi-check-circle-fill ms-1"></i>
                            {% else %}
                                <i class="bi bi-exclamation-triangle-fill ms-1"></i>
                            {% endif %}
                        </td>
                        <td colspan="3"></td>
                    </tr>
                    <tr class="fw-bold">
                        <td colspan="6" class="text-end">{% trans "STATUS:" %}</td>
                        <td colspan="2" class="text-center
                            {% if is_balanced %}text-success
                            {% else %}text-warning{% endif %}">
                            {% if is_balanced %}
                                <span class="text-success">{% trans "BALANCED" %}</span>
                            {% else %}
                                <span class="text-warning">{% trans "OUT OF BALANCE" %}</span>
                            {% endif %}
                        </td>
                        <td colspan="3"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-journal-x display-1 text-muted mb-3"></i>
            <h4 class="text-muted">{% trans "No Transactions Found" %}</h4>
            <p class="text-muted">
                {% if filter_form.is_bound %}
                    {% trans "No transactions match your current filter criteria. Try adjusting the filters above." %}
                {% else %}
                    {% trans "Apply filters above to view general ledger transactions." %}
                {% endif %}
            </p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block page_specific_js %}
<script>
function exportToCSV() {
    // Simple CSV export functionality
    const table = document.getElementById('generalLedgerTable');
    let csv = [];
    
    // Get headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push('"' + th.textContent.trim() + '"');
    });
    csv.push(headers.join(','));
    
    // Get data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            // Clean up the cell content
            let content = td.textContent.trim();
            content = content.replace(/"/g, '""'); // Escape quotes
            row.push('"' + content + '"');
        });
        csv.push(row.join(','));
    });
    
    // Download
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'general_ledger_' + new Date().toISOString().split('T')[0] + '.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function exportToPDF() {
    // For now, just print the page - can be enhanced with proper PDF generation
    window.print();
}

// Initialize Select2 for account dropdowns if available
$(document).ready(function() {
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2-account').select2({
            placeholder: "{% trans 'Select an account...' %}",
            allowClear: true,
            width: '100%'
        });
    }
});
</script>
{% endblock %}

# apps/tenants/management/commands/test_tenant_creation.py

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from apps.tenants.models import School, Domain
from apps.schools.models import StaffUser
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

class Command(BaseCommand):
    help = 'Test tenant creation process and StaffUser auto-creation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--owner-email',
            type=str,
            required=True,
            help='Email of the owner user (must exist in public schema)'
        )
        parser.add_argument(
            '--school-name',
            type=str,
            required=True,
            help='Name of the test school'
        )
        parser.add_argument(
            '--schema-name',
            type=str,
            required=True,
            help='Schema name for the test tenant'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up the test tenant after creation'
        )

    def handle(self, *args, **options):
        owner_email = options['owner_email']
        school_name = options['school_name']
        schema_name = options['schema_name']
        cleanup = options.get('cleanup', False)
        
        self.stdout.write(f'Testing tenant creation for: {school_name} (schema: {schema_name})')
        
        # Check if owner exists
        try:
            owner = User.objects.get(email=owner_email)
            self.stdout.write(f'Found owner: {owner.email} ({owner.first_name} {owner.last_name})')
        except User.DoesNotExist:
            raise CommandError(f'Owner user with email "{owner_email}" does not exist')
        
        # Check if tenant already exists
        if School.objects.filter(schema_name=schema_name).exists():
            if cleanup:
                self.stdout.write(f'Cleaning up existing tenant: {schema_name}')
                School.objects.filter(schema_name=schema_name).delete()
            else:
                raise CommandError(f'Tenant with schema "{schema_name}" already exists. Use --cleanup to remove it first.')
        
        # Create tenant
        self.stdout.write(f'Creating tenant: {school_name}')
        
        try:
            # Create the school (tenant)
            school = School.objects.create(
                name=school_name,
                schema_name=schema_name,
                owner=owner
            )
            self.stdout.write(self.style.SUCCESS(f'Created school: {school.name} (ID: {school.pk})'))
            
            # Create domain
            domain = Domain.objects.create(
                domain=f'{schema_name}.myapp.test',
                tenant=school,
                is_primary=True
            )
            self.stdout.write(self.style.SUCCESS(f'Created domain: {domain.domain}'))
            
            # Check if StaffUser was created automatically
            self.stdout.write(f'Checking if StaffUser was created automatically...')
            
            with schema_context(schema_name):
                try:
                    staff_users = StaffUser.objects.filter(email__iexact=owner_email)
                    
                    if staff_users.exists():
                        staff_user = staff_users.first()
                        self.stdout.write(self.style.SUCCESS(f'✅ StaffUser created automatically: {staff_user.email} (ID: {staff_user.pk})'))
                        self.stdout.write(f'   - is_owner_profile: {getattr(staff_user, "is_owner_profile", "N/A")}')
                        self.stdout.write(f'   - is_superuser: {staff_user.is_superuser}')
                        self.stdout.write(f'   - is_staff: {staff_user.is_staff}')
                        self.stdout.write(f'   - is_active: {staff_user.is_active}')
                        self.stdout.write(f'   - groups: {list(staff_user.groups.values_list("name", flat=True))}')
                    else:
                        self.stdout.write(self.style.ERROR(f'❌ StaffUser NOT created automatically for {owner_email}'))
                        
                        # Try to create manually using the signal function
                        self.stdout.write('Attempting to create StaffUser manually...')
                        from apps.tenants.signals import create_staff_user_for_owner
                        
                        staff_user, created = create_staff_user_for_owner(school)
                        if staff_user:
                            self.stdout.write(self.style.SUCCESS(f'✅ Manually created StaffUser: {staff_user.email} (ID: {staff_user.pk})'))
                        else:
                            self.stdout.write(self.style.ERROR(f'❌ Failed to create StaffUser manually'))
                
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error checking StaffUser: {str(e)}'))
                    logger.exception(f'Error in StaffUser check for tenant {schema_name}')
            
            # Cleanup if requested
            if cleanup:
                self.stdout.write(f'Cleaning up test tenant: {schema_name}')
                try:
                    school.delete()  # This should also delete the domain due to CASCADE
                    self.stdout.write(self.style.SUCCESS('Cleanup complete'))
                except Exception as cleanup_error:
                    self.stdout.write(self.style.WARNING(f'Cleanup had issues but tenant was removed: {cleanup_error}'))
                    # Try to clean up manually
                    try:
                        Domain.objects.filter(tenant=school).delete()
                        from django.db import connection
                        with connection.cursor() as cursor:
                            cursor.execute(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE")
                        self.stdout.write(self.style.SUCCESS('Manual cleanup completed'))
                    except Exception as manual_cleanup_error:
                        self.stdout.write(self.style.ERROR(f'Manual cleanup also failed: {manual_cleanup_error}'))
            else:
                self.stdout.write(f'Test tenant created successfully. Use --cleanup to remove it later.')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating tenant: {str(e)}'))
            logger.exception(f'Error creating test tenant {schema_name}')
            raise CommandError(f'Failed to create tenant: {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('Test complete!'))

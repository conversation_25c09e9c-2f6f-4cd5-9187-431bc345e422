# D:\school_fees_saas_v2\apps\hr\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, FormView
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from decimal import Decimal # For LeaveBalance calculations

from apps.common.mixins import TenantPermissionRequiredMixin

# --- Authentication & Permissions ---
import logging
from django.urls import reverse_lazy, reverse
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib.auth.decorators import login_required # Not used for CBVs here

# --- Models ---
from .models import EmployeeProfile, LeaveType, LeaveBalance, LeaveRequest
from apps.schools.models import StaffUser # Needed for type checking request.user

# --- Forms ---
from .forms import (
    StaffLeaveRequestForm,
    AdminLeaveRequestUpdateForm,
    # LeaveBalanceForm, # Uncomment if you create this form
)
from .models import LeaveType, LeaveRequest
from .forms import LeaveTypeForm, StaffLeaveRequestForm


# --- THIS IS WHERE THE FIX GOES ---
# You are already importing other forms, just add PayrollPeriodForm to the list
from .forms import (
    SalaryComponentForm, 
    TaxBracketForm, 
    StaffSalaryForm, 
    StaffSalaryComponentFormSet, 
    PayrollPeriodForm  # <<< ADD THIS
)

# Import your models
from .models import SalaryComponent, TaxBracket, StaffSalary
from apps.schools.models import StaffUser

logger = logging.getLogger(__name__)


# ========================================
# --- Leave Type CRUD Views ---
# ========================================

# D:\school_fees_saas_v2\apps\hr\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages

from .models import LeaveType, LeaveRequest # Add LeaveBalance, StaffUser later
# from .forms import LeaveTypeForm # Add LeaveRequestForm later
# from apps.schools.models import StaffUser # If needed for requestor info

# --- LeaveType CRUD ---
class LeaveTypeListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = LeaveType
    template_name = 'hr/leavetype_list.html'
    context_object_name = 'leave_types'
    permission_required = 'hr.view_leavetype' # Ensure these permissions exist
    login_url = reverse_lazy('schools:staff_login') # Or your tenant login

    def get_queryset(self):
        # Ensures data is fetched from the current tenant's schema
        return LeaveType.objects.all().order_by('name')

class LeaveTypeCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = LeaveType
    form_class = LeaveTypeForm
    template_name = 'hr/leavetype_form.html'
    permission_required = 'hr.add_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    success_message = "Leave Type '%(name)s' created successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Leave Type"
        context['form_mode'] = "create"
        return context

class LeaveTypeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveType
    form_class = LeaveTypeForm
    template_name = 'hr/leavetype_form.html'
    permission_required = 'hr.change_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    success_message = "Leave Type '%(name)s' updated successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Update Leave Type: {self.object.name}"
        context['form_mode'] = "update"
        return context

class LeaveTypeDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = LeaveType
    template_name = 'hr/leavetype_confirm_delete.html'
    permission_required = 'hr.delete_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    login_url = reverse_lazy('schools:staff_login')

    def post(self, request, *args, **kwargs):
        # Add success message manually for DeleteView
        # Check if it has associated leave requests first, ideally
        leave_type_name = self.get_object().name
        if LeaveRequest.objects.filter(leave_type=self.get_object()).exists():
            messages.error(request, f"Cannot delete '{leave_type_name}' as it has associated leave requests.")
            return redirect(self.success_url)
        
        messages.success(request, f"Leave Type '{leave_type_name}' deleted successfully.")
        return super().post(request, *args, **kwargs)

# --- END LeaveType CRUD ---

# ========================================
# --- Leave Request Views ---
# ========================================

class StaffLeaveRequestListView(LoginRequiredMixin, ListView): # No specific permission, just login
    model = LeaveRequest
    template_name = 'hr/staff_leaverequest_list.html'
    feature_code = 'HR_LEAVE_MANAGEMENT'
    context_object_name = 'leave_requests'
    login_url = reverse_lazy('schools:staff_login')

    def get_queryset(self):
        try:
            # Ensure the user has an employee profile
            employee_profile = self.request.user.employeeprofile # Or self.request.user.hr_profile
            # If the above line fails with DoesNotExist, it will be caught by the except block
            
            # Now filter LeaveRequest objects by this profile
            return LeaveRequest.objects.filter(employee=employee_profile).order_by('-start_date')
        except AttributeError: # If 'employeeprofile' doesn't even exist as an attribute
            messages.error(self.request, "Your employee profile attribute is missing. Please contact admin.")
            return LeaveRequest.objects.none() 
        except self.request.user.employeeprofile.RelatedObjectDoesNotExist: # Or specific model's DoesNotExist
            # This is the most likely scenario if the record is missing
            messages.error(self.request, "Your employee profile is not set up correctly to view leave requests. Please contact an administrator.")
            return LeaveRequest.objects.none() # Return an empty queryset
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return LeaveRequest.objects.none()

    def dispatch(self, request, *args, **kwargs):
        # Alternative place for the check
        if not hasattr(request.user, 'employeeprofile') or request.user.employeeprofile is None:
            messages.error(request, "Your employee profile is not set up correctly to view leave requests. Please contact an administrator.")
            # Redirect to dashboard or another appropriate page
            return redirect('schools:dashboard') # Or wherever appropriate
        return super().dispatch(request, *args, **kwargs)
    
    # def get_queryset(self):
    #     # Staff can only see their own leave requests
    #     if hasattr(self.request.user, 'hr_profile') and self.request.user.hr_profile:
    #         employee_profile = self.request.user.hr_profile
    #         # Assuming 'request_date' is the correct field for submission time
    #         return LeaveRequest.objects.filter(employee=employee_profile).order_by('-request_date')
    #     else:
    #         # Handle case where user might not have an hr_profile
    #         # This might indicate a data setup issue or a user type that shouldn't access this
    #         messages.warning(self.request, "Your employee profile is not set up correctly to view leave requests.")
    #         return LeaveRequest.objects.none() # Return an empty queryset
        
    # # def get_queryset(self):
    # #     # Staff can only see their own leave requests
    # #     return LeaveRequest.objects.filter(staff=self.request.user).order_by('-submitted_at')

class StaffLeaveRequestCreateView(LoginRequiredMixin, SuccessMessageMixin, CreateView):
    model = LeaveRequest
    form_class = StaffLeaveRequestForm
    template_name = 'hr/staff_leaverequest_form.html'
    success_url = reverse_lazy('hr:staff_leaverequest_list')
    success_message = "Your leave request has been submitted successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user # Pass current user to the form
        return kwargs

    def form_valid(self, form):
        form.instance.staff = self.request.user # Assign staff member making the request
        # Optionally set manager if you have that logic (e.g., from staff profile)
        # form.instance.approved_by = ... (leave as None initially)
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Apply for Leave"
        return context

class StaffLeaveRequestDetailView(LoginRequiredMixin, DetailView):
    model = LeaveRequest
    template_name = 'hr/staff_leaverequest_detail.html'
    context_object_name = 'leave_request'
    login_url = reverse_lazy('schools:staff_login')

    def get_queryset(self):
        # Staff can only see their own leave requests
        return LeaveRequest.objects.filter(staff=self.request.user)
        
class StaffLeaveRequestCancelView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveRequest
    fields = [] # No fields to edit, just changing status
    template_name = 'hr/staff_leaverequest_cancel_confirm.html' # Confirmation template
    success_url = reverse_lazy('hr:staff_leaverequest_list')
    success_message = "Your leave request has been cancelled."
    login_url = reverse_lazy('schools:staff_login')

    def get_queryset(self):
        # Staff can only cancel their own PENDING leave requests
        return LeaveRequest.objects.filter(staff=self.request.user, status=LeaveRequest.LeaveStatus.PENDING)

    def form_valid(self, form):
        leave_request = form.save(commit=False)
        if leave_request.status == LeaveRequest.LeaveStatus.PENDING:
            leave_request.status = LeaveRequest.LeaveStatus.CANCELLED_BY_STAFF
            leave_request.status_changed_at = timezone.now()
            leave_request.status_reason = "Cancelled by staff member."
            leave_request.save()
        else:
            messages.error(self.request, "This leave request cannot be cancelled as it's no longer pending.")
            return redirect(self.success_url) # Or to detail view
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.object.status != LeaveRequest.LeaveStatus.PENDING:
            context['can_cancel'] = False
        else:
            context['can_cancel'] = True
        return context

# --- END Staff Leave Request Views ---


# ========================================
# --- Leave Admin Views ---
# ========================================

class AdminLeaveRequestListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = LeaveRequest
    template_name = 'hr/admin_leaverequest_list.html' # Create this template
    context_object_name = 'leave_requests'
    permission_required = 'hr.view_leaverequest' # Or a more specific admin permission
    login_url = reverse_lazy('schools:staff_login')
    paginate_by = 20 # Optional pagination

    def get_queryset(self):
        # HR Admins see all requests, or filter as needed (e.g., by status, department)
        # For now, let's show all, ordered by submission time or status
        return LeaveRequest.objects.all().order_by('status', '-request_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Administer Leave Requests"
        # Add any filters to context if you implement form-based filtering
        # context['filter_form'] = LeaveRequestFilterForm(self.request.GET)
        return context



class LeaveRequestAdminUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveRequest
    form_class = AdminLeaveRequestUpdateForm
    template_name = 'hr/leave_request_admin_update_form.html'
    success_url = reverse_lazy('hr:leave_request_list') # Or a dedicated admin list
    success_message = "Leave request status updated."
    context_object_name = 'leave_request'
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'hr.change_leaverequest' # Permission to approve/reject

    def form_valid(self, form):
        leave_request = form.save(commit=False)
        admin_user = self.request.user

        # Ensure admin_user has an EmployeeProfile to be an approver
        # This might not always be true (e.g. platform superuser vs. tenant staff admin)
        # For now, assume admin performing action has an hr_profile
        if not isinstance(admin_user, StaffUser) or not hasattr(admin_user, 'hr_profile'):
            messages.error(self.request, "Action requires the approver to have a staff profile.")
            return self.form_invalid(form)

        original_status = LeaveRequest.objects.get(pk=leave_request.pk).status
        new_status = leave_request.status

        leave_request.approved_by = admin_user.hr_profile
        leave_request.approval_date = timezone.now() if new_status in ['APPROVED', 'REJECTED', 'CANCELLED_BY_ADMIN'] else None

        try:
            with transaction.atomic():
                response = super().form_valid(form) # Saves the leave_request

                # Update LeaveBalance
                employee_profile = leave_request.employee
                leave_type = leave_request.leave_type
                days_requested = leave_request.number_of_days

                balance, created = LeaveBalance.objects.get_or_create(
                    employee=employee_profile,
                    leave_type=leave_type,
                    # academic_year=... # If balances are yearly
                    defaults={'days_taken': Decimal('0.00'), 'days_accrued': leave_type.default_annual_days or Decimal('0.00')}
                )

                if new_status == 'APPROVED' and original_status != 'APPROVED':
                    balance.days_taken = (balance.days_taken or Decimal('0.00')) + days_requested
                    messages.info(self.request, f"Leave balance updated: {days_requested} days deducted for {employee_profile.user.full_name}.")
                elif original_status == 'APPROVED' and new_status != 'APPROVED': # Reverting an approval
                    balance.days_taken = (balance.days_taken or Decimal('0.00')) - days_requested
                    messages.info(self.request, f"Leave balance reverted: {days_requested} days added back for {employee_profile.user.full_name}.")
                
                balance.save()
                # TODO: Send notification to staff about status change
            return response
        except Exception as e:
            messages.error(self.request, f"Error updating leave request or balance: {e}")
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f'Admin Update Leave: {self.object.employee.user.full_name} ({self.object.leave_type.name})'
        return context


# --- Leave Balance Views ---
class LeaveBalanceListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = LeaveBalance
    template_name = 'hr/leave_balance_list.html'
    context_object_name = 'balances'
    paginate_by = 50
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'hr.view_leavebalance' # Admin/Manager permission

    def get_queryset(self):
        return LeaveBalance.objects.select_related(
            'employee__user', 'leave_type'
        ).order_by('employee__user__last_name', 'leave_type__name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = 'All Staff Leave Balances (Admin View)'
        return context

# TODO: Add views for manually adjusting LeaveBalance if needed (e.g., LeaveBalanceUpdateView)
# These would also need appropriate permissions.



# D:\school_fees_saas_v2\apps\hr\views.py
# D:\school_fees_saas_v2\apps\hr\views.py
from django.shortcuts import render, redirect, get_object_or_404 # Add get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils import timezone

from .models import LeaveBalance, LeaveRequest, LeaveType
# Assuming EmployeeProfile is in hr.models or schools.models
# Let's assume it's in hr.models for this example. Adjust if it's elsewhere.
from .models import EmployeeProfile # Make sure this is imported
from apps.schools.models import StaffUser
from django.core.exceptions import ObjectDoesNotExist # Import this

@login_required(login_url='schools:staff_login')
def my_leave_summary_view(request):
    if not isinstance(request.user, StaffUser):
        return redirect('schools:staff_login')

    staff_user_instance = request.user
    current_year_val = timezone.now().year
    employee_profile_instance = None # Initialize
    leave_balances = LeaveBalance.objects.none() # Initialize to empty queryset
    leave_requests = LeaveRequest.objects.none() # Initialize to empty queryset

    try:
        # Corrected: Use 'user' as the field name linking EmployeeProfile to StaffUser
        employee_profile_instance = EmployeeProfile.objects.get(user=staff_user_instance)

    except EmployeeProfile.DoesNotExist:
        # This means no EmployeeProfile is linked to this StaffUser.
        # You might want to add a message to the user or log this.
        # e.g., messages.warning(request, "Your employee profile is not yet set up. Please contact an administrator.")
        pass # leave_balances and leave_requests remain empty querysets
    # Removed the AttributeError catch as DoesNotExist is more specific for .get()

    if employee_profile_instance:
        # This block will only execute if an EmployeeProfile was found.
        leave_balances = LeaveBalance.objects.filter(
            employee=employee_profile_instance,
            year_or_period_info=current_year_val # <--- Corrected field name
        ).select_related('leave_type').order_by('leave_type__name')

        # Adjust LeaveRequest query based on how it links
        # Assuming LeaveRequest ALSO links to EmployeeProfile via an 'employee' field:
        leave_requests = LeaveRequest.objects.filter(
            employee=employee_profile_instance, # This 'employee' field on LeaveRequest also needs to be correct
        ).select_related('leave_type').order_by('-request_date')[:10]
        
        # OR If LeaveRequest links directly to StaffUser via a 'staff_member' or 'user' field:
        # leave_requests = LeaveRequest.objects.filter(
        #     user=staff_user_instance, # or staff_member=staff_user_instance
        # ).select_related('leave_type').order_by('-request_date')[:10]

    context = {
        'view_title': "My Leave Summary",
        'leave_balances': leave_balances,
        'leave_requests': leave_requests,
        'current_staff_member': staff_user_instance,
    }
    return render(request, 'hr/my_leave_summary.html', context)




from .payroll import PayrollProcessor
from .models import Payslip, PayslipLineItem, StaffUser

class GeneratePayrollView(TenantPermissionRequiredMixin, FormView):
    template_name = 'hr/generate_payroll_form.html'
    form_class = PayrollPeriodForm # A simple form with month/year fields
    success_url = reverse_lazy('hr:payslip_list') # Redirect to the list of generated payslips
    permission_required = 'hr.add_payslip' # Or a custom 'can_run_payroll' permission

    def form_valid(self, form):
        pay_period_start = form.cleaned_data['pay_period_start']
        pay_period_end = form.cleaned_data['pay_period_end']
        
        active_staff = StaffUser.objects.filter(is_active=True)
        generated_count = 0
        
        for staff in active_staff:
            # Check if a payslip for this period already exists
            if Payslip.objects.filter(staff_user=staff, pay_period_start=pay_period_start).exists():
                logger.info(f"Payslip for {staff.email} for this period already exists. Skipping.")
                continue

            processor = PayrollProcessor(staff, pay_period_start, pay_period_end)
            result = processor.run()
            
            if result:
                with transaction.atomic():
                    # Create the main Payslip record
                    payslip = Payslip.objects.create(
                        staff_user=staff,
                        pay_period_start=pay_period_start,
                        pay_period_end=pay_period_end,
                        gross_earnings=result['gross_earnings'],
                        total_deductions=result['total_deductions'],
                        net_pay=result['net_pay'],
                        status=Payslip.PayslipStatus.GENERATED
                    )
                    
                    # Create the line items
                    line_items = []
                    for item in result['earnings_lines']:
                        line_items.append(PayslipLineItem(
                            payslip=payslip, name=item['name'], type='EARNING', 
                            amount=item['amount'], source_component=item.get('source')
                        ))
                    for item in result['deductions_lines']:
                        line_items.append(PayslipLineItem(
                            payslip=payslip, name=item['name'], type='DEDUCTION', 
                            amount=item['amount'], source_component=item.get('source')
                        ))
                    
                    PayslipLineItem.objects.bulk_create(line_items)
                    generated_count += 1
        
        messages.success(self.request, f"Successfully generated {generated_count} payslips for the selected period.")
        return super().form_valid(form)
    






# Generated by Django 5.1.9 on 2025-06-19 19:31

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('setting_name', models.CharField(max_length=100, unique=True, verbose_name='Setting Name')),
                ('setting_value', models.TextField(verbose_name='Setting Value')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Platform Setting',
                'verbose_name_plural': 'Platform Settings',
                'ordering': ['setting_name'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('PAYMENT_GATEWAY', 'Payment Gateway Action'), ('CONFIG_CHANGE', 'Configuration Change'), ('SECURITY', 'Security Event'), ('SYSTEM', 'System Action'), ('MAINTENANCE_MODE_TOGGLE', 'Maintenance Mode Toggle'), ('PLATFORM_SETTING_CHANGE', 'Platform Setting Change'), ('TENANT_CREATE', 'Tenant Created'), ('TENANT_STATUS_CHANGE', 'Tenant Status Change'), ('OTHER', 'Other Platform Action')], db_index=True, max_length=30, verbose_name='Action Type')),
                ('model_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Model Name')),
                ('object_pk', models.CharField(blank=True, max_length=100, null=True, verbose_name='Object PK')),
                ('description', models.TextField(verbose_name='Description')),
                ('details', models.JSONField(blank=True, null=True, verbose_name='Additional Details')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.CharField(blank=True, max_length=500)),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Timestamp')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_log_entries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Audit Log',
                'verbose_name_plural': 'Platform Audit Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceMode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name_override', models.CharField(blank=True, help_text="Name to display during maintenance mode (e.g., 'Our Platform')", max_length=100, null=True)),
                ('is_enabled', models.BooleanField(db_index=True, default=False, verbose_name='Maintenance Mode Enabled')),
                ('title', models.CharField(blank=True, default='System Maintenance', max_length=200)),
                ('message', models.TextField(default='The platform is currently under maintenance. We expect to be back shortly. Thank you for your patience.', verbose_name='Maintenance Message')),
                ('allowed_ips', models.TextField(blank=True, help_text='Comma-separated or newline-separated IP addresses that can bypass maintenance mode.', verbose_name='Allowed IPs')),
                ('bypass_key', models.CharField(blank=True, help_text='A secret key in URL query (e.g., ?maintenance_bypass=secretkey) to bypass mode.', max_length=50, null=True, verbose_name='Bypass Key')),
                ('bypass_staff', models.BooleanField(default=True, help_text='Allow platform superusers/staff to bypass maintenance mode.')),
                ('enabled_at', models.DateTimeField(blank=True, null=True, verbose_name='Enabled At')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enabled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='maintenance_mode_activations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Maintenance Mode Setting',
                'verbose_name_plural': 'Maintenance Mode Settings',
            },
        ),
        migrations.CreateModel(
            name='PlatformAnnouncement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('content', models.TextField(verbose_name='Content')),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Announcement becomes visible from this date/time.', verbose_name='Publish Date')),
                ('expiry_date', models.DateTimeField(blank=True, help_text='Optional: Announcement will be hidden/archived after this date/time.', null=True, verbose_name='Expiry Date')),
                ('is_published', models.BooleanField(db_index=True, default=True, help_text='Uncheck to keep as a draft.', verbose_name='Is Published')),
                ('severity', models.CharField(choices=[('INFO', 'Informational'), ('SUCCESS', 'Success'), ('WARNING', 'Warning'), ('DANGER', 'Critical/Maintenance')], default='INFO', max_length=10, verbose_name='Severity Level')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_platform_announcements', to=settings.AUTH_USER_MODEL, verbose_name='Platform Author')),
            ],
            options={
                'verbose_name': 'Platform Announcement',
                'verbose_name_plural': 'Platform Announcements',
                'ordering': ['-publish_date'],
            },
        ),
        migrations.CreateModel(
            name='SystemNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('message', models.TextField(verbose_name='Message')),
                ('notification_type', models.CharField(choices=[('INFO', 'Information'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('SUCCESS', 'Success')], default='INFO', max_length=20, verbose_name='Notification Type')),
                ('is_active', models.BooleanField(db_index=True, default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Notification becomes visible from this date/time.', verbose_name='Publish Date')),
                ('expires_at', models.DateTimeField(blank=True, help_text='Notification will be hidden after this date/time.', null=True, verbose_name='Expires At')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_system_notifications', to=settings.AUTH_USER_MODEL)),
                ('target_users', models.ManyToManyField(blank=True, help_text='Select specific platform users. Leave empty for a general platform announcement.', related_name='platform_notifications_received', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Notification',
                'verbose_name_plural': 'System Notifications',
                'ordering': ['-publish_date', '-created_at'],
            },
        ),
    ]

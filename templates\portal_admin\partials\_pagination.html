{% if is_paginated %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination pagination-sm justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                    <span aria-hidden="true">««</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                    <span aria-hidden="true">«</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled"><span class="page-link">««</span></li>
            <li class="page-item disabled"><span class="page-link">«</span></li>
        {% endif %}

        {% for i in paginator.page_range %}
            {% if page_obj.number == i %}
                <li class="page-item active" aria-current="page"><span class="page-link">{{ i }}</span></li>
            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
            {% elif i == page_obj.number|add:'-3' or i == page_obj.number|add:'3' %}
                <li class="page-item disabled"><span class="page-link">...</span></li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                    <span aria-hidden="true">»</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                    <span aria-hidden="true">»»</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled"><span class="page-link">»</span></li>
            <li class="page-item disabled"><span class="page-link">»»</span></li>
        {% endif %}
    </ul>
</nav>
{% endif %}

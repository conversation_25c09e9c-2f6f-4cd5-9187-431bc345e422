{# D:\school_fees_saas_v2\apps\hr\templates\hr\pdf\payslip_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load static humanize math_filters %}

{% block pdf_title %}Payslip - {{ payslip.payroll_run.pay_period_start|date:"F Y" }} - {{ payslip.staff_member.get_full_name }}{% endblock %}

{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 60%; padding-right: 5mm; vertical-align: top;">
                {% if school_profile and school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 18mm; max-width: 50mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0; font-size: 14pt; color: #333; font-weight: bold;">
                    {% if school_profile %}{{ school_profile.school_name_on_reports|default:tenant.name }}{% else %}{{ tenant.name }}{% endif %}
                </h2>
                {% if school_profile and school_profile.get_full_address %}
                    <p style="margin: 1mm 0 0 0; font-size: 8pt; color: #555;">{{ school_profile.get_full_address }}</p>
                {% endif %}
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;">
                <h1 style="margin:0; font-size:24pt; color: #3498db; text-transform: uppercase;">Payslip</h1>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Pay Period:</strong> {{ payslip.payroll_run.pay_period_start|date:"d M Y" }} - {{ payslip.payroll_run.pay_period_end|date:"d M Y" }}</p>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Payment Date:</strong> {{ payslip.payroll_run.payment_date|date:"d M Y" }}</p>
            </td>
        </tr>
    </table>
    <hr style="margin: 2mm 0; border: none; border-top: 0.5px solid #555;">
{% endblock pdf_header_content %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .employee-details-table td { padding: 1mm 0; font-size: 9pt; border: none; }
        
        .two-col-container { width: 100%; margin-top: 5mm; border-collapse: separate; border-spacing: 5mm 0; }
        .two-col-container > tbody > tr > td { vertical-align: top; padding: 0; }

        .payslip-section-table { width: 100%; border-collapse: collapse; font-size: 9.5pt; }
        .payslip-section-table th, .payslip-section-table td { border: 0.5px solid #ccc; padding: 2.5mm; }
        .payslip-section-table thead th { background-color: #f2f2f2; font-weight: bold; text-align: left; }
        .payslip-section-table .amount-col { text-align: right; width: 35%; font-family: 'Menlo', 'Consolas', monospace; }
        .payslip-section-table .summary-row { font-weight: bold; background-color: #f2f2f2; border-top: 1px solid #333; }

        .net-pay-section { margin-top: 5mm; text-align: right; }
        .net-pay-box { display: inline-block; background-color: #0d6efd; color: white; padding: 3mm 5mm; border-radius: 4px; }
        .net-pay-box span { font-size: 11pt; }
        .net-pay-box strong { font-size: 12pt; font-weight: bold; letter-spacing: 0.5px; }

        .notes-section { margin-top: 7mm; font-size: 8pt; border-top: 1px solid #eee; padding-top: 3mm; }
    </style>
{% endblock %}


{% block pdf_main_content %}
    <table class="employee-details-table" style="width: 100%; margin-bottom: 5mm;">
        <tr>
            <td style="width: 50%;"><strong>Employee Name:</strong> {{ payslip.staff_member.get_full_name }}</td>
            <td style="width: 50%;"><strong>Employee ID:</strong> {{ payslip.staff_member.employee_id|default:"N/A" }}</td>
        </tr>
        <tr>
            <td><strong>Designation:</strong> {{ payslip.staff_member.designation|default:"N/A" }}</td>
            <td><strong>Department:</strong> N/A</td>
        </tr>
    </table>

    <!-- Main container table for two columns -->
    <table class="two-col-container">
        <tr>
            <!-- Left Column: Earnings -->
            <td style="width: 50%;">
                <table class="payslip-section-table">
                    <thead>
                        <tr>
                            <th>Earnings</th>
                            <th class="amount-col">Amount ({{ school_profile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>Basic Salary</td><td class="amount-col">{{ payslip.basic_salary|floatformat:2|intcomma }}</td></tr>
                        {% if payslip.allowances > 0 %}<tr><td>Allowances</td><td class="amount-col">{{ payslip.allowances|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.bonuses > 0 %}<tr><td>Bonuses</td><td class="amount-col">{{ payslip.bonuses|floatformat:2|intcomma }}</td></tr>{% endif %}
                        
                        {# Display positive adjustments as an earning #}
                        {% if payslip.adjustments > 0 %}
                            <tr><td>Adjustments (Bonus)</td><td class="amount-col">{{ payslip.adjustments|floatformat:2|intcomma }}</td></tr>
                        {% endif %}

                        <tr class="summary-row"><td>Gross Earnings</td><td class="amount-col">{{ payslip.gross_earnings|floatformat:2|intcomma }}</td></tr>
                    </tbody>
                </table>
            </td>
            <!-- Right Column: Deductions -->
            <td style="width: 50%;">
                <table class="payslip-section-table">
                    <thead>
                        <tr>
                            <th>Deductions</th>
                            <th class="amount-col">Amount ({{ school_profile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if payslip.tax_deductions > 0 %}<tr><td>Tax (PAYE)</td><td class="amount-col">{{ payslip.tax_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.pension_deductions > 0 %}<tr><td>Pension</td><td class="amount-col">{{ payslip.pension_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.loan_repayments > 0 %}<tr><td>Loan Repayments</td><td class="amount-col">{{ payslip.loan_repayments|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.other_deductions > 0 %}<tr><td>Other Deductions</td><td class="amount-col">{{ payslip.other_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}

                        {# Display negative adjustments as a deduction #}
                        {% if payslip.adjustments < 0 %}
                            <tr><td>Adjustments (Deduction)</td><td class="amount-col">{{ payslip.adjustments|abs|floatformat:2|intcomma }}</td></tr>
                        {% endif %}

                        <tr class="summary-row"><td>Total Deductions</td><td class="amount-col">{{ payslip.total_deductions|floatformat:2|intcomma }}</td></tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>

    <!-- Net Pay Section -->
    <div class="net-pay-section">
        <div class="net-pay-box">
            <span>NET PAY: </span>
            <strong>{{ school_profile.currency_symbol|default:'$' }}{{ payslip.net_pay|floatformat:2|intcomma }}</strong>
        </div>
    </div>

    {% if payslip.notes %}
        <div class="notes-section">
            <strong>Notes:</strong> {{ payslip.notes|linebreaksbr }}
        </div>
    {% endif %}

{% endblock pdf_main_content %}







{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\pdf\payslip_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize %}

{% block pdf_title %}Payslip - {{ payslip.payroll_run.pay_period_start|date:"F Y" }} - {{ payslip.staff_member.get_full_name }}{% endblock %}

{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse; vertical-align: top;">
        <tr>
            <td style="width: 60%; padding-right: 5mm; vertical-align: top;">
                {% if school_profile and school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 18mm; max-width: 50mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0; font-size: 14pt; color: #333; font-weight: bold;">
                    {% if school_profile %}{{ school_profile.school_name_on_reports|default:tenant.name }}{% else %}{{ tenant.name }}{% endif %}
                </h2>
                {% if school_profile and school_profile.get_full_address %}
                    <p style="margin: 1mm 0 0 0; font-size: 8pt; color: #555;">{{ school_profile.get_full_address }}</p>
                {% endif %}
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;">
                <h1 style="margin:0; font-size:24pt; color: #3498db; text-transform: uppercase;">Payslip</h1>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Pay Period:</strong> {{ payslip.payroll_run.pay_period_start|date:"d M, Y" }} - {{ payslip.payroll_run.pay_period_end|date:"d M, Y" }}</p>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Payment Date:</strong> {{ payslip.payroll_run.payment_date|date:"d M, Y" }}</p>
            </td>
        </tr>
    </table>
    <hr style="margin: 2mm 0; border: none; border-top: 0.5px solid #555;">
{% endblock pdf_header_content %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .employee-details-table td { padding: 1mm 0; font-size: 9pt; border: none; }
        
        .two-col-container { width: 100%; margin-top: 5mm; border-collapse: separate; border-spacing: 5mm 0; }
        .two-col-container > tbody > tr > td { vertical-align: top; padding: 0; }

        .payslip-section-table { width: 100%; border-collapse: collapse; font-size: 9.5pt; }
        .payslip-section-table th, .payslip-section-table td { border: 0.5px solid #ccc; padding: 2.5mm; }
        .payslip-section-table thead th { background-color: #f2f2f2; font-weight: bold; text-align: left; }
        .payslip-section-table .amount-col { text-align: right; width: 35%; }
        .payslip-section-table .summary-row { font-weight: bold; background-color: #f2f2f2; border-top: 1px solid #333; }

        /* ADD THESE NEW STYLES */
        .net-pay-section {
            margin-top: 5mm;
            text-align: right; /* Aligns the box to the right */
        }
        .net-pay-box {
            display: inline-block; /* Allows the box to size to its content */
            background-color: #0d6efd; /* A strong Bootstrap blue */
            color: white;
            padding: 3mm 5mm;
            border-radius: 4px;
        }
        .net-pay-box span {
            font-size: 11pt;
        }
        .net-pay-box strong {
            font-size: 12pt;
            font-weight: bold;
            letter-spacing: 0.5px;
        }

        .notes-section { margin-top: 7mm; font-size: 8pt; border-top: 1px solid #eee; padding-top: 3mm; }
    </style>
{% endblock %}


{% block pdf_main_content %}
    <table class="employee-details-table" style="width: 100%; margin-bottom: 5mm;">
        <tr>
            <td style="width: 50%;"><strong>Employee Name:</strong> {{ payslip.staff_member.get_full_name }}</td>
            <td style="width: 50%;"><strong>Employee ID:</strong> {{ payslip.staff_member.employee_id|default:"N/A" }}</td>
        </tr>
        <tr>
            <td><strong>Designation:</strong> {{ payslip.staff_member.designation|default:"N/A" }}</td>
            <td><strong>Department:</strong> N/A</td>
        </tr>
    </table>

    <!-- Main container table for two columns -->
    <table class="two-col-container">
        <tr>
            <!-- Left Column: Earnings -->
            <td style="width: 50%;">
                <table class="payslip-section-table">
                    <thead>
                        <tr>
                            <th>Earnings</th>
                            <th class="amount-col">Amount ({{ school_profile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>Basic Salary</td><td class="amount-col">{{ payslip.basic_salary|floatformat:2|intcomma }}</td></tr>
                        {% if payslip.allowances > 0 %}<tr><td>Allowances</td><td class="amount-col">{{ payslip.allowances|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.bonuses > 0 %}<tr><td>Bonuses</td><td class="amount-col">{{ payslip.bonuses|floatformat:2|intcomma }}</td></tr>{% endif %}
                        <tr class="summary-row"><td>Gross Earnings</td><td class="amount-col">{{ payslip.gross_earnings|floatformat:2|intcomma }}</td></tr>
                    </tbody>
                </table>
            </td>
            <!-- Right Column: Deductions -->
            <td style="width: 50%;">
                <table class="payslip-section-table">
                    <thead>
                        <tr>
                            <th>Deductions</th>
                            <th class="amount-col">Amount ({{ school_profile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if payslip.tax_deductions > 0 %}<tr><td>Tax (PAYE)</td><td class="amount-col">{{ payslip.tax_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.pension_deductions > 0 %}<tr><td>Pension</td><td class="amount-col">{{ payslip.pension_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.loan_repayments > 0 %}<tr><td>Loan Repayments</td><td class="amount-col">{{ payslip.loan_repayments|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% if payslip.other_deductions > 0 %}<tr><td>Other Deductions</td><td class="amount-col">{{ payslip.other_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
                        {% comment %} <tr class="summary-row"><td>Total Deductions</td><td class="amount-col">{{ payslip.total_deductions|floatformat:2|intcomma }}</td></tr> #########
                    </tbody>
                </table>
            </td>
        </tr>
    </table>

    <!-- Net Pay Section -->
    <div class="net-pay-section">
        <div class="net-pay-box">
            <span>NET PAY: </span>
            <strong>{{ school_profile.currency_symbol|default:'$' }}{{ payslip.net_pay|floatformat:2|intcomma }}</strong>
        </div>
    </div>

    {% if payslip.notes %}
        <div class="notes-section">
            <strong>Notes:</strong> {{ payslip.notes|linebreaksbr }}
        </div>
    {% endif %}

{% endblock pdf_main_content %}
 {% endcomment %}




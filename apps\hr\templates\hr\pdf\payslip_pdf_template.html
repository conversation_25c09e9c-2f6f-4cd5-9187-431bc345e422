{# D:\school_fees_saas_v2\apps\hr\templates\hr\pdf\payslip_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize %}

{% block pdf_title %}Payslip - {{ payslip.payroll_run.pay_period_start|date:"F Y" }} - {{ payslip.staff_member.get_full_name }}{% endblock %}

{% block pdf_header_content %}
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="width: 60%; vertical-align: top;">
                {% if school_profile.logo and school_profile.logo.path %}
                    <img src="{{ school_profile.logo.path }}" style="max-height: 18mm; max-width: 50mm; margin-bottom: 3mm;" alt="School Logo">
                {% endif %}
                <h2 style="margin: 0; font-size: 14pt; color: #333;">{{ school_profile.school_name_on_reports|default:tenant.name }}</h2>
                <p style="margin: 1mm 0 0 0; font-size: 8pt; color: #555;">{{ school_profile.get_full_address }}</p>
            </td>
            <td style="width: 40%; text-align: right; vertical-align: top;">
                <h1 style="margin:0; font-size:24pt; color: #3498db;">PAYSLIP</h1>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Pay Period:</strong> {{ payslip.payroll_run.pay_period_start|date:"d M Y" }} - {{ payslip.payroll_run.pay_period_end|date:"d M Y" }}</p>
                <p style="margin: 1mm 0 0 0; font-size: 9pt;"><strong>Payment Date:</strong> {{ payslip.payroll_run.payment_date|date:"d M Y" }}</p>
            </td>
        </tr>
    </table>
    <hr style="margin: 2mm 0; border: none; border-top: 0.5px solid #555;">
{% endblock pdf_header_content %}

{% block pdf_extra_styles %}
    {{ block.super }}
    <style type="text/css">
        .employee-details-table td { padding: 1mm 2mm; font-size: 9pt; }
        .payslip-main-table { width: 100%; margin-top: 5mm; border-collapse: collapse; font-size: 9.5pt; }
        .payslip-main-table th, .payslip-main-table td { border: 0.5px solid #ccc; padding: 2.5mm; }
        .payslip-main-table thead th { background-color: #f2f2f2; font-weight: bold; text-align: left; }
        .payslip-main-table .amount-col { text-align: right; }
        .summary-row { font-weight: bold; background-color: #f2f2f2; }
        .net-pay-row { font-weight: bold; background-color: #3498db; color: white; font-size: 11pt; }
    </style>
{% endblock %}

{% block pdf_main_content %}
    <table class="employee-details-table" style="width: 100%; margin-bottom: 5mm;">
        <tr>
            <td style="width: 50%;"><strong>Employee Name:</strong> {{ payslip.staff_member.get_full_name }}</td>
            <td style="width: 50%;"><strong>Employee ID:</strong> {{ payslip.staff_member.employee_id|default:"N/A" }}</td>
        </tr>
        <tr>
            <td><strong>Designation:</strong> {{ payslip.staff_member.designation|default:"N/A" }}</td>
            <td><strong>Department:</strong> {% comment %}Add department if you have it{% endcomment %}N/A</td>
        </tr>
    </table>

    <table class="payslip-main-table">
        <thead>
            <tr>
                <th style="width: 70%;">Description</th>
                <th style="width: 30%;" class="amount-col">Amount ({{ school_profile.currency_symbol|default:'$' }})</th>
            </tr>
        </thead>
        <tbody>
            <!-- Earnings -->
            <tr><td colspan="2" style="background-color: #eafaf1; font-weight: bold;">Earnings</td></tr>
            <tr><td>Basic Salary</td><td class="amount-col">{{ payslip.basic_salary|floatformat:2|intcomma }}</td></tr>
            {% if payslip.allowances > 0 %}<tr><td>Allowances</td><td class="amount-col">{{ payslip.allowances|floatformat:2|intcomma }}</td></tr>{% endif %}
            {% if payslip.bonuses > 0 %}<tr><td>Bonuses</td><td class="amount-col">{{ payslip.bonuses|floatformat:2|intcomma }}</td></tr>{% endif %}
            <tr class="summary-row"><td>Gross Earnings</td><td class="amount-col">{{ payslip.gross_earnings|floatformat:2|intcomma }}</td></tr>

            <!-- Deductions -->
            <tr><td colspan="2" style="background-color: #feefef; font-weight: bold;">Deductions</td></tr>
            {% if payslip.tax_deductions > 0 %}<tr><td>Tax Deductions (PAYE)</td><td class="amount-col">{{ payslip.tax_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
            {% if payslip.pension_deductions > 0 %}<tr><td>Pension Deductions</td><td class="amount-col">{{ payslip.pension_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
            {% if payslip.loan_repayments > 0 %}<tr><td>Loan Repayments</td><td class="amount-col">{{ payslip.loan_repayments|floatformat:2|intcomma }}</td></tr>{% endif %}
            {% if payslip.other_deductions > 0 %}<tr><td>Other Deductions</td><td class="amount-col">{{ payslip.other_deductions|floatformat:2|intcomma }}</td></tr>{% endif %}
            <tr class="summary-row"><td>Total Deductions</td><td class="amount-col">{{ payslip.total_deductions|floatformat:2|intcomma }}</td></tr>
        </tbody>
        <tfoot>
            <tr class="net-pay-row">
                <td>Net Pay</td>
                <td class="amount-col">{{ payslip.net_pay|floatformat:2|intcomma }}</td>
            </tr>
        </tfoot>
    </table>

    {% if payslip.notes %}
        <div style="margin-top: 5mm; font-size: 8pt; border: 1px solid #eee; padding: 2mm;">
            <strong>Notes:</strong> {{ payslip.notes|linebreaksbr }}
        </div>
    {% endif %}

{% endblock pdf_main_content %}



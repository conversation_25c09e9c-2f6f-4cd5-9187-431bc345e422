# Generated by Django 5.1.9 on 2025-06-29 19:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reporting', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'Report Permission',
                'verbose_name_plural': 'Report Permissions',
                'permissions': [('view_outstanding_fees_report', 'Can view outstanding fees report'), ('view_collection_report', 'Can view collection report'), ('view_student_ledger_report', 'Can view student ledger report')],
                'managed': False,
                'default_permissions': (),
            },
        ),
    ]

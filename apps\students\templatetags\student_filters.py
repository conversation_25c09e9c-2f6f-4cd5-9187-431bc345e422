# apps/students/templatetags/student_filters.py
from django import template
from datetime import date

register = template.Library()

@register.filter
def age(birth_date):
    """Calculate age from birth date"""
    if not birth_date:
        return None
    
    today = date.today()
    age = today.year - birth_date.year
    
    # Check if birthday has occurred this year
    if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
        age -= 1
    
    return age

@register.filter
def age_display(birth_date):
    """Display age with proper formatting"""
    calculated_age = age(birth_date)
    if calculated_age is None:
        return "N/A"
    
    if calculated_age == 1:
        return "1 year old"
    else:
        return f"{calculated_age} years old"
    
    
    
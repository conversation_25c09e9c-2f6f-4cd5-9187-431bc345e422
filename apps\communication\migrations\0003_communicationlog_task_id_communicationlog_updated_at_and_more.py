# Generated by Django 5.1.9 on 2025-06-19 21:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('communication', '0002_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='communicationlog',
            name='task_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True, verbose_name='Celery Task ID'),
        ),
        migrations.AddField(
            model_name='communicationlog',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Last Updated'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='body_preview',
            field=models.TextField(blank=True, help_text='A short preview or summary of the message content.', null=True, verbose_name='Body Preview'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype', verbose_name='Related Object Type'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Logged At'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='error_message',
            field=models.TextField(blank=True, null=True, verbose_name='Error Message (if any)'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='message_type',
            field=models.CharField(choices=[('EMAIL', 'Generic Email'), ('WELCOME', 'Welcome Email'), ('FEE_REMINDER', 'Fee Reminder'), ('PAYMENT_CONFIRM', 'Payment Confirmation'), ('INVOICE_ISSUED', 'Invoice Issued'), ('LEAVE_STATUS', 'Leave Status Update'), ('TENANT_GENERAL', 'General Tenant Notification'), ('PLATFORM_ANNOUNCEMENT_EMAIL', 'Platform Announcement (Email)'), ('SMS', 'SMS'), ('APP_NOTIFICATION', 'In-App Notification'), ('OTHER', 'Other Communication')], default='EMAIL', max_length=30, verbose_name='Message Type'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='object_id',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True, verbose_name='Related Object ID'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='recipient_email',
            field=models.EmailField(blank=True, db_index=True, max_length=254, null=True, verbose_name='Recipient Email'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='recipient_phone',
            field=models.CharField(blank=True, db_index=True, max_length=30, null=True, verbose_name='Recipient Phone'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='sent_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when the communication was successfully dispatched.', null=True, verbose_name='Sent At'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='sent_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='initiated_communications', to=settings.AUTH_USER_MODEL, verbose_name='Initiated By (User)'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('SENT', 'Sent'), ('FAILED', 'Failed'), ('DELIVERED', 'Delivered'), ('OPENED', 'Opened'), ('CLICKED', 'Clicked'), ('INVALID_RECIPIENT', 'Invalid Recipient'), ('UNSUBSCRIBED', 'Unsubscribed')], db_index=True, default='PENDING', max_length=20, verbose_name='Status'),
        ),
        migrations.AlterField(
            model_name='communicationlog',
            name='subject',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Subject'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['content_type', 'object_id'], name='communicati_content_40ada6_idx'),
        ),
    ]

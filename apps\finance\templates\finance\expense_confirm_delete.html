{# D:\school_fees_saas_V2\apps\finance\templates\finance\expense_confirm_delete.html #}
{% extends "tenant_base.html" %}

{% load core_tags %}

{% block title %}Confirm Delete - Expense{% endblock %}

{% block content %}
<div class="container mt-4"><div class="row justify-content-center"><div class="col-md-8">
<div class="card shadow-sm"><div class="card-header"><h4 class="mb-0">Confirm Delete Expense</h4></div><div class="card-body">
    <p>Are you sure you want to delete this expense record?</p>
    <p><strong>Description:</strong> {{ expense.description|default:"N/A" }}</p>
    <p><strong>Amount:</strong> {{ expense.amount|intcomma }}</p>
    <p><strong>Date:</strong> {{ expense.expense_date|date:"Y-m-d" }}</p>
    <p class="text-danger"><small>Warning: Deleting this expense will NOT automatically reverse its journal entry. This must be done manually if required.</small></p>
    <form method="post">{% csrf_token %}
        <button type="submit" class="btn btn-danger">Yes, Delete</button>
        <a href="{% url 'finance:expense_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div></div></div></div></div>
{% endblock %}


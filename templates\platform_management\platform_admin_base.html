{# D:\school_fees_saas_v2\templates\platform_management\platform_admin_base.html #}
{% extends "public_base.html" %} {# Or "base.html" if public_base is too specific #}
{% load static i18n core_tags %}

{% block public_page_title %}{% block platform_admin_page_title %}Platform Management{% endblock %}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    {# Add any CSS specific to the platform admin layout #}
    <link rel="stylesheet" href="{% static 'css/platform_admin_styles.css' %}"> 
    {% block extra_platform_admin_css %}{% endblock %}
{% endblock %}

{# Override the navbar from public_base.html to provide a platform admin specific navbar #}
{% block navbar %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-navbar shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{% url 'platform_management:dashboard' %}">
                <i class="bi bi-shield-lock-fill me-2"></i>Platform Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#platformAdminNavbar" aria-controls="platformAdminNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="platformAdminNavbar">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    {% active_nav_link 'platform_management:dashboard' 'Dashboard' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-grid-1x2-fill' %}
                    {% active_nav_link 'platform_management:platformannouncement_list' 'Announcements' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-megaphone-fill' %}
                    {% active_nav_link 'platform_management:systemnotification_list' 'Notifications' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-bell-fill' %}
                    {% active_nav_link 'platform_management:platformsetting_list' 'Settings' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-gear-fill' %}
                    {% active_nav_link 'platform_management:auditlog_list' 'Audit Logs' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-journal-text' %}
                    {% active_nav_link 'platform_management:maintenance_mode_toggle' 'Maintenance' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-cone-striped' %}
                    {# Add other platform admin links here - e.g., tenant management, user management for platform #}
                </ul>
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="platformUserDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i> {{ user.get_full_name|default:user.email }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="platformUserDropdown">
                                <li><a class="dropdown-item" href="{% url 'admin:index' %}" target="_blank"><i class="bi bi-sliders me-2"></i>Django Admin</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'users:school_admin_logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li> {# Assuming this is your public admin logout #}
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
{% endblock navbar %}

{# Override the main content block from public_base.html #}
{% block content %}
    {# Remove welcome_message_area if it was in public_base's content block #}
    {# Remove content_full_width if it was in public_base's content block #}
    
    <div class="container-fluid mt-3 mb-5 platform-admin-main-content">
        {# Display messages framework output #}
        {% if messages %}
            <div class="container"> {# Or container-fluid if you want alerts full width #}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block platform_admin_page_content %}
            <p class="alert alert-info">Platform admin page content goes here. Override 'platform_admin_page_content'.</p>
        {% endblock platform_admin_page_content %}
    </div>
{% endblock content %}

{# You might want a different footer or to reuse public_base.html's footer via {{ block.super }} #}
{% block footer %}
    <footer class="footer mt-auto py-3 bg-dark text-white-50">
        <div class="container text-center">
            <p class="mb-0 small">Platform Administration © {% now "Y" %} {{ site_name|default:"School Fees SaaS" }}.</p>
        </div>
    </footer>
{% endblock footer %}



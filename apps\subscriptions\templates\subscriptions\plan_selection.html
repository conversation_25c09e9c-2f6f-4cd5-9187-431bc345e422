{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\plan_selection.html #}
{% extends "tenant_base.html" %} {# Or "parent_portal_base.html" if that's what it should extend #}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:_("Select Subscription Plan") }}{% endblock %} {# Or parent_portal_page_title #}

{% block extra_tenant_css %} {# Or extra_parent_portal_css #}
    {{ block.super }}
    {# Copied styles from pricing_page.html for consistency - consider a shared CSS file #}
    <style>
        .plan-card { border: 1px solid #dee2e6; border-radius: 0.75rem; transition: all 0.3s ease-in-out; height: 100%; display: flex; flex-direction: column; }
        .plan-card:hover { box-shadow: 0 0.75rem 1.5rem rgba(0,0,0,.1)!important; transform: translateY(-5px); }
        .plan-card .card-header { background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; border-top-left-radius: calc(0.75rem - 1px); border-top-right-radius: calc(0.75rem - 1px); }
        .plan-card .price { font-size: 2.8rem; font-weight: 700; color: var(--bs-primary); }
        .plan-card .price-cycle { font-size: 1rem; color: #6c757d; font-weight: 400; }
        .plan-card ul.features { list-style: none; padding-left: 0; margin-bottom: 1.5rem; }
        .plan-card ul.features li { padding: 0.6rem 0; border-bottom: 1px solid #f1f1f1; font-size: 0.95rem; }
        .plan-card ul.features li:last-child { border-bottom: none; }
        .plan-card .btn-select-plan { font-weight: 500; width: 100%; }
        .current-plan-highlight { border: 3px solid var(--bs-success) !important; box-shadow: 0 0.5rem 1rem rgba(25,135,84,.35)!important; }
        .plan-card .card-body { display: flex; flex-direction: column; flex-grow: 1; }
        .plan-card .card-footer-action { margin-top: auto; padding-top: 1rem; }
        .plan-pricing-options { min-height: 40px; }
        .btn-group .btn { border-radius: 0; }
        .btn-group .btn:first-child { border-top-left-radius: .375rem; border-bottom-left-radius: .375rem; }
        .btn-group .btn:last-child { border-top-right-radius: .375rem; border-bottom-right-radius: .375rem; }
    </style>
{% endblock %}

{% block tenant_specific_content %} {# Or parent_portal_page_content #}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            {% if subscription %}
            <li class="breadcrumb-item"><a href="{% url 'subscriptions:subscription_details' %}">{% trans "My Subscription" %}</a></li>
            {% endif %}
            <li class="breadcrumb-item active">{% trans "Select Plan" %}</li>
        </ol>
    </nav>
</div>

<section class="section plans">
    <div class="text-center mb-4">
        <p class="lead">{% trans "Choose the plan that best fits your school's needs." %}</p>
        {% if subscription and subscription.plan %}
            <p class="text-muted">{% blocktrans with current_plan_name=subscription.plan.name %}Your current plan is: <strong>{{ current_plan_name }}</strong>.{% endblocktrans %}</p>
        {% elif subscription and subscription.status == 'PENDING' %}
            <p class="alert alert-info">{% trans "Your subscription is currently pending. Selecting a new plan will update your choice." %}</p>
        {% else %}
            <p class="alert alert-info">{% trans "Your school does not have an active subscription yet. Please select a plan to get started or activate your trial." %}</p>
        {% endif %}
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if plans_data_list %} {# Use the context_object_name from the view #}
    <div class="row justify-content-center g-lg-4 g-md-3 g-2">
        {% for plan_item in plans_data_list %} {# Iterate through the processed list #}
            {% with plan=plan_item.instance %} {# Get the actual plan instance #}
            <div class="col-lg-4 col-md-6 d-flex">
                <div class="card plan-card text-center shadow-sm h-100 {% if current_plan_pk == plan.pk %}current-plan-highlight{% endif %}">
                    <div class="card-header py-3">
                        <h4 class="my-0 fw-semibold">{{ plan.name }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="plan-pricing-options">
                            <h1 class="card-title pricing-card-title price mt-3 mb-0">
                                {{ school_currency_symbol|default:'$' }}{{ plan.price_monthly|floatformat:2|intcomma }}
                                <small class="text-muted fw-light price-cycle">/ {% trans "month" %}</small>
                            </h1>
                            {% if plan.price_annually > 0 %}
                                <p class="text-muted small mt-1">
                                    {% trans "Or" %} {{ school_currency_symbol|default:'$' }}{{ plan.price_annually|floatformat:2|intcomma }} {% trans "billed annually" %}
                                    {% if plan_item.show_savings %}
                                        <span class="d-block text-success fw-bold">
                                            ({% blocktrans with savings=plan_item.savings_if_annual|floatformat:2|intcomma currency=school_currency_symbol|default:'$' %}Save {{ currency }}{{ savings }}{% endblocktrans %})
                                        </span>
                                    {% endif %}
                                </p>
                            {% endif %}
                        </div>
                        
                        <p class="mt-3 mb-3 text-muted small">{{ plan.description|default:"Comprehensive features."|truncatewords:15 }}</p>
                        
                        <ul class="features mb-4 text-start">
                            {% if plan.trial_period_days > 0 %}
                                <li class="fw-bold"><i class="bi bi-patch-check-fill text-primary me-2"></i>{% blocktrans with days=plan.trial_period_days %}{{ days }}-day free trial{% endblocktrans %}</li>
                            {% endif %}
                            <li><i class="bi bi-people text-muted me-2"></i>{% if plan.max_students %}{% blocktrans with count=plan.max_students %}{{ count }} Students{% endblocktrans %}{% else %}{% trans "Unlimited Students" %}{% endif %}</li>
                            <li><i class="bi bi-person-badge text-muted me-2"></i>{% if plan.max_staff %}{% blocktrans with count=plan.max_staff %}{{ count }} Staff{% endblocktrans %}{% else %}{% trans "Unlimited Staff" %}{% endif %}</li>
                            {% for feature in plan.features.all|slice:":4" %}
                                <li><i class="bi bi-check-lg text-muted me-2"></i>{{ feature.name }}</li>
                            {% empty %}
                                <li><i class="bi bi-check-lg text-muted me-2"></i>{% trans "Core Platform Features" %}</li>
                            {% endfor %}
                            {% if plan.features.all.count > 4 %}
                                <li class="text-muted"><i class="bi bi-three-dots text-muted me-2"></i>{% trans "And more..." %}</li>
                            {% endif %}
                        </ul>
                        
                        <div class="card-footer-action">
                            {% if current_plan_pk == plan.pk %}
                                <button class="btn btn-lg btn-success btn-select-plan w-100" disabled>
                                    <i class="bi bi-check-circle-fill me-1"></i> {% trans "Your Current Plan" %}
                                </button>
                            {% else %}
                                <div class="btn-group w-100" role="group" aria-label="Plan Billing Cycle Options">
                                    {% if plan.price_monthly > 0 or plan.trial_period_days > 0 %} {# Show monthly if price > 0 or it's a trial #}
                                    <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='monthly' %}" 
                                        class="btn btn-primary btn-select-plan">
                                        {% trans "Choose Monthly" %}
                                        {% if plan.trial_period_days > 0 and plan.price_monthly <= 0 %}
                                            <small class="d-block">({% trans "Start Trial" %})</small>
                                        {% endif %}
                                    </a>
                                    {% endif %}
                                    {% if plan.price_annually > 0 %}
                                    <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='annually' %}" 
                                        class="btn btn-outline-primary btn-select-plan">
                                        {% trans "Choose Annually" %}
                                        {% if plan_item.show_savings %}<small class="d-block">({% trans "Save" %} {{ school_currency_symbol|default:'$' }}{{ plan_item.savings_if_annual|floatformat:2|intcomma }})</small>{% endif %}
                                    </a>
                                    {% endif %}
                                </div>
                                {% if plan.price_monthly <= 0 and plan.price_annually <= 0 and plan.trial_period_days <= 0 %}
                                    <p class="text-muted small mt-2">{% trans "This plan is not currently available for selection." %}</p>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endwith %}
        {% endfor %}
    </div>
    {% else %}
    <div class="alert alert-info text-center">{% trans "No subscription plans are currently available to select." %}</div>
    {% endif %}
</section>
{% endblock tenant_specific_content %}






{% comment %} {# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\plan_selection.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:_("Select Subscription Plan") }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'subscriptions/css/plans.css' %}"> {# Create this CSS file #}
{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'subscriptions:subscription_details' %}">{% trans "My Subscription" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Select Plan" %}</li>
        </ol>
    </nav>
</div>

<section class="section plans">
    <div class="text-center mb-4">
        <p class="lead">{% trans "Choose the plan that best fits your school's needs." %}</p>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if plans %}
    <div class="row justify-content-center g-lg-4 g-md-3 g-2">
        {% for plan in plans %}
        <div class="col-lg-4 col-md-6">
            <div class="card plan-card text-center shadow-sm h-100 {% if current_plan_pk == plan.pk %}current-plan-highlight{% endif %}">
                <div class="card-header py-3">
                    <h4 class="my-0 fw-normal">{{ plan.name }}</h4>
                </div>
                <div class="card-body d-flex flex-column pb-4">
                    <h1 class="card-title pricing-card-title price mt-3">
                        {{ school_currency_symbol|default:'$' }}<span class="plan-price-monthly">{{ plan.price_monthly|floatformat:2|intcomma }}</span>
                        <span class="plan-price-annually" style="display:none;">{{ plan.price_annually|floatformat:2|intcomma }}</span>
                        <small class="text-muted fw-light price-cycle">
                            <span class="cycle-monthly">/ {% trans "month" %}</span>
                            <span class="cycle-annually" style="display:none;">/ {% trans "year" %}</span>
                        </small>
                    </h1>
                    
                    <p class="mt-3 mb-3 text-muted small">{{ plan.description|default:"Comprehensive features."|truncatewords:15 }}</p>
                    
                    <ul class="features list-unstyled mb-4">
                        {% if plan.trial_period_days > 0 %}
                            <li><i class="bi bi-patch-check-fill text-success me-2"></i><strong>{% blocktrans with days=plan.trial_period_days %}{{ days }}-day free trial{% endblocktrans %}</strong></li>
                        {% endif %}
                        <li><i class="bi bi-people-fill text-primary me-2"></i>{% if plan.max_students %}{% blocktrans with count=plan.max_students %}{{ count }} Students{% endblocktrans %}{% else %}{% trans "Unlimited Students" %}{% endif %}</li>
                        <li><i class="bi bi-person-badge-fill text-primary me-2"></i>{% if plan.max_staff %}{% blocktrans with count=plan.max_staff %}{{ count }} Staff{% endblocktrans %}{% else %}{% trans "Unlimited Staff" %}{% endif %}</li>
                        {% for feature in plan.features.all|slice:":3" %}
                            <li><i class="bi bi-check-lg text-muted me-2"></i>{{ feature.name }}</li>
                        {% empty %}
                            <li><i class="bi bi-check-lg text-muted me-2"></i>{% trans "Core Features" %}</li>
                        {% endfor %}
                        {% if plan.features.all.count > 3 %}<li class="text-muted">& {% trans "more..." %}</li>{% endif %}
                    </ul>
                    
                    <div class="mt-auto">
                        {% if current_plan_pk == plan.pk %}
                            <button class="btn btn-lg btn-block btn-outline-success btn-select-plan" disabled>{% trans "Your Current Plan" %}</button>
                        {% else %}
                            <div class="btn-group w-100" role="group">
                                <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='monthly' %}" class="btn btn-lg btn-primary btn-select-plan btn-monthly {% if not plan.price_monthly or plan.price_monthly <= 0 %}disabled{% endif %}">
                                    {% trans "Select Monthly" %}
                                </a>
                                <a href="{% url 'subscriptions:initiate_checkout' plan_slug=plan.slug billing_cycle='annually' %}" class="btn btn-lg btn-outline-primary btn-select-plan btn-annually {% if not plan.price_annually or plan.price_annually <= 0 %}disabled{% endif %}">
                                    {% trans "Select Annually" %}
                                    {% if plan.price_annually > 0 and plan.price_monthly > 0 and plan.price_annually < plan.price_monthly|multiply:12 %}
                                        <small class="d-block text-success">({% trans "Save" %} {{ school_currency_symbol|default:'$' }}{{ plan.price_monthly|multiply:12|sub:plan.price_annually|floatformat:2|intcomma }})</small>
                                    {% endif %}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="alert alert-info text-center">{% trans "No subscription plans are currently available to select." %}</div>
    {% endif %}
</section>
{% endblock tenant_specific_content %}
 {% endcomment %}


# D:\school_fees_saas_v2\apps\accounting\admin.py

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _ # For admin display descriptions
from django.db.models import Sum, F, Case, When, Value, DecimalField, Q # Keep if used in AccountAdmin get_queryset
from django.db.models.functions import Coalesce # Keep if used in AccountAdmin get_queryset
from decimal import Decimal # Keep if used in AccountAdmin get_queryset

from .models import AccountType, Account, JournalEntry, JournalLine, GeneralLedger


@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'classification', 'statement_section', 'normal_balance')
    list_filter = ('classification', 'statement_section', 'normal_balance')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {'fields': ('name', 'classification', 'normal_balance', 'description')}),
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )



@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'get_account_type_name_admin', 'is_active', 'can_be_used_in_je', 'parent_account_link_admin', 'display_current_balance')
    list_filter = ('account_type', 'is_active', 'can_be_used_in_je', 'account_type__classification') # Added classification
    search_fields = ('name', 'code', 'description')
    list_editable = ('is_active', 'can_be_used_in_je')    
    ordering = ('code',) # Changed from 'code' to 'account_code' to match your model
    list_select_related = ('account_type', 'parent_account')
    autocomplete_fields = ['account_type', 'parent_account'] # Added parent_account

    fieldsets = (
        (None, {'fields': ('account_type', 'code', 'name', 'description')}), # Changed 'code' to 'account_code'
        ('Hierarchy & Status', {'fields': ('parent_account', 'is_active', 'can_be_used_in_je', 'is_control_account')}),
        # Removed 'display_current_balance_detail' as get_queryset provides calculated_balance
        # ('Balance Information (Detail View)', {'fields': ('display_current_balance_detail',), 'classes': ('collapse',)}),
        ('Audit Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    readonly_fields = ('created_at', 'updated_at', 'display_current_balance_detail',) # display_current_balance_detail can show the result of get_balance()

    @admin.display(description='Account Type', ordering='account_type__name')
    def get_account_type_name_admin(self, obj): # Renamed to avoid conflict if model has same property
        return obj.account_type.name if obj.account_type else "N/A"

    @admin.display(description='Parent Account', ordering='parent_account__name')
    def parent_account_link_admin(self, obj): # Renamed
        if obj.parent_account:
            link = reverse("admin:accounting_account_change", args=[obj.parent_account.id])
            return format_html('<a href="{}">{}</a>', link, obj.parent_account)
        return "—"

    @admin.display(description='Current Balance', ordering='calculated_balance')
    def display_current_balance(self, obj):
        # This relies on the 'calculated_balance' annotation from get_queryset
        balance = getattr(obj, 'calculated_balance', None)
        if balance is not None:
            # Consider account's normal balance for display style if needed
            return f"{balance:,.2f}" 
        return "N/A (Recalc)"

    @admin.display(description='Balance (Live Query)') # Use this in readonly_fields for detail view
    def display_current_balance_detail(self, obj):
        return obj.get_balance() # Assuming Account model has get_balance() that queries live

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Assumes JournalEntry has status and JournalLine links to JournalEntry
        # And JournalLine now has debit_amount and credit_amount
        
        # Sum of debits to this account from POSTED journal entries
        debit_sum = Coalesce(Sum(
            F('journal_lines__debit_amount'), 
            filter=Q(journal_lines__journal_entry__status=JournalEntry.StatusChoices.POSTED)
        ), Decimal('0.00'), output_field=DecimalField())
        
        # Sum of credits to this account from POSTED journal entries
        credit_sum = Coalesce(Sum(
            F('journal_lines__credit_amount'),
            filter=Q(journal_lines__journal_entry__status=JournalEntry.StatusChoices.POSTED)
        ), Decimal('0.00'), output_field=DecimalField())

        qs = qs.annotate(
            total_debits_posted=debit_sum,
            total_credits_posted=credit_sum,
        )
        
        # Calculate balance based on normal balance of the account type
        # Assuming AccountType model has 'normal_balance' field with choices like AccountType.NormalBalanceChoices.DEBIT/CREDIT
        qs = qs.annotate(
            calculated_balance=Case(
                When(account_type__normal_balance=AccountType.NormalBalanceChoices.DEBIT, # Ensure this choice value is correct
                    then=F('total_debits_posted') - F('total_credits_posted')),
                When(account_type__normal_balance=AccountType.NormalBalanceChoices.CREDIT, # Ensure this choice value is correct
                    then=F('total_credits_posted') - F('total_debits_posted')),
                default=Value(Decimal('0.00')), # Should not happen if normal_balance is set
                output_field=DecimalField()
            )
        )
        return qs


# --- JournalEntryLine Inline ---
class JournalLineInline(admin.TabularInline): # Renamed from JournalEntryLineInline
    model = JournalLine # Use the corrected model name
    extra = 2 # Start with 2 empty forms for new JEs
    autocomplete_fields = ['account']
    fields = ('account', 'entry_type', 'amount', 'description') # Based on your restored JournalLine model
    readonly_fields = ()
    # classes = ['collapse']


# --- JournalEntry Admin ---
@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = (
        'id_link_display',
        'date',
        'narration_display',
        'entry_type', # Added for clarity
        'status',     # Display the status field
        'total_debits_display', # Renamed display method
        'total_credits_display',# Renamed display method
        'is_balanced_display',
        'posted_at_display', # Display for posted_at
        'created_by_display'
    )
    list_filter = (
        'date',
        'status',       # <<< CORRECTED: Use 'status' field for filtering
        'entry_type',   # Filter by the type of JE
        'created_by',   # Filter by user who created it
        'posted_at'     # Filter by when it was posted
    )
    search_fields = ('id', 'entry_number', 'narration', 'lines__account__name', 'lines__account__code', 'lines__description')
    date_hierarchy = 'date'
    inlines = [JournalLineInline] # Corrected inline name
    readonly_fields = (
        'entry_number', # Assuming auto-generated or set on save
        'created_at', 'updated_at', 
        'created_by', 'last_modified_by', 
        'posted_at',
        'total_debits_display', 'total_credits_display', 'is_balanced_display',
    )
    list_select_related = ('created_by', 'last_modified_by') # For performance

    fieldsets = (
        (None, {'fields': ('date', 'narration', 'entry_type', 'status')}), # Added entry_type, status
        ('Totals (Calculated)', {
            'fields': (('total_debits_display', 'total_credits_display', 'is_balanced_display'),)
        }),
        ('Posting Information', { # Added section for posting info
            'fields': ('posted_at',),
            'classes': ('collapse',) 
        }),
        ('Audit', {
            'fields': ('created_by', 'last_modified_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    actions = ['post_selected_journal_entries', 'unpost_selected_journal_entries']


    @admin.display(description='JE ID', ordering='id')
    def id_link_display(self, obj):
        link = reverse("admin:accounting_journalentry_change", args=[obj.id])
        # Use entry_number if available, else pk
        display_id = obj.entry_number if obj.entry_number else f"PK-{obj.id}"
        return format_html('<a href="{}">{}</a>', link, display_id)

    @admin.display(description='Narration', ordering='narration')
    def narration_display(self, obj):
        return (obj.narration[:75] + '...') if obj.narration and len(obj.narration) > 75 else obj.narration

    @admin.display(description='Total Debit')
    def total_debits_display(self, obj): # Renamed
        return obj.total_debits # Property from JournalEntry model

    @admin.display(description='Total Credit')
    def total_credits_display(self, obj): # Renamed
        return obj.total_credits # Property from JournalEntry model

    @admin.display(description='Balanced?', boolean=True)
    def is_balanced_display(self, obj):
        is_bal, _ = obj.is_entry_balanced() # Method returns (bool, diff)
        return is_bal

    @admin.display(description='Posted At', ordering='posted_at') # New display method for posted_at
    def posted_at_display(self, obj):
        if obj.posted_at:
            return obj.posted_at.strftime('%Y-%m-%d %H:%M')
        return "Not Posted"

    @admin.display(description='Created By', ordering='created_by__email')
    def created_by_display(self, obj):
        if obj.created_by:
            return obj.created_by.email # Or get_full_name()
        return "System/Unknown"

    def save_model(self, request, obj, form, change):
        if not obj.pk: # If creating new
            obj.created_by = request.user
        obj.last_modified_by = request.user
        super().save_model(request, obj, form, change)
    
    def save_formset(self, request, form, formset, change):
        lines = formset.save(commit=False)
        for line in lines:
            # Perform any line-specific logic before saving if needed
            line.save()
        formset.save_m2m() # Important for ManyToMany fields, though not directly used here for lines
        
        # After lines are saved, ensure the JE instance is re-evaluated for balance and totals
        # especially if clean() on JE relies on saved lines.
        if formset.instance.pk:
            try:
                formset.instance.clean() # Re-validate the parent JE
            except ValidationError as e:
                form.add_error(None, e) # Add errors to the main form to display them
                # This might not be the best place if using inlines, as errors on parent might not show easily.
                # Consider how to display JE-level validation errors from inline changes.
            formset.instance.save() # Re-save parent JE if clean() made changes or to trigger signals

    @admin.action(description=_("Post selected journal entries"))
    def post_selected_journal_entries(self, request, queryset):
        posted_count = 0
        for entry in queryset:
            try:
                if entry.status == JournalEntry.StatusChoices.DRAFT:
                    entry.post(user=request.user)
                    posted_count += 1
            except ValidationError as e:
                self.message_user(request, f"Error posting JE {entry.entry_number or entry.pk}: {e}", level=admin. अबmessages.ERROR)
        if posted_count:
            self.message_user(request, f"{posted_count} journal entries posted successfully.", level=admin.messages.SUCCESS)

    @admin.action(description=_("Unpost selected journal entries"))
    def unpost_selected_journal_entries(self, request, queryset):
        unposted_count = 0
        for entry in queryset:
            try:
                if entry.status == JournalEntry.StatusChoices.POSTED:
                    entry.unpost(user=request.user)
                    unposted_count += 1
            except ValidationError as e:
                self.message_user(request, f"Error unposting JE {entry.entry_number or entry.pk}: {e}", level=admin.messages.ERROR)
        if unposted_count:
            self.message_user(request, f"{unposted_count} journal entries unposted successfully.", level=admin.messages.SUCCESS)



# --- JournalLine Admin (Standalone - Optional, as it's an Inline) ---

@admin.register(JournalLine)
class JournalLineAdmin(admin.ModelAdmin):
    list_display = (
        'id', # Added ID for quick reference
        'journal_entry_link_display',
        'account_name_display',
        'debit_amount_display',   # NEW
        'credit_amount_display',  # NEW
        'description_display_short'
    )
    list_filter = (
        'account__account_type__name', # Filter by account type name
        'account__name',               # Filter by account name
        'journal_entry__date',         # Filter by JE date
        # 'entry_type' CANNOT BE USED HERE ANYMORE
    )
    search_fields = ('journal_entry__narration', 'account__name', 'account__account_code', 'description') # Changed 'code' to 'account_code'
    autocomplete_fields = ['account', 'journal_entry']
    list_select_related = ('journal_entry', 'account', 'account__account_type')
    ordering = ('-journal_entry__date', 'journal_entry__id', 'id')
    
    # Fields for add/change form if editing JournalLine directly (usually done via inline)
    fields = ('journal_entry', 'account', 'debit_amount', 'credit_amount', 'description') 

    @admin.display(description='JE', ordering='journal_entry__id')
    def journal_entry_link_display(self, obj):
        link = reverse("admin:accounting_journalentry_change", args=[obj.journal_entry.id])
        display_id = obj.journal_entry.entry_number if obj.journal_entry.entry_number else f"JE-{obj.journal_entry.id}"
        return format_html('<a href="{}">{} ({})</a>', link, display_id, obj.journal_entry.date.strftime('%Y-%m-%d'))

    @admin.display(description='Account', ordering='account__name')
    def account_name_display(self, obj):
        return str(obj.account)

    @admin.display(description='Debit', ordering='debit_amount') # NEW
    def debit_amount_display(self, obj):
        return f"{obj.debit_amount:,.2f}" if obj.debit_amount > 0 else "—"

    @admin.display(description='Credit', ordering='credit_amount') # NEW
    def credit_amount_display(self, obj):
        return f"{obj.credit_amount:,.2f}" if obj.credit_amount > 0 else "—"

    @admin.display(description='Description', ordering='description')
    def description_display_short(self, obj):
        desc = obj.description or ""
        return (desc[:40] + '...') if len(desc) > 40 else desc
    
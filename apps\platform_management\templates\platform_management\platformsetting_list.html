{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformsetting_list.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{% trans "Platform Settings" %}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{% trans "Platform Settings" %}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Settings" %}</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row mb-3">
        <div class="col-md-6">
            {# No create button here, assuming settings are pre-defined or managed via Django admin for creation #}
            {# Or add: <a href="{% url 'platform_management:platformsetting_create' %}" class="btn btn-primary">New Setting</a> #}
        </div>
        <div class="col-md-6">
            <form method="get" class="input-group">
                <input type="text" name="q" class="form-control" placeholder="{% trans 'Search settings...' %}" value="{{ search_query|default:'' }}">
                <button class="btn btn-outline-secondary" type="submit"><i class="bi bi-search"></i></button>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">{% trans "All Platform Settings" %}</h5>
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "Setting Name" %}</th>
                            <th>{% trans "Value" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Active" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for setting in page_obj %}
                        <tr>
                            <td><strong>{{ setting.setting_name }}</strong></td>
                            <td><small>{{ setting.setting_value|truncatechars:70 }}</small></td>
                            <td><small>{{ setting.description|truncatechars:70 }}</small></td>
                            <td>
                                {% if setting.is_active %}
                                    <span class="badge bg-success">{% trans "Yes" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{% trans "No" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'platform_management:platformsetting_update' pk=setting.pk %}" class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil-square"></i> {% trans "Edit" %}</a>
                                <a href="{% url 'platform_management:platformsetting_delete' pk=setting.pk %}" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete' %}"><i class="bi bi-trash"></i></a> {# <<< NEW DELETE LINK #}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include "partials/_pagination.html" with page_obj=page_obj %}
            {% else %}
            <p class="text-muted">{% trans "No platform settings found." %}</p>
            {% endif %}
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}



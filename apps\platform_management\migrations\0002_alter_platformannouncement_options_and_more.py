# Generated by Django 5.1.9 on 2025-06-21 22:14

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('platform_management', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='platformannouncement',
            options={'ordering': ['-publish_date', '-created_at'], 'verbose_name': 'Platform Announcement', 'verbose_name_plural': 'Platform Announcements'},
        ),
        migrations.RemoveField(
            model_name='platformannouncement',
            name='severity',
        ),
        migrations.AlterField(
            model_name='platformannouncement',
            name='author',
            field=models.ForeignKey(blank=True, limit_choices_to={'is_staff': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='platform_announcements_authored', to=settings.AUTH_USER_MODEL),
        ),
        migrations.Alter<PERSON>ield(
            model_name='platformannouncement',
            name='content',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='platformannouncement',
            name='expiry_date',
            field=models.DateTimeField(blank=True, help_text='Optional: Date and time when the announcement should no longer be visible.', null=True),
        ),
        migrations.AlterField(
            model_name='platformannouncement',
            name='is_published',
            field=models.BooleanField(default=False, help_text='Check to make this announcement visible.'),
        ),
        migrations.AlterField(
            model_name='platformannouncement',
            name='publish_date',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Date and time when the announcement should become visible.'),
        ),
        migrations.AlterField(
            model_name='platformannouncement',
            name='title',
            field=models.CharField(max_length=255),
        ),
    ]

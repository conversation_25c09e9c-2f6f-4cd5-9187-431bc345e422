#!/usr/bin/env python
"""
Debug script to check form rendering issues
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_tenants.utils import schema_context
from apps.fees.forms import FeeStructureForm, FeeStructureItemInlineFormSet
from apps.fees.models import FeeStructure
from apps.schools.models import AcademicYear, Term

def debug_form_rendering():
    print("=== DEBUGGING FORM RENDERING ===")
    
    with schema_context('alpha'):
        # Test form initialization
        print("\n1. Testing Form Initialization...")
        form = FeeStructureForm()
        
        print(f"Academic Year field queryset count: {form.fields['academic_year'].queryset.count()}")
        print(f"Term field queryset count: {form.fields['term'].queryset.count()}")
        
        # Test formset initialization
        print("\n2. Testing Formset Initialization...")
        formset = FeeStructureItemInlineFormSet()
        print(f"Formset forms count: {len(formset.forms)}")
        print(f"Formset management form: {formset.management_form}")
        
        # Test with academic year selected
        print("\n3. Testing with Academic Year Selected...")
        academic_years = AcademicYear.objects.all()
        if academic_years.exists():
            first_year = academic_years.first()
            form_data = {'academic_year': first_year.id}
            form = FeeStructureForm(data=form_data)
            print(f"Form with academic year - Term queryset count: {form.fields['term'].queryset.count()}")
            
            # Test terms for this year
            terms = Term.objects.filter(academic_year=first_year)
            print(f"Available terms for {first_year.name}: {list(terms.values_list('name', flat=True))}")
        
        # Test formset with instance
        print("\n4. Testing Formset with Instance...")
        formset_with_instance = FeeStructureItemInlineFormSet(instance=None)
        print(f"Formset with None instance - forms count: {len(formset_with_instance.forms)}")
        
        # Check empty form
        print("\n5. Testing Empty Form...")
        empty_form = formset.empty_form
        print(f"Empty form fields: {list(empty_form.fields.keys())}")
        for field_name, field in empty_form.fields.items():
            print(f"  {field_name}: {field.__class__.__name__}")
        
        print("\n=== DEBUG COMPLETE ===")

if __name__ == "__main__":
    debug_form_rendering()

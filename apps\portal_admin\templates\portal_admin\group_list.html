{# D:\school_fees_saas_v2\templates\portal_admin\group_list.html #}
{% extends "tenant_base.html" %}

{% load static humanize widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
        <div>
            {% if can_add_group %} {# Context variable from view #}
                <a href="{% url 'portal_admin:group_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle-fill me-1"></i> Create New Role
                </a>
            {% endif %}
            {% if perms.portal_admin.manage_staff_roles %} {# Using your new permission #}
                <a href="{% url 'portal_admin:assign_staff_roles' %}" class="btn btn-info ms-2">
                    <i class="bi bi-person-check-fill me-1"></i> Assign Staff to Roles
                </a>
            {% endif %}
        </div>
    </div>

    {% if messages %}
        {% include "partials/_messages.html" %}
    {% endif %}

    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0">Available Roles</h5>
        </div>
        <div class="card-body p-0">
            {% if groups %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>Role Name</th>
                                <th>Permissions Count</th>
                                <th>Users Assigned</th> {# <-- NEW COLUMN HEADER #}
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for group in groups %}
                                <tr>
                                    <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
                                    <td>
                                        <a href="{% url 'portal_admin:group_detail' pk=group.pk %}">
                                            {{ group.name }}
                                        </a>
                                    </td>
                                    <td>{{ group.permissions.count }}</td>
                                    <td>{{ group.user_count }}</td> {# <-- DISPLAY THE ANNOTATED COUNT #}
                                    <td class="actions-column">
                                        <a href="{% url 'portal_admin:group_detail' pk=group.pk %}" class="btn btn-outline-info btn-sm" title="View Details">
                                            <i class="bi bi-eye-fill"></i>
                                        </a>
                                        {% if perms.auth.change_group %}
                                        <a href="{% url 'portal_admin:group_update' pk=group.pk %}" class="btn btn-outline-primary btn-sm" title="Edit Name">
                                            <i class="bi bi-pencil-square"></i>
                                        </a>
                                        {% endif %}
                                        {% if perms.portal_admin.manage_role_permissions %} {# Use specific perm for managing perms #}
                                        <a href="{% url 'portal_admin:group_permissions_assign' group_pk=group.pk %}" class="btn btn-outline-warning btn-sm" title="Manage Permissions">
                                            <i class="bi bi-key-fill"></i>
                                        </a>
                                        {% endif %}
                                        {% if perms.auth.delete_group %}
                                            {% if group.name != "School Administrators" and group.name != "Accountants" %} {# Example: Prevent deletion of core roles #}
                                            <a href="{% url 'portal_admin:group_delete' pk=group.pk %}" class="btn btn-outline-danger btn-sm" title="Delete Role">
                                                <i class="bi bi-trash-fill"></i>
                                            </a>
                                            {% else %}
                                            <button class="btn btn-outline-secondary btn-sm" disabled title="Core roles cannot be deleted">
                                                <i class="bi bi-trash-fill"></i>
                                            </button>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="card-body">
                    <p class="text-muted">No roles (groups) found. Please create one.</p>
                </div>
            {% endif %}
        </div>
        {% if is_paginated %}
            <div class="card-footer">
                {% include "partials/_pagination.html" with page_obj=page_obj %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}







{% comment %} {# D:\school_fees_saas_v2\templates\portal_admin\group_list.html #}
{% extends "tenant_base.html" %}
{% load core_tags %} {# Assuming active_nav_link or other tags are here #}

{% block title %}{{ view_title|default:"Manage Roles" }}{% endblock %}

{% block tenant_specific_content %} {# Or your main content block in tenant_base.html #}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">{{ view_title|default:"Manage Roles (Groups)" }}</h1>
        {% if perms.auth.add_group %}
        <a href="{% url 'portal_admin:group_create' %}" class="btn btn-primary btn-sm">
            <i class="bi bi-plus-circle-fill me-1"></i> Create New Role/Group
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    {% if groups %}
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered table-sm">
                <thead class="table-light">
                    <tr>
                        <th>Role/Group Name</th>
                        <th class="text-center">Users Assigned</th>
                        <th>Staff Members</th>
                        <th class="text-center" style="width: 30%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group_obj in groups %} {# Changed loop var to group_obj to avoid clash with group in perms #}
                    <tr>
                        <td>{{ group_obj.name }}</td>
                        <td class="text-center">{{ group_obj.user_count }}</td> {# From annotate in view #}
                        <td>
                            {% if group_obj.staff_members %}
                                {% for staff in group_obj.staff_members %}
                                    <span class="badge bg-secondary me-1">{{ staff.get_full_name|default:staff.email }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">No staff assigned</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if perms.auth.change_group %}
                            <a href="{% url 'portal_admin:group_update' group_obj.pk %}" class="btn btn-sm btn-secondary" title="Edit Name"><i class="bi bi-pencil-square"></i> Edit</a>
                            <a href="{% url 'portal_admin:assign_permissions_to_group' group_obj.pk %}" class="btn btn-sm btn-info" title="Manage Permissions"><i class="bi bi-shield-lock-fill"></i> Permissions</a>
                            {% endif %}
                            {% if perms.auth.delete_group %}
                            <form action="{% url 'portal_admin:group_delete' group_obj.pk %}" method="post" style="display: inline; margin-left: 5px;" onsubmit="return confirm('Are you sure you want to delete role \'{{ group_obj.name|escapejs }}\'? This action cannot be undone and will unassign users from this role.');">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-danger" title="Delete Role"><i class="bi bi-trash3-fill"></i> Delete</button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">No roles/groups have been defined yet for this school.</div>
    {% endif %}

    {% include "partials/_pagination.html" %} {# If you add pagination to the view #}

    <div class="mt-4">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a>
    </div>
</div>
{% endblock tenant_specific_content %}

 {% endcomment %}

"""
Management command to create InvoiceSequence records for all tenants
that don't have one yet.
"""

from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.schools.models import InvoiceSequence


class Command(BaseCommand):
    help = 'Create InvoiceSequence records for all tenants that do not have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything',
        )
        parser.add_argument(
            '--standardize-to-13-digits',
            action='store_true',
            help='Update all existing sequences to use 13-digit format (INV- + 9 padding digits)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        standardize = options['standardize_to_13_digits']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        if standardize:
            self.stdout.write(
                self.style.SUCCESS('STANDARDIZATION MODE - Will update sequences to 13-digit format')
            )
        
        # Get all tenant schools (excluding public schema)
        tenants = School.objects.exclude(schema_name='public')
        
        self.stdout.write(f'Found {tenants.count()} tenant(s) to check')
        
        created_count = 0
        existing_count = 0
        updated_count = 0
        
        for tenant in tenants:
            self.stdout.write(f'\nChecking tenant: {tenant.name} ({tenant.schema_name})')
            
            try:
                with schema_context(tenant.schema_name):
                    # Check if InvoiceSequence table exists and has records
                    try:
                        sequence = InvoiceSequence.objects.first()

                        if sequence:
                            existing_count += 1
                            current_padding = sequence.padding_digits
                            target_padding = 9  # For 13-digit format: INV- (4) + 9 digits = 13

                            self.stdout.write(
                                self.style.SUCCESS(f'  ✓ InvoiceSequence exists (padding: {current_padding})')
                            )

                            # Check if standardization is needed
                            if standardize and current_padding != target_padding:
                                if not dry_run:
                                    old_format = f"{sequence.prefix}{sequence.last_number:0{current_padding}d}"
                                    sequence.padding_digits = target_padding
                                    sequence.save(update_fields=['padding_digits'])
                                    new_format = f"{sequence.prefix}{sequence.last_number:0{target_padding}d}"
                                    updated_count += 1
                                    self.stdout.write(
                                        self.style.SUCCESS(f'  ✓ Updated to 13-digit format: {old_format} → {new_format}')
                                    )
                                else:
                                    old_format = f"{sequence.prefix}{sequence.last_number:0{current_padding}d}"
                                    new_format = f"{sequence.prefix}{sequence.last_number:0{target_padding}d}"
                                    updated_count += 1
                                    self.stdout.write(
                                        self.style.WARNING(f'  → Would update: {old_format} → {new_format}')
                                    )
                            elif standardize:
                                self.stdout.write(
                                    self.style.SUCCESS(f'  ✓ Already using 13-digit format')
                                )
                        else:
                            # Create new sequence with 13-digit format
                            target_padding = 9 if standardize else 5  # Use 13-digit if standardizing

                            if not dry_run:
                                sequence = InvoiceSequence.objects.create(
                                    prefix='INV-',
                                    last_number=0,
                                    padding_digits=target_padding
                                )
                                created_count += 1
                                format_desc = "13-digit" if target_padding == 9 else "5-digit"
                                self.stdout.write(
                                    self.style.SUCCESS(f'  ✓ Created {format_desc} InvoiceSequence: {sequence}')
                                )
                            else:
                                created_count += 1
                                format_desc = "13-digit" if target_padding == 9 else "5-digit"
                                self.stdout.write(
                                    self.style.WARNING(f'  → Would create {format_desc} InvoiceSequence with prefix "INV-"')
                                )
                    except Exception as table_error:
                        self.stdout.write(
                            self.style.ERROR(f'  ❌ Table error: {table_error}')
                        )
                        self.stdout.write(
                            self.style.WARNING(f'  → Tenant schema may need migration: python manage.py migrate_schemas --schema={tenant.schema_name}')
                        )
            except Exception as schema_error:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Schema error: {schema_error}')
                )
        
        # Summary
        self.stdout.write('\n' + '='*60)
        self.stdout.write('SUMMARY:')
        self.stdout.write(f'  Tenants checked: {tenants.count()}')
        self.stdout.write(f'  Existing sequences: {existing_count}')

        if dry_run:
            self.stdout.write(f'  Would create: {created_count}')
            if standardize:
                self.stdout.write(f'  Would update to 13-digit: {updated_count}')
        else:
            self.stdout.write(f'  Created sequences: {created_count}')
            if standardize:
                self.stdout.write(f'  Updated to 13-digit: {updated_count}')

        if (created_count > 0 or updated_count > 0) and not dry_run:
            if standardize:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ All tenants now have 13-digit InvoiceSequence format!')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ All tenants now have InvoiceSequence records!')
                )
        elif (created_count > 0 or updated_count > 0) and dry_run:
            self.stdout.write(
                self.style.WARNING('\n⚠️  Run without --dry-run to apply the changes')
            )
        else:
            if standardize:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ All tenants already have 13-digit InvoiceSequence format!')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ All tenants already have InvoiceSequence records!')
                )

{# D:\school_fees_saas_v2\apps\hr\templates\hr\confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Deletion") }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>{{ view_title }}</h4>
                </div>
                <div class="card-body p-4">
                    <p class="lead">Are you sure you want to delete the following object?</p>
                    <div class="alert alert-light border p-3 mb-4">
                        <strong class="d-block">{{ object|model_name_display|title }}:</strong>
                        <h5 class="mb-0">{{ object }}</h5>
                    </div>
                    <form method="post">
                        {% csrf_token %}
                        <p class="text-muted">This action cannot be undone.</p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{{ cancel_url|default:request.META.HTTP_REFERER|default:'..' }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-danger"><i class="bi bi-trash-fill me-1"></i> Yes, Delete</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



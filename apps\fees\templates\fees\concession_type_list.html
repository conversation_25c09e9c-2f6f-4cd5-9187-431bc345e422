{# D:\school_fees_saas_v2\apps\fees\templates\fees\concession_type_list.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Manage Concession Types" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Manage Concession Types" }}</h1>
        {% if perms.fees.add_concessiontype %}
            <a href="{% url 'fees:concession_type_create' %}" class="btn btn-primary">
                <i class="bi bi-percent me-2"></i>Add New Concession Type
            </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    {% if concession_types %}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm data-table">
                    <thead class="table-light">
                        <tr>
                            <th>Concession Name</th>
                            <th>Type</th>
                            <th>Value</th>
                            <th>Description</th>
                            <th class="text-center">Is Active?</th>
                            <th style="width: 130px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for ctype in concession_types %} {# Changed loop variable #}
                        <tr>
                            <td>{{ ctype.name }}</td>
                            <td>{{ ctype.get_type_display }}</td>
                            <td>
                                {% if ctype.type == 'PERCENTAGE' %}
                                    {{ ctype.value|floatformat:2 }}%
                                {% else %}
                                    {{ request.tenant.profile.currency_symbol|default:'$' }}{{ ctype.value|floatformat:2|intcomma }}
                                {% endif %}
                            </td>
                            <td>{{ ctype.description|truncatewords:15|default:"-" }}</td>
                            <td class="text-center">
                                {% if ctype.is_active %}
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                {% else %}
                                    <i class="bi bi-x-circle-fill text-secondary"></i>
                                {% endif %}
                            </td>
                            <td class="action-buttons">
                                {% if perms.fees.change_concessiontype %}
                                <a href="{% url 'fees:concession_type_update' ctype.pk %}" class="btn btn-secondary btn-sm" title="Edit Concession Type"><i class="bi bi-pencil-fill"></i></a>
                                {% endif %}
                                {% if perms.fees.delete_concessiontype %}
                                <form action="{% url 'fees:concession_type_delete' ctype.pk %}" method="post" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete Concession Type" onclick="return confirm('Are you sure you want to delete concession type \'{{ ctype.name|escapejs }}\'?');"><i class="bi bi-trash-fill"></i></button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info mt-3">
        No concession types found.
        {% if perms.fees.add_concessiontype %}
            <a href="{% url 'fees:concession_type_create' %}" class="alert-link ms-2">Add the first one?</a>
        {% endif %}
    </div>
    {% endif %}

    {% include "partials/_pagination.html" %}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left-circle me-2"></i>Back to Dashboard</a>
    </div>
</div>
{% endblock %}


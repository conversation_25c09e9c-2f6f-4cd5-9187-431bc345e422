/* D:\school_fees_saas_v2\apps\platform_management\static\platform_management\css\dashboard_styles.css */

/* --- General Dashboard Styling --- */
body.platform-admin-dashboard-page { /* Add this class to <body> in platform_admin_base.html */
    background-color: #f4f6f9; /* Light grey background for the page */
    font-family: 'Poppins', sans-serif; /* Ensure consistent font */
}

.platform-admin-dashboard-page .pagetitle {
    margin-bottom: 20px;
}

.platform-admin-dashboard-page .pagetitle h1 {
    font-size: 24px;
    font-weight: 600;
    color: #012970; /* A deep blue for titles */
}

.platform-admin-dashboard-page .breadcrumb {
    font-size: 14px;
}
.platform-admin-dashboard-page .breadcrumb-item.active {
    color: #555;
}

.platform-admin-dashboard-page .section.dashboard {
    padding-top: 10px;
}

/* --- Info Cards Styling --- */
.platform-admin-dashboard-page .info-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 0 30px rgba(1, 41, 112, 0.08); /* Softer shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem; /* Consistent spacing */
}

.platform-admin-dashboard-page .info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(1, 41, 112, 0.12);
}

.platform-admin-dashboard-page .info-card .card-body {
    padding: 20px 25px;
}

.platform-admin-dashboard-page .info-card .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #012970;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.platform-admin-dashboard-page .info-card .card-icon {
    font-size: 28px;
    width: 56px;
    height: 56px;
    flex-shrink: 0; /* Prevent icon from shrinking */
    margin-right: 15px; /* Space between icon and text */
}

/* Specific Info Card Icon Colors (Examples - choose your brand colors) */
.platform-admin-dashboard-page .info-card.sales-card .card-icon { /* For Platform Settings */
    background-color: #eef7ff; /* Light blue */
    color: #0d6efd; /* Primary blue */
}
.platform-admin-dashboard-page .info-card.revenue-card .card-icon { /* For Announcements */
    background-color: #fff0e6; /* Light orange */
    color: #fd7e14; /* Orange */
}
.platform-admin-dashboard-page .info-card.customers-card .card-icon { /* For System Notifications */
    background-color: #e6f7f2; /* Light teal */
    color: #20c997; /* Teal */
}
/* Maintenance mode card icon is styled inline based on status */

.platform-admin-dashboard-page .info-card .ps-3 h6 {
    font-size: 24px;
    font-weight: 700;
    color: #012970;
    margin-bottom: 5px;
}
.platform-admin-dashboard-page .info-card .ps-3 .text-muted {
    font-size: 13px;
}

/* --- Recent Audit Logs Card --- */
.platform-admin-dashboard-page .recent-sales .card-title { /* Reusing .recent-sales class from example */
    font-size: 18px;
    font-weight: 600;
    color: #012970;
}
.platform-admin-dashboard-page .recent-sales .card-title span {
    font-size: 14px;
    color: #899bbd;
    font-weight: 400;
}
.platform-admin-dashboard-page .recent-sales .table thead th {
    font-size: 13px;
    color: #555;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.platform-admin-dashboard-page .recent-sales .table tbody td {
    font-size: 14px;
    vertical-align: middle;
}
.platform-admin-dashboard-page .recent-sales .table .badge {
    font-size: 0.75em;
}

/* --- Quick Actions Card --- */
.platform-admin-dashboard-page .col-lg-4 .card .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #012970;
}
.platform-admin-dashboard-page .col-lg-4 .list-group-item {
    border-left: none;
    border-right: none;
    padding: 12px 15px;
    font-size: 15px;
}
.platform-admin-dashboard-page .col-lg-4 .list-group-item:first-child {
    border-top: none;
}
.platform-admin-dashboard-page .col-lg-4 .list-group-item:last-child {
    border-bottom: none;
}
.platform-admin-dashboard-page .col-lg-4 .list-group-item a {
    text-decoration: none;
    color: #012970;
    transition: color 0.2s ease;
}
.platform-admin-dashboard-page .col-lg-4 .list-group-item a:hover {
    color: #0d6efd; /* Primary blue on hover */
}

/* Optional: Styling for the right column layout itself */
.platform-admin-dashboard-page .col-lg-4 .card {
    margin-bottom: 2rem;
}



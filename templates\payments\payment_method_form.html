{% extends "tenant_base.html" %}
{% load static %}

{% block title %}Payment Method{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .errorlist {
        color: #dc3545;
        font-size: 0.875rem;
        list-style: none;
        padding: 0;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .form-check {
        margin-bottom: 1.5rem;
        padding-left: 0;
    }

    .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .form-check-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    .form-check-label {
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        display: flex;
        align-items: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-credit-card me-3"></i>
                            Payment Method
                        </h3>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Payment Method Details -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-money-check-alt me-2"></i>Payment Method Details</h4>
                            </div>

                            <!-- Name Field -->
                            <div class="form-floating">
                                <input type="text"
                                       class="form-control{% if form.name.errors %} is-invalid{% endif %}"
                                       id="{{ form.name.id_for_label }}"
                                       name="{{ form.name.name }}"
                                       value="{{ form.name.value|default:'' }}"
                                       placeholder="Enter payment method name"
                                       {% if form.name.field.required %}required{% endif %}>
                                <label for="{{ form.name.id_for_label }}">
                                    <i class="fas fa-tag icon-input"></i>Payment Method Name
                                </label>
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Type Field -->
                            <div class="form-floating">
                                <select class="form-select{% if form.type.errors %} is-invalid{% endif %}"
                                        id="{{ form.type.id_for_label }}"
                                        name="{{ form.type.name }}"
                                        {% if form.type.field.required %}required{% endif %}>
                                    <option value="">Select payment method type</option>
                                    {% for choice in form.type.field.choices %}
                                        {% if choice.0 %}
                                            <option value="{{ choice.0 }}" {% if form.type.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                <label for="{{ form.type.id_for_label }}">
                                    <i class="fas fa-list icon-input"></i>Payment Method Type
                                </label>
                                {% if form.type.help_text %}
                                    <div class="form-text">{{ form.type.help_text }}</div>
                                {% endif %}
                                {% if form.type.errors %}
                                    <div class="invalid-feedback">{{ form.type.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Linked Account Field -->
                            <div class="form-floating">
                                <select class="form-select{% if form.linked_account.errors %} is-invalid{% endif %}"
                                        id="{{ form.linked_account.id_for_label }}"
                                        name="{{ form.linked_account.name }}"
                                        {% if form.linked_account.field.required %}required{% endif %}>
                                    <option value="">Select linked account</option>
                                    {% for choice in form.linked_account.field.choices %}
                                        {% if choice.0 %}
                                            <option value="{{ choice.0 }}" {% if form.linked_account.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                <label for="{{ form.linked_account.id_for_label }}">
                                    <i class="fas fa-university icon-input"></i>Linked Account
                                </label>
                                {% if form.linked_account.help_text %}
                                    <div class="form-text">{{ form.linked_account.help_text }}</div>
                                {% endif %}
                                {% if form.linked_account.errors %}
                                    <div class="invalid-feedback">{{ form.linked_account.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Description Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.description.errors %} is-invalid{% endif %}"
                                          id="{{ form.description.id_for_label }}"
                                          name="{{ form.description.name }}"
                                          placeholder="Enter description"
                                          style="height: 120px;">{{ form.description.value|default:'' }}</textarea>
                                <label for="{{ form.description.id_for_label }}">
                                    <i class="fas fa-align-left icon-input"></i>Description
                                </label>
                                {% if form.description.help_text %}
                                    <div class="form-text">{{ form.description.help_text }}</div>
                                {% endif %}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Is Active Field -->
                            {% if form.is_active %}
                            <div class="form-check">
                                <input class="form-check-input{% if form.is_active.errors %} is-invalid{% endif %}"
                                       type="checkbox"
                                       id="{{ form.is_active.id_for_label }}"
                                       name="{{ form.is_active.name }}"
                                       {% if form.is_active.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    <i class="fas fa-toggle-on icon-input"></i>Active Payment Method
                                </label>
                                {% if form.is_active.help_text %}
                                    <div class="form-text">{{ form.is_active.help_text }}</div>
                                {% endif %}
                                {% if form.is_active.errors %}
                                    <div class="invalid-feedback">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-end gap-3 mt-5">
                                <a href="{% url 'payments:payment_method_list' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-circle me-2"></i>
                                    {% if object %}Update Payment Method{% else %}Create Payment Method{% endif %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Form validation and enhancement
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');

    // Form submission handling
    if (form && submitButton) {
        form.addEventListener('submit', function(e) {
            const originalText = submitButton.innerHTML;
            // Prevent double submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Processing...';

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }, 10000);
        });
    }

    // Enhanced form field interactions
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(function(control) {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');

            // Add validation feedback
            if (this.hasAttribute('required') && !this.value) {
                this.classList.add('is-invalid');
            } else if (this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Name field validation
    const nameField = form.querySelector('input[name="name"]');
    if (nameField) {
        nameField.addEventListener('input', function() {
            // Remove extra spaces and validate
            this.value = this.value.replace(/\s+/g, ' ').trim();
        });
    }

    // Add loading animation to form
    const formFloating = document.querySelectorAll('.form-floating');
    formFloating.forEach(function(floating, index) {
        floating.style.animationDelay = (index * 0.1) + 's';
        floating.classList.add('animate__animated', 'animate__fadeInUp');
    });
});
</script>
{% endblock %}

{% extends "tenant_base.html" %}
{% load static i18n %} {# Removed crispy_forms_tags if you're not using it globally in this template #}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        /* Same styles as journalentry_form.html for consistency */
        .formset-row { margin-bottom: 0.5rem; }
        .formset-row .form-control-sm, .formset-row .form-select-sm { font-size: 0.8rem; }
        /* Add styles for add/remove buttons if using JS for dynamic formsets */
        .is-invalid .form-select { /* Bootstrap 5 might need this for select borders */
            border-color: var(--bs-danger);
        }
        .is-invalid .form-control {
            border-color: var(--bs-danger);
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
    <h1>{{ view_title }}</h1>
    <hr>

    <form method="post" id="journalEntryUpdateForm">
        {% csrf_token %}
        
        <div class="card shadow-sm mb-3">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Details" %} (JE: {{ journal_entry.entry_number|default_if_none:journal_entry.pk }})</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                        {{ form.date }}
                        {% if form.date.errors %}<div class="invalid-feedback d-block">{{ form.date.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-5 mb-3">
                        <label for="{{ form.narration.id_for_label }}" class="form-label">{{ form.narration.label }}</label>
                        {{ form.narration }}
                        {% if form.narration.errors %}<div class="invalid-feedback d-block">{{ form.narration.errors|join:", " }}</div>{% endif %}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="{{ form.reference_number.id_for_label }}" class="form-label">{{ form.reference_number.label }}</label>
                        {{ form.reference_number }}
                        {% if form.reference_number.errors %}<div class="invalid-feedback d-block">{{ form.reference_number.errors|join:", " }}</div>{% endif %}
                    </div>
                </div>
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}<br>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Journal Entry Lines" %}</h5>
            </div>
            <div class="card-body">
                {{ line_formset.management_form }}
                {% if line_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {% for error in line_formset.non_form_errors %}
                            {{ error }}<br>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="table-responsive">
                    <table class="table table-sm" id="lines-table">
                        <thead>
                            <tr>
                                <th style="width: 30%;">{% trans "Account" %}</th>
                                <th style="width: 15%;" class="text-end">{% trans "Debit" %}</th>
                                <th style="width: 15%;" class="text-end">{% trans "Credit" %}</th>
                                <th style="width: 35%;">{% trans "Description" %}</th>
                                {% if line_formset.can_delete %}<th>{% trans "Del?" %}</th>{% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for form_line in line_formset %}
                            <tr class="formset-row {% if form_line.errors %}table-danger{% endif %}" id="{{ line_formset.prefix }}-{{ forloop.counter0 }}">
                                <td>
                                    {{ form_line.id }} {# Hidden ID field for the form line #}
                                    {{ form_line.account }}
                                    {% for error in form_line.account.errors %}<div class="text-danger small mt-1">{{ error }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.debit_amount }}
                                    {% for error in form_line.debit_amount.errors %}<div class="text-danger small mt-1">{{ error }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.credit_amount }}
                                    {% for error in form_line.credit_amount.errors %}<div class="text-danger small mt-1">{{ error }}</div>{% endfor %}
                                </td>
                                <td>
                                    {{ form_line.description }}
                                    {% for error in form_line.description.errors %}<div class="text-danger small mt-1">{{ error }}</div>{% endfor %}
                                </td>
                                {% if line_formset.can_delete %}
                                <td>
                                    {# Show DELETE checkbox only if the form has an instance (it's not a new/extra form) #}
                                    {% if form_line.instance.pk %}{{ form_line.DELETE }}{% endif %}
                                </td>
                                {% endif %}
                            </tr>
                            {% if form_line.non_field_errors %} {# For errors like "cannot have both debit and credit" #}
                                <tr><td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="p-0">
                                    <div class="alert alert-danger py-1 px-2 small mb-0 rounded-0">
                                    {% for error in form_line.non_field_errors %}{{ error }}{% endfor %}
                                    </div>
                                </td></tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td class="text-end fw-bold">{% trans "Totals:" %}</td>
                                <td class="text-end fw-bold" id="total-debit">0.00</td>
                                <td class="text-end fw-bold" id="total-credit">0.00</td>
                                <td colspan="{% if line_formset.can_delete %}2{% else %}1{% endif %}" id="balance-status" class="fw-bold"></td>
                            </tr>
                            <tr>
                                <td colspan="{% if line_formset.can_delete %}5{% else %}4{% endif %}" class="text-end">
                                    <button type="button" class="btn btn-sm btn-outline-success add-formset-row">
                                        <i class="bi bi-plus-circle"></i> {% trans "Add Line" %}
                                    </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-success">
                <i class="bi bi-save-fill me-1"></i> {% trans "Save Changes to Draft" %} {# MODIFIED BUTTON TEXT #}
            </button>
            <a href="{% url 'accounting:journalentry_detail' pk=journal_entry.pk %}" class="btn btn-outline-secondary">{% trans "Cancel" %}</a>
        </div>
    </form>

    {# Template for empty_form - MUST be outside the main <form> tag #}
    {# Ensure this matches the structure of rows within your <tbody> #}
    <template id="{{ line_formset.prefix }}-empty-form-template">
        {% spaceless %}
        <tr class="formset-row" id="{{ line_formset.empty_form.prefix }}"> {# No table-danger by default #}
            <td>
                {{ line_formset.empty_form.id }}
                {{ line_formset.empty_form.account }}
            </td>
            <td>{{ line_formset.empty_form.debit_amount }}</td>
            <td>{{ line_formset.empty_form.credit_amount }}</td>
            <td>{{ line_formset.empty_form.description }}</td>
            {% if line_formset.can_delete %}
            <td>
                {# No DELETE checkbox for a brand new empty form initially #}
            </td>
            {% endif %}
        </tr>
        {% endspaceless %}
    </template>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
    {{ block.super }}
    {# Same JavaScript as in journalentry_form.html for totals and adding rows #}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        function calculateTotals() {
            let totalDebit = 0;
            let totalCredit = 0;
            document.querySelectorAll('#lines-table tbody tr.formset-row').forEach(function(row) {
                const debitInput = row.querySelector('.jel-debit');
                const creditInput = row.querySelector('.jel-credit');
                const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');

                if (deleteCheckbox && deleteCheckbox.checked) {
                    return; 
                }

                if (debitInput && debitInput.value) {
                    const debitVal = parseFloat(debitInput.value);
                    if (!isNaN(debitVal)) totalDebit += debitVal;
                }
                if (creditInput && creditInput.value) {
                    const creditVal = parseFloat(creditInput.value);
                    if (!isNaN(creditVal)) totalCredit += creditVal;
                }
            });
            
            const totalDebitEl = document.getElementById('total-debit');
            const totalCreditEl = document.getElementById('total-credit');
            if(totalDebitEl) totalDebitEl.textContent = totalDebit.toFixed(2);
            if(totalCreditEl) totalCreditEl.textContent = totalCredit.toFixed(2);

            const balanceStatusEl = document.getElementById('balance-status');
            if (balanceStatusEl) {
                if (totalDebit === totalCredit && (totalDebit > 0 || totalCredit > 0)) {
                    balanceStatusEl.textContent = '{% trans "Balanced" %}';
                    balanceStatusEl.className = 'fw-bold text-success';
                } else if (actual_lines_count > 0 && totalDebit !== totalCredit ) { // Check actual_lines_count
                    balanceStatusEl.textContent = `{% trans "Out of Balance:" %} ${(totalDebit - totalCredit).toFixed(2)}`;
                    balanceStatusEl.className = 'fw-bold text-danger';
                } else {
                    balanceStatusEl.textContent = '';
                    balanceStatusEl.className = 'fw-bold';
                }
            }
        }
        
        let actual_lines_count = 0; // Keep track of non-empty lines for balance status
        function updateActualLinesCountAndRecalculate() {
            actual_lines_count = 0;
            document.querySelectorAll('#lines-table tbody tr.formset-row').forEach(function(row) {
                const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
                if (deleteCheckbox && deleteCheckbox.checked) return;

                const accountInput = row.querySelector('.jel-account');
                const debitInput = row.querySelector('.jel-debit');
                const creditInput = row.querySelector('.jel-credit');
                if (accountInput && accountInput.value && 
                    ((debitInput && parseFloat(debitInput.value) > 0) || (creditInput && parseFloat(creditInput.value) > 0))
                ) {
                    actual_lines_count++;
                }
            });
            calculateTotals();
        }


        function attachListenersToRow(row) {
            row.querySelectorAll('.jel-debit, .jel-credit, .jel-account, .jel-entry-type').forEach(function(input) {
                input.addEventListener('input', updateActualLinesCountAndRecalculate);
                input.addEventListener('change', updateActualLinesCountAndRecalculate); 
            });
            const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
            if(deleteCheckbox){
                deleteCheckbox.addEventListener('change', updateActualLinesCountAndRecalculate);
            }
        }

        document.querySelectorAll('#lines-table tbody tr.formset-row').forEach(attachListenersToRow);
        updateActualLinesCountAndRecalculate(); // Initial calculation and count

        const addRowButton = document.querySelector('.add-formset-row');
        if (addRowButton) {
            const formsetTableBody = document.getElementById('lines-table').querySelector('tbody');
            const totalFormsInput = document.getElementById('id_{{ line_formset.prefix }}-TOTAL_FORMS');
            const emptyFormTemplate = document.getElementById('{{ line_formset.prefix }}-empty-form-template');
            
            if (totalFormsInput && emptyFormTemplate && emptyFormTemplate.innerHTML) {
                addRowButton.addEventListener('click', function() {
                    let formIdx = parseInt(totalFormsInput.value);
                    let newFormHtml = emptyFormTemplate.innerHTML.replace(/__prefix__/g, formIdx);
                    
                    const tempTbody = document.createElement('tbody'); // Use tbody to parse full row structure
                    tempTbody.innerHTML = newFormHtml;
                    const newRow = tempTbody.querySelector('tr.formset-row'); 

                    if (newRow) {
                        formsetTableBody.appendChild(newRow);
                        attachListenersToRow(newRow);
                        totalFormsInput.value = formIdx + 1;
                        newRow.querySelector('.jel-account')?.focus();
                        updateActualLinesCountAndRecalculate();
                    } else {
                        console.error("Could not create new row from empty_form template. Check template content and structure.");
                    }
                });
            } else {
                console.warn("TOTAL_FORMS input or empty_form template content not found/empty. Dynamic row adding might not work correctly.");
            }
        }
    });
    </script>
{% endblock extra_tenant_js %}





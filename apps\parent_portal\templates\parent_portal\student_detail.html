{% extends "parent_portal/parent_portal_base.html" %}
{% load static humanize %}

{% block parent_portal_page_title %}{{ view_title|default:"Student Details" }}{% endblock parent_portal_page_title %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Parent Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ student.full_name }} - Details</li>
        </ol>
    </nav>

    <h1 class="mb-3">{{ view_title }}</h1>

    {% if student %}
    <div class="card">
        <div class="card-header">
            Student Information
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 text-center">
                    {% if student.photo %}
                        <img src="{{ student.photo.url }}" alt="{{ student.full_name }}" class="img-thumbnail mb-3" style="max-width: 150px; max-height: 150px;">
                    {% else %}
                        <div class="img-thumbnail mb-3 d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; background-color: #f0f0f0;">
                            <span class="text-muted">No Photo</span>
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-9">
                    <h4 class="card-title">{{ student.full_name }}</h4>
                    <p><strong>Admission Number:</strong> {{ student.admission_number }}</p>
                    <p><strong>Class:</strong> {{ student.current_class|default:"N/A" }} {{ student.current_section|default:"" }}</p>
                    <p><strong>Roll Number:</strong> {{ student.roll_number|default:"N/A" }}</p>
                    <p><strong>Date of Birth:</strong> {{ student.date_of_birth|date:"d M Y"|default:"N/A" }}</p>
                    <p><strong>Gender:</strong> {{ student.get_gender_display|default:"N/A" }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-{% if student.is_active %}success{% else %}danger{% endif %}">{{ student.get_status_display }}</span></p>
                </div>
            </div>
            {# Add more student details here as needed from your Student model #}
            <hr>
            <p><strong>Guardian Information (on student record):</strong></p>
            <p>{{ student.guardian1_full_name }} ({{ student.guardian1_relationship }}) - {{ student.guardian1_phone }}</p>
            {% if student.guardian2_full_name %}
            <p>{{ student.guardian2_full_name }} ({{ student.guardian2_relationship }}) - {{ student.guardian2_phone }}</p>
            {% endif %}
        </div>
    </div>
    {% else %}
        <p>Student details could not be loaded.</p>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary me-2"><i class="bi bi-arrow-left-circle"></i> Back to Dashboard</a>
        {% if student %}
        <a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}" class="btn btn-info me-2"><i class="bi bi-receipt"></i> View {{ student.first_name }}'s Fees</a>
        {# --- ADDED PAYMENT HISTORY LINK HERE --- #}
        <a href="{% url 'parent_portal:student_payment_history_specific' student_pk=student.pk %}" class="btn btn-success">
            <i class="bi bi-cash-stack"></i> View {{ student.first_name }}'s Payment History
        </a>
        {# --- END OF ADDED LINK --- #}
        {% endif %}
    </div>
</div>
{% endblock parent_portal_main_content %}





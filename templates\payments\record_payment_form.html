{% extends "tenant_base.html" %}
{% load static %}

{% load humanize %}

{% block title %}Record Payment{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
        max-width: 900px;
        margin: 2rem auto;
    }

    .premium-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #20c997, #17a2b8, #6f42c1, #e83e8c, #fd7e14, #20c997);
        background-size: 300% 100%;
        animation: rainbow-border 3s ease-in-out infinite;
        z-index: 10;
    }

    @keyframes rainbow-border {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .premium-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 60px rgba(31, 38, 135, 0.6);
    }

    .premium-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(32, 201, 151, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(23, 162, 184, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(111, 66, 193, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .premium-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shine 2s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem 0.75rem 0.5rem 0.75rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        transition: all 0.3s ease;
        font-size: 1rem;
        position: relative;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .form-floating > .form-control::before,
    .form-floating > .form-select::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 1rem;
        background: linear-gradient(45deg, transparent, rgba(32, 201, 151, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #20c997;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25), 0 8px 25px rgba(32, 201, 151, 0.15);
        transform: translateY(-3px) scale(1.02);
        background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
    }

    .form-floating:hover > .form-control,
    .form-floating:hover > .form-select {
        border-color: #20c997;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(32, 201, 151, 0.1);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1.25rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #20c997;
        font-weight: 500;
        z-index: 2;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
        color: #20c997;
        background: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }



    .btn-premium {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 1rem;
        padding: 1.2rem 3rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 200px;
        font-size: 0.95rem;
    }

    .btn-premium:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(40, 167, 69, 0.6);
        color: white;
        text-decoration: none;
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1.2rem 3rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 200px;
        font-size: 0.95rem;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
        background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1.2rem;
        padding: 2rem;
        margin-bottom: 2.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    }

    .fieldset-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.6s ease;
    }

    .fieldset-header:hover::before {
        left: 100%;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 700;
        margin: 0;
        font-size: 1.3rem;
        text-shadow: 0 1px 3px rgba(102, 126, 234, 0.2);
        position: relative;
        z-index: 2;
    }

    .fieldset-header i {
        font-size: 1.5rem;
        margin-right: 0.8rem;
        color: #667eea;
        text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .premium-body {
        padding: 3rem 2rem;
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
    }

    .premium-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(32, 201, 151, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(23, 162, 184, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .errorlist {
        color: #dc3545;
        font-size: 0.875rem;
        list-style: none;
        padding: 0;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="premium-card">
                    <div class="premium-header">
                        <i class="fas fa-credit-card" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.9;"></i>
                        <h3 class="mb-2 fw-bold">Record Payment</h3>
                        <p class="mb-0 opacity-75">
                            {% if invoice %}
                                Process payment for Invoice #{{ invoice.invoice_number|default:invoice.pk }}
                            {% else %}
                                Record a new payment transaction for student fees
                            {% endif %}
                        </p>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        {% if invoice %}
                            <div class="alert alert-info mb-4">
                                <h6 class="alert-heading">Payment for Invoice</h6>
                                <p class="mb-1"><strong>Invoice:</strong> #{{ invoice.invoice_number|default:invoice.pk }}</p>
                                <p class="mb-1"><strong>Student:</strong> {{ invoice.student.full_name }}</p>
                                <p class="mb-0"><strong>Amount Due:</strong> {{ request.tenant.profile.currency_symbol|default:"$" }}{{ invoice.amount_due|floatformat:2|intcomma }}</p>
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Student & Invoice Information -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-user-graduate me-2"></i>Student & Invoice Information</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Parent Payer Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.parent_payer.errors %} is-invalid{% endif %}"
                                                id="{{ form.parent_payer.id_for_label }}"
                                                name="{{ form.parent_payer.name }}"
                                                {% if form.parent_payer.field.required %}required{% endif %}>
                                            <option value="">Select parent (optional)</option>
                                            {% for choice in form.parent_payer.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.parent_payer.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.parent_payer.id_for_label }}">
                                            <i class="fas fa-user-friends icon-input"></i>Parent Payer
                                        </label>
                                        {% if form.parent_payer.help_text %}
                                            <div class="form-text">{{ form.parent_payer.help_text }}</div>
                                        {% endif %}
                                        {% if form.parent_payer.errors %}
                                            <div class="invalid-feedback">{{ form.parent_payer.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Student Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.student.errors %} is-invalid{% endif %}"
                                                id="{{ form.student.id_for_label }}"
                                                name="{{ form.student.name }}"
                                                {% if form.student.field.required %}required{% endif %}>
                                            <option value="">Select student</option>
                                            {% for choice in form.student.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.student.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.student.id_for_label }}">
                                            <i class="fas fa-user icon-input"></i>Student
                                        </label>
                                        {% if form.student.errors %}
                                            <div class="invalid-feedback">{{ form.student.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <!-- Invoice Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.invoice_to_apply_to.errors %} is-invalid{% endif %}"
                                                id="{{ form.invoice_to_apply_to.id_for_label }}"
                                                name="{{ form.invoice_to_apply_to.name }}">
                                            <option value="">Select invoice (optional)</option>
                                            {% for choice in form.invoice_to_apply_to.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.invoice_to_apply_to.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.invoice_to_apply_to.id_for_label }}">
                                            <i class="fas fa-file-invoice icon-input"></i>Invoice to Apply To
                                        </label>
                                        {% if form.invoice_to_apply_to.help_text %}
                                            <div class="form-text">{{ form.invoice_to_apply_to.help_text }}</div>
                                        {% endif %}
                                        {% if form.invoice_to_apply_to.errors %}
                                            <div class="invalid-feedback">{{ form.invoice_to_apply_to.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Details -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-money-bill-wave me-2"></i>Payment Details</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Amount Field with Currency Symbol -->
                                    <div class="form-floating">
                                        <input type="number"
                                               class="form-control{% if form.amount.errors %} is-invalid{% endif %}"
                                               id="{{ form.amount.id_for_label }}"
                                               name="{{ form.amount.name }}"
                                               value="{{ form.amount.value|default:'' }}"
                                               step="0.01"
                                               placeholder="0.00"
                                               {% if form.amount.field.required %}required{% endif %}>
                                        <label for="{{ form.amount.id_for_label }}">
                                            <i class="fas fa-dollar-sign icon-input"></i>Payment Amount ({{ request.tenant.profile.currency_symbol|default:"$" }})
                                        </label>
                                        {% if form.amount.errors %}
                                            <div class="invalid-feedback">{{ form.amount.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Payment Date Field -->
                                    <div class="form-floating">
                                        <input type="date"
                                               class="form-control{% if form.payment_date.errors %} is-invalid{% endif %}"
                                               id="{{ form.payment_date.id_for_label }}"
                                               name="{{ form.payment_date.name }}"
                                               value="{{ form.payment_date.value|default:'' }}"
                                               {% if form.payment_date.field.required %}required{% endif %}>
                                        <label for="{{ form.payment_date.id_for_label }}">
                                            <i class="fas fa-calendar-alt icon-input"></i>Payment Date
                                        </label>
                                        {% if form.payment_date.errors %}
                                            <div class="invalid-feedback">{{ form.payment_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Payment Method Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.payment_method.errors %} is-invalid{% endif %}"
                                                id="{{ form.payment_method.id_for_label }}"
                                                name="{{ form.payment_method.name }}"
                                                {% if form.payment_method.field.required %}required{% endif %}>
                                            <option value="">Select payment method</option>
                                            {% for choice in form.payment_method.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.payment_method.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.payment_method.id_for_label }}">
                                            <i class="fas fa-credit-card icon-input"></i>Payment Method
                                        </label>
                                        {% if form.payment_method.errors %}
                                            <div class="invalid-feedback">{{ form.payment_method.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Academic Year Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.academic_year.errors %} is-invalid{% endif %}"
                                                id="{{ form.academic_year.id_for_label }}"
                                                name="{{ form.academic_year.name }}"
                                                {% if form.academic_year.field.required %}required{% endif %}>
                                            <option value="">Select academic year</option>
                                            {% for choice in form.academic_year.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.academic_year.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.academic_year.id_for_label }}">
                                            <i class="fas fa-graduation-cap icon-input"></i>Academic Year
                                        </label>
                                        {% if form.academic_year.errors %}
                                            <div class="invalid-feedback">{{ form.academic_year.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Transaction Reference Field -->
                            <div class="form-floating">
                                <input type="text"
                                       class="form-control{% if form.transaction_reference.errors %} is-invalid{% endif %}"
                                       id="{{ form.transaction_reference.id_for_label }}"
                                       name="{{ form.transaction_reference.name }}"
                                       value="{{ form.transaction_reference.value|default:'' }}"
                                       placeholder="Enter transaction reference">
                                <label for="{{ form.transaction_reference.id_for_label }}">
                                    <i class="fas fa-hashtag icon-input"></i>Transaction Reference
                                </label>
                                {% if form.transaction_reference.help_text %}
                                    <div class="form-text">{{ form.transaction_reference.help_text }}</div>
                                {% endif %}
                                {% if form.transaction_reference.errors %}
                                    <div class="invalid-feedback">{{ form.transaction_reference.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Additional Information -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-info-circle me-2"></i>Additional Information</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Payment Type Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.payment_type.errors %} is-invalid{% endif %}"
                                                id="{{ form.payment_type.id_for_label }}"
                                                name="{{ form.payment_type.name }}"
                                                {% if form.payment_type.field.required %}required{% endif %}>
                                            <option value="">Select payment type</option>
                                            {% for choice in form.payment_type.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.payment_type.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.payment_type.id_for_label }}">
                                            <i class="fas fa-tag icon-input"></i>Payment Type
                                        </label>
                                        {% if form.payment_type.errors %}
                                            <div class="invalid-feedback">{{ form.payment_type.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Status Field -->
                                    <div class="form-floating">
                                        <select class="form-select{% if form.status.errors %} is-invalid{% endif %}"
                                                id="{{ form.status.id_for_label }}"
                                                name="{{ form.status.name }}"
                                                {% if form.status.field.required %}required{% endif %}>
                                            <option value="">Select status</option>
                                            {% for choice in form.status.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.status.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.status.id_for_label }}">
                                            <i class="fas fa-check-circle icon-input"></i>Status
                                        </label>
                                        {% if form.status.errors %}
                                            <div class="invalid-feedback">{{ form.status.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.notes.errors %} is-invalid{% endif %}"
                                          id="{{ form.notes.id_for_label }}"
                                          name="{{ form.notes.name }}"
                                          placeholder="Enter any additional notes"
                                          style="height: 120px;">{{ form.notes.value|default:'' }}</textarea>
                                <label for="{{ form.notes.id_for_label }}">
                                    <i class="fas fa-sticky-note icon-input"></i>Notes
                                </label>
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-credit-card me-2"></i>Record Payment
                                </button>
                                {% if invoice %}
                                    <a href="{% url 'fees:invoice_detail' invoice.pk %}" class="btn-secondary-premium">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Invoice
                                    </a>
                                {% else %}
                                    <a href="{% url 'payments:payment_list' %}" class="btn-secondary-premium">
                                        <i class="fas fa-list me-2"></i>View All Payments
                                    </a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Form validation and enhancement
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    const amountField = form.querySelector('input[name="amount"]');
    const paymentDateField = form.querySelector('input[name="payment_date"]');

    // Set default payment date to today
    if (paymentDateField && !paymentDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        paymentDateField.value = today;
    }

    // Amount field formatting and validation
    if (amountField) {
        amountField.addEventListener('input', function() {
            // Remove any non-numeric characters except decimal point
            this.value = this.value.replace(/[^0-9.]/g, '');

            // Ensure only one decimal point
            const parts = this.value.split('.');
            if (parts.length > 2) {
                this.value = parts[0] + '.' + parts.slice(1).join('');
            }

            // Limit to 2 decimal places
            if (parts[1] && parts[1].length > 2) {
                this.value = parts[0] + '.' + parts[1].substring(0, 2);
            }
        });

        amountField.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value) && value > 0) {
                this.value = value.toFixed(2);
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (this.value) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            }
        });
    }

    // Form submission handling
    if (form && submitButton) {
        form.addEventListener('submit', function(e) {
            // Validate amount
            if (amountField) {
                const amount = parseFloat(amountField.value);
                if (isNaN(amount) || amount <= 0) {
                    e.preventDefault();
                    amountField.classList.add('is-invalid');
                    amountField.focus();
                    return false;
                }
            }

            // Prevent double submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Payment...';
            submitButton.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Record Payment';
                submitButton.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            }, 10000);
        });
    }

    // Enhanced form field interactions
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(function(control) {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');

            // Add validation feedback
            if (this.hasAttribute('required') && !this.value) {
                this.classList.add('is-invalid');
            } else if (this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Student selection change handler
    const studentField = form.querySelector('select[name="student"]');
    const invoiceField = form.querySelector('select[name="invoice_to_apply_to"]');

    if (studentField && invoiceField) {
        studentField.addEventListener('change', function() {
            // Clear invoice selection when student changes
            invoiceField.value = '';
            // In a real implementation, you might want to filter invoices by student
        });
    }

    // Add loading animation to form
    const formFloating = document.querySelectorAll('.form-floating');
    formFloating.forEach(function(floating, index) {
        floating.style.animationDelay = (index * 0.1) + 's';
        floating.classList.add('animate__animated', 'animate__fadeInUp');
    });
});
</script>
{% endblock %}


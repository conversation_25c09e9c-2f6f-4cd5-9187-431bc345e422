# Generated by Django 5.1.9 on 2025-07-06 08:55

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0004_taxbracket_payslip_salarycomponent_paysliplineitem_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='payslip',
            options={'ordering': ['-pay_period_start', 'staff_user__last_name']},
        ),
        migrations.AlterModelOptions(
            name='paysliplineitem',
            options={'ordering': ['type', 'name']},
        ),
        migrations.AlterModelOptions(
            name='salarycomponent',
            options={'ordering': ['component_type', 'name'], 'verbose_name': 'Salary Component', 'verbose_name_plural': 'Salary Components'},
        ),
        migrations.AlterModelOptions(
            name='staffsalary',
            options={},
        ),
        migrations.RenameField(
            model_name='salarycomponent',
            old_name='type',
            new_name='component_type',
        ),
        migrations.AlterUniqueTogether(
            name='salarycomponent',
            unique_together={('name', 'component_type')},
        ),
        migrations.RemoveField(
            model_name='staffsalary',
            name='is_active',
        ),
        migrations.AlterField(
            model_name='payslip',
            name='gross_earnings',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='net_pay',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='total_deductions',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12),
        ),
        migrations.AlterField(
            model_name='salarycomponent',
            name='is_statutory',
            field=models.BooleanField(default=False, help_text='Check if this is a mandatory government/statutory deduction (e.g., Income Tax).', verbose_name='Is Statutory'),
        ),
        migrations.AlterField(
            model_name='taxbracket',
            name='deduction_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Flat Deduction Amount'),
        ),
        migrations.AlterField(
            model_name='taxbracket',
            name='rate_percent',
            field=models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Tax Rate (%)'),
        ),
        migrations.CreateModel(
            name='SalaryGrade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Grade Name')),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Salary Grade',
                'verbose_name_plural': 'Salary Grades',
                'ordering': ['name'],
                'unique_together': {('name',)},
            },
        ),
        migrations.AddField(
            model_name='staffsalary',
            name='grade',
            field=models.ForeignKey(blank=True, help_text='The salary grade determining recurring allowances and deductions.', null=True, on_delete=django.db.models.deletion.PROTECT, to='hr.salarygrade'),
        ),
        migrations.CreateModel(
            name='SalaryGradeComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Amount')),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='grade_links', to='hr.salarycomponent')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='hr.salarygrade')),
            ],
            options={
                'verbose_name': 'Salary Grade Component',
                'verbose_name_plural': 'Salary Grade Components',
                'ordering': ['component__component_type', 'component__name'],
                'unique_together': {('grade', 'component')},
            },
        ),
        migrations.DeleteModel(
            name='StaffSalaryComponent',
        ),
    ]

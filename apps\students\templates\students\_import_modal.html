{# apps/students/templates/students/_import_modal.html - Student Import Modal #}
{% load i18n %}

<!-- Student Import Modal -->
<div class="modal fade" id="importStudentsModal" tabindex="-1" aria-labelledby="importStudentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="importStudentsModalLabel">
                    <i class="bi bi-upload me-2"></i>{% trans "Import Students from Excel" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importStudentsForm" method="post" enctype="multipart/form-data" action="{% url 'students:import_students' %}">
                    {% csrf_token %}
                    
                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>{% trans "Import Instructions" %}</h6>
                        <ul class="mb-0">
                            <li>{% trans "Download the template file to see the required format" %}</li>
                            <li>{% trans "Fill in student data following the column headers exactly" %}</li>
                            <li>{% trans "Save your file as Excel (.xlsx) format" %}</li>
                            <li>{% trans "Upload the file using the form below" %}</li>
                        </ul>
                    </div>

                    <!-- Template Download -->
                    <div class="mb-4">
                        <h6>{% trans "Step 1: Download Template" %}</h6>
                        <a href="{% url 'students:download_import_template' %}" class="btn btn-outline-primary">
                            <i class="bi bi-download me-2"></i>{% trans "Download Excel Template" %}
                        </a>
                        <small class="form-text text-muted d-block mt-1">
                            {% trans "Download this template file and fill it with your student data." %}
                        </small>
                    </div>

                    <!-- File Upload -->
                    <div class="mb-4">
                        <h6>{% trans "Step 2: Upload Completed File" %}</h6>
                        <div class="mb-3">
                            <label for="import_file" class="form-label">{% trans "Select Excel File" %}</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx,.xls" required>
                            <small class="form-text text-muted">
                                {% trans "Accepted formats: .xlsx, .xls (Maximum size: 10MB)" %}
                            </small>
                        </div>
                    </div>

                    <!-- Import Options -->
                    <div class="mb-4">
                        <h6>{% trans "Step 3: Import Options" %}</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing" value="1">
                            <label class="form-check-label" for="update_existing">
                                {% trans "Update existing students if admission number matches" %}
                            </label>
                            <small class="form-text text-muted d-block">
                                {% trans "If unchecked, duplicate admission numbers will be skipped." %}
                            </small>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="validate_only" name="validate_only" value="1">
                            <label class="form-check-label" for="validate_only">
                                {% trans "Validate only (don't save data)" %}
                            </label>
                            <small class="form-text text-muted d-block">
                                {% trans "Check this to preview import results without saving." %}
                            </small>
                        </div>
                    </div>

                    <!-- Progress Bar (hidden initially) -->
                    <div id="importProgress" class="mb-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">{% trans "Processing import..." %}</small>
                    </div>

                    <!-- Results Container (hidden initially) -->
                    <div id="importResults" style="display: none;">
                        <h6>{% trans "Import Results" %}</h6>
                        <div id="importResultsContent"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>{% trans "Cancel" %}
                </button>
                <button type="submit" form="importStudentsForm" class="btn btn-success" id="importSubmitBtn">
                    <i class="bi bi-upload me-2"></i>{% trans "Import Students" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importStudentsForm');
    const importProgress = document.getElementById('importProgress');
    const importResults = document.getElementById('importResults');
    const importResultsContent = document.getElementById('importResultsContent');
    const submitBtn = document.getElementById('importSubmitBtn');
    const fileInput = document.getElementById('import_file');

    // File input validation
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Check file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    alert('{% trans "File size must be less than 10MB" %}');
                    this.value = '';
                    return;
                }

                // Check file type
                const allowedTypes = [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-excel'
                ];
                if (!allowedTypes.includes(file.type)) {
                    alert('{% trans "Please select a valid Excel file (.xlsx or .xls)" %}');
                    this.value = '';
                    return;
                }
            }
        });
    }

    // Form submission
    if (importForm) {
        importForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            
            // Show progress
            importProgress.style.display = 'block';
            importResults.style.display = 'none';
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

            // Simulate progress
            let progress = 0;
            const progressBar = importProgress.querySelector('.progress-bar');
            const progressInterval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);

            // Submit form
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(function() {
                    importProgress.style.display = 'none';
                    importResults.style.display = 'block';
                    
                    if (data.success) {
                        importResultsContent.innerHTML = `
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle me-2"></i>{% trans "Import Successful" %}</h6>
                                <ul class="mb-0">
                                    <li>{% trans "Students imported:" %} ${data.imported_count || 0}</li>
                                    <li>{% trans "Students updated:" %} ${data.updated_count || 0}</li>
                                    <li>{% trans "Students skipped:" %} ${data.skipped_count || 0}</li>
                                </ul>
                            </div>
                        `;
                        
                        if (!document.getElementById('validate_only').checked) {
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    } else {
                        let errorHtml = `
                            <div class="alert alert-danger">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>{% trans "Import Failed" %}</h6>
                                <p>${data.message || '{% trans "An error occurred during import" %}'}</p>
                        `;
                        
                        if (data.errors && data.errors.length > 0) {
                            errorHtml += '<ul class="mb-0">';
                            data.errors.forEach(function(error) {
                                errorHtml += `<li>${error}</li>`;
                            });
                            errorHtml += '</ul>';
                        }
                        
                        errorHtml += '</div>';
                        importResultsContent.innerHTML = errorHtml;
                    }
                    
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Import Students" %}';
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                importProgress.style.display = 'none';
                importResults.style.display = 'block';
                
                importResultsContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-exclamation-triangle me-2"></i>{% trans "Import Error" %}</h6>
                        <p>{% trans "An unexpected error occurred. Please try again." %}</p>
                    </div>
                `;
                
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Import Students" %}';
                
                console.error('Import error:', error);
            });
        });
    }

    // Reset modal when closed
    const modal = document.getElementById('importStudentsModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            importForm.reset();
            importProgress.style.display = 'none';
            importResults.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Import Students" %}';
        });
    }
});
</script>

{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\make_payment_summary.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Review & Pay Fees") }}{% endblock parent_portal_page_title %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .invoice-summary-table th, .invoice-summary-table td {
            vertical-align: middle;
            font-size: 0.9rem;
        }
        .invoice-summary-table .currency-value {
            text-align: right;
        }
        .total-payable-section {
            background-color: #f8f9fa; /* Light grey background */
            border-top: 2px solid #007bff; /* Primary color accent */
            padding: 1.5rem;
            margin-top: 1.5rem;
            border-radius: 0.25rem;
        }
        .total-payable-amount {
            font-size: 2rem;
            font-weight: bold;
            color: var(--bs-primary);
        }
        .payment-methods img {
            max-height: 40px;
            margin: 0 0.5rem;
            opacity: 0.7;
        }
    </style>
{% endblock extra_parent_portal_css %}

{% block parent_portal_main_content %}
<div class="container mt-4 mb-5">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8"> {# Wider column for payment details #}

            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="mb-0 display-6">{{ view_title }}</h1>
                {% if school_profile and school_profile.logo %}
                    <img src="{{ school_profile.logo.url }}" alt="{{ request.tenant.name }} Logo" style="max-height: 50px;" class="rounded d-none d-sm-block">
                {% endif %}
            </div>
            <p class="text-muted">{% trans "Parent" %}: {{ parent.get_full_name|default:parent.email }}</p>
            <p class="text-muted">{% trans "School" %}: {{ request.tenant.name }}</p>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% if not tenant_features.ONLINE_PAYMENTS %}
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {% trans "Online payments are not currently enabled for this school. Please contact administration for alternative payment methods." %}
                </div>
            {% endif %}

            {% if outstanding_invoices %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Outstanding Invoices Included in This Payment" %}</h5>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover invoice-summary-table mb-0">
                            <thead>
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Invoice No." %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th class="currency-value">{% trans "Balance Due" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in outstanding_invoices %}
                                <tr>
                                    <td>{{ invoice.student.get_short_name|default:invoice.student.full_name }}</td>
                                    <td>
                                        <a href="{% url 'parent_portal:student_fees' student_pk=invoice.student.pk %}#invoice-{{invoice.pk}}" title="View all fees for {{invoice.student.first_name}}">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.due_date|date:"d M Y" }}</td>
                                    <td class="currency-value">
                                        {{ school_profile.currency_symbol|default:"$" }}{{ invoice.balance_due|floatformat:2|intcomma }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="total-payable-section text-center">
                    <h4 class="text-muted fw-normal">{% trans "Total Amount Due" %}</h4>
                    <p class="total-payable-amount mb-3">
                        {{ school_profile.currency_symbol|default:"$" }}{{ total_amount_to_pay|floatformat:2|intcomma }}
                    </p>

                    {% if tenant_features.ONLINE_PAYMENTS and total_amount_to_pay > 0 %}
                        {# This is where your Payment Gateway form/button would go #}
                        {# Example for Payfast (simplified - actual fields depend on gateway) #}
                        <form method="POST" action="{{ settings.PAYFAST_PROCESS_URL }}"> {# PAYFAST_PROCESS_URL from settings.py #}
                            {% comment %} {% csrf_token %}  CSRF might not be needed if posting to external URL, check gateway docs {% endcomment %}
                            
                            {# These would be dynamically generated in the view's get_context_data and passed in 'payment_gateway_data' #}
                            {# <input type="hidden" name="merchant_id" value="{{ payment_gateway_data.merchant_id }}"> #}
                            {# <input type="hidden" name="merchant_key" value="{{ payment_gateway_data.merchant_key }}"> #}
                            {# <input type="hidden" name="return_url" value="{{ payment_gateway_data.return_url }}"> #}
                            {# <input type="hidden" name="cancel_url" value="{{ payment_gateway_data.cancel_url }}"> #}
                            {# <input type="hidden" name="notify_url" value="{{ payment_gateway_data.notify_url }}"> #}
                            
                            {# <input type="hidden" name="name_first" value="{{ parent.first_name|default:'' }}"> #}
                            {# <input type="hidden" name="name_last" value="{{ parent.last_name|default:'' }}"> #}
                            {# <input type="hidden" name="email_address" value="{{ parent.email }}"> #}
                            
                            {# <input type="hidden" name="amount" value="{{ total_amount_to_pay|floatformat:2 }}"> #}
                            {# <input type="hidden" name="item_name" value="School Fees Payment for {{ request.tenant.name }}"> #}
                            {# <input type="hidden" name="item_description" value="Consolidated payment for outstanding invoices."> #}
                            {# <input type="hidden" name="custom_str1" value="parent_id_{{ parent.pk }}"> #}
                            {# <input type="hidden" name="custom_str2" value="tenant_schema_{{ request.tenant.schema_name }}"> #}
                            {# Add other required or optional fields for your gateway #}

                            <button type="submit" class="btn btn-lg btn-success w-100 mb-3">
                                <i class="bi bi-shield-lock-fill me-2"></i>{% trans "Proceed to Secure Payment" %}
                            </button>
                            <p class="small text-muted">{% trans "You will be redirected to our secure payment partner to complete your transaction." %}</p>
                        </form>
                        
                        <div class="payment-methods mt-3">
                            {# Example logos - replace with actual or relevant ones #}
                            <img src="{% static 'img/payment_icons/visa.png' %}" alt="Visa">
                            <img src="{% static 'img/payment_icons/mastercard.png' %}" alt="Mastercard">
                            {# <img src="{% static 'img/payment_icons/payfast_logo_sm.png' %}" alt="Payfast"> #}
                        </div>

                    {% elif total_amount_to_pay <= 0 %}
                        <div class="alert alert-success mt-3">
                            <i class="bi bi-check-circle-fill me-2"></i>{% trans "There are no outstanding fees to be paid at this time." %}
                        </div>
                    {% endif %}
                </div>

            {% else %}
                <div class="alert alert-success text-center py-4">
                    <i class="bi bi-patch-check-fill fs-1 text-success d-block mb-2"></i>
                    <h4 class="alert-heading">{% trans "All Clear!" %}</h4>
                    <p>{% trans "There are currently no outstanding invoices for your children." %}</p>
                </div>
            {% endif %}

            <div class="mt-4 text-center">
                <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left-circle me-1"></i>{% trans "Back to Dashboard" %}
                </a>
                <a href="{% url 'parent_portal:payment_history_all' %}" class="btn btn-outline-info ms-2">
                    <i class="bi bi-list-ul me-1"></i>{% trans "View Full Payment History" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock parent_portal_main_content %}



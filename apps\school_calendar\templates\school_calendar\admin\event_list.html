{% extends "base.html" %}
{% load static i18n %}

{% block title %}{% trans "Manage Events" %} - {% trans "Calendar Admin" %}{% endblock %}

{% block extra_css %}
<style>
    .admin-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }
    
    .event-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    
    .event-card:hover {
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }
    .priority-urgent { background: #f5c6cb; color: #721c24; }
    
    .event-meta {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Admin Header -->
    <div class="card shadow-sm">
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-gear me-2"></i>
                        {% trans "Event Management" %}
                    </h1>
                    <p class="mb-0 opacity-75">{% trans "Create, edit, and manage school calendar events" %}</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>{% trans "Create New Event" %}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number">{{ events|length }}</div>
                        <small class="text-muted">{% trans "Total Events" %}</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number text-success">
                            {% for event in events %}{% if event.is_upcoming %}{{ forloop.counter }}{% endif %}{% endfor %}
                        </div>
                        <small class="text-muted">{% trans "Upcoming" %}</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number text-warning">
                            {% for event in events %}{% if event.is_ongoing %}{{ forloop.counter }}{% endif %}{% endfor %}
                        </div>
                        <small class="text-muted">{% trans "Ongoing" %}</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number text-info">
                            {% for event in events %}{% if event.requires_rsvp %}{{ forloop.counter }}{% endif %}{% endfor %}
                        </div>
                        <small class="text-muted">{% trans "RSVP Events" %}</small>
                    </div>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">{% trans "Event Type" %}</label>
                        <select name="type" class="form-select">
                            <option value="">{% trans "All Types" %}</option>
                            <option value="ACADEMIC">{% trans "Academic" %}</option>
                            <option value="SPORTS">{% trans "Sports" %}</option>
                            <option value="CULTURAL">{% trans "Cultural" %}</option>
                            <option value="MEETING">{% trans "Meeting" %}</option>
                            <option value="HOLIDAY">{% trans "Holiday" %}</option>
                            <option value="EXAM">{% trans "Examination" %}</option>
                            <option value="PARENT">{% trans "Parent Event" %}</option>
                            <option value="STAFF">{% trans "Staff Event" %}</option>
                            <option value="OTHER">{% trans "Other" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{% trans "Priority" %}</label>
                        <select name="priority" class="form-select">
                            <option value="">{% trans "All Priorities" %}</option>
                            <option value="LOW">{% trans "Low" %}</option>
                            <option value="MEDIUM">{% trans "Medium" %}</option>
                            <option value="HIGH">{% trans "High" %}</option>
                            <option value="URGENT">{% trans "Urgent" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{% trans "Status" %}</label>
                        <select name="status" class="form-select">
                            <option value="">{% trans "All Events" %}</option>
                            <option value="upcoming">{% trans "Upcoming" %}</option>
                            <option value="ongoing">{% trans "Ongoing" %}</option>
                            <option value="past">{% trans "Past" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel me-1"></i>{% trans "Filter" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Navigation Links -->
            <div class="mb-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'school_calendar:calendar' %}">{% trans "Calendar" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Manage Events" %}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- Events List -->
    <div class="row mt-4">
        {% for event in events %}
        <div class="col-lg-6 mb-4">
            <div class="card event-card h-100" style="border-left-color: {{ event.category.color|default:'#007bff' }}">
                <div class="card-header d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="mb-1">
                            <a href="{% url 'school_calendar:event_detail' event.pk %}" class="text-decoration-none">
                                {{ event.title }}
                            </a>
                        </h5>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-secondary">{{ event.get_event_type_display }}</span>
                            <span class="priority-badge priority-{{ event.priority|lower }}">
                                {{ event.get_priority_display }}
                            </span>
                            {% if event.category %}
                            <span class="badge" style="background-color: {{ event.category.color }}">
                                {{ event.category.name }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'school_calendar:event_detail' event.pk %}">
                                <i class="bi bi-eye me-2"></i>{% trans "View Details" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'school_calendar:admin_event_edit' event.pk %}">
                                <i class="bi bi-pencil me-2"></i>{% trans "Edit" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{% url 'school_calendar:admin_event_delete' event.pk %}">
                                <i class="bi bi-trash me-2"></i>{% trans "Delete" %}
                            </a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="event-meta mb-3">
                        <div class="row">
                            <div class="col-6">
                                <i class="bi bi-calendar me-1"></i>
                                {% if event.is_multi_day %}
                                    {{ event.start_date|date:"M j" }} - {{ event.end_date|date:"M j, Y" }}
                                {% else %}
                                    {{ event.start_date|date:"M j, Y" }}
                                {% endif %}
                            </div>
                            <div class="col-6">
                                {% if not event.is_all_day and event.start_time %}
                                <i class="bi bi-clock me-1"></i>
                                {{ event.start_time|time:"g:i A" }}
                                {% if event.end_time %}
                                    - {{ event.end_time|time:"g:i A" }}
                                {% endif %}
                                {% else %}
                                <i class="bi bi-sun me-1"></i>{% trans "All Day" %}
                                {% endif %}
                            </div>
                        </div>
                        {% if event.location %}
                        <div class="mt-2">
                            <i class="bi bi-geo-alt me-1"></i>{{ event.location }}
                        </div>
                        {% endif %}
                    </div>
                    
                    {% if event.description %}
                    <p class="text-muted mb-3">{{ event.description|truncatewords:20 }}</p>
                    {% endif %}
                    
                    <!-- Event Status Indicators -->
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            {% if event.is_upcoming %}
                            <span class="badge bg-success">{% trans "Upcoming" %}</span>
                            {% elif event.is_ongoing %}
                            <span class="badge bg-warning">{% trans "Ongoing" %}</span>
                            {% elif event.is_past %}
                            <span class="badge bg-secondary">{% trans "Past" %}</span>
                            {% endif %}
                            
                            {% if event.requires_rsvp %}
                            <span class="badge bg-info">{% trans "RSVP Required" %}</span>
                            {% endif %}
                            
                            {% if not event.is_public %}
                            <span class="badge bg-warning">{% trans "Private" %}</span>
                            {% endif %}
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{% url 'school_calendar:event_detail' event.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="{% url 'school_calendar:admin_event_edit' event.pk %}" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-pencil"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                {% if event.requires_rsvp %}
                <div class="card-footer bg-light">
                    <small class="text-muted">
                        <i class="bi bi-people me-1"></i>
                        {% trans "RSVP Responses:" %} 
                        <span class="text-success">{{ event.attendees.count }}</span>
                        {% if event.max_attendees %}
                        / {{ event.max_attendees }}
                        {% endif %}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted mb-3"></i>
                <h4 class="text-muted">{% trans "No Events Found" %}</h4>
                <p class="text-muted">{% trans "Start by creating your first school event." %}</p>
                <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>{% trans "Create First Event" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Events pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                            <i class="bi bi-chevron-left"></i> {% trans "Previous" %}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                            {% trans "Next" %} <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
    
    <!-- Quick Actions Footer -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h6 class="mb-3">{% trans "Quick Actions" %}</h6>
                    <div class="btn-group" role="group">
                        <a href="{% url 'school_calendar:calendar' %}" class="btn btn-outline-primary">
                            <i class="bi bi-calendar me-1"></i>{% trans "View Calendar" %}
                        </a>
                        <a href="{% url 'school_calendar:admin_event_create' %}" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-1"></i>{% trans "Create Event" %}
                        </a>
                        <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-info">
                            <i class="bi bi-list me-1"></i>{% trans "Public View" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit filter form on change
    const filterSelects = document.querySelectorAll('.filter-section select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Add hover effects to event cards
    const eventCards = document.querySelectorAll('.event-card');
    eventCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}

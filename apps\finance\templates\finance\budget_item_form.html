{# D:\school_fees_saas_V2\apps\finance\templates\finance\budget_item_form.html #}
{% extends "tenant_base.html" %}

{% load static core_tags %}

{% block title %}{{ view_title|default:"Budget Item Management" }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .budget-form-card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .budget-form-card .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            padding: 2rem 2rem 1.5rem;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .form-floating > .form-control,
        .form-floating > .form-select {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
        }
        .btn-success {
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(40, 167, 69, 0.3);
        }
        .btn-outline-secondary {
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .field-icon {
            color: #28a745;
            margin-right: 0.5rem;
        }
        .form-floating.focused > .form-control,
        .form-floating.focused > .form-select {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        @media (max-width: 768px) {
            .budget-form-card .card-header {
                padding: 1.5rem 1rem 1rem;
            }
            .budget-form-card .card-body {
                padding: 1.5rem 1rem;
            }
            .admin-icon {
                padding: 1rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8 col-xl-7">
            <div class="card budget-form-card">
                <div class="card-header text-white text-center">
                    <i class="bi bi-calculator" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.9;"></i>
                    <h3 class="mb-2 fw-bold">{{ view_title|default:"Budget Item Management" }}</h3>
                    <p class="mb-0 opacity-75">
                        {% if object %}
                            Update budget item details and account linkage
                        {% else %}
                            Create a new budget item for financial planning
                        {% endif %}
                    </p>
                </div>

                <div class="card-body p-4 p-md-5">
                    {% include "partials/_messages.html" %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2 mb-4">
                            {% for error in form.non_field_errors %}
                                {{ error }}{% if not forloop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Budget Item Name Field #}
                        <div class="mb-4 form-floating">
                            {{ form.name }}
                            <label for="{{ form.name.id_for_label }}">
                                <i class="bi bi-tag-fill field-icon"></i>{{ form.name.label }}
                            </label>
                            {% if form.name.help_text %}
                                <div class="help-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Description Field #}
                        <div class="mb-4 form-floating">
                            {{ form.description }}
                            <label for="{{ form.description.id_for_label }}">
                                <i class="bi bi-file-text-fill field-icon"></i>{{ form.description.label }}
                            </label>
                            {% if form.description.help_text %}
                                <div class="help-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Budget Type Field #}
                        <div class="mb-4 form-floating">
                            {{ form.budget_item_type }}
                            <label for="{{ form.budget_item_type.id_for_label }}">
                                <i class="bi bi-diagram-3-fill field-icon"></i>{{ form.budget_item_type.label }}
                            </label>
                            {% if form.budget_item_type.help_text %}
                                <div class="help-text">{{ form.budget_item_type.help_text }}</div>
                            {% endif %}
                            {% if form.budget_item_type.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.budget_item_type.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Linked Chart of Accounts Field #}
                        <div class="mb-5 form-floating">
                            {{ form.linked_coa_account }}
                            <label for="{{ form.linked_coa_account.id_for_label }}">
                                <i class="bi bi-link-45deg field-icon"></i>{{ form.linked_coa_account.label }}
                            </label>
                            {% if form.linked_coa_account.help_text %}
                                <div class="help-text">{{ form.linked_coa_account.help_text }}</div>
                            {% endif %}
                            {% if form.linked_coa_account.errors %}
                                <div class="invalid-feedback d-block mt-1">
                                    {% for error in form.linked_coa_account.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Action Buttons #}
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'finance:budget_item_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}Update Budget Item{% else %}Create Budget Item{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{# Enhanced JavaScript functionality #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add focus effects for better UX
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(function(control) {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });

    // Add loading state to submit button
    const budgetForm = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');

    if (budgetForm && submitBtn) {
        budgetForm.addEventListener('submit', function() {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Re-enable button after 5 seconds in case of form validation errors
            setTimeout(function() {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });
    }

    // Auto-resize textarea
    const textarea = document.querySelector('textarea');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Add character counter for name field
    const nameField = document.querySelector('#floatingBudgetName');
    if (nameField) {
        const maxLength = 100; // Adjust based on your model field max_length
        const counter = document.createElement('small');
        counter.className = 'text-muted mt-1 d-block';
        counter.style.textAlign = 'right';
        nameField.parentElement.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - nameField.value.length;
            counter.textContent = `${nameField.value.length}/${maxLength} characters`;
            counter.style.color = remaining < 10 ? '#dc3545' : '#6c757d';
        }

        nameField.addEventListener('input', updateCounter);
        updateCounter(); // Initialize counter
    }
});
</script>
{% endblock %}
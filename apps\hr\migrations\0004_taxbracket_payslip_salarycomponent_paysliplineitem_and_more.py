# Generated by Django 5.1.9 on 2025-07-06 06:50

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0003_alter_leavetype_options_and_more'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TaxBracket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Bracket Name/Description')),
                ('from_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='From Amount')),
                ('to_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='To Amount')),
                ('rate_percent', models.DecimalField(decimal_places=2, help_text='e.g., 25 for 25%', max_digits=5, verbose_name='Tax Rate (%)')),
                ('deduction_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Deduction Amount')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Tax Bracket',
                'verbose_name_plural': 'Tax Brackets',
                'ordering': ['from_amount'],
            },
        ),
        migrations.CreateModel(
            name='Payslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pay_period_start', models.DateField()),
                ('pay_period_end', models.DateField()),
                ('gross_earnings', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_deductions', models.DecimalField(decimal_places=2, max_digits=12)),
                ('net_pay', models.DecimalField(decimal_places=2, max_digits=12)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('GENERATED', 'Generated'), ('PAID', 'Paid'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('generated_on', models.DateTimeField(auto_now_add=True)),
                ('paid_on', models.DateField(blank=True, null=True)),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payslips', to='schools.staffuser')),
            ],
            options={
                'verbose_name': 'Payslip',
                'verbose_name_plural': 'Payslips',
                'ordering': ['-pay_period_start', 'staff_user__last_name'],
                'unique_together': {('staff_user', 'pay_period_start', 'pay_period_end')},
            },
        ),
        migrations.CreateModel(
            name='SalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Component Name')),
                ('type', models.CharField(choices=[('EARNING', 'Earning'), ('DEDUCTION', 'Deduction')], max_length=10, verbose_name='Component Type')),
                ('is_statutory', models.BooleanField(default=False, help_text='Check if this is a mandatory government/statutory deduction (e.g., Income Tax, Social Security).', verbose_name='Is Statutory')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Salary Component',
                'verbose_name_plural': 'Salary Components',
                'ordering': ['type', 'name'],
                'unique_together': {('name', 'type')},
            },
        ),
        migrations.CreateModel(
            name='PayslipLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('EARNING', 'Earning'), ('DEDUCTION', 'Deduction')], max_length=10)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payslip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='hr.payslip')),
                ('source_component', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.salarycomponent')),
            ],
            options={
                'verbose_name': 'Payslip Line Item',
                'verbose_name_plural': 'Payslip Line Items',
                'ordering': ['type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='StaffSalary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='Basic Salary')),
                ('effective_date', models.DateField(default=django.utils.timezone.now, verbose_name='Effective Date')),
                ('is_active', models.BooleanField(default=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('staff_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='salary_structure', to='schools.staffuser')),
            ],
            options={
                'verbose_name': 'Staff Salary',
                'verbose_name_plural': 'Staff Salaries',
                'ordering': ['-effective_date', 'staff_user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='StaffSalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='Amount')),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='hr.salarycomponent')),
                ('staff_salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='hr.staffsalary')),
            ],
            options={
                'verbose_name': 'Staff Salary Component',
                'verbose_name_plural': 'Staff Salary Components',
                'unique_together': {('staff_salary', 'component')},
            },
        ),
    ]

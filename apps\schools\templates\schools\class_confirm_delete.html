{# D:\school_fees_saas_v2\apps\schools\templates\schools\class_confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static core_tags %}

{% block title %}Confirm Delete Class/Grade{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">Confirm Delete: {{ object.name }}</h4>
                </div>
                <div class="card-body p-4">
                    <p class="lead">Are you sure you want to delete the Class/Grade: <strong>"{{ object.name }}"</strong>?</p>
                    <p class="text-danger"><strong>Warning:</strong> Deleting this class may also affect associated sections, students, fee structures, or other related data. This action might be irreversible depending on database constraints (e.g., PROTECT on foreign keys).</p>

                    <form method="post">
                        {% csrf_token %}
                        <div class="mt-4 d-flex justify-content-end">
                            <a href="{% url 'schools:class_list' %}" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-danger">Yes, Delete Class/Grade</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
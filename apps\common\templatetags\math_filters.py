# D:\school_fees_saas_v2\apps\common\templatetags\math_filters.py

from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()

@register.filter(name='sub')
def subtract(value, arg):
    """
    Subtracts the argument from the value. Prioritizes Decimal for precision.
    Example: {{ value|sub:5 }}
    """
    try:
        # Best case: Both are or can be converted to Decimal
        return Decimal(str(value)) - Decimal(str(arg))
    except (ValueError, TypeError, InvalidOperation):
        try:
            # Fallback for non-decimal types or if one is None
            return (value or 0) - (arg or 0)
        except (ValueError, TypeError):
            # If all else fails, return an empty string to avoid template errors
            return ''


@register.filter(name='mul')
def multiply(value, arg):
    """
    Multiplies the value by the arg.
    Example: {{ value|mul:1.05 }}
    """
    try:
        # Use float for general purpose multiplication
        return float(value) * float(arg)
    except (ValueError, TypeError):
        try:
            return (value or 0) * (arg or 0)
        except (ValueError, TypeError):
            return ''


@register.filter(name='abs')
def absolute_value(value):
    """
    Returns the absolute value of a number. Prioritizes Decimal for precision.
    Example: {{ value|abs }}
    """
    try:
        # Best case: It is or can be converted to Decimal
        return abs(Decimal(str(value)))
    except (ValueError, TypeError, InvalidOperation):
        try:
            # Fallback for non-decimal types or if one is None
            return abs(value or 0)
        except (ValueError, TypeError):
            return ''

# You can also add other useful math filters here if needed.
# For example, a division filter:
@register.filter(name='div')
def divide(value, arg):
    """
    Divides the value by the arg. Handles division by zero.
    Example: {{ value|div:2 }}
    """
    try:
        arg_decimal = Decimal(str(arg))
        if arg_decimal == 0:
            return None # Or return 0, or an error string
        return Decimal(str(value)) / arg_decimal
    except (ValueError, TypeError, InvalidOperation):
        return None # Or return ''
    
    
    


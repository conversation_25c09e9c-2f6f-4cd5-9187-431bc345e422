# apps/common/templatetags/math_filters.py
from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()


# --- ADD THIS NEW FILTER ---
@register.filter(name='mul')
def multiply(value, arg):
    """Multiplies the value by the arg."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        try:
            return (value or 0) * (arg or 0)
        except (ValueError, TypeError):
            return ''
# @register.filter
# def multiply(value, arg):
#     try:
#         return Decimal(str(value)) * Decimal(str(arg))
#     except (TypeError, ValueError, InvalidOperation):
#         return None # Or 0, or raise error


# D:\school_fees_saas_v2\apps\common\templatetags\math_filters.py
from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()

@register.filter(name='sub') # Register the filter with the name 'sub'
def subtract(value, arg):
    """
    Subtracts the argument from the value.
    This is a more robust version that handles Decimals and potential errors.
    """
    try:
        # Try to convert both to Decimal for accurate financial math
        return Decimal(value) - Decimal(arg)
    except (ValueError, TypeError, InvalidOperation):
        try:
            # Fallback for non-decimal types or if one is None
            return (value or 0) - (arg or 0)
        except (ValueError, TypeError):
            # If all else fails, return an empty string to avoid template errors
            return ''
        
# @register.filter
# def subtract(value, arg):
#     try:
#         return Decimal(str(value)) - Decimal(str(arg))
#     except (TypeError, ValueError, InvalidOperation):
#         return None # Or 0
    
    
# D:\school_fees_saas_v2\apps\common\templatetags\math_filters.py
from django import template
from decimal import Decimal, InvalidOperation # Import Decimal and InvalidOperation

register = template.Library()

@register.filter(name='abs')
def absolute_value(value):
    """Returns the absolute value of a number."""
    try:
        # Attempt to convert to Decimal first for precision with monetary values
        if isinstance(value, str):
            try:
                num_value = Decimal(value)
            except InvalidOperation: # Fallback if string is not a valid decimal
                num_value = float(value)
        elif not isinstance(value, (int, float, Decimal)):
            num_value = float(value) # Try to convert other types to float
        else:
            num_value = value
        
        return abs(num_value)
    except (ValueError, TypeError, InvalidOperation):
        # Handle cases where conversion or abs fails gracefully
        # You might want to log this error as well
        return value # Return original value or 0, or an empty string



# D:\school_fees_saas_v2\apps\common\templatetags\math_filters.py
from django import template

register = template.Library()

@register.filter
def sub(value, arg):
    """Subtracts the arg from the value."""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        try:
            # Handle cases where value or arg might be None
            return float(value or 0) - float(arg or 0)
        except (ValueError, TypeError):
            return ''
#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import psycopg
from django.conf import settings

def mark_migration_applied(cur, app_name, migration_name):
    """Mark a migration as applied in the current schema"""
    try:
        # Check if the migration record already exists
        cur.execute('''
            SELECT id FROM django_migrations 
            WHERE app = %s AND name = %s
        ''', (app_name, migration_name))
        
        existing = cur.fetchone()
        
        if existing:
            return f'✅ {app_name}.{migration_name} already marked as applied'
        else:
            # Insert the migration record
            cur.execute('''
                INSERT INTO django_migrations (app, name, applied)
                VALUES (%s, %s, NOW())
            ''', (app_name, migration_name))
            return f'✅ Marked {app_name}.{migration_name} as applied'
            
    except Exception as e:
        return f'❌ Error processing {app_name}.{migration_name}: {e}'

print("Fixing migration conflicts based on actual schema structures...")

db_settings = settings.DATABASES['default']
conn = psycopg.connect(
    host=db_settings['HOST'],
    port=db_settings['PORT'],
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD']
)
conn.autocommit = True
cur = conn.cursor()

# Get all tenant schemas that have schools_schoolprofile table
cur.execute("""
    SELECT DISTINCT table_schema 
    FROM information_schema.tables 
    WHERE table_name = 'schools_schoolprofile'
    AND table_schema NOT IN ('information_schema', 'pg_catalog', 'public')
    ORDER BY table_schema
""")
schemas_with_table = [row[0] for row in cur.fetchall()]

print(f"Found schemas with schools_schoolprofile table: {schemas_with_table}")

for schema in schemas_with_table:
    print(f'\nProcessing schema: {schema}')
    
    # Set the search path to the schema
    cur.execute(f'SET search_path TO "{schema}";')
    
    # Check the structure of the table
    cur.execute("""
        SELECT column_name
        FROM information_schema.key_column_usage 
        WHERE table_schema = %s AND table_name = 'schools_schoolprofile'
        AND constraint_name LIKE '%%pkey%%'
    """, (schema,))
    pk_result = cur.fetchone()
    primary_key = pk_result[0] if pk_result else None
    
    # Check if id column exists
    cur.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'schools_schoolprofile' 
            AND column_name = 'id'
        )
    """, (schema,))
    has_id = cur.fetchone()[0]
    
    # Check if school_id column exists
    cur.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'schools_schoolprofile' 
            AND column_name = 'school_id'
        )
    """, (schema,))
    has_school_id = cur.fetchone()[0]
    
    print(f'  Structure: PK={primary_key}, has_id={has_id}, has_school_id={has_school_id}')
    
    # Determine which migrations to mark as applied
    migrations_to_mark = []
    
    # Always mark these for schemas that already have the table structure
    if schema in ['beta', 'alpha']:
        migrations_to_mark.extend([
            ('fees', '0004_feepermissions_feestructure_total_amount'),
            ('schools', '0006_remove_schoolprofile_school'),
        ])
    
    # If schema has school_id as PK (old structure), mark the id-adding migration as applied
    # to prevent the "multiple primary keys" error
    if primary_key == 'school_id' and not has_id:
        migrations_to_mark.append(('schools', '0003_remove_school_foreign_key'))
        print(f'  ⚠️  Schema {schema} has old structure, marking migration as applied to prevent conflicts')
    
    # If schema already has id column, mark the migration as applied
    if has_id:
        migrations_to_mark.append(('schools', '0003_remove_school_foreign_key'))
    
    # Apply the migration markings
    for app_name, migration_name in migrations_to_mark:
        result = mark_migration_applied(cur, app_name, migration_name)
        print(f'  {result}')

conn.close()
print('\nMigration conflict fix completed!')
print('You can now run: python manage.py migrate')

/* apps/subscriptions/static/subscriptions/css/plans.css */
.plan-card ul.features li {
    font-size: 0.9rem;
    padding: 0.3rem 0;
}
.plan-card .btn-select-plan {
    font-size: 0.95rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.current-plan-highlight {
    border: 3px solid var(--bs-success) !important; /* Bootstrap success color */
    box-shadow: 0 0.5rem 1rem rgba(25,135,84,.35)!important;
}
.plan-card .btn-group .btn {
    border-radius: 0; /* Makes them flush in btn-group */
}
.plan-card .btn-group .btn:first-child {
    border-top-left-radius: .375rem;
    border-bottom-left-radius: .375rem;
}
.plan-card .btn-group .btn:last-child {
    border-top-right-radius: .375rem;
    border-bottom-right-radius: .375rem;
}
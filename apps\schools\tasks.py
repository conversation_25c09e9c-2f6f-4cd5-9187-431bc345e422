# from celery import shared_task
# from django_tenants.utils import tenant_context
# from apps.tenants.models import School
# from .models import SchoolProfile
# import logging
# logger = logging.getLogger(__name__)

# @shared_task
# def create_school_profile_task(tenant_pk):
#     try:
#         tenant = School.objects.get(pk=tenant_pk) # Get tenant from public schema
#         with tenant_context(tenant):
#             profile, created = SchoolProfile.objects.get_or_create(school=tenant)
#             if created:
#                 logger.info(f"CELERY TASK: CREATED SchoolProfile for {tenant.name}")
#     except School.DoesNotExist:
#         logger.error(f"CELERY TASK ERROR: Tenant PK {tenant_pk} not found.")
#     except Exception as e:
#         logger.error(f"CELERY TASK ERROR creating SchoolProfile for tenant PK {tenant_pk}: {e}", exc_info=True)
        
        
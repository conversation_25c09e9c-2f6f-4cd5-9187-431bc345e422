# D:\school_fees_saas_v2\apps\parent_portal\views.py

import logging
from decimal import Decimal

from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login as auth_login, logout as auth_logout, authenticate
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.core.exceptions import PermissionDenied, FieldError
from django.db.models import Sum, F, Q, DecimalField, Exists, OuterRef # Not all used here, but good to have
from django.db.models.functions import Coalesce
from django.http import Http404
from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from django.utils.http import url_has_allowed_host_and_scheme
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView, UpdateView, ListView, DetailView

from apps.common.features import tenant_has_feature_for_parent_portal

from apps.common.mixins import ParentLoginRequiredMixin


from django.shortcuts import render, redirect, get_object_or_404 # Example existing imports
from django.urls import reverse_lazy, reverse
from django.views.generic import TemplateView, ListView, DetailView # Your existing CBV imports
from django.views.generic.base import View # <<< --- ADD THIS IMPORT ---
from django.views.generic.edit import FormView # You used this for SelectInvoicesForPaymentView

# ... other imports like models, forms, mixins, messages, logger, timezone, Decimal ...
from django.http import HttpResponseForbidden, HttpResponse # For other views potentially
from django.db.models import F, Q, Sum #, Coalesce, DecimalField
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.contrib.auth import logout as auth_logout
from django.utils import timezone # If used in this file

# Model Imports (ensure these are correct and comprehensive for all views in this file)
from apps.students.models import Student, ParentUser
from apps.fees.models import Invoice
from apps.schools.models import SchoolProfile
from apps.payments.models import Payment, PaymentAllocation

# Form Imports
from .forms import SelectInvoicesForPaymentForm # Example

# Mixin Imports
from apps.common.mixins import ParentLoginRequiredMixin # Example

# Service Imports
from apps.payments.services import record_parent_payment # Example

import logging
logger = logging.getLogger(__name__)

# Additional imports for parent invoice viewing
from django.http import HttpResponse, Http404
from apps.common.utils import render_to_pdf, PDF_AVAILABLE


# --- Model Imports ---
PARENT_USER_MODEL_IMPORTED = False
STUDENT_MODEL_IMPORTED = False
INVOICE_MODEL_IMPORTED = False
PAYMENT_MODEL_IMPORTED = False
SCHOOL_PROFILE_MODEL_IMPORTED = False

try:
    from apps.students.models import ParentUser # Assuming ParentUser is now in apps.users
    PARENT_USER_MODEL_IMPORTED = True
except ImportError:
    ParentUser = None # type: ignore
    logger.critical("PARENT_PORTAL: CRITICAL - ParentUser model not found (expected in apps.users.models). Parent portal will not function.")

try:
    from apps.students.models import Student
    STUDENT_MODEL_IMPORTED = True
except ImportError:
    Student = None # type: ignore
    logger.critical("PARENT_PORTAL: CRITICAL - Student model not found. Parent portal functionality will be severely limited.")

try:
    from apps.fees.models import Invoice
    INVOICE_MODEL_IMPORTED = True
except ImportError:
    Invoice = None # type: ignore
    logger.warning("PARENT_PORTAL: Invoice model not found. Fee-related information will be unavailable.")

try:
    from apps.payments.models import Payment
    PAYMENT_MODEL_IMPORTED = True
except ImportError:
    Payment = None # type: ignore
    logger.warning("PARENT_PORTAL: Payment model not found. Payment history will be unavailable.")

try:
    from apps.schools.models import SchoolProfile
    SCHOOL_PROFILE_MODEL_IMPORTED = True
except ImportError:
    SchoolProfile = None # type: ignore
    logger.warning("PARENT_PORTAL: SchoolProfile model not found. School-specific details may be unavailable.")

# --- Form Imports ---
try:
    from .forms import ParentLoginForm, ParentProfileUpdateForm
except ImportError:
    ParentLoginForm, ParentProfileUpdateForm = None, None # type: ignore
    logger.critical("PARENT_PORTAL: CRITICAL - ParentLoginForm or ParentProfileUpdateForm not found in parent_portal.forms.")


# --- Constants ---
PARENT_PORTAL_FEATURE_CODE = 'PARENT_PORTAL'

# --- Utility Function for Feature Check ---
def _tenant_has_parent_portal_feature(tenant):
    if not tenant or not hasattr(tenant, 'subscription'): return False
    # Assuming subscriptions.utils.tenant_has_feature exists and works
    try:
        from apps.subscriptions.utils import tenant_has_feature as check_feature
        return check_feature(tenant, PARENT_PORTAL_FEATURE_CODE)
    except ImportError:
        logger.error("PARENT_PORTAL: Could not import tenant_has_feature utility. Defaulting to feature disabled.")
        return False


# --- Custom Mixins for Parent Portal ---
class ParentLoginRequiredMixin(LoginRequiredMixin):
    """ Ensures user is logged in, is a ParentUser, and PARENT_PORTAL feature is active. """
    login_url = reverse_lazy('parent_portal:login') # Centralized login URL

    def dispatch(self, request, *args, **kwargs):
        if not PARENT_USER_MODEL_IMPORTED: # Check if ParentUser model itself loaded
            messages.error(request, _("Parent portal is temporarily unavailable. Please try again later. (Error: M01)"))
            logger.critical("ParentLoginRequiredMixin: ParentUser model not available. Aborting.")
            return redirect(reverse('public_site:home'))

        if not request.user.is_authenticated:
            return self.handle_no_permission() # Let LoginRequiredMixin handle redirect

        # Check if user is ParentUser - be more flexible with type checking
        is_parent_user = False
        if hasattr(request.user, '_meta') and request.user._meta.model_name == 'parentuser':
            is_parent_user = True
        elif isinstance(request.user, ParentUser):
            is_parent_user = True

        if not is_parent_user:
            user_identifier = getattr(request.user, 'email', str(request.user))
            messages.error(request, _("Access Denied. This area is for parent accounts only."))
            logger.warning(f"ParentLoginRequiredMixin: User '{user_identifier}' (type: {type(request.user)}) is not ParentUser. Logging out.")
            auth_logout(request) # Log out incorrect user type
            return redirect(self.login_url) # Redirect to parent login

        if not request.user.is_active:
            messages.error(request, _("Your parent account is inactive. Please contact support."))
            logger.warning(f"ParentLoginRequiredMixin: ParentUser '{request.user.email}' is inactive. Logging out.")
            auth_logout(request)
            return redirect(self.login_url)

        # Tenant and Feature Check
        tenant = getattr(request, 'tenant', None)
        if not tenant:
            logger.error("ParentLoginRequiredMixin: No tenant context found on request. Parent portal functionality may be impaired.")
            messages.error(request, _("School information not found. Please try accessing through your school's portal link."))
            return redirect(reverse('public_site:home')) # Fallback to public site

        if not tenant_has_feature_for_parent_portal(tenant):
            messages.warning(request, _("The Parent Portal is not currently enabled for this school."))
            logger.warning(f"ParentLoginRequiredMixin: PARENT_PORTAL feature disabled for tenant '{tenant.name}'. Access denied for parent '{request.user.email}'.")
            return redirect(reverse('public_site:home')) # Or a "feature_not_available_for_tenant" page
            
        return super().dispatch(request, *args, **kwargs)


class ParentOwnsStudentMixin:
    """ Verifies logged-in parent is linked to student_pk. Sets self.student. """
    def dispatch(self, request, *args, **kwargs):
        # Assumes ParentLoginRequiredMixin has already run and request.user is a valid ParentUser
        student_pk = self.kwargs.get('student_pk')
        if not student_pk:
            logger.error("ParentOwnsStudentMixin: 'student_pk' not found in URL kwargs.")
            raise Http404(_("Student identifier missing."))

        if not STUDENT_MODEL_IMPORTED:
            logger.critical("ParentOwnsStudentMixin: Student model not available. Cannot verify ownership.")
            messages.error(request, _("System error: Student data unavailable (M02)."))
            return redirect(reverse('parent_portal:dashboard'))

        parent = request.user
        try:
            self.student = parent.children.select_related('current_class', 'current_section').get(pk=student_pk, is_active=True)
            logger.info(f"ParentOwnsStudentMixin: Parent {parent.email} authorized for student {self.student.pk}.")
        except Student.DoesNotExist:
            logger.warning(f"ParentOwnsStudentMixin: Parent {parent.email} attempted to access student PK {student_pk} (not linked, inactive, or non-existent).")
            raise Http404(_("Student not found or you do not have permission to view this student's details."))
        except AttributeError: # If parent.children related_name is wrong
            logger.error(f"ParentOwnsStudentMixin: ParentUser {parent.email} does not have 'children' attribute. Check Student.parents M2M related_name.")
            messages.error(request, _("System error: Parent-child link configuration issue (A01)."))
            return redirect(reverse('parent_portal:dashboard')) # Fallback
            
        return super().dispatch(request, *args, **kwargs)



import logging
from decimal import Decimal
# ... (all your other necessary Django imports) ...

logger = logging.getLogger(__name__)

# --- Model Imports (Centralized) ---
try:
    from apps.students.models import ParentUser
    logger.info("PARENT_PORTAL: ParentUser model successfully imported from apps.users.models.")
except ImportError:
    ParentUser = None
    logger.critical("PARENT_PORTAL: CRITICAL - ParentUser model could not be imported (expected apps.users.models).")

try:
    from apps.subscriptions.models import Feature # Needed for the utility function
    logger.info("PARENT_PORTAL: Feature model successfully imported from apps.subscriptions.models.")
except ImportError:
    Feature = None
    logger.warning("PARENT_PORTAL: Feature model not found from apps.subscriptions.models. Feature checking might be affected.")
# ... (other model imports: Student, Invoice, etc.)

# --- Constants ---
PARENT_PORTAL_FEATURE_CODE = 'PARENT_PORTAL'

# --- Utility Function (defined within this module or imported) ---
def tenant_has_feature_for_parent_portal(tenant, feature_code):
    logger.debug(f"[UTIL_tenant_has_feature] Checking feature '{feature_code}' for tenant '{getattr(tenant, 'name', 'N/A_TENANT')}'")
    if not tenant: 
        logger.warning("[UTIL_tenant_has_feature] Tenant object is None.")
        return False
    if not Feature: # Check if Feature model was imported
        logger.error("[UTIL_tenant_has_feature] Feature model not available for checking.")
        return False # Default to false if Feature model isn't available
    if not hasattr(tenant, 'subscription'):
        logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' has no 'subscription' attribute.")
        return False # Assume false if no subscription attribute
        
    try:
        sub = tenant.subscription # Accesses the OneToOneField
        if sub and sub.is_usable and sub.plan:
            has_feat = sub.plan.features.filter(code=feature_code).exists()
            logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}', Plan '{sub.plan.name}', Feature '{feature_code}': {has_feat}")
            return has_feat
        elif sub and sub.is_usable and not sub.plan:
            logger.warning(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' subscription (PK {sub.pk}) is usable but has no plan linked.")
        elif sub and not sub.is_usable:
            logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' subscription is not usable (Status: {sub.status}).")
        elif not sub:
            logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' has no subscription object linked.")
    except Exception as e:
        logger.error(f"[UTIL_tenant_has_feature] Error checking feature '{feature_code}' for tenant '{tenant.name}': {e}", exc_info=True)
    return False # Default to False on any issue

# ... (Your Mixins: ParentUserRequiredMixin, ParentFeatureRequiredMixin, ParentLoginRequiredMixin, ParentOwnsStudentMixin) ...
# Ensure ParentFeatureRequiredMixin also uses tenant_has_feature_for_parent_portal

from django.conf import settings # If PARENT_PORTAL_FEATURE_CODE is in settings
# or from apps.common.constants import PARENT_PORTAL_FEATURE_CODE

from apps.students.models import ParentUser # Adjust path as necessary
from .forms import ParentLoginForm # Adjust path as necessary
from apps.common.features import tenant_has_feature_for_parent_portal # Assuming it's here

from apps.common.features import tenant_has_feature_for_parent_portal

# Other necessary imports:
from django.shortcuts import render, redirect
from django.urls import reverse
from django.contrib.auth import login as auth_login, logout as auth_logout, authenticate
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils.http import url_has_allowed_host_and_scheme
import logging

logger = logging.getLogger(__name__)

# Make sure PARENT_PORTAL_FEATURE_CODE is defined
# Example:
PARENT_PORTAL_FEATURE_CODE = "PARENT_PORTAL_ACCESS"

# --- Authentication Views ---
def parent_login_view(request):
    # ... (Your existing parent_login_view code from the last snippet) ...
    # The line causing the error was:
    # if not tenant_has_feature_for_parent_portal(tenant, PARENT_PORTAL_FEATURE_CODE):
    # This will now work because the function is defined above.
    # ...
    logger.info(f"--- [PARENT_LOGIN_VIEW] START --- User: {getattr(request.user, 'email', request.user)}, Auth: {request.user.is_authenticated}, Type: {type(request.user)}, Method: {request.method}, Path: {request.path}")

    if not ParentUser:
        messages.error(request, _("Parent login system is temporarily unavailable. (Error Code: MDL_PU_NF)"))
        logger.critical("[PARENT_LOGIN_VIEW] CRITICAL: ParentUser model is None. Aborting.")
        return redirect(reverse('public_site:home')) 

    if not ParentLoginForm:
        messages.error(request, _("Parent login system is temporarily unavailable. (Error Code: FRM_PLF_NF)"))
        logger.critical("[PARENT_LOGIN_VIEW] CRITICAL: ParentLoginForm is None. Aborting.")
        return redirect(reverse('public_site:home'))

    tenant = getattr(request, 'tenant', None)
    if not tenant:
        messages.error(request, _("School context (tenant) not found. Parent login is unavailable."))
        logger.error("[PARENT_LOGIN_VIEW] Tenant object not found on request. Aborting.")
        return redirect(reverse('public_site:home'))
    
    logger.debug(f"[PARENT_LOGIN_VIEW] Tenant context: {tenant.name} (Schema: {tenant.schema_name})")

    if request.user.is_authenticated:
        logger.debug(f"[PARENT_LOGIN_VIEW] User '{getattr(request.user, 'email', 'Unknown User')}' is already authenticated.")
        if isinstance(request.user, ParentUser):
            logger.info(f"[PARENT_LOGIN_VIEW] User '{request.user.email}' is already an authenticated ParentUser. Redirecting to dashboard.")
            return redirect(reverse('parent_portal:dashboard'))
        else: 
            logger.info(f"[PARENT_LOGIN_VIEW] User '{getattr(request.user, 'email', 'Unknown User')}' (type: {type(request.user)}) is authenticated but not a ParentUser. Logging out to allow parent login.")
            auth_logout(request)
            messages.info(request, _("You have been logged out from your previous session. Please log in with your parent credentials."))
            return redirect(reverse('parent_portal:login'))
    else:
        logger.debug("[PARENT_LOGIN_VIEW] User is not authenticated. Proceeding to display/process login form.")

    form = ParentLoginForm(request, data=request.POST or None) 

    if request.method == 'POST':
        logger.debug(f"[PARENT_LOGIN_VIEW] POST request received. Form data: {request.POST.dict()}")
        if form.is_valid():
            logger.info(f"[PARENT_LOGIN_VIEW] ParentLoginForm is VALID. Cleaned data: {form.cleaned_data}")
            email = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            
            logger.debug(f"[PARENT_LOGIN_VIEW] Attempting to authenticate user: {email}")
            user = authenticate(request, username=email, password=password)
            
            logger.debug(f"[PARENT_LOGIN_VIEW] authenticate() result: {user} (Type: {type(user)})")
            if user and hasattr(user, 'backend'):
                logger.debug(f"[PARENT_LOGIN_VIEW] Authenticated by backend: {user.backend}")

            if user is not None and isinstance(user, ParentUser) and user.is_active:
                logger.info(f"[PARENT_LOGIN_VIEW] Authentication successful for ParentUser: {user.email}")
                
                # Use the defined utility function
                if not tenant_has_feature_for_parent_portal(tenant): # This line should now work
                    messages.error(request, _("Parent Portal access is not currently enabled for this school. Please contact administration if you believe this is an error."))
                    logger.warning(f"[PARENT_LOGIN_VIEW] Login attempt by ParentUser {email} for tenant '{tenant.name}', but PARENT_PORTAL feature is DISABLED. Login aborted.")
                else:
                    logger.info(f"[PARENT_LOGIN_VIEW] PARENT_PORTAL feature ENABLED for tenant '{tenant.name}'. Proceeding with login for {user.email}.")
                    auth_login(request, user) 
                    messages.success(request, _(f"Welcome back, {user.get_full_name() or user.email}!"))
                    logger.info(f"ParentLoginView: ParentUser '{user.email}' LOGGED IN successfully for tenant '{tenant.name}'.")
                    
                    next_url_param = request.POST.get('next') or request.GET.get('next')
                    default_redirect_url = reverse('parent_portal:dashboard')
                    safe_redirect_url = default_redirect_url

                    if next_url_param:
                        logger.debug(f"[PARENT_LOGIN_VIEW] 'next' URL parameter found: {next_url_param}")
                        if url_has_allowed_host_and_scheme(next_url_param, allowed_hosts={request.get_host()}, require_https=request.is_secure()):
                            safe_redirect_url = next_url_param
                            logger.debug(f"[PARENT_LOGIN_VIEW] Redirecting to safe 'next' URL: {safe_redirect_url}")
                        else:
                            messages.warning(request, _("The redirect address after login was not recognized as safe."))
                            logger.warning(f"[PARENT_LOGIN_VIEW] Unsafe 'next' URL '{next_url_param}' provided. Using default redirect.")
                    else:
                        logger.debug(f"[PARENT_LOGIN_VIEW] No 'next' URL parameter. Using default redirect: {safe_redirect_url}")
                    
                    return redirect(safe_redirect_url)
            else: 
                if user is None: logger.warning(f"[PARENT_LOGIN_VIEW] Authentication FAILED for '{email}'. authenticate() returned None.")
                elif not isinstance(user, ParentUser): logger.warning(f"[PARENT_LOGIN_VIEW] User '{email}' authenticated but IS NOT a ParentUser (Type: {type(user)}). Denying parent portal login.")
                elif not user.is_active: logger.warning(f"[PARENT_LOGIN_VIEW] ParentUser '{email}' authenticated but IS NOT ACTIVE. Denying login.")
                messages.error(request, _("Invalid parent credentials, account type, or account is inactive."))
        
        elif request.method == 'POST' and not form.is_valid():
            logger.warning(f"[PARENT_LOGIN_VIEW] ParentLoginForm submitted but is INVALID. Errors: {form.errors.as_json()}")
            messages.error(request, _("Please correct the errors highlighted in the login form."))
    
    context = {
        'form': form, 
        'tenant_name': tenant.name, 
        'view_title': _("Parent Portal Login"),
        'next': request.GET.get('next', '')
    }
    logger.debug(f"[PARENT_LOGIN_VIEW] Rendering login form. Next URL: {context['next']}")
    return render(request, 'parent_portal/login.html', context)


def parent_logout_view(request):
    parent_name = getattr(request.user, 'first_name', '') # Get name before logout
    auth_logout(request)
    messages.info(request, _(f"Goodbye {parent_name}, you have been successfully logged out.") if parent_name else _("You have been successfully logged out."))
    return redirect(reverse('parent_portal:login'))



import logging
from decimal import Decimal

from django.contrib import messages
from django.contrib.auth import logout as auth_logout
# from django.contrib.auth.mixins import LoginRequiredMixin # Using custom ParentLoginRequiredMixin
from django.db.models import Sum, F, Q, DecimalField
from django.db.models.functions import Coalesce
from django.shortcuts import redirect, reverse, get_object_or_404
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView, ListView, DetailView
from django.http import Http404


logger = logging.getLogger(__name__)

# --- Model Imports & Availability Flags ---
# These flags help manage optional model dependencies gracefully.

PARENT_USER_MODEL_IMPORTED = False
STUDENT_MODEL_IMPORTED = False
try:
    from apps.students.models import ParentUser, Student
    PARENT_USER_MODEL_IMPORTED = True
    STUDENT_MODEL_IMPORTED = True
    logger.info("ParentViews: ParentUser and Student models successfully imported from apps.students.models.")
except ImportError:
    ParentUser = None
    Student = None
    logger.critical("ParentViews: CRITICAL - Failed to import ParentUser or Student model from apps.students.models.")

INVOICE_MODEL_IMPORTED = False
try:
    from apps.fees.models import Invoice
    INVOICE_MODEL_IMPORTED = True
    logger.info("ParentViews: Invoice model successfully imported from apps.fees.models.")
except ImportError:
    Invoice = None
    logger.error("ParentViews: FAILED to import Invoice model from apps.fees.models.")

SCHOOL_PROFILE_MODEL_IMPORTED = False
try:
    from apps.schools.models import SchoolProfile
    SCHOOL_PROFILE_MODEL_IMPORTED = True
    logger.info("ParentViews: SchoolProfile model successfully imported from apps.schools.models.")
except ImportError:
    SchoolProfile = None
    logger.error("ParentViews: FAILED to import SchoolProfile model from apps.schools.models.")


# --- Feature Check Import ---
# Assuming this function correctly checks based on Subscription -> Plan -> Feature
try:
    from apps.common.features import tenant_has_feature_for_parent_portal
    FEATURES_MODULE_IMPORTED = True
    logger.info("ParentViews: Feature checking module successfully imported.")
except ImportError:
    def tenant_has_feature_for_parent_portal(tenant): # Fallback
        logger.error("ParentViews: FAILED to import tenant_has_feature_for_parent_portal. Defaulting to False.")
        return False
    FEATURES_MODULE_IMPORTED = False


# --- Custom Mixins for Parent Portal ---
# Using the refined ParentLoginRequiredMixin from apps.common.mixins
from apps.common.mixins import ParentLoginRequiredMixin


class ParentOwnsStudentMixin:
    """
    Ensures the logged-in parent is linked to the student specified by pk_url_kwarg.
    Sets self.student on the view instance if successful.
    Must be used after ParentLoginRequiredMixin.
    """
    student_model = Student # Use the imported Student model
    student_lookup_kwarg = 'student_pk'
    student_context_name = 'student' # Name for the student in context

    def dispatch(self, request, *args, **kwargs):
        if not STUDENT_MODEL_IMPORTED or self.student_model is None:
            logger.error("ParentOwnsStudentMixin: Student model not available.")
            messages.error(request, _("Student information is currently unavailable."))
            return redirect(reverse_lazy('parent_portal:dashboard'))

        # ParentLoginRequiredMixin already ensures request.user is an authenticated ParentUser
        parent = request.user
        student_pk = self.kwargs.get(self.student_lookup_kwarg)

        if not student_pk:
            logger.warning(f"ParentOwnsStudentMixin: No '{self.student_lookup_kwarg}' found in URL kwargs for parent {parent.email}.")
            messages.error(request, _("Student identifier missing from request."))
            return redirect(reverse_lazy('parent_portal:dashboard'))

        try:
            # Assuming ParentUser has a 'children' M2M field to Student
            # This will raise Student.DoesNotExist if not found or not linked
            self.student = parent.children.get(pk=student_pk, is_active=True)
            logger.debug(f"ParentOwnsStudentMixin: Verified parent {parent.email} owns student {self.student.pk} ({self.student.get_full_name()}).")
        except self.student_model.DoesNotExist:
            logger.warning(f"ParentOwnsStudentMixin: Student PK {student_pk} not found, not active, or not linked to parent {parent.email}.")
            messages.error(request, _("The requested student record was not found or you do not have permission to view it."))
            return redirect(reverse_lazy('parent_portal:my_children_list')) # Or dashboard
        except AttributeError: # If parent.children does not exist
            logger.error(f"ParentOwnsStudentMixin: ParentUser model for {parent.email} does not have a 'children' attribute. Check M2M relationship from ParentUser to Student.")
            messages.error(request, _("System error: Could not verify student linkage."))
            return redirect(reverse_lazy('parent_portal:dashboard'))
        except Exception as e:
            logger.error(f"ParentOwnsStudentMixin: Unexpected error verifying student ownership for parent {parent.email}, student_pk {student_pk}: {e}", exc_info=True)
            messages.error(request, _("An unexpected error occurred."))
            return redirect(reverse_lazy('parent_portal:dashboard'))
            
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if hasattr(self, 'student'): # Ensure self.student was set by dispatch
            context[self.student_context_name] = self.student
        return context



# # ... (ensure these imports are present at the top of your file) ...
from django.views.generic import TemplateView
from django.db.models import Sum, F, Q, DecimalField
from django.db.models.functions import Coalesce
from decimal import Decimal
from apps.common.mixins import ParentLoginRequiredMixin
from apps.students.models import Student
from apps.fees.models import Invoice
from apps.announcements.models import Announcement
from apps.schools.models import SchoolProfile

class ParentDashboardView(ParentLoginRequiredMixin, TemplateView):
    template_name = 'parent_portal/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        parent = self.request.user 
        
        context['view_title'] = _("My Dashboard")
        context['parent'] = parent
        
        # Get linked students
        linked_students_qs = Student.objects.none()
        if hasattr(parent, 'children'):
            linked_students_qs = parent.children.filter(is_active=True).select_related(
                'current_class', 'current_section'
            ).order_by('first_name')
        context['linked_students'] = linked_students_qs
        
        # Initialize context variables
        total_outstanding = Decimal('0.00')
        recent_invoices = []
        children_invoice_summary = []

        if linked_students_qs.exists():
            student_pks = linked_students_qs.values_list('pk', flat=True)
            
            # Define correct outstanding statuses
            outstanding_statuses = [
                Invoice.InvoiceStatus.SENT,
                Invoice.InvoiceStatus.PARTIALLY_PAID,
                Invoice.InvoiceStatus.OVERDUE
            ]

            # Fetch all outstanding invoices for all children in ONE query
            all_outstanding_invoices = Invoice.objects.filter(
                student_id__in=student_pks,
                status__in=outstanding_statuses
            ).select_related('student', 'academic_year') # Add any other related fields needed

            # --- THIS IS THE KEY FIX ---
            # Calculate the grand total outstanding using the correct database fields
            grand_total_aggregation = all_outstanding_invoices.aggregate(
                total=Coalesce(
                    Sum(
                        F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')
                    ), 
                    Decimal('0.00'),
                    output_field=DecimalField()
                )
            )
            total_outstanding = grand_total_aggregation.get('total', Decimal('0.00'))
            # --- END OF KEY FIX ---

            # Get recent invoices by sorting the list we already have
            
            # recent_invoices = []
            # if total_outstanding > 0:
            #     student_pks = [student.pk for student in children_with_fees]
            #     recent_invoices = Invoice.objects.filter(
            #         student_id__in=student_pks,
            #         status__in=outstanding_statuses
            #     ).select_related('student').order_by('-due_date')[:5]
            
            # context['recent_invoices'] = recent_invoices
        
            recent_invoices = sorted(
                list(all_outstanding_invoices), 
                key=lambda inv: inv.due_date, 
                reverse=True
            )[:5]

            # Build the per-child summary without extra DB hits
            student_totals = {student.id: {'balance': Decimal('0.00'), 'count': 0, 'latest_invoice': None} for student in linked_students_qs}
            for inv in all_outstanding_invoices:
                balance = inv.balance_due # Use the property for this in-memory calculation
                if balance > 0:
                    if inv.student_id in student_totals:
                        student_totals[inv.student_id]['balance'] += balance
                        student_totals[inv.student_id]['count'] += 1
                        # Track the latest invoice (by due date)
                        if (student_totals[inv.student_id]['latest_invoice'] is None or 
                            inv.due_date > student_totals[inv.student_id]['latest_invoice'].due_date):
                            student_totals[inv.student_id]['latest_invoice'] = inv
            
            for student in linked_students_qs:
                summary_data = student_totals.get(student.id)
                if summary_data and summary_data['balance'] > 0:
                    children_invoice_summary.append({
                        'student': student,
                        'outstanding_amount': summary_data['balance'],
                        'invoice_count': summary_data['count'],
                        'latest_invoice': summary_data['latest_invoice']
                    })
        
        # Add payment history context data
        recent_payments = []
        total_payments_made = Decimal('0.00')
        if linked_students_qs.exists():
            # Get recent payments for all children
            all_payments = Payment.objects.filter(
                student_id__in=student_pks
            ).select_related('student', 'payment_method', 'academic_year').order_by('-payment_date')

            recent_payments = all_payments[:10]  # Last 10 payments
            total_payments_made = all_payments.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00'), output_field=DecimalField())
            )['total']

        # Pass correctly calculated data to the template
        context['total_outstanding_fees'] = total_outstanding
        context['recent_invoices'] = recent_invoices
        context['children_invoice_summary'] = children_invoice_summary
        context['recent_payments'] = recent_payments
        context['total_payments_made'] = total_payments_made

        # Your other context logic is fine
        try:
            context['announcements'] = Announcement.get_parent_announcements(limit=3)
        except Exception:
            context['announcements'] = []
        context['school_profile'] = SchoolProfile.objects.first()

        logger.info(f"ParentDashboard: Final context prepared for parent {parent.email}. Outstanding: {total_outstanding}, Payments: {total_payments_made}")
        return context
    
    
# class ParentDashboardView(ParentLoginRequiredMixin, TemplateView):
#     template_name = 'parent_portal/dashboard.html'

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         parent = self.request.user 
        
#         context['parent'] = parent
        
#         context['view_title'] = _("My Dashboard")
        
#         # Import logging at the top
#         import logging
#         logger = logging.getLogger(__name__)

#         # Get announcements for parents
#         try:
#             from apps.announcements.models import Announcement
#             parent_announcements = Announcement.get_parent_announcements(limit=5)
#             context['announcements'] = parent_announcements
#         except (ImportError, Exception) as e:
#             # Log the error and continue with empty announcements
#             logger.warning(f"Could not load parent announcements: {e}")
#             context['announcements'] = []
        
#         linked_students_qs = Student.objects.none() 
#         if STUDENT_MODEL_IMPORTED and Student and hasattr(parent, 'children'):
#             try:
#                 linked_students_qs = parent.children.filter(is_active=True).select_related(
#                 'current_class', 'current_section'
#                 ).order_by('first_name', 'last_name')
#                 logger.debug(f"ParentDashboard: Found {linked_students_qs.count()} active students for parent {parent.email}")
#             except Exception as e:
#                 logger.error(f"ParentDashboard: Error fetching linked students for parent {parent.email}: {e}", exc_info=True)
#         context['linked_students'] = linked_students_qs
        
#         total_outstanding = Decimal('0.00')
#         recent_invoices = []
#         children_invoice_summary = []

#         if INVOICE_MODEL_IMPORTED and Invoice and linked_students_qs.exists():
#             student_pks = list(linked_students_qs.values_list('pk', flat=True))
#             if student_pks:
#                 try:
#                     outstanding_statuses = [
#                     Invoice.InvoiceStatus.DRAFT,
#                     Invoice.InvoiceStatus.SENT,
#                     Invoice.InvoiceStatus.PARTIALLY_PAID,
#                     Invoice.InvoiceStatus.OVERDUE
#                 ]

#                     # Get outstanding invoices with details
#                     outstanding_invoices = Invoice.objects.filter(
#                         student_id__in=student_pks,
#                         status__in=outstanding_statuses
#                     ).select_related('student', 'academic_year', 'term').order_by('-issue_date')

#                     # Calculate total outstanding using actual database fields
#                     # balance_due = net_billable_amount - amount_paid
#                     # net_billable_amount = subtotal_amount - total_concession_amount
#                     outstanding_aggregation = outstanding_invoices.aggregate(
#                         calculated_total_due=Coalesce(
#                             Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
#                             Decimal('0.00'),
#                             output_field=DecimalField()
#                         )
#                     )
#                     total_outstanding = outstanding_aggregation.get('calculated_total_due', Decimal('0.00'))

#                     # Get recent invoices (last 5)
#                     recent_invoices = list(outstanding_invoices[:5])

#                     # Create summary per child
#                     for student in linked_students_qs:
#                         student_invoices = outstanding_invoices.filter(student=student)
#                         student_total = student_invoices.aggregate(
#                             total=Coalesce(
#                                 Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
#                                 Decimal('0.00')
#                             )
#                         )['total']

#                         children_invoice_summary.append({
#                             'student': student,
#                             'outstanding_amount': student_total,
#                             'invoice_count': student_invoices.count(),
#                             'latest_invoice': student_invoices.first() if student_invoices.exists() else None
#                         })

#                 except AttributeError:
#                     logger.error(f"ParentDashboard: AttributeError accessing Invoice.InvoiceStatus or missing Invoice field.")
#                 except Exception as e:
#                     logger.error(f"ParentDashboard: Unexpected error calculating outstanding fees: {e}", exc_info=True)

#         context['total_outstanding'] = total_outstanding
#         context['total_outstanding_fees'] = total_outstanding  # For template compatibility
#         context['recent_invoices'] = recent_invoices
#         context['children_invoice_summary'] = children_invoice_summary
        
#         school_profile_instance = None
#         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
#             try:
#                 school_profile_instance = SchoolProfile.objects.first()
#             except Exception as e:
#                 logger.error(f"ParentDashboard: Error fetching SchoolProfile: {e}", exc_info=True)
#         context['school_profile'] = school_profile_instance

#         # Add tenant features context for payment buttons
#         try:
#             from apps.tenants.models import TenantFeatures
#             tenant_features = TenantFeatures.objects.filter(tenant=self.request.tenant).first()
#             context['tenant_features'] = tenant_features or TenantFeatures()  # Default empty features
#         except (ImportError, Exception) as e:
#             logger.warning(f"ParentDashboard: Could not load tenant features: {e}")
#             # Create a mock object with default values
#             class MockTenantFeatures:
#                 ONLINE_PAYMENTS = True  # Default to enabled
#             context['tenant_features'] = MockTenantFeatures()

# # # #         logger.info(f"ParentDashboard: Context prepared for parent {parent.email}. Students: {linked_students_qs.count()}, Outstanding: {total_outstanding}")
# # #         return context

class ParentProfileDisplayView(ParentLoginRequiredMixin, DetailView):
    model = ParentUser # Handled by ParentLoginRequiredMixin to be ParentUser
    template_name = 'parent_portal/profile_display.html'
    context_object_name = 'parent_profile'

    def get_object(self, queryset=None): return self.request.user
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("My Profile")
        return context

class ParentProfileUpdateView(ParentLoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = ParentUser
    form_class = ParentProfileUpdateForm
    template_name = 'parent_portal/profile_form.html'
    success_url = reverse_lazy('parent_portal:profile_display')
    success_message = _("Your profile has been updated successfully.")

    def get_object(self, queryset=None): return self.request.user
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Edit My Profile")
        return context
    def form_invalid(self, form): # Add user feedback for invalid form
        messages.error(self.request, _("Please correct the errors highlighted below."))
        return super().form_invalid(form)


from django.views.generic import ListView # Assuming it's a ListView

from apps.common.mixins import ParentLoginRequiredMixin
    

class ParentStudentDetailView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, TemplateView):
    template_name = 'parent_portal/student_detail_dashboard.html' # As per your view
    # ParentOwnsStudentMixin sets self.student and adds it to context as 'student' (if student_context_name is 'student')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs) # This will include 'student' from ParentOwnsStudentMixin
        student = context.get('student') # Or self.student, but safer from context if mixin guarantees it
        
        if not student: # Should be caught by ParentOwnsStudentMixin, but good check
            logger.error(f"ParentStudentDetailView: Student object not found in context or self for parent {self.request.user.email}")
            raise Http404("Student not found or access denied.")

        context['view_title'] = _(f"Overview for {student.get_full_name()}")
        # 'student' is already in context thanks to ParentOwnsStudentMixin.get_context_data and super() call
        
        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
            try:
                context['school_profile'] = SchoolProfile.objects.first()
            except Exception:
                context['school_profile'] = None
        return context


class ParentStudentDetailView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, TemplateView):
    template_name = 'parent_portal/student_detail_dashboard.html' # As per your view
    # ParentOwnsStudentMixin sets self.student and adds it to context as 'student' (if student_context_name is 'student')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs) # This will include 'student' from ParentOwnsStudentMixin
        student = context.get('student') # Or self.student, but safer from context if mixin guarantees it
        
        if not student: # Should be caught by ParentOwnsStudentMixin, but good check
            logger.error(f"ParentStudentDetailView: Student object not found in context or self for parent {self.request.user.email}")
            raise Http404("Student not found or access denied.")

        context['view_title'] = _(f"Overview for {student.get_full_name()}")
        # 'student' is already in context thanks to ParentOwnsStudentMixin.get_context_data and super() call
        
        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
            try:
                context['school_profile'] = SchoolProfile.objects.first()
            except Exception:
                context['school_profile'] = None
        return context


# D:\school_fees_saas_v2\apps\parent_portal\views.py

# ... (ensure these imports are at the top) ...
from django.views.generic import ListView
from apps.common.mixins import ParentLoginRequiredMixin, ParentOwnsStudentMixin
from apps.fees.models import Invoice

class ParentStudentFeesView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, ListView):
    """
    Displays the full invoice history for a single, specific student.
    """
    model = Invoice
    template_name = 'parent_portal/student_fees.html'
    context_object_name = 'student_invoices'
    paginate_by = 15
    # The ParentOwnsStudentMixin will run first. It verifies the parent owns the
    # student and sets `self.student` on the view instance for us to use.

    def get_queryset(self):
        """
        Fetches all relevant invoices for the specific student verified by the ParentOwnsStudentMixin.
        Includes DRAFT invoices with amounts > 0 so parents can see prepared invoices.
        """
        # self.student is guaranteed to be set and correct by ParentOwnsStudentMixin
        logger.info(f"Fetching invoice history for student '{self.student.get_full_name()}' (PK: {self.student.pk}).")

        from django.db.models import Q

        return Invoice.objects.filter(
            student=self.student
        ).exclude(
            status__in=[Invoice.InvoiceStatus.DRAFT, Invoice.InvoiceStatus.VOID, Invoice.InvoiceStatus.CANCELLED]
        ).order_by('-issue_date')
        
        # return Invoice.objects.filter(
        #     student=self.student
        # ).filter(
        #     # Include non-draft invoices OR draft invoices with actual amounts
        #     Q(status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PENDING,
        #                 Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE,
        #                 Invoice.InvoiceStatus.PAID]) |
        #     Q(status=Invoice.InvoiceStatus.DRAFT, subtotal_amount__gt=0)
        # ).exclude(
        #     # Still exclude void and cancelled invoices
        #     status__in=[Invoice.InvoiceStatus.VOID, Invoice.InvoiceStatus.CANCELLED]
        # ).order_by('-issue_date')

    def get_context_data(self, **kwargs):
        """
        Adds extra context to the template, including the total outstanding
        balance for this specific student.
        """
        context = super().get_context_data(**kwargs)
        # self.student is available from the mixin
        student = self.student
        
        context['view_title'] = _(f"Fee Details for {student.get_full_name()}")
        
        # --- THIS IS THE KEY FIX FOR THE "0.00" on this page ---
        # We call the helper method on the student model to get their balance.
        # This performs a fresh, accurate calculation every time the page is loaded.
        try:
            context['total_student_outstanding'] = student.get_outstanding_balance()
            logger.info(f"Calculated outstanding balance for student {student.pk}: {context['total_student_outstanding']}")
        except Exception as e:
            logger.error(f"Error calculating outstanding balance for student {student.pk}: {e}", exc_info=True)
            context['total_student_outstanding'] = Decimal('0.00')
        
        return context

# class ParentStudentFeesView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, ListView):
#     model = Invoice
#     template_name = 'parent_portal/student_fees.html'
#     context_object_name = 'student_invoices'
#     paginate_by = 10
#     # ParentOwnsStudentMixin sets self.student and adds 'student' to context

#     def get_queryset(self):
#         if not INVOICE_MODEL_IMPORTED or self.model is None:
#             return self.model.objects.none() if self.model else []
        
#         # self.student is set by ParentOwnsStudentMixin's dispatch
#         if not hasattr(self, 'student') or not self.student:
#             logger.warning(f"ParentStudentFeesView: self.student not set by ParentOwnsStudentMixin for parent {self.request.user.email}")
#             return self.model.objects.none() if self.model else []
            
#         return self.model.objects.filter(student=self.student).select_related('academic_year', 'term').order_by('-issue_date', '-created_at')

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs) # Includes 'student' from ParentOwnsStudentMixin
#         student = context.get('student') # Or self.student

#         if not student:
#             logger.error(f"ParentStudentFeesView: Student object not found in context or self for parent {self.request.user.email}")
#             # ListView might still try to render without it, but it's an issue.
#             # Or raise Http404 here.
#             context['view_title'] = _("Fee History Error")
#             context['total_student_outstanding'] = Decimal('0.00')
#             if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
#                 context['school_profile'] = SchoolProfile.objects.first()
#             return context

#         context['view_title'] = _(f"Fee History for {student.get_full_name()}")
        
#         total_outstanding = Decimal('0.00')
#         if INVOICE_MODEL_IMPORTED and Invoice:
#             # Aggregate on all invoices for this student, not just the paginated list (self.object_list)
#             all_student_invoices = Invoice.objects.filter(student=student)
#             try:
#                 from apps.fees.models import InvoiceStatus
#                 outstanding_statuses = [
#                     InvoiceStatus.SENT,
#                     InvoiceStatus.PARTIALLY_PAID,
#                     InvoiceStatus.OVERDUE
#                 ]
#                 # Use actual database fields for calculation
#                 outstanding_agg = all_student_invoices.filter(
#                     status__in=outstanding_statuses
#                 ).aggregate(
#                     total=Coalesce(
#                         Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
#                         Decimal('0.00'),
#                         output_field=DecimalField()
#                     )
#                 )
#                 total_outstanding = outstanding_agg.get('total', Decimal('0.00'))
#             except AttributeError:
#                 logger.error(f"ParentStudentFeesView: AttributeError accessing Invoice.InvoiceStatus or missing Invoice field.")
#             except Exception as e:
#                 logger.error(f"ParentStudentFeesView: Error calculating outstanding for student {student.pk}: {e}", exc_info=True)

#         context['total_student_outstanding'] = student.get_outstanding_balance()
        
#         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
#             try:
#                 context['school_profile'] = SchoolProfile.objects.first()
#             except Exception:
#                 context['school_profile'] = None
#         return context



class ParentStudentPaymentHistoryView(ParentLoginRequiredMixin, ListView):
    model = Payment
    template_name = 'parent_portal/student_payment_history.html'
    context_object_name = 'payment_list'
    paginate_by = 15

    def get_queryset(self):
        print("\n--- [DEBUG] PARENT STUDENT PAYMENT HISTORY VIEW: get_queryset() CALLED ---")

        # Get the student PK from the URL
        student_pk = self.kwargs.get('student_pk')
        if not student_pk:
            print("--- [DEBUG] ERROR: student_pk not found in URL kwargs. Returning empty queryset.")
            return Payment.objects.none()
        print(f"--- [DEBUG] student_pk from URL: {student_pk}")

        # Get the logged-in parent
        parent = self.request.user
        print(f"--- [DEBUG] Logged-in parent: {parent.email}")

        # Try to get the student object
        try:
            self.student = get_object_or_404(
                Student, 
                pk=student_pk,
                parents=parent
            )
            print(f"--- [DEBUG] Found Student: {self.student.get_full_name()}")
        except Exception as e:
            print(f"--- [DEBUG] ERROR in get_object_or_404: {e}. Could not find student for this parent. Returning empty queryset.")
            self.student = None # Ensure self.student is None on failure
            return Payment.objects.none()

        # Check the payment status enum/string
        # Let's assume you have a PaymentStatus class on the Payment model
        try:
            completed_status = Payment.PaymentStatus.COMPLETED
            print(f"--- [DEBUG] Using payment status for filtering: '{completed_status}'")
        except AttributeError:
            print("--- [DEBUG] WARNING: Payment.PaymentStatus.COMPLETED not found. Falling back to string 'COMPLETED'.")
            completed_status = 'COMPLETED'
        
        # Build the final queryset
        queryset = Payment.objects.filter(
            student=self.student,
            status=completed_status
        ).select_related(
            'payment_method',
            'processed_by_staff'
        ).prefetch_related(
            'allocations__invoice'
        ).order_by('-payment_date')
        
        print(f"--- [DEBUG] FINAL QUERYSET COUNT: {queryset.count()}")
        print("--- [DEBUG] END OF get_queryset() ---\n")
        return queryset

    def get_context_data(self, **kwargs):
        print("\n--- [DEBUG] PARENT STUDENT PAYMENT HISTORY VIEW: get_context_data() CALLED ---")
        context = super().get_context_data(**kwargs)
        
        # self.student should have been set by get_queryset
        context['student'] = getattr(self, 'student', None)
        
        if context['student']:
            context['view_title'] = _(f"Payment History for {self.student.get_full_name()}")
            print(f"--- [DEBUG] Context['student'] is: {context['student'].get_full_name()}")
        else:
            context['view_title'] = _("Payment History")
            print("--- [DEBUG] Context['student'] is None.")
            
        print(f"--- [DEBUG] Final context keys being sent to template: {list(context.keys())}")
        print("--- [DEBUG] END OF get_context_data() ---\n")
        return context


# class ParentStudentPaymentHistoryView(ParentLoginRequiredMixin, ListView):
#     """
#     Displays a paginated list of all completed payments for a specific student,
#     ensuring the student belongs to the logged-in parent.
#     """
#     model = Payment
#     template_name = 'parent_portal/student_payment_history.html'
#     context_object_name = 'payment_list' # The template will loop through 'payment_list'
#     paginate_by = 15 # Show 15 payments per page

#     def get_queryset(self):
#         """
#         Fetches payments for a specific student. This method is called by the ListView.
#         """
#         # First, safely get the Student object, ensuring it belongs to the logged-in parent.
#         # This is a crucial security check.
#         # self.kwargs['student_pk'] comes from the URL pattern <int:student_pk>
#         try:
#             self.student = get_object_or_404(
#                 Student, 
#                 pk=self.kwargs['student_pk'],
#                 parents=self.request.user # Filter to ensure student belongs to this parent
#             )
#         except (KeyError, ValueError):
#             logger.error(f"ParentStudentPaymentHistoryView: Invalid or missing student_pk in URL.")
#             return Payment.objects.none() # Return an empty queryset if URL is malformed

#         # Now, filter the Payment model for this specific student's completed payments.
        
#         queryset = Payment.objects.filter(
#             student=self.student,
#             status=Payment.PaymentStatus.COMPLETED
#         ).select_related(
#             'payment_method',
#             'processed_by_staff'
#         ).prefetch_related(
#             'allocations__invoice'  # <<< ADD THIS LINE FOR EFFICIENCY
#         ).order_by('-payment_date')
    
#         # queryset = Payment.objects.filter(
#         #     student=self.student,
#         #     status=Payment.PaymentStatus.COMPLETED # Use the enum from the Payment model
#         # ).select_related(
#         #     'payment_method',   # To show the payment method name without extra queries
#         #     'processed_by_staff'  # To show staff name if available
#         # ).order_by('-payment_date') # Show most recent payments first
        
#         logger.info(f"ParentStudentPaymentHistoryView: Found {queryset.count()} completed payments for student '{self.student.get_full_name()}' (PK: {self.student.pk}).")
#         return queryset

#     def get_context_data(self, **kwargs):
#         """
#         Adds the specific student and a clear view title to the template context.
#         """
#         context = super().get_context_data(**kwargs)
#         # self.student was set in get_queryset, so it's safely available here.
#         # Add it to the context so the template can display the student's name, etc.
#         context['student'] = getattr(self, 'student', None)
#         if context['student']:
#             context['view_title'] = _(f"Payment History for {self.student.get_full_name()}")
#         else:
#             context['view_title'] = _("Payment History")
            
#         return context
    
    

# class ParentStudentPaymentHistoryView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, ListView): # For listing payments
#     model = Payment
#     template_name = 'parent_portal/student_payment_history_list.html'
#     context_object_name = 'student_payments'
#     paginate_by = 10
#     # ParentOwnsStudentMixin sets self.student

#     def get_queryset(self):
#         if not PAYMENT_MODEL_IMPORTED: return Payment.objects.none()
#         # Payments directly for student OR payments allocated to invoices for this student
#         return Payment.objects.filter(
#             Q(student=self.student) | Q(allocations__invoice__student=self.student)
#         ).select_related('payment_method', 'academic_year').prefetch_related('allocations__invoice').distinct().order_by('-payment_date', '-created_at')

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         student = self.student
#         context['view_title'] = _(f"Payment History for {student.get_full_name()}")
#         context['student'] = student
#         if SCHOOL_PROFILE_MODEL_IMPORTED and hasattr(self.request, 'tenant'):
#             try: context['school_profile'] = SchoolProfile.objects.get()
#             except: context['school_profile'] = None
#         return context
    
    
class ParentAllChildrenPaymentHistoryView(ParentLoginRequiredMixin, ListView): # No ParentOwnsStudentMixin needed
    model = Payment
    template_name = 'parent_portal/payment_history_all_children_list.html' # A different template
    context_object_name = 'all_payments'
    paginate_by = 20

    def get_queryset(self):
        if not Payment or not Student: # Check models loaded
            return self.model.objects.none()

        parent = self.request.user
        # Get PKs of all active students linked to this parent
        student_pks = parent.children.filter(is_active=True).values_list('pk', flat=True)

        if not student_pks:
            return Payment.objects.none()
            
        queryset = Payment.objects.filter(
            Q(student_id__in=student_pks) | Q(invoice__student_id__in=student_pks)
        ).select_related(
            'student', 'invoice', 'invoice__student', 'payment_method', 'academic_year'
        ).distinct().order_by('-payment_date', '-created_at')
        
        logger.debug(f"Fetching all payment history for parent {parent.email}, children PKs: {list(student_pks)}. Found {queryset.count()} payments.")
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("My Full Payment History (All Children)")
        context['parent'] = self.request.user # For template display

        school_profile_instance = None
        if SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
            try: school_profile_instance = SchoolProfile.objects.get()
            except (SchoolProfile.DoesNotExist, SchoolProfile.MultipleObjectsReturned): pass
        context['school_profile'] = school_profile_instance
        return context


# D:\school_fees_saas_v2\apps\parent_portal\views.py
# ... (other imports: TemplateView, ParentLoginRequiredMixin, ParentUser, Student, Invoice, SchoolProfile, Decimal, Sum, F, Coalesce, DecimalField, _, logger, messages)

class MakePaymentSummaryView(ParentLoginRequiredMixin, TemplateView):
    template_name = 'parent_portal/make_payment_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        parent = self.request.user
        tenant = self.request.tenant # Available due to TenantMainMiddleware

        context['view_title'] = _("Review and Pay Fees")
        context['parent'] = parent

        outstanding_invoices_qs = Invoice.objects.none()
        total_amount_to_pay_val = Decimal('0.00')

        if PARENT_USER_MODEL_IMPORTED and ParentUser and Student and INVOICE_MODEL_IMPORTED and Invoice and hasattr(parent, 'children'):
            try:
                # Get IDs of students linked to this parent
                student_pks = list(parent.children.filter(is_active=True).values_list('pk', flat=True))
                
                if student_pks:
                    outstanding_statuses = [
                        Invoice.InvoiceStatus.SENT,
                        Invoice.InvoiceStatus.PARTIALLY_PAID,
                        Invoice.InvoiceStatus.OVERDUE
                    ]
                    
                    # Fetch all outstanding invoices for these students
                    outstanding_invoices_qs = Invoice.objects.filter(
                        student_id__in=student_pks,
                        status__in=outstanding_statuses,
                        # Optional: Ensure balance_due > 0 if balance_due is a calculated field not always positive
                    ).select_related('student', 'academic_year', 'term').order_by('student__first_name', 'due_date')

                    # Calculate total amount to pay from these outstanding invoices
                    # Assuming Invoice model has a 'balance_due' @property or you calculate it
                    # For simplicity, let's recalculate here if 'balance_due' is not a direct field/property
                    # or sum up a 'balance_due' field if it exists.
                    
                    # If Invoice has balance_due property or field:
                    # total_amount_to_pay_val = outstanding_invoices_qs.aggregate(
                    #     total=Coalesce(Sum('balance_due'), Decimal('0.00'), output_field=DecimalField())
                    # )['total']

                    # Or, calculating it again:
                    aggregation = outstanding_invoices_qs.aggregate(
                        total_payable=Coalesce(
                            Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
                            Decimal('0.00'),
                            output_field=DecimalField()
                        )
                    )
                    total_amount_to_pay_val = aggregation.get('total_payable', Decimal('0.00'))

                    logger.debug(f"MakePaymentSummaryView: Found {outstanding_invoices_qs.count()} outstanding invoices for parent {parent.email}, total: {total_amount_to_pay_val}")
                else:
                    logger.debug(f"MakePaymentSummaryView: Parent {parent.email} has no active students linked.")
            except Exception as e:
                logger.error(f"MakePaymentSummaryView: Error fetching outstanding invoices for parent {parent.email}: {e}", exc_info=True)
                messages.error(self.request, _("Could not retrieve outstanding fee details."))
        
        context['outstanding_invoices'] = outstanding_invoices_qs
        context['total_amount_to_pay'] = total_amount_to_pay_val
        
        school_profile_instance = None
        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and tenant:
            try:
                school_profile_instance = SchoolProfile.objects.first() # Specific to current tenant
            except Exception as e:
                logger.error(f"MakePaymentSummaryView: Error fetching SchoolProfile: {e}")
        context['school_profile'] = school_profile_instance
        
        # For Payfast/Ozow integration later, you'd prepare payment gateway data here:
        # context['payment_gateway_data'] = self.prepare_payment_gateway_data(total_amount_to_pay_val, parent, outstanding_invoices_qs)
        
        logger.info(f"MakePaymentSummaryView: Context prepared for parent {parent.email}. Amount to pay: {total_amount_to_pay_val}")
        return context

    # def prepare_payment_gateway_data(self, amount, parent, invoices):
    #     # Logic to generate merchant_id, merchant_key, return_url, cancel_url, notify_url,
    #     # item_name, item_description, amount, custom_str1 (e.g., parent_id), etc.
    #     # This data would be used to build the hidden form fields for the payment gateway.
    #     gateway_data = {}
    #     # ...
    #     return gateway_data
    


class ParentPaymentHistoryView(ParentLoginRequiredMixin, TemplateView):
    template_name = 'parent_portal/payment_history_list.html' # Using _list for consistency, create this template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        parent = self.request.user
        context['view_title'] = _("My Payment History")
        context['parent'] = parent

        # Get linked students
        linked_students_qs = parent.children.filter(is_active=True).select_related(
            'current_class', 'current_section'
        ).order_by('first_name')

        # Fetch actual payment records linked to this parent's children
        payment_list = []
        if linked_students_qs.exists():
            student_pks = linked_students_qs.values_list('pk', flat=True)
            payment_list = Payment.objects.filter(
                student_id__in=student_pks
            ).select_related('student', 'payment_method', 'academic_year').prefetch_related(
                'allocations__invoice'
            ).order_by('-payment_date', '-created_at')

        context['payment_list'] = payment_list
        context['linked_students'] = linked_students_qs

        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'): # Ensure SchoolProfile is available
            try: context['school_profile'] = SchoolProfile.objects.first()
            except: context['school_profile'] = None

        logger.info(f"ParentPaymentHistoryView: Context prepared for parent {parent.email}. Found {payment_list.count()} payments.")
        return context



# apps/parent_portal/views.py
# ... (existing imports: TemplateView, ParentLoginRequiredMixin, _, messages, logger, reverse, redirect, reverse_lazy) ...
from django.http import HttpResponseForbidden # For cases where session data is missing

class MockPaymentGatewayView(ParentLoginRequiredMixin, TemplateView):
    template_name = 'parent_portal/mock_payment_gateway.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Simulated Payment Gateway")
        
        payment_context = self.request.session.get('payment_context')
        if not payment_context:
            logger.warning(f"MockPaymentGatewayView: No payment_context in session for user {self.request.user.email}. Redirecting.")
            messages.error(self.request, _("Payment session expired or is invalid. Please try selecting invoices again."))
            # Redirecting to select_invoices might be better if that's where they came from
            # but needs a robust way to handle if that URL also requires session data.
            # For now, redirect to dashboard.
            # To prevent infinite loop if dashboard also has issues, use a known good URL.
            # This should ideally not happen if the flow is correct.
            # kwargs['bypass_to_url'] = reverse_lazy('parent_portal:select_invoices_for_payment') # This would cause redirect loop if no context
            kwargs['payment_error'] = True # Flag for template
            return context

        context['invoice_details'] = payment_context.get('invoice_details', [])
        context['total_amount_to_pay'] = payment_context.get('total_amount_to_pay', '0.00')
        context['currency'] = payment_context.get('currency', '$')
        context['parent'] = self.request.user # For display if needed

        logger.info(f"MockPaymentGatewayView: Displaying mock payment page for user {self.request.user.email}, amount {context['total_amount_to_pay']} {context['currency']}")
        return context

    def dispatch(self, request, *args, **kwargs):
        # Check for payment_context early in dispatch
        payment_context = request.session.get('payment_context')
        if not payment_context:
            logger.warning(f"MockPaymentGatewayView Dispatch: No payment_context in session for user {request.user.email}.")
            messages.error(request, _("Your payment session is invalid or has expired. Please start over by selecting invoices."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment')) # Redirect to selection page
        return super().dispatch(request, *args, **kwargs)



# apps/parent_portal/views.py
# ... (existing imports: View, ParentLoginRequiredMixin, _, messages, logger, reverse, redirect, reverse_lazy) ...
# We'll need a function to do the actual recording, let's call it record_parent_payment
from apps.payments.services import record_parent_payment # We will create this service function

class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Using base View for simple POST handling
    
    def post(self, request, *args, **kwargs):
        payment_context = request.session.get('payment_context')
        payment_outcome = request.POST.get('payment_outcome')

        if not payment_context:
            logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
            messages.error(request, _("Your payment session has expired. Please select invoices again."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

        # Clear the payment context from session after retrieving it
        # to prevent re-processing or issues if user hits back button.
        # Do this early, but make sure all needed data is extracted first.
        # For real gateways, this context might be tied to a PaymentIntent object.
        
        invoice_ids = payment_context.get('invoice_ids', [])
        total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
        total_amount_paid = Decimal(total_amount_paid_str) # Convert back to Decimal
        
        # Remove payment_context from session now that we have what we need
        if 'payment_context' in request.session:
            del request.session['payment_context']
            request.session.modified = True # Ensure session is saved

        if payment_outcome == 'success':
            logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
            
            # --- Call the Payment Recording Logic ---
            try:
                # Pass necessary info to the recording service/function
                # The service function will handle creating Payment, PaymentAllocations, JEs, etc.
                payment_successful, message, payment_instance = record_parent_payment(
                    parent_user=request.user,
                    tenant=request.tenant, # Make sure request.tenant is available
                    invoice_pks_to_pay=invoice_ids,
                    amount_paid_by_parent=total_amount_paid,
                    payment_method_code="ONLINE_MOCK", # Specific code for mock payments
                    transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}" # Example mock ref
                )

                if payment_successful:
                    messages.success(request, message or _("Payment processed successfully!"))
                    # Redirect to a success page, perhaps showing payment details
                    # Or redirect to the payment history page
                    return redirect(reverse_lazy('parent_portal:mock_payment_success', kwargs={'payment_pk': payment_instance.pk if payment_instance else 0}))
                else:
                    messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
                    return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

            except Exception as e:
                logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
                messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
                return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

        elif payment_outcome == 'failure':
            logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
            messages.error(request, _("Your simulated payment failed as requested."))
            return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
        else:
            logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
            messages.error(request, _("Invalid payment simulation outcome."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
    
    class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Now 'View' is defined
        template_name = None # Not directly rendering a template, but handling POST and redirecting

    def post(self, request, *args, **kwargs):
        # ... your existing post logic from the previous response ...
        payment_context = request.session.get('payment_context')
        payment_outcome = request.POST.get('payment_outcome')

        if not payment_context:
            logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
            messages.error(request, _("Your payment session has expired. Please select invoices again."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
        
        invoice_ids = payment_context.get('invoice_ids', [])
        total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
        total_amount_paid = Decimal(total_amount_paid_str)
        
        if 'payment_context' in request.session:
            del request.session['payment_context']
            request.session.modified = True

        if payment_outcome == 'success':
            logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
            try:
                payment_successful, message, payment_instance = record_parent_payment(
                    parent_user=request.user,
                    tenant=request.tenant,
                    invoice_pks_to_pay=invoice_ids,
                    amount_paid_by_parent=total_amount_paid,
                    payment_method_code="ONLINE_MOCK",
                    transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}"
                )
                if payment_successful:
                    messages.success(request, message or _("Payment processed successfully!"))
                    success_url_name = 'parent_portal:mock_payment_success'
                    # Pass payment_pk if payment_instance is not None
                    success_kwargs = {'payment_pk': payment_instance.pk} if payment_instance else {}
                    if not payment_instance: # If payment_instance is None, can't use pk. Maybe simple success page.
                        # This case should ideally be handled by record_parent_payment returning False earlier
                        logger.error("ProcessMockPaymentView: payment_instance is None after successful record_parent_payment call. This is unexpected.")
                        # Fallback to a generic success page if you have one, or dashboard.
                        return redirect(reverse_lazy('parent_portal:dashboard')) # Or a generic payment success page

                    return redirect(reverse_lazy(success_url_name, kwargs=success_kwargs))
                else:
                    messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
                    return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
            except Exception as e:
                logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
                messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
                return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

        elif payment_outcome == 'failure':
            logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
            messages.error(request, _("Your simulated payment failed as requested."))
            return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
        else:
            logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
            messages.error(request, _("Invalid payment simulation outcome."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

    def get(self, request, *args, **kwargs):
        # POST-only view, redirect GET requests
        logger.warning(f"ProcessMockPaymentView: GET request received, redirecting. User: {request.user.email}")
        messages.error(request, _("This action must be performed via a form submission."))
        return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))



# apps/parent_portal/views.py
# ... (existing imports: TemplateView, ParentLoginRequiredMixin, Invoice, Student, ParentUser, _, messages, logger, Decimal, Q, etc.) ...
from django.views.generic.edit import FormView # For handling the form submission
from django.urls import reverse, reverse_lazy
from .forms import SelectInvoicesForPaymentForm # We'll create this form

class SelectInvoicesForPaymentView(ParentLoginRequiredMixin, FormView):
    template_name = 'parent_portal/select_invoices_for_payment.html'
    form_class = SelectInvoicesForPaymentForm # A form to handle the selection
    success_url = reverse_lazy('parent_portal:mock_payment_gateway') # Redirect to mock gateway

    def get_form_kwargs(self):
        # Pass the parent user to the form so it can fetch their children's invoices
        kwargs = super().get_form_kwargs()
        kwargs['parent'] = self.request.user

        # Check if a specific invoice is pre-selected via URL parameter
        invoice_id = self.request.GET.get('invoice')
        if invoice_id:
            kwargs['preselected_invoice_id'] = invoice_id

        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        parent = self.request.user
        context['view_title'] = _("Pay School Fees")
        context['parent'] = parent

        # The form itself will handle displaying the invoices and selection
        # We can also pass the invoices to the context separately if needed for display outside the form
        
        # Fetch outstanding invoices for all children of this parent
        # This is similar to what the form will do, but can be used for display
        outstanding_invoices_qs = Invoice.objects.none()
        if INVOICE_MODEL_IMPORTED and PARENT_USER_MODEL_IMPORTED and Student and isinstance(parent, ParentUser):
            try:
                children_pks = parent.children.filter(is_active=True).values_list('pk', flat=True)
                if children_pks:
                    from apps.fees.models import InvoiceStatus
                    outstanding_statuses = [
                        InvoiceStatus.SENT,
                        InvoiceStatus.PARTIALLY_PAID,
                        InvoiceStatus.OVERDUE
                    ]
                    outstanding_invoices_qs = Invoice.objects.filter(
                        student_id__in=children_pks,
                        status__in=outstanding_statuses,
                    ).exclude( # Using exclude to ensure balance_due > 0 effectively
                        # balance_due <= 0 means: (subtotal_amount - total_concession_amount - amount_paid) <= 0
                        Q(subtotal_amount__lte=F('total_concession_amount') + F('amount_paid'))
                    ).select_related('student').order_by('student__first_name', 'due_date')
                context['outstanding_invoices'] = outstanding_invoices_qs
                logger.debug(f"SelectInvoicesView: Found {outstanding_invoices_qs.count()} outstanding invoices for parent {parent.email}")
            except Exception as e:
                logger.error(f"SelectInvoicesView: Error fetching outstanding invoices for parent {parent.email}: {e}", exc_info=True)
                context['outstanding_invoices'] = Invoice.objects.none()
        
        if SCHOOL_PROFILE_MODEL_IMPORTED and hasattr(self.request, 'tenant'):
            try: context['school_profile'] = SchoolProfile.objects.first()
            except: context['school_profile'] = None

        return context

    def form_valid(self, form):
        # This method is called when the form (containing selected invoice IDs) is submitted and valid.
        selected_invoice_ids = form.cleaned_data.get('selected_invoices') # From ModelMultipleChoiceField
        
        if not selected_invoice_ids:
            messages.error(self.request, _("Please select at least one invoice to pay."))
            return self.form_invalid(form)

        # Store selected invoice IDs and total amount in session to pass to the mock gateway
        # A more robust way for real gateways would be to create a PaymentIntent/Order record here.
        invoices_to_pay = Invoice.objects.filter(pk__in=[inv.pk for inv in selected_invoice_ids])
        total_to_pay = Decimal('0.00')
        invoice_details_for_session = []

        for inv in invoices_to_pay:
            if inv.balance_due > 0: # Ensure we are only processing invoices with a balance
                total_to_pay += inv.balance_due
                invoice_details_for_session.append({
                    'id': inv.pk,
                    'number': inv.invoice_number,
                    'amount_due': str(inv.balance_due) # Store as string for session
                })
            else:
                messages.warning(self.request, _(f"Invoice {inv.invoice_number} has no outstanding balance and was excluded."))


        if total_to_pay <= 0:
            messages.error(self.request, _("No amount to pay for the selected invoices."))
            return self.form_invalid(form)

        self.request.session['payment_context'] = {
            'invoice_ids': [inv['id'] for inv in invoice_details_for_session],
            'invoice_details': invoice_details_for_session, # For display on mock page
            'total_amount_to_pay': str(total_to_pay),
            'currency': self.request.tenant.schoolprofile.currency_code if self.request.tenant.schoolprofile else 'USD' # Example
        }
        logger.info(f"SelectInvoicesView: Parent {self.request.user.email} selected {len(invoice_details_for_session)} invoices, total {total_to_pay}. Redirecting to mock gateway.")
        return super().form_valid(form)



class ParentInvoiceDetailView(ParentLoginRequiredMixin, DetailView):
    """Allow parents to view invoice details for their children's invoices"""
    model = Invoice
    template_name = 'fees/invoice_detail.html'  # Reuse the existing template
    context_object_name = 'invoice'

    def get_queryset(self):
        """Ensure parents can only view invoices for their children"""
        if not INVOICE_MODEL_IMPORTED or not PARENT_USER_MODEL_IMPORTED:
            return Invoice.objects.none()

        parent = self.request.user
        if not hasattr(parent, 'children'):
            return Invoice.objects.none()

        # Get student IDs for this parent's children
        student_ids = list(parent.children.filter(is_active=True).values_list('pk', flat=True))

        return Invoice.objects.filter(
            student_id__in=student_ids
        ).select_related(
            'student', 'academic_year', 'term', 'created_by',
            'student__current_class', 'student__current_section'
        ).prefetch_related(
            'details__fee_head', 'details__concession_type'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add school profile for template
        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile:
            try:
                context['school_profile'] = SchoolProfile.objects.first()
            except:
                context['school_profile'] = None

        # Add user type flags for template
        context['user_type_flags'] = {
            'IS_TENANT_PARENT_USER': True,
            'IS_TENANT_STAFF_USER': False
        }

        return context


class ParentInvoicePDFView(ParentLoginRequiredMixin, View):
    """Allow parents to view PDF of their children's invoices"""

    def get(self, request, pk):
        if not INVOICE_MODEL_IMPORTED or not PARENT_USER_MODEL_IMPORTED:
            raise Http404("Invoice system not available")

        parent = request.user
        if not hasattr(parent, 'children'):
            raise Http404("No children found")

        # Get student IDs for this parent's children
        student_ids = list(parent.children.filter(is_active=True).values_list('pk', flat=True))

        # Get the invoice, ensuring it belongs to one of parent's children
        try:
            invoice = Invoice.objects.select_related(
                'student', 'academic_year', 'term', 'created_by',
                'student__current_class', 'student__current_section'
            ).prefetch_related(
                'details__fee_head', 'details__concession_type'
            ).get(pk=pk, student_id__in=student_ids)
        except Invoice.DoesNotExist:
            raise Http404("Invoice not found or access denied")

        # Get school profile
        school_profile = None
        if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile:
            try:
                school_profile = SchoolProfile.objects.first()
            except:
                pass

        # Check if PDF generation is available
        if not PDF_AVAILABLE:
            return HttpResponse("PDF generation is not available on this system.", status=500)

        # Prepare context for PDF template
        context = {
            'invoice': invoice,
            'school_profile': school_profile,
            'tenant': request.tenant if hasattr(request, 'tenant') else None,
        }

        # Generate PDF using the same template as staff
        pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)

        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"Invoice-{invoice.invoice_number_display or f'Draft-{invoice.pk}'}.pdf"

            # Handle different PDF options based on query parameters
            if request.GET.get('download'):
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
            else:
                response['Content-Disposition'] = f'inline; filename="{filename}"'

            return response

        return HttpResponse("Error generating PDF.", status=500)



# apps/parent_portal/views.py
# ... (existing imports: View, ParentLoginRequiredMixin, _, messages, logger, reverse, redirect, reverse_lazy) ...
# We'll need a function to do the actual recording, let's call it record_parent_payment
from apps.payments.services import record_parent_payment # We will create this service function

class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Using base View for simple POST handling
    
    def post(self, request, *args, **kwargs):
        payment_context = request.session.get('payment_context')
        payment_outcome = request.POST.get('payment_outcome')

        if not payment_context:
            logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
            messages.error(request, _("Your payment session has expired. Please select invoices again."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

        # Clear the payment context from session after retrieving it
        # to prevent re-processing or issues if user hits back button.
        # Do this early, but make sure all needed data is extracted first.
        # For real gateways, this context might be tied to a PaymentIntent object.
        
        invoice_ids = payment_context.get('invoice_ids', [])
        total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
        total_amount_paid = Decimal(total_amount_paid_str) # Convert back to Decimal
        
        # Remove payment_context from session now that we have what we need
        if 'payment_context' in request.session:
            del request.session['payment_context']
            request.session.modified = True # Ensure session is saved

        if payment_outcome == 'success':
            logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
            
            # --- Call the Payment Recording Logic ---
            try:
                # Pass necessary info to the recording service/function
                # The service function will handle creating Payment, PaymentAllocations, JEs, etc.
                payment_successful, message, payment_instance = record_parent_payment(
                    parent_user=request.user,
                    tenant=request.tenant, # Make sure request.tenant is available
                    invoice_pks_to_pay=invoice_ids,
                    amount_paid_by_parent=total_amount_paid,
                    payment_method_code="ONLINE_MOCK", # Specific code for mock payments
                    transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}" # Example mock ref
                )

                if payment_successful:
                    messages.success(request, message or _("Payment processed successfully!"))
                    # Redirect to a success page, perhaps showing payment details
                    # Or redirect to the payment history page
                    return redirect(reverse_lazy('parent_portal:mock_payment_success', kwargs={'payment_pk': payment_instance.pk if payment_instance else 0}))
                else:
                    messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
                    return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

            except Exception as e:
                logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
                messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
                return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

        elif payment_outcome == 'failure':
            logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
            messages.error(request, _("Your simulated payment failed as requested."))
            return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
        else:
            logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
            messages.error(request, _("Invalid payment simulation outcome."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
    
    class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Now 'View' is defined
        template_name = None # Not directly rendering a template, but handling POST and redirecting

    def post(self, request, *args, **kwargs):
        # ... your existing post logic from the previous response ...
        payment_context = request.session.get('payment_context')
        payment_outcome = request.POST.get('payment_outcome')

        if not payment_context:
            logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
            messages.error(request, _("Your payment session has expired. Please select invoices again."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
        
        invoice_ids = payment_context.get('invoice_ids', [])
        total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
        total_amount_paid = Decimal(total_amount_paid_str)
        
        if 'payment_context' in request.session:
            del request.session['payment_context']
            request.session.modified = True

        if payment_outcome == 'success':
            logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
            try:
                payment_successful, message, payment_instance = record_parent_payment(
                    parent_user=request.user,
                    tenant=request.tenant,
                    invoice_pks_to_pay=invoice_ids,
                    amount_paid_by_parent=total_amount_paid,
                    payment_method_code="ONLINE_MOCK",
                    transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}"
                )
                if payment_successful:
                    messages.success(request, message or _("Payment processed successfully!"))
                    success_url_name = 'parent_portal:mock_payment_success'
                    # Pass payment_pk if payment_instance is not None
                    success_kwargs = {'payment_pk': payment_instance.pk} if payment_instance else {}
                    if not payment_instance: # If payment_instance is None, can't use pk. Maybe simple success page.
                        # This case should ideally be handled by record_parent_payment returning False earlier
                        logger.error("ProcessMockPaymentView: payment_instance is None after successful record_parent_payment call. This is unexpected.")
                        # Fallback to a generic success page if you have one, or dashboard.
                        return redirect(reverse_lazy('parent_portal:dashboard')) # Or a generic payment success page

                    return redirect(reverse_lazy(success_url_name, kwargs=success_kwargs))
                else:
                    messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
                    return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
            except Exception as e:
                logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
                messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
                return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

        elif payment_outcome == 'failure':
            logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
            messages.error(request, _("Your simulated payment failed as requested."))
            return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
        else:
            logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
            messages.error(request, _("Invalid payment simulation outcome."))
            return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

    def get(self, request, *args, **kwargs):
        # POST-only view, redirect GET requests
        logger.warning(f"ProcessMockPaymentView: GET request received, redirecting. User: {request.user.email}")
        messages.error(request, _("This action must be performed via a form submission."))
        return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))



# D:\school_fees_saas_v2\apps\parent_portal\views.py
# ... (other imports: TemplateView, ParentLoginRequiredMixin, ParentUser, Student, Invoice, SchoolProfile, Decimal, Sum, F, Coalesce, DecimalField, _, logger, messages)

class MyChildrenListView(ParentLoginRequiredMixin, ListView):
    model = Student # Set directly if Student is imported
    template_name = 'parent_portal/my_children_list.html'
    context_object_name = 'linked_students' # To match common usage in templates
    paginate_by = 10

    def get_queryset(self):
        if not STUDENT_MODEL_IMPORTED or self.model is None or \
            not PARENT_USER_MODEL_IMPORTED or ParentUser is None or \
            not isinstance(self.request.user, ParentUser):
            logger.warning("MyChildrenListView: Prerequisites not met (Student/ParentUser model or user type).")
            return self.model.objects.none() if self.model else []
        
        try:
            # Assuming ParentUser.children is the M2M accessor
            return self.request.user.children.filter(is_active=True).order_by('first_name', 'last_name')
        except AttributeError:
            logger.error(f"MyChildrenListView: ParentUser {self.request.user.email} has no 'children' attribute.")
            return self.model.objects.none() if self.model else []

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("My Children")
        if PARENT_USER_MODEL_IMPORTED and ParentUser and isinstance(self.request.user, ParentUser):
            context['parent'] = self.request.user # Make parent available
        return context



class ChildrenFeesSummaryView(ParentLoginRequiredMixin, TemplateView):
    template_name = 'parent_portal/children_fees_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        parent = self.request.user # This is a ParentUser instance due to the mixin
        
        context['view_title'] = _("My Children's Fees Summary")
        context['parent'] = parent

        children_fee_details = []
        grand_total_invoiced = Decimal('0.00')
        grand_total_paid = Decimal('0.00')
        grand_total_outstanding = Decimal('0.00')

        if Student and hasattr(parent, 'children'):
            try:
                linked_students = parent.children.filter(is_active=True).order_by('first_name', 'last_name')
                
                for student in linked_students:
                    student_data = {
                        'student_obj': student,
                        'total_invoiced': Decimal('0.00'),
                        'total_paid': Decimal('0.00'),
                        'total_outstanding': Decimal('0.00'),
                        'has_outstanding': False
                    }

                    if Invoice: # Check if Invoice model is available
                        # Aggregate invoiced amounts (all invoices, regardless of status, represent an amount billed)
                        # Use actual database fields: net_billable_amount = subtotal_amount - total_concession_amount
                        invoiced_agg = Invoice.objects.filter(student=student).aggregate(
                            sum_invoiced=Coalesce(
                                Sum(F('subtotal_amount') - F('total_concession_amount')),
                                Decimal('0.00'),
                                output_field=DecimalField()
                            )
                        )
                        student_data['total_invoiced'] = invoiced_agg['sum_invoiced']

                        # Aggregate paid amounts
                        paid_agg = Invoice.objects.filter(student=student).aggregate(
                            sum_paid=Coalesce(Sum('amount_paid'), Decimal('0.00'), output_field=DecimalField())
                        )
                        student_data['total_paid'] = paid_agg['sum_paid']

                        # Calculate outstanding for this student
                        student_data['total_outstanding'] = student_data['total_invoiced'] - student_data['total_paid']
                        if student_data['total_outstanding'] > Decimal('0.00'):
                            student_data['has_outstanding'] = True
                        
                        # Add to grand totals
                        grand_total_invoiced += student_data['total_invoiced']
                        grand_total_paid += student_data['total_paid']
                        grand_total_outstanding += student_data['total_outstanding']
                    
                    children_fee_details.append(student_data)
                
                logger.debug(f"ChildrenFeesSummaryView: Prepared fee details for {len(children_fee_details)} children of parent {parent.email}")

            except Exception as e:
                logger.error(f"ChildrenFeesSummaryView: Error processing student fees for parent {parent.email}: {e}", exc_info=True)
                messages.error(self.request, _("An error occurred while retrieving fee details for your children."))
        
        context['children_fee_details'] = children_fee_details
        context['grand_total_invoiced'] = grand_total_invoiced
        context['grand_total_paid'] = grand_total_paid
        context['grand_total_outstanding'] = grand_total_outstanding
        
        # Get School Profile for currency symbol etc.
        school_profile_instance = None
        if SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
            try:
                school_profile_instance = SchoolProfile.objects.first() # Assuming one profile per tenant schema
            except Exception as e:
                logger.error(f"ChildrenFeesSummaryView: Error fetching SchoolProfile: {e}")
        context['school_profile'] = school_profile_instance
        
        logger.info(f"ChildrenFeesSummaryView: Context prepared for parent {parent.email}. Children details count: {len(children_fee_details)}")
        return context
    
    
    
    
# ==============================================================================
# --- ADD THESE MISSING VIEWS ---
# ==============================================================================

class MockPaymentSuccessView(ParentLoginRequiredMixin, DetailView):
    """
    Displays a success message after a payment is processed.
    It fetches the specific payment to show details on the receipt.
    """
    model = Payment # We are detailing a specific Payment object
    template_name = 'parent_portal/payment_status.html'
    context_object_name = 'payment'
    pk_url_kwarg = 'payment_pk' # The name of the kwarg in the URL

    def get_queryset(self):
        """Security: only show payments made by the logged-in parent."""
        return Payment.objects.filter(parent_payer=self.request.user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status'] = 'success'
        context['view_title'] = _("Payment Successful")
        context['message_title'] = _("Thank You! Your Payment Was Successful.")
        context['message_body'] = _(
            "Your payment has been received and applied to your account. "
            "You can view the receipt below or find it in your payment history."
        )
        return context


class MockPaymentFailureView(ParentLoginRequiredMixin, TemplateView):
    """
    Displays a failure message after a simulated payment fails.
    """
    template_name = 'parent_portal/payment_status.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status'] = 'failure'
        context['view_title'] = _("Payment Failed")
        context['message_title'] = _("Your Payment Was Not Processed")
        context['message_body'] = _(
            "The simulated payment failed as requested. No charges were made. "
            "You can try the payment process again from your dashboard."
        )
        return context    




















# # D:\school_fees_saas_v2\apps\parent_portal\views.py

# import logging
# from decimal import Decimal

# from django.conf import settings
# from django.contrib import messages
# from django.contrib.auth import login as auth_login, logout as auth_logout, authenticate
# from django.contrib.auth.mixins import LoginRequiredMixin
# from django.contrib.messages.views import SuccessMessageMixin
# from django.core.exceptions import PermissionDenied, FieldError
# from django.db.models import Sum, F, Q, DecimalField, Exists, OuterRef # Not all used here, but good to have
# from django.db.models.functions import Coalesce
# from django.http import Http404
# from django.shortcuts import render, redirect, get_object_or_404
# from django.utils import timezone
# from django.utils.http import url_has_allowed_host_and_scheme
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView, UpdateView, ListView, DetailView

# from apps.common.features import tenant_has_feature_for_parent_portal

# from apps.common.mixins import ParentLoginRequiredMixin


# from django.shortcuts import render, redirect, get_object_or_404 # Example existing imports
# from django.urls import reverse_lazy, reverse
# from django.views.generic import TemplateView, ListView, DetailView # Your existing CBV imports
# from django.views.generic.base import View # <<< --- ADD THIS IMPORT ---
# from django.views.generic.edit import FormView # You used this for SelectInvoicesForPaymentView

# from django.http import HttpResponseForbidden, HttpResponse # For other views potentially
# from django.db.models import F, Q, Sum #, Coalesce, DecimalField
# from django.utils.translation import gettext_lazy as _
# from django.contrib import messages
# from django.contrib.auth import logout as auth_logout
# from django.utils import timezone # If used in this file

# # Model Imports (ensure these are correct and comprehensive for all views in this file)
# from apps.students.models import Student, ParentUser
# from apps.fees.models import Invoice
# from apps.schools.models import SchoolProfile
# from apps.payments.models import Payment, PaymentAllocation # If used directly in views, though likely via services

# # Form Imports
# from .forms import SelectInvoicesForPaymentForm # Example

# # Mixin Imports
# from apps.common.mixins import ParentLoginRequiredMixin # Example

# # Service Imports
# from apps.payments.services import record_parent_payment # Example

# import logging
# logger = logging.getLogger(__name__)

# # Additional imports for parent invoice viewing
# from django.http import HttpResponse, Http404
# from apps.common.utils import render_to_pdf, PDF_AVAILABLE

# # --- Model Imports ---
# PARENT_USER_MODEL_IMPORTED = False
# STUDENT_MODEL_IMPORTED = False
# INVOICE_MODEL_IMPORTED = False
# PAYMENT_MODEL_IMPORTED = False
# SCHOOL_PROFILE_MODEL_IMPORTED = False
# # 
# try:
#     from apps.students.models import ParentUser # Assuming ParentUser is now in apps.users
#     PARENT_USER_MODEL_IMPORTED = True
# except ImportError:
#     ParentUser = None # type: ignore
#     logger.critical("PARENT_PORTAL: CRITICAL - ParentUser model not found (expected in apps.users.models). Parent portal will not function.")

# # # try:
# # #     from apps.students.models import Student
# # #     STUDENT_MODEL_IMPORTED = True
# # # except ImportError:
# # #     Student = None # type: ignore
# # #     logger.critical("PARENT_PORTAL: CRITICAL - Student model not found. Parent portal functionality will be severely limited.")

# # # try:
# # #     from apps.fees.models import Invoice
# # #     INVOICE_MODEL_IMPORTED = True
# # # except ImportError:
# # #     Invoice = None # type: ignore
# # #     logger.warning("PARENT_PORTAL: Invoice model not found. Fee-related information will be unavailable.")

# # # try:
# # #     from apps.payments.models import Payment
# # #     PAYMENT_MODEL_IMPORTED = True
# # # except ImportError:
# # #     Payment = None # type: ignore
# # #     logger.warning("PARENT_PORTAL: Payment model not found. Payment history will be unavailable.")

# # # try:
# # #     from apps.schools.models import SchoolProfile
# # #     SCHOOL_PROFILE_MODEL_IMPORTED = True
# # # except ImportError:
# # #     SchoolProfile = None # type: ignore
# # #     logger.warning("PARENT_PORTAL: SchoolProfile model not found. School-specific details may be unavailable.")

# # # # --- Form Imports ---
# # # try:
# # #     from .forms import ParentLoginForm, ParentProfileUpdateForm
# # # except ImportError:
# # #     ParentLoginForm, ParentProfileUpdateForm = None, None # type: ignore
# # #     logger.critical("PARENT_PORTAL: CRITICAL - ParentLoginForm or ParentProfileUpdateForm not found in parent_portal.forms.")


# # # # --- Constants ---
# # # PARENT_PORTAL_FEATURE_CODE = 'PARENT_PORTAL'

# # # # --- Utility Function for Feature Check ---
# # # def _tenant_has_parent_portal_feature(tenant):
# # #     if not tenant or not hasattr(tenant, 'subscription'): return False
# # #     # Assuming subscriptions.utils.tenant_has_feature exists and works
# # #     try:
# # #         from apps.subscriptions.utils import tenant_has_feature as check_feature
# # #         return check_feature(tenant, PARENT_PORTAL_FEATURE_CODE)
# # #     except ImportError:
# # #         logger.error("PARENT_PORTAL: Could not import tenant_has_feature utility. Defaulting to feature disabled.")
# # #         return False


# # # # --- Custom Mixins for Parent Portal ---
# # # class ParentLoginRequiredMixin(LoginRequiredMixin):
# # #     """ Ensures user is logged in, is a ParentUser, and PARENT_PORTAL feature is active. """
# # #     login_url = reverse_lazy('parent_portal:login') # Centralized login URL

# # #     def dispatch(self, request, *args, **kwargs):
# # #         if not PARENT_USER_MODEL_IMPORTED: # Check if ParentUser model itself loaded
# # #             messages.error(request, _("Parent portal is temporarily unavailable. Please try again later. (Error: M01)"))
# # #             logger.critical("ParentLoginRequiredMixin: ParentUser model not available. Aborting.")
# # #             return redirect(reverse('public_site:home'))

# # #         if not request.user.is_authenticated:
# # #             return self.handle_no_permission() # Let LoginRequiredMixin handle redirect

# # #         # Check if user is ParentUser - be more flexible with type checking
# # #         is_parent_user = False
# # #         if hasattr(request.user, '_meta') and request.user._meta.model_name == 'parentuser':
# # #             is_parent_user = True
# # #         elif isinstance(request.user, ParentUser):
# # #             is_parent_user = True

# # #         if not is_parent_user:
# # #             user_identifier = getattr(request.user, 'email', str(request.user))
# # #             messages.error(request, _("Access Denied. This area is for parent accounts only."))
# # #             logger.warning(f"ParentLoginRequiredMixin: User '{user_identifier}' (type: {type(request.user)}) is not ParentUser. Logging out.")
# # #             auth_logout(request) # Log out incorrect user type
# # #             return redirect(self.login_url) # Redirect to parent login

# # #         if not request.user.is_active:
# # #             messages.error(request, _("Your parent account is inactive. Please contact support."))
# # #             logger.warning(f"ParentLoginRequiredMixin: ParentUser '{request.user.email}' is inactive. Logging out.")
# # #             auth_logout(request)
# # #             return redirect(self.login_url)

# # #         # Tenant and Feature Check
# # #         tenant = getattr(request, 'tenant', None)
# # #         if not tenant:
# # #             logger.error("ParentLoginRequiredMixin: No tenant context found on request. Parent portal functionality may be impaired.")
# # #             messages.error(request, _("School information not found. Please try accessing through your school's portal link."))
# # #             return redirect(reverse('public_site:home')) # Fallback to public site

# # #         if not tenant_has_feature_for_parent_portal(tenant):
# # #             messages.warning(request, _("The Parent Portal is not currently enabled for this school."))
# # #             logger.warning(f"ParentLoginRequiredMixin: PARENT_PORTAL feature disabled for tenant '{tenant.name}'. Access denied for parent '{request.user.email}'.")
# # #             return redirect(reverse('public_site:home')) # Or a "feature_not_available_for_tenant" page
            
# # #         return super().dispatch(request, *args, **kwargs)


# # # class ParentOwnsStudentMixin:
# # #     """ Verifies logged-in parent is linked to student_pk. Sets self.student. """
# # #     def dispatch(self, request, *args, **kwargs):
# # #         # Assumes ParentLoginRequiredMixin has already run and request.user is a valid ParentUser
# # #         student_pk = self.kwargs.get('student_pk')
# # #         if not student_pk:
# # #             logger.error("ParentOwnsStudentMixin: 'student_pk' not found in URL kwargs.")
# # #             raise Http404(_("Student identifier missing."))

# # #         if not STUDENT_MODEL_IMPORTED:
# # #             logger.critical("ParentOwnsStudentMixin: Student model not available. Cannot verify ownership.")
# # #             messages.error(request, _("System error: Student data unavailable (M02)."))
# # #             return redirect(reverse('parent_portal:dashboard'))

# # #         parent = request.user
# # #         try:
# # #             self.student = parent.children.select_related('current_class', 'current_section').get(pk=student_pk, is_active=True)
# # #             logger.info(f"ParentOwnsStudentMixin: Parent {parent.email} authorized for student {self.student.pk}.")
# # #         except Student.DoesNotExist:
# # #             logger.warning(f"ParentOwnsStudentMixin: Parent {parent.email} attempted to access student PK {student_pk} (not linked, inactive, or non-existent).")
# # #             raise Http404(_("Student not found or you do not have permission to view this student's details."))
# # #         except AttributeError: # If parent.children related_name is wrong
# # #             logger.error(f"ParentOwnsStudentMixin: ParentUser {parent.email} does not have 'children' attribute. Check Student.parents M2M related_name.")
# # #             messages.error(request, _("System error: Parent-child link configuration issue (A01)."))
# # #             return redirect(reverse('parent_portal:dashboard')) # Fallback
            
# # #         return super().dispatch(request, *args, **kwargs)



# # # import logging
# # # from decimal import Decimal
# # # # ... (all your other necessary Django imports) ...

# # # logger = logging.getLogger(__name__)

# # # # --- Model Imports (Centralized) ---
# # # try:
# # #     from apps.students.models import ParentUser
# # #     logger.info("PARENT_PORTAL: ParentUser model successfully imported from apps.users.models.")
# # # except ImportError:
# # #     ParentUser = None
# # #     logger.critical("PARENT_PORTAL: CRITICAL - ParentUser model could not be imported (expected apps.users.models).")

# # # try:
# # #     from apps.subscriptions.models import Feature # Needed for the utility function
# # #     logger.info("PARENT_PORTAL: Feature model successfully imported from apps.subscriptions.models.")
# # # except ImportError:
# # #     Feature = None
# # #     logger.warning("PARENT_PORTAL: Feature model not found from apps.subscriptions.models. Feature checking might be affected.")
# # # # ... (other model imports: Student, Invoice, etc.)

# # # # --- Constants ---
# # # PARENT_PORTAL_FEATURE_CODE = 'PARENT_PORTAL'

# # # # --- Utility Function (defined within this module or imported) ---
# # # def tenant_has_feature_for_parent_portal(tenant, feature_code):
# # #     logger.debug(f"[UTIL_tenant_has_feature] Checking feature '{feature_code}' for tenant '{getattr(tenant, 'name', 'N/A_TENANT')}'")
# # #     if not tenant: 
# # #         logger.warning("[UTIL_tenant_has_feature] Tenant object is None.")
# # #         return False
# # #     if not Feature: # Check if Feature model was imported
# # #         logger.error("[UTIL_tenant_has_feature] Feature model not available for checking.")
# # #         return False # Default to false if Feature model isn't available
# # #     if not hasattr(tenant, 'subscription'):
# # #         logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' has no 'subscription' attribute.")
# # #         return False # Assume false if no subscription attribute
        
# # #     try:
# # #         sub = tenant.subscription # Accesses the OneToOneField
# # #         if sub and sub.is_usable and sub.plan:
# # #             has_feat = sub.plan.features.filter(code=feature_code).exists()
# # #             logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}', Plan '{sub.plan.name}', Feature '{feature_code}': {has_feat}")
# # #             return has_feat
# # #         elif sub and sub.is_usable and not sub.plan:
# # #             logger.warning(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' subscription (PK {sub.pk}) is usable but has no plan linked.")
# # #         elif sub and not sub.is_usable:
# # #             logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' subscription is not usable (Status: {sub.status}).")
# # #         elif not sub:
# # #             logger.debug(f"[UTIL_tenant_has_feature] Tenant '{tenant.name}' has no subscription object linked.")
# # #     except Exception as e:
# # #         logger.error(f"[UTIL_tenant_has_feature] Error checking feature '{feature_code}' for tenant '{tenant.name}': {e}", exc_info=True)
# # #     return False # Default to False on any issue

# # # # ... (Your Mixins: ParentUserRequiredMixin, ParentFeatureRequiredMixin, ParentLoginRequiredMixin, ParentOwnsStudentMixin) ...
# # # # Ensure ParentFeatureRequiredMixin also uses tenant_has_feature_for_parent_portal

# # # from django.conf import settings # If PARENT_PORTAL_FEATURE_CODE is in settings
# # # # or from apps.common.constants import PARENT_PORTAL_FEATURE_CODE

# # # from apps.students.models import ParentUser # Adjust path as necessary
# # # from .forms import ParentLoginForm # Adjust path as necessary
# # # from apps.common.features import tenant_has_feature_for_parent_portal # Assuming it's here

# # # from apps.common.features import tenant_has_feature_for_parent_portal

# # # # Other necessary imports:
# # # from django.shortcuts import render, redirect
# # # from django.urls import reverse
# # # from django.contrib.auth import login as auth_login, logout as auth_logout, authenticate
# # # from django.contrib import messages
# # # from django.utils.translation import gettext_lazy as _
# # # from django.utils.http import url_has_allowed_host_and_scheme
# # # import logging

# # # logger = logging.getLogger(__name__)

# # # # Make sure PARENT_PORTAL_FEATURE_CODE is defined
# # # # Example:
# # # PARENT_PORTAL_FEATURE_CODE = "PARENT_PORTAL_ACCESS"

# # # # --- Authentication Views ---
# # # def parent_login_view(request):
# # #     # ... (Your existing parent_login_view code from the last snippet) ...
# # #     # The line causing the error was:
# # #     # if not tenant_has_feature_for_parent_portal(tenant, PARENT_PORTAL_FEATURE_CODE):
# # #     # This will now work because the function is defined above.
# # #     # ...
# # #     logger.info(f"--- [PARENT_LOGIN_VIEW] START --- User: {getattr(request.user, 'email', request.user)}, Auth: {request.user.is_authenticated}, Type: {type(request.user)}, Method: {request.method}, Path: {request.path}")

# # #     if not ParentUser:
# # #         messages.error(request, _("Parent login system is temporarily unavailable. (Error Code: MDL_PU_NF)"))
# # #         logger.critical("[PARENT_LOGIN_VIEW] CRITICAL: ParentUser model is None. Aborting.")
# # #         return redirect(reverse('public_site:home')) 

# # #     if not ParentLoginForm:
# # #         messages.error(request, _("Parent login system is temporarily unavailable. (Error Code: FRM_PLF_NF)"))
# # #         logger.critical("[PARENT_LOGIN_VIEW] CRITICAL: ParentLoginForm is None. Aborting.")
# # #         return redirect(reverse('public_site:home'))

# # #     tenant = getattr(request, 'tenant', None)
# # #     if not tenant:
# # #         messages.error(request, _("School context (tenant) not found. Parent login is unavailable."))
# # #         logger.error("[PARENT_LOGIN_VIEW] Tenant object not found on request. Aborting.")
# # #         return redirect(reverse('public_site:home'))
    
# # #     logger.debug(f"[PARENT_LOGIN_VIEW] Tenant context: {tenant.name} (Schema: {tenant.schema_name})")

# # #     if request.user.is_authenticated:
# # #         logger.debug(f"[PARENT_LOGIN_VIEW] User '{getattr(request.user, 'email', 'Unknown User')}' is already authenticated.")
# # #         if isinstance(request.user, ParentUser):
# # #             logger.info(f"[PARENT_LOGIN_VIEW] User '{request.user.email}' is already an authenticated ParentUser. Redirecting to dashboard.")
# # #             return redirect(reverse('parent_portal:dashboard'))
# # #         else: 
# # #             logger.info(f"[PARENT_LOGIN_VIEW] User '{getattr(request.user, 'email', 'Unknown User')}' (type: {type(request.user)}) is authenticated but not a ParentUser. Logging out to allow parent login.")
# # #             auth_logout(request)
# # #             messages.info(request, _("You have been logged out from your previous session. Please log in with your parent credentials."))
# # #             return redirect(reverse('parent_portal:login'))
# # #     else:
# # #         logger.debug("[PARENT_LOGIN_VIEW] User is not authenticated. Proceeding to display/process login form.")

# # #     form = ParentLoginForm(request, data=request.POST or None) 

# # #     if request.method == 'POST':
# # #         logger.debug(f"[PARENT_LOGIN_VIEW] POST request received. Form data: {request.POST.dict()}")
# # #         if form.is_valid():
# # #             logger.info(f"[PARENT_LOGIN_VIEW] ParentLoginForm is VALID. Cleaned data: {form.cleaned_data}")
# # #             email = form.cleaned_data.get('username')
# # #             password = form.cleaned_data.get('password')
            
# # #             logger.debug(f"[PARENT_LOGIN_VIEW] Attempting to authenticate user: {email}")
# # #             user = authenticate(request, username=email, password=password)
            
# # #             logger.debug(f"[PARENT_LOGIN_VIEW] authenticate() result: {user} (Type: {type(user)})")
# # #             if user and hasattr(user, 'backend'):
# # #                 logger.debug(f"[PARENT_LOGIN_VIEW] Authenticated by backend: {user.backend}")

# # #             if user is not None and isinstance(user, ParentUser) and user.is_active:
# # #                 logger.info(f"[PARENT_LOGIN_VIEW] Authentication successful for ParentUser: {user.email}")
                
# # #                 # Use the defined utility function
# # #                 if not tenant_has_feature_for_parent_portal(tenant): # This line should now work
# # #                     messages.error(request, _("Parent Portal access is not currently enabled for this school. Please contact administration if you believe this is an error."))
# # #                     logger.warning(f"[PARENT_LOGIN_VIEW] Login attempt by ParentUser {email} for tenant '{tenant.name}', but PARENT_PORTAL feature is DISABLED. Login aborted.")
# # #                 else:
# # #                     logger.info(f"[PARENT_LOGIN_VIEW] PARENT_PORTAL feature ENABLED for tenant '{tenant.name}'. Proceeding with login for {user.email}.")
# # #                     auth_login(request, user) 
# # #                     messages.success(request, _(f"Welcome back, {user.get_full_name() or user.email}!"))
# # #                     logger.info(f"ParentLoginView: ParentUser '{user.email}' LOGGED IN successfully for tenant '{tenant.name}'.")
                    
# # #                     next_url_param = request.POST.get('next') or request.GET.get('next')
# # #                     default_redirect_url = reverse('parent_portal:dashboard')
# # #                     safe_redirect_url = default_redirect_url

# # #                     if next_url_param:
# # #                         logger.debug(f"[PARENT_LOGIN_VIEW] 'next' URL parameter found: {next_url_param}")
# # #                         if url_has_allowed_host_and_scheme(next_url_param, allowed_hosts={request.get_host()}, require_https=request.is_secure()):
# # #                             safe_redirect_url = next_url_param
# # #                             logger.debug(f"[PARENT_LOGIN_VIEW] Redirecting to safe 'next' URL: {safe_redirect_url}")
# # #                         else:
# # #                             messages.warning(request, _("The redirect address after login was not recognized as safe."))
# # #                             logger.warning(f"[PARENT_LOGIN_VIEW] Unsafe 'next' URL '{next_url_param}' provided. Using default redirect.")
# # #                     else:
# # #                         logger.debug(f"[PARENT_LOGIN_VIEW] No 'next' URL parameter. Using default redirect: {safe_redirect_url}")
                    
# # #                     return redirect(safe_redirect_url)
# # #             else: 
# # #                 if user is None: logger.warning(f"[PARENT_LOGIN_VIEW] Authentication FAILED for '{email}'. authenticate() returned None.")
# # #                 elif not isinstance(user, ParentUser): logger.warning(f"[PARENT_LOGIN_VIEW] User '{email}' authenticated but IS NOT a ParentUser (Type: {type(user)}). Denying parent portal login.")
# # #                 elif not user.is_active: logger.warning(f"[PARENT_LOGIN_VIEW] ParentUser '{email}' authenticated but IS NOT ACTIVE. Denying login.")
# # #                 messages.error(request, _("Invalid parent credentials, account type, or account is inactive."))
        
# # #         elif request.method == 'POST' and not form.is_valid():
# # #             logger.warning(f"[PARENT_LOGIN_VIEW] ParentLoginForm submitted but is INVALID. Errors: {form.errors.as_json()}")
# # #             messages.error(request, _("Please correct the errors highlighted in the login form."))
    
# # #     context = {
# # #         'form': form, 
# # #         'tenant_name': tenant.name, 
# # #         'view_title': _("Parent Portal Login"),
# # #         'next': request.GET.get('next', '')
# # #     }
# # #     logger.debug(f"[PARENT_LOGIN_VIEW] Rendering login form. Next URL: {context['next']}")
# # #     return render(request, 'parent_portal/login.html', context)


# # # def parent_logout_view(request):
# # #     parent_name = getattr(request.user, 'first_name', '') # Get name before logout
# # #     auth_logout(request)
# # #     messages.info(request, _(f"Goodbye {parent_name}, you have been successfully logged out.") if parent_name else _("You have been successfully logged out."))
# # #     return redirect(reverse('parent_portal:login'))



# # # import logging
# # # from decimal import Decimal

# # # from django.contrib import messages
# # # from django.contrib.auth import logout as auth_logout
# # # # from django.contrib.auth.mixins import LoginRequiredMixin # Using custom ParentLoginRequiredMixin
# # # from django.db.models import Sum, F, Q, DecimalField
# # # from django.db.models.functions import Coalesce
# # # from django.shortcuts import redirect, reverse, get_object_or_404
# # # from django.urls import reverse_lazy
# # # from django.utils.translation import gettext_lazy as _
# # # from django.views.generic import TemplateView, ListView, DetailView
# # # from django.http import Http404


# # # logger = logging.getLogger(__name__)

# # # # --- Model Imports & Availability Flags ---
# # # # These flags help manage optional model dependencies gracefully.

# # # PARENT_USER_MODEL_IMPORTED = False
# # # STUDENT_MODEL_IMPORTED = False
# # # try:
# # #     from apps.students.models import ParentUser, Student
# # #     PARENT_USER_MODEL_IMPORTED = True
# # #     STUDENT_MODEL_IMPORTED = True
# # #     logger.info("ParentViews: ParentUser and Student models successfully imported from apps.students.models.")
# # # except ImportError:
# # #     ParentUser = None
# # #     Student = None
# # #     logger.critical("ParentViews: CRITICAL - Failed to import ParentUser or Student model from apps.students.models.")

# # # INVOICE_MODEL_IMPORTED = False
# # # try:
# # #     from apps.fees.models import Invoice
# # #     INVOICE_MODEL_IMPORTED = True
# # #     logger.info("ParentViews: Invoice model successfully imported from apps.fees.models.")
# # # except ImportError:
# # #     Invoice = None
# # #     logger.error("ParentViews: FAILED to import Invoice model from apps.fees.models.")

# # # SCHOOL_PROFILE_MODEL_IMPORTED = False
# # # try:
# # #     from apps.schools.models import SchoolProfile
# # #     SCHOOL_PROFILE_MODEL_IMPORTED = True
# # #     logger.info("ParentViews: SchoolProfile model successfully imported from apps.schools.models.")
# # # except ImportError:
# # #     SchoolProfile = None
# # #     logger.error("ParentViews: FAILED to import SchoolProfile model from apps.schools.models.")


# # # # --- Feature Check Import ---
# # # # Assuming this function correctly checks based on Subscription -> Plan -> Feature
# # # try:
# # #     from apps.common.features import tenant_has_feature_for_parent_portal
# # #     FEATURES_MODULE_IMPORTED = True
# # #     logger.info("ParentViews: Feature checking module successfully imported.")
# # # except ImportError:
# # #     def tenant_has_feature_for_parent_portal(tenant): # Fallback
# # #         logger.error("ParentViews: FAILED to import tenant_has_feature_for_parent_portal. Defaulting to False.")
# # #         return False
# # #     FEATURES_MODULE_IMPORTED = False


# # # # --- Custom Mixins for Parent Portal ---
# # # # Using the refined ParentLoginRequiredMixin from apps.common.mixins
# # # from apps.common.mixins import ParentLoginRequiredMixin


# # # class ParentOwnsStudentMixin:
# # #     """
# # #     Ensures the logged-in parent is linked to the student specified by pk_url_kwarg.
# # #     Sets self.student on the view instance if successful.
# # #     Must be used after ParentLoginRequiredMixin.
# # #     """
# # #     student_model = Student # Use the imported Student model
# # #     student_lookup_kwarg = 'student_pk'
# # #     student_context_name = 'student' # Name for the student in context

# # #     def dispatch(self, request, *args, **kwargs):
# # #         if not STUDENT_MODEL_IMPORTED or self.student_model is None:
# # #             logger.error("ParentOwnsStudentMixin: Student model not available.")
# # #             messages.error(request, _("Student information is currently unavailable."))
# # #             return redirect(reverse_lazy('parent_portal:dashboard'))

# # #         # ParentLoginRequiredMixin already ensures request.user is an authenticated ParentUser
# # #         parent = request.user
# # #         student_pk = self.kwargs.get(self.student_lookup_kwarg)

# # #         if not student_pk:
# # #             logger.warning(f"ParentOwnsStudentMixin: No '{self.student_lookup_kwarg}' found in URL kwargs for parent {parent.email}.")
# # #             messages.error(request, _("Student identifier missing from request."))
# # #             return redirect(reverse_lazy('parent_portal:dashboard'))

# # #         try:
# # #             # Assuming ParentUser has a 'children' M2M field to Student
# # #             # This will raise Student.DoesNotExist if not found or not linked
# # #             self.student = parent.children.get(pk=student_pk, is_active=True)
# # #             logger.debug(f"ParentOwnsStudentMixin: Verified parent {parent.email} owns student {self.student.pk} ({self.student.get_full_name()}).")
# # #         except self.student_model.DoesNotExist:
# # #             logger.warning(f"ParentOwnsStudentMixin: Student PK {student_pk} not found, not active, or not linked to parent {parent.email}.")
# # #             messages.error(request, _("The requested student record was not found or you do not have permission to view it."))
# # #             return redirect(reverse_lazy('parent_portal:my_children_list')) # Or dashboard
# # #         except AttributeError: # If parent.children does not exist
# # #             logger.error(f"ParentOwnsStudentMixin: ParentUser model for {parent.email} does not have a 'children' attribute. Check M2M relationship from ParentUser to Student.")
# # #             messages.error(request, _("System error: Could not verify student linkage."))
# # #             return redirect(reverse_lazy('parent_portal:dashboard'))
# # #         except Exception as e:
# # #             logger.error(f"ParentOwnsStudentMixin: Unexpected error verifying student ownership for parent {parent.email}, student_pk {student_pk}: {e}", exc_info=True)
# # #             messages.error(request, _("An unexpected error occurred."))
# # #             return redirect(reverse_lazy('parent_portal:dashboard'))
            
# # #         return super().dispatch(request, *args, **kwargs)

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         if hasattr(self, 'student'): # Ensure self.student was set by dispatch
# # #             context[self.student_context_name] = self.student
# # #         return context


# # # class ParentDashboardView(ParentLoginRequiredMixin, TemplateView):
# # #     template_name = 'parent_portal/dashboard.html'

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         parent = self.request.user 
        
# # #         context['view_title'] = _("My Dashboard")
# # #         context['parent'] = parent

# # #         # Import logging at the top
# # #         import logging
# # #         logger = logging.getLogger(__name__)

# # #         # Get announcements for parents
# # #         try:
# # #             from apps.announcements.models import Announcement
# # #             parent_announcements = Announcement.get_parent_announcements(limit=5)
# # #             context['announcements'] = parent_announcements
# # #         except (ImportError, Exception) as e:
# # #             # Log the error and continue with empty announcements
# # #             logger.warning(f"Could not load parent announcements: {e}")
# # #             context['announcements'] = []
        
# # #         linked_students_qs = Student.objects.none() 
# # #         if STUDENT_MODEL_IMPORTED and Student and hasattr(parent, 'children'):
# # #             try:
# # #                 linked_students_qs = parent.children.filter(is_active=True).select_related(
# # #                     'current_class', 'current_section'
# # #                 ).order_by('first_name', 'last_name')
# # #                 logger.debug(f"ParentDashboard: Found {linked_students_qs.count()} active students for parent {parent.email}")
# # #             except Exception as e:
# # #                 logger.error(f"ParentDashboard: Error fetching linked students for parent {parent.email}: {e}", exc_info=True)
# # #         context['linked_students'] = linked_students_qs
        
# # #         total_outstanding = Decimal('0.00')
# # #         recent_invoices = []
# # #         children_invoice_summary = []

# # #         if INVOICE_MODEL_IMPORTED and Invoice and linked_students_qs.exists():
# # #             student_pks = list(linked_students_qs.values_list('pk', flat=True))
# # #             if student_pks:
# # #                 try:
# # #                     outstanding_statuses = [
# # #                         Invoice.InvoiceStatus.DRAFT,
# # #                         Invoice.InvoiceStatus.SENT,
# # #                         Invoice.InvoiceStatus.PARTIALLY_PAID,
# # #                         Invoice.InvoiceStatus.OVERDUE
# # #                     ]

# # #                     # Get outstanding invoices with details
# # #                     outstanding_invoices = Invoice.objects.filter(
# # #                         student_id__in=student_pks,
# # #                         status__in=outstanding_statuses
# # #                     ).select_related('student', 'academic_year', 'term').order_by('-issue_date')

# # #                     # Calculate total outstanding using actual database fields
# # #                     # balance_due = net_billable_amount - amount_paid
# # #                     # net_billable_amount = subtotal_amount - total_concession_amount
# # #                     outstanding_aggregation = outstanding_invoices.aggregate(
# # #                         calculated_total_due=Coalesce(
# # #                             Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
# # #                             Decimal('0.00'),
# # #                             output_field=DecimalField()
# # #                         )
# # #                     )
# # #                     total_outstanding = outstanding_aggregation.get('calculated_total_due', Decimal('0.00'))

# # #                     # Get recent invoices (last 5)
# # #                     recent_invoices = list(outstanding_invoices[:5])

# # #                     # Create summary per child
# # #                     for student in linked_students_qs:
# # #                         student_invoices = outstanding_invoices.filter(student=student)
# # #                         student_total = student_invoices.aggregate(
# # #                             total=Coalesce(
# # #                                 Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
# # #                                 Decimal('0.00')
# # #                             )
# # #                         )['total']

# # #                         children_invoice_summary.append({
# # #                             'student': student,
# # #                             'outstanding_amount': student_total,
# # #                             'invoice_count': student_invoices.count(),
# # #                             'latest_invoice': student_invoices.first() if student_invoices.exists() else None
# # #                         })

# # #                 except AttributeError:
# # #                     logger.error(f"ParentDashboard: AttributeError accessing Invoice.InvoiceStatus or missing Invoice field.")
# # #                 except Exception as e:
# # #                     logger.error(f"ParentDashboard: Unexpected error calculating outstanding fees: {e}", exc_info=True)

# # #         context['total_outstanding'] = total_outstanding
# # #         context['total_outstanding_fees'] = total_outstanding  # For template compatibility
# # #         context['recent_invoices'] = recent_invoices
# # #         context['children_invoice_summary'] = children_invoice_summary
        
# # #         school_profile_instance = None
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
# # #             try:
# # #                 school_profile_instance = SchoolProfile.objects.first()
# # #             except Exception as e:
# # #                 logger.error(f"ParentDashboard: Error fetching SchoolProfile: {e}", exc_info=True)
# # #         context['school_profile'] = school_profile_instance

# # #         # Add tenant features context for payment buttons
# # #         try:
# # #             from apps.tenants.models import TenantFeatures
# # #             tenant_features = TenantFeatures.objects.filter(tenant=self.request.tenant).first()
# # #             context['tenant_features'] = tenant_features or TenantFeatures()  # Default empty features
# # #         except (ImportError, Exception) as e:
# # #             logger.warning(f"ParentDashboard: Could not load tenant features: {e}")
# # #             # Create a mock object with default values
# # #             class MockTenantFeatures:
# # #                 ONLINE_PAYMENTS = True  # Default to enabled
# # #             context['tenant_features'] = MockTenantFeatures()

# # #         logger.info(f"ParentDashboard: Context prepared for parent {parent.email}. Students: {linked_students_qs.count()}, Outstanding: {total_outstanding}")
# # #         return context


# # # class ParentProfileDisplayView(ParentLoginRequiredMixin, DetailView):
# # #     model = ParentUser # Handled by ParentLoginRequiredMixin to be ParentUser
# # #     template_name = 'parent_portal/profile_display.html'
# # #     context_object_name = 'parent_profile'

# # #     def get_object(self, queryset=None): return self.request.user
# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         context['view_title'] = _("My Profile")
# # #         return context

# # # class ParentProfileUpdateView(ParentLoginRequiredMixin, SuccessMessageMixin, UpdateView):
# # #     model = ParentUser
# # #     form_class = ParentProfileUpdateForm
# # #     template_name = 'parent_portal/profile_form.html'
# # #     success_url = reverse_lazy('parent_portal:profile_display')
# # #     success_message = _("Your profile has been updated successfully.")

# # #     def get_object(self, queryset=None): return self.request.user
# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         context['view_title'] = _("Edit My Profile")
# # #         return context
# # #     def form_invalid(self, form): # Add user feedback for invalid form
# # #         messages.error(self.request, _("Please correct the errors highlighted below."))
# # #         return super().form_invalid(form)


# # # from django.views.generic import ListView # Assuming it's a ListView

# # # from apps.common.mixins import ParentLoginRequiredMixin
    

# # # class ParentStudentDetailView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, TemplateView):
# # #     template_name = 'parent_portal/student_detail_dashboard.html' # As per your view
# # #     # ParentOwnsStudentMixin sets self.student and adds it to context as 'student' (if student_context_name is 'student')

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs) # This will include 'student' from ParentOwnsStudentMixin
# # #         student = context.get('student') # Or self.student, but safer from context if mixin guarantees it
        
# # #         if not student: # Should be caught by ParentOwnsStudentMixin, but good check
# # #             logger.error(f"ParentStudentDetailView: Student object not found in context or self for parent {self.request.user.email}")
# # #             raise Http404("Student not found or access denied.")

# # #         context['view_title'] = _(f"Overview for {student.get_full_name()}")
# # #         # 'student' is already in context thanks to ParentOwnsStudentMixin.get_context_data and super() call
        
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
# # #             try:
# # #                 context['school_profile'] = SchoolProfile.objects.first()
# # #             except Exception:
# # #                 context['school_profile'] = None
# # #         return context


# # # class ParentStudentDetailView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, TemplateView):
# # #     template_name = 'parent_portal/student_detail_dashboard.html' # As per your view
# # #     # ParentOwnsStudentMixin sets self.student and adds it to context as 'student' (if student_context_name is 'student')

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs) # This will include 'student' from ParentOwnsStudentMixin
# # #         student = context.get('student') # Or self.student, but safer from context if mixin guarantees it
        
# # #         if not student: # Should be caught by ParentOwnsStudentMixin, but good check
# # #             logger.error(f"ParentStudentDetailView: Student object not found in context or self for parent {self.request.user.email}")
# # #             raise Http404("Student not found or access denied.")

# # #         context['view_title'] = _(f"Overview for {student.get_full_name()}")
# # #         # 'student' is already in context thanks to ParentOwnsStudentMixin.get_context_data and super() call
        
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
# # #             try:
# # #                 context['school_profile'] = SchoolProfile.objects.first()
# # #             except Exception:
# # #                 context['school_profile'] = None
# # #         return context


# # # class ParentStudentFeesView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, ListView):
# # #     model = Invoice
# # #     template_name = 'parent_portal/student_fees.html'
# # #     context_object_name = 'student_invoices'
# # #     paginate_by = 10
# # #     # ParentOwnsStudentMixin sets self.student and adds 'student' to context

# # #     def get_queryset(self):
# # #         if not INVOICE_MODEL_IMPORTED or self.model is None:
# # #             return self.model.objects.none() if self.model else []
        
# # #         # self.student is set by ParentOwnsStudentMixin's dispatch
# # #         if not hasattr(self, 'student') or not self.student:
# # #             logger.warning(f"ParentStudentFeesView: self.student not set by ParentOwnsStudentMixin for parent {self.request.user.email}")
# # #             return self.model.objects.none() if self.model else []
            
# # #         return self.model.objects.filter(student=self.student).select_related('academic_year', 'term').order_by('-issue_date', '-created_at')

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs) # Includes 'student' from ParentOwnsStudentMixin
# # #         student = context.get('student') # Or self.student

# # #         if not student:
# # #             logger.error(f"ParentStudentFeesView: Student object not found in context or self for parent {self.request.user.email}")
# # #             # ListView might still try to render without it, but it's an issue.
# # #             # Or raise Http404 here.
# # #             context['view_title'] = _("Fee History Error")
# # #             context['total_student_outstanding'] = Decimal('0.00')
# # #             if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
# # #                 context['school_profile'] = SchoolProfile.objects.first()
# # #             return context

# # #         context['view_title'] = _(f"Fee History for {student.get_full_name()}")
        
# # #         total_outstanding = Decimal('0.00')
# # #         if INVOICE_MODEL_IMPORTED and Invoice:
# # #             # Aggregate on all invoices for this student, not just the paginated list (self.object_list)
# # #             all_student_invoices = Invoice.objects.filter(student=student)
# # #             try:
# # #                 from apps.fees.models import InvoiceStatus
# # #                 outstanding_statuses = [
# # #                     InvoiceStatus.SENT,
# # #                     InvoiceStatus.PARTIALLY_PAID,
# # #                     InvoiceStatus.OVERDUE
# # #                 ]
# # #                 # Use actual database fields for calculation
# # #                 outstanding_agg = all_student_invoices.filter(
# # #                     status__in=outstanding_statuses
# # #                 ).aggregate(
# # #                     total=Coalesce(
# # #                         Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
# # #                         Decimal('0.00'),
# # #                         output_field=DecimalField()
# # #                     )
# # #                 )
# # #                 total_outstanding = outstanding_agg.get('total', Decimal('0.00'))
# # #             except AttributeError:
# # #                 logger.error(f"ParentStudentFeesView: AttributeError accessing Invoice.InvoiceStatus or missing Invoice field.")
# # #             except Exception as e:
# # #                 logger.error(f"ParentStudentFeesView: Error calculating outstanding for student {student.pk}: {e}", exc_info=True)

# # #         context['total_student_outstanding'] = total_outstanding
        
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'):
# # #             try:
# # #                 context['school_profile'] = SchoolProfile.objects.first()
# # #             except Exception:
# # #                 context['school_profile'] = None
# # #         return context


# # # class ParentStudentPaymentHistoryView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, ListView): # For listing payments
# # #     model = Payment
# # #     template_name = 'parent_portal/student_payment_history_list.html'
# # #     context_object_name = 'student_payments'
# # #     paginate_by = 10
# # #     # ParentOwnsStudentMixin sets self.student

# # #     def get_queryset(self):
# # #         if not PAYMENT_MODEL_IMPORTED: return Payment.objects.none()
# # #         # Payments directly for student OR payments allocated to invoices for this student
# # #         return Payment.objects.filter(
# # #             Q(student=self.student) | Q(allocations__invoice__student=self.student)
# # #         ).select_related('payment_method', 'academic_year').prefetch_related('allocations__invoice').distinct().order_by('-payment_date', '-created_at')

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         student = self.student
# # #         context['view_title'] = _(f"Payment History for {student.get_full_name()}")
# # #         context['student'] = student
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and hasattr(self.request, 'tenant'):
# # #             try: context['school_profile'] = SchoolProfile.objects.get()
# # #             except: context['school_profile'] = None
# # #         return context
    
    
# # # class ParentAllChildrenPaymentHistoryView(ParentLoginRequiredMixin, ListView): # No ParentOwnsStudentMixin needed
# # #     model = Payment
# # #     template_name = 'parent_portal/payment_history_all_children_list.html' # A different template
# # #     context_object_name = 'all_payments'
# # #     paginate_by = 20

# # #     def get_queryset(self):
# # #         if not Payment or not Student: # Check models loaded
# # #             return self.model.objects.none()

# # #         parent = self.request.user
# # #         # Get PKs of all active students linked to this parent
# # #         student_pks = parent.children.filter(is_active=True).values_list('pk', flat=True)

# # #         if not student_pks:
# # #             return Payment.objects.none()
            
# # #         queryset = Payment.objects.filter(
# # #             Q(student_id__in=student_pks) | Q(invoice__student_id__in=student_pks)
# # #         ).select_related(
# # #             'student', 'invoice', 'invoice__student', 'payment_method', 'academic_year'
# # #         ).distinct().order_by('-payment_date', '-created_at')
        
# # #         logger.debug(f"Fetching all payment history for parent {parent.email}, children PKs: {list(student_pks)}. Found {queryset.count()} payments.")
# # #         return queryset

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         context['view_title'] = _("My Full Payment History (All Children)")
# # #         context['parent'] = self.request.user # For template display

# # #         school_profile_instance = None
# # #         if SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
# # #             try: school_profile_instance = SchoolProfile.objects.get()
# # #             except (SchoolProfile.DoesNotExist, SchoolProfile.MultipleObjectsReturned): pass
# # #         context['school_profile'] = school_profile_instance
# # #         return context


# # # # D:\school_fees_saas_v2\apps\parent_portal\views.py

# # # # ... other imports ...
# # # from django.views.generic import TemplateView
# # # from django.db.models import Sum, F, DecimalField, Q
# # # from django.db.models.functions import Coalesce
# # # from decimal import Decimal

# # # # Import your models (ensure paths are correct)
# # # from apps.students.models import Student, ParentUser # Assuming Student is here too
# # # from apps.fees.models import Invoice
# # # from apps.schools.models import SchoolProfile # Or wherever your SchoolProfile is

# # # from apps.common.mixins import ParentLoginRequiredMixin # Your existing mixin

# # # import logging
# # # logger = logging.getLogger(__name__)


# # # class MyChildrenListView(ParentLoginRequiredMixin, ListView):
# # #     model = Student # Set directly if Student is imported
# # #     template_name = 'parent_portal/my_children_list.html'
# # #     context_object_name = 'linked_students' # To match common usage in templates
# # #     paginate_by = 10

# # #     def get_queryset(self):
# # #         if not STUDENT_MODEL_IMPORTED or self.model is None or \
# # #             not PARENT_USER_MODEL_IMPORTED or ParentUser is None or \
# # #             not isinstance(self.request.user, ParentUser):
# # #             logger.warning("MyChildrenListView: Prerequisites not met (Student/ParentUser model or user type).")
# # #             return self.model.objects.none() if self.model else []
        
# # #         try:
# # #             # Assuming ParentUser.children is the M2M accessor
# # #             return self.request.user.children.filter(is_active=True).order_by('first_name', 'last_name')
# # #         except AttributeError:
# # #             logger.error(f"MyChildrenListView: ParentUser {self.request.user.email} has no 'children' attribute.")
# # #             return self.model.objects.none() if self.model else []

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         context['view_title'] = _("My Children")
# # #         if PARENT_USER_MODEL_IMPORTED and ParentUser and isinstance(self.request.user, ParentUser):
# # #             context['parent'] = self.request.user # Make parent available
# # #         return context



# # # class ChildrenFeesSummaryView(ParentLoginRequiredMixin, TemplateView):
# # #     template_name = 'parent_portal/children_fees_summary.html'

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         parent = self.request.user # This is a ParentUser instance due to the mixin
        
# # #         context['view_title'] = _("My Children's Fees Summary")
# # #         context['parent'] = parent

# # #         children_fee_details = []
# # #         grand_total_invoiced = Decimal('0.00')
# # #         grand_total_paid = Decimal('0.00')
# # #         grand_total_outstanding = Decimal('0.00')

# # #         if Student and hasattr(parent, 'children'):
# # #             try:
# # #                 linked_students = parent.children.filter(is_active=True).order_by('first_name', 'last_name')
                
# # #                 for student in linked_students:
# # #                     student_data = {
# # #                         'student_obj': student,
# # #                         'total_invoiced': Decimal('0.00'),
# # #                         'total_paid': Decimal('0.00'),
# # #                         'total_outstanding': Decimal('0.00'),
# # #                         'has_outstanding': False
# # #                     }

# # #                     if Invoice: # Check if Invoice model is available
# # #                         # Aggregate invoiced amounts (all invoices, regardless of status, represent an amount billed)
# # #                         # Use actual database fields: net_billable_amount = subtotal_amount - total_concession_amount
# # #                         invoiced_agg = Invoice.objects.filter(student=student).aggregate(
# # #                             sum_invoiced=Coalesce(
# # #                                 Sum(F('subtotal_amount') - F('total_concession_amount')),
# # #                                 Decimal('0.00'),
# # #                                 output_field=DecimalField()
# # #                             )
# # #                         )
# # #                         student_data['total_invoiced'] = invoiced_agg['sum_invoiced']

# # #                         # Aggregate paid amounts
# # #                         paid_agg = Invoice.objects.filter(student=student).aggregate(
# # #                             sum_paid=Coalesce(Sum('amount_paid'), Decimal('0.00'), output_field=DecimalField())
# # #                         )
# # #                         student_data['total_paid'] = paid_agg['sum_paid']

# # #                         # Calculate outstanding for this student
# # #                         student_data['total_outstanding'] = student_data['total_invoiced'] - student_data['total_paid']
# # #                         if student_data['total_outstanding'] > Decimal('0.00'):
# # #                             student_data['has_outstanding'] = True
                        
# # #                         # Add to grand totals
# # #                         grand_total_invoiced += student_data['total_invoiced']
# # #                         grand_total_paid += student_data['total_paid']
# # #                         grand_total_outstanding += student_data['total_outstanding']
                    
# # #                     children_fee_details.append(student_data)
                
# # #                 logger.debug(f"ChildrenFeesSummaryView: Prepared fee details for {len(children_fee_details)} children of parent {parent.email}")

# # #             except Exception as e:
# # #                 logger.error(f"ChildrenFeesSummaryView: Error processing student fees for parent {parent.email}: {e}", exc_info=True)
# # #                 messages.error(self.request, _("An error occurred while retrieving fee details for your children."))
        
# # #         context['children_fee_details'] = children_fee_details
# # #         context['grand_total_invoiced'] = grand_total_invoiced
# # #         context['grand_total_paid'] = grand_total_paid
# # #         context['grand_total_outstanding'] = grand_total_outstanding
        
# # #         # Get School Profile for currency symbol etc.
# # #         school_profile_instance = None
# # #         if SchoolProfile and hasattr(self.request, 'tenant') and self.request.tenant:
# # #             try:
# # #                 school_profile_instance = SchoolProfile.objects.first() # Assuming one profile per tenant schema
# # #             except Exception as e:
# # #                 logger.error(f"ChildrenFeesSummaryView: Error fetching SchoolProfile: {e}")
# # #         context['school_profile'] = school_profile_instance
        
# # #         logger.info(f"ChildrenFeesSummaryView: Context prepared for parent {parent.email}. Children details count: {len(children_fee_details)}")
# # #         return context
    


# # # # D:\school_fees_saas_v2\apps\parent_portal\views.py
# # # # ... (other imports: TemplateView, ParentLoginRequiredMixin, ParentUser, Student, Invoice, SchoolProfile, Decimal, Sum, F, Coalesce, DecimalField, _, logger, messages)

# # # class MakePaymentSummaryView(ParentLoginRequiredMixin, TemplateView):
# # #     template_name = 'parent_portal/make_payment_summary.html'

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         parent = self.request.user
# # #         tenant = self.request.tenant # Available due to TenantMainMiddleware

# # #         context['view_title'] = _("Review and Pay Fees")
# # #         context['parent'] = parent

# # #         outstanding_invoices_qs = Invoice.objects.none()
# # #         total_amount_to_pay_val = Decimal('0.00')

# # #         if PARENT_USER_MODEL_IMPORTED and ParentUser and Student and INVOICE_MODEL_IMPORTED and Invoice and hasattr(parent, 'children'):
# # #             try:
# # #                 # Get IDs of students linked to this parent
# # #                 student_pks = list(parent.children.filter(is_active=True).values_list('pk', flat=True))
                
# # #                 if student_pks:
# # #                     outstanding_statuses = [
# # #                         Invoice.InvoiceStatus.SENT,
# # #                         Invoice.InvoiceStatus.PARTIALLY_PAID,
# # #                         Invoice.InvoiceStatus.OVERDUE
# # #                     ]
                    
# # #                     # Fetch all outstanding invoices for these students
# # #                     outstanding_invoices_qs = Invoice.objects.filter(
# # #                         student_id__in=student_pks,
# # #                         status__in=outstanding_statuses,
# # #                         # Optional: Ensure balance_due > 0 if balance_due is a calculated field not always positive
# # #                     ).select_related('student', 'academic_year', 'term').order_by('student__first_name', 'due_date')

# # #                     # Calculate total amount to pay from these outstanding invoices
# # #                     # Assuming Invoice model has a 'balance_due' @property or you calculate it
# # #                     # For simplicity, let's recalculate here if 'balance_due' is not a direct field/property
# # #                     # or sum up a 'balance_due' field if it exists.
                    
# # #                     # If Invoice has balance_due property or field:
# # #                     # total_amount_to_pay_val = outstanding_invoices_qs.aggregate(
# # #                     #     total=Coalesce(Sum('balance_due'), Decimal('0.00'), output_field=DecimalField())
# # #                     # )['total']

# # #                     # Or, calculating it again:
# # #                     aggregation = outstanding_invoices_qs.aggregate(
# # #                         total_payable=Coalesce(
# # #                             Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')),
# # #                             Decimal('0.00'),
# # #                             output_field=DecimalField()
# # #                         )
# # #                     )
# # #                     total_amount_to_pay_val = aggregation.get('total_payable', Decimal('0.00'))

# # #                     logger.debug(f"MakePaymentSummaryView: Found {outstanding_invoices_qs.count()} outstanding invoices for parent {parent.email}, total: {total_amount_to_pay_val}")
# # #                 else:
# # #                     logger.debug(f"MakePaymentSummaryView: Parent {parent.email} has no active students linked.")
# # #             except Exception as e:
# # #                 logger.error(f"MakePaymentSummaryView: Error fetching outstanding invoices for parent {parent.email}: {e}", exc_info=True)
# # #                 messages.error(self.request, _("Could not retrieve outstanding fee details."))
        
# # #         context['outstanding_invoices'] = outstanding_invoices_qs
# # #         context['total_amount_to_pay'] = total_amount_to_pay_val
        
# # #         school_profile_instance = None
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and tenant:
# # #             try:
# # #                 school_profile_instance = SchoolProfile.objects.first() # Specific to current tenant
# # #             except Exception as e:
# # #                 logger.error(f"MakePaymentSummaryView: Error fetching SchoolProfile: {e}")
# # #         context['school_profile'] = school_profile_instance
        
# # #         # For Payfast/Ozow integration later, you'd prepare payment gateway data here:
# # #         # context['payment_gateway_data'] = self.prepare_payment_gateway_data(total_amount_to_pay_val, parent, outstanding_invoices_qs)
        
# # #         logger.info(f"MakePaymentSummaryView: Context prepared for parent {parent.email}. Amount to pay: {total_amount_to_pay_val}")
# # #         return context

# # #     # def prepare_payment_gateway_data(self, amount, parent, invoices):
# # #     #     # Logic to generate merchant_id, merchant_key, return_url, cancel_url, notify_url,
# # #     #     # item_name, item_description, amount, custom_str1 (e.g., parent_id), etc.
# # #     #     # This data would be used to build the hidden form fields for the payment gateway.
# # #     #     gateway_data = {}
# # #     #     # ...
# # #     #     return gateway_data
    


# # # class ParentPaymentHistoryView(ParentLoginRequiredMixin, TemplateView):
# # #     template_name = 'parent_portal/payment_history_list.html' # Using _list for consistency, create this template

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         parent = self.request.user
# # #         context['view_title'] = _("My Payment History")
# # #         context['parent'] = parent
        
# # #         # TODO: Fetch actual payment records linked to this parent/their children
# # #         # For example, if you have a Payment model:
# # #         # from apps.payments.models import Payment
# # #         # context['payments'] = Payment.objects.filter(
# # #         #     invoice__student__parents=parent # Example of a deep relation
# # #         # ).order_by('-payment_date')
# # #         context['payments'] = [] # Placeholder
        
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile and hasattr(self.request, 'tenant'): # Ensure SchoolProfile is available
# # #             try: context['school_profile'] = SchoolProfile.objects.first()
# # #             except: context['school_profile'] = None

# # #         logger.info(f"ParentPaymentHistoryView: Context prepared for parent {parent.email}")
# # #         return context




# # # # apps/parent_portal/views.py
# # # # ... (existing imports: TemplateView, ParentLoginRequiredMixin, Invoice, Student, ParentUser, _, messages, logger, Decimal, Q, etc.) ...
# # # from django.views.generic.edit import FormView # For handling the form submission
# # # from django.urls import reverse, reverse_lazy
# # # from .forms import SelectInvoicesForPaymentForm # We'll create this form

# # # class SelectInvoicesForPaymentView(ParentLoginRequiredMixin, FormView):
# # #     template_name = 'parent_portal/select_invoices_for_payment.html'
# # #     form_class = SelectInvoicesForPaymentForm # A form to handle the selection
# # #     success_url = reverse_lazy('parent_portal:mock_payment_gateway') # Redirect to mock gateway

# # #     def get_form_kwargs(self):
# # #         # Pass the parent user to the form so it can fetch their children's invoices
# # #         kwargs = super().get_form_kwargs()
# # #         kwargs['parent'] = self.request.user

# # #         # Check if a specific invoice is pre-selected via URL parameter
# # #         invoice_id = self.request.GET.get('invoice')
# # #         if invoice_id:
# # #             kwargs['preselected_invoice_id'] = invoice_id

# # #         return kwargs

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)
# # #         parent = self.request.user
# # #         context['view_title'] = _("Pay School Fees")
# # #         context['parent'] = parent

# # #         # The form itself will handle displaying the invoices and selection
# # #         # We can also pass the invoices to the context separately if needed for display outside the form
        
# # #         # Fetch outstanding invoices for all children of this parent
# # #         # This is similar to what the form will do, but can be used for display
# # #         outstanding_invoices_qs = Invoice.objects.none()
# # #         if INVOICE_MODEL_IMPORTED and PARENT_USER_MODEL_IMPORTED and Student and isinstance(parent, ParentUser):
# # #             try:
# # #                 children_pks = parent.children.filter(is_active=True).values_list('pk', flat=True)
# # #                 if children_pks:
# # #                     from apps.fees.models import InvoiceStatus
# # #                     outstanding_statuses = [
# # #                         InvoiceStatus.SENT,
# # #                         InvoiceStatus.PARTIALLY_PAID,
# # #                         InvoiceStatus.OVERDUE
# # #                     ]
# # #                     outstanding_invoices_qs = Invoice.objects.filter(
# # #                         student_id__in=children_pks,
# # #                         status__in=outstanding_statuses,
# # #                     ).exclude( # Using exclude to ensure balance_due > 0 effectively
# # #                         # balance_due <= 0 means: (subtotal_amount - total_concession_amount - amount_paid) <= 0
# # #                         Q(subtotal_amount__lte=F('total_concession_amount') + F('amount_paid'))
# # #                     ).select_related('student').order_by('student__first_name', 'due_date')
# # #                 context['outstanding_invoices'] = outstanding_invoices_qs
# # #                 logger.debug(f"SelectInvoicesView: Found {outstanding_invoices_qs.count()} outstanding invoices for parent {parent.email}")
# # #             except Exception as e:
# # #                 logger.error(f"SelectInvoicesView: Error fetching outstanding invoices for parent {parent.email}: {e}", exc_info=True)
# # #                 context['outstanding_invoices'] = Invoice.objects.none()
        
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and hasattr(self.request, 'tenant'):
# # #             try: context['school_profile'] = SchoolProfile.objects.first()
# # #             except: context['school_profile'] = None

# # #         return context

# # #     def form_valid(self, form):
# # #         # This method is called when the form (containing selected invoice IDs) is submitted and valid.
# # #         selected_invoice_ids = form.cleaned_data.get('selected_invoices') # From ModelMultipleChoiceField
        
# # #         if not selected_invoice_ids:
# # #             messages.error(self.request, _("Please select at least one invoice to pay."))
# # #             return self.form_invalid(form)

# # #         # Store selected invoice IDs and total amount in session to pass to the mock gateway
# # #         # A more robust way for real gateways would be to create a PaymentIntent/Order record here.
# # #         invoices_to_pay = Invoice.objects.filter(pk__in=[inv.pk for inv in selected_invoice_ids])
# # #         total_to_pay = Decimal('0.00')
# # #         invoice_details_for_session = []

# # #         for inv in invoices_to_pay:
# # #             if inv.balance_due > 0: # Ensure we are only processing invoices with a balance
# # #                 total_to_pay += inv.balance_due
# # #                 invoice_details_for_session.append({
# # #                     'id': inv.pk,
# # #                     'number': inv.invoice_number,
# # #                     'amount_due': str(inv.balance_due) # Store as string for session
# # #                 })
# # #             else:
# # #                 messages.warning(self.request, _(f"Invoice {inv.invoice_number} has no outstanding balance and was excluded."))


# # #         if total_to_pay <= 0:
# # #             messages.error(self.request, _("No amount to pay for the selected invoices."))
# # #             return self.form_invalid(form)

# # #         self.request.session['payment_context'] = {
# # #             'invoice_ids': [inv['id'] for inv in invoice_details_for_session],
# # #             'invoice_details': invoice_details_for_session, # For display on mock page
# # #             'total_amount_to_pay': str(total_to_pay),
# # #             'currency': self.request.tenant.schoolprofile.currency_code if self.request.tenant.schoolprofile else 'USD' # Example
# # #         }
# # #         logger.info(f"SelectInvoicesView: Parent {self.request.user.email} selected {len(invoice_details_for_session)} invoices, total {total_to_pay}. Redirecting to mock gateway.")
# # #         return super().form_valid(form) # This will redirect to success_url


# # # class ParentInvoiceDetailView(ParentLoginRequiredMixin, DetailView):
# # #     """Allow parents to view invoice details for their children's invoices"""
# # #     model = Invoice
# # #     template_name = 'fees/invoice_detail.html'  # Reuse the existing template
# # #     context_object_name = 'invoice'

# # #     def get_queryset(self):
# # #         """Ensure parents can only view invoices for their children"""
# # #         if not INVOICE_MODEL_IMPORTED or not PARENT_USER_MODEL_IMPORTED:
# # #             return Invoice.objects.none()

# # #         parent = self.request.user
# # #         if not hasattr(parent, 'children'):
# # #             return Invoice.objects.none()

# # #         # Get student IDs for this parent's children
# # #         student_ids = list(parent.children.filter(is_active=True).values_list('pk', flat=True))

# # #         return Invoice.objects.filter(
# # #             student_id__in=student_ids
# # #         ).select_related(
# # #             'student', 'academic_year', 'term', 'created_by',
# # #             'student__current_class', 'student__current_section'
# # #         ).prefetch_related(
# # #             'details__fee_head', 'details__concession_type'
# # #         )

# # #     def get_context_data(self, **kwargs):
# # #         context = super().get_context_data(**kwargs)

# # #         # Add school profile for template
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile:
# # #             try:
# # #                 context['school_profile'] = SchoolProfile.objects.first()
# # #             except:
# # #                 context['school_profile'] = None

# # #         # Add user type flags for template
# # #         context['user_type_flags'] = {
# # #             'IS_TENANT_PARENT_USER': True,
# # #             'IS_TENANT_STAFF_USER': False
# # #         }

# # #         return context


# # class ParentInvoicePDFView(ParentLoginRequiredMixin, View):
# #     """Allow parents to view PDF of their children's invoices"""

# #     def get(self, request, pk):
# #         if not INVOICE_MODEL_IMPORTED or not PARENT_USER_MODEL_IMPORTED:
# # #             raise Http404("Invoice system not available")

# # #         parent = request.user
# # #         if not hasattr(parent, 'children'):
# # #             raise Http404("No children found")

# # #         # Get student IDs for this parent's children
# # #         student_ids = list(parent.children.filter(is_active=True).values_list('pk', flat=True))

# # #         # Get the invoice, ensuring it belongs to one of parent's children
# # #         try:
# # #             invoice = Invoice.objects.select_related(
# # #                 'student', 'academic_year', 'term', 'created_by',
# # #                 'student__current_class', 'student__current_section'
# # #             ).prefetch_related(
# # #                 'details__fee_head', 'details__concession_type'
# # #             ).get(pk=pk, student_id__in=student_ids)
# # #         except Invoice.DoesNotExist:
# # #             raise Http404("Invoice not found or access denied")

# # #         # Get school profile
# # #         school_profile = None
# # #         if SCHOOL_PROFILE_MODEL_IMPORTED and SchoolProfile:
# # #             try:
# # #                 school_profile = SchoolProfile.objects.first()
# # #             except:
# # #                 pass

# # #         # Check if PDF generation is available
# # #         if not PDF_AVAILABLE:
# # #             return HttpResponse("PDF generation is not available on this system.", status=500)

# # #         # Prepare context for PDF template
# # #         context = {
# # #             'invoice': invoice,
# # #             'school_profile': school_profile,
# # #             'tenant': request.tenant if hasattr(request, 'tenant') else None,
# # #         }

# # #         # Generate PDF using the same template as staff
# # #         pdf = render_to_pdf('fees/pdf/invoice_pdf_template.html', context)

# # #         if pdf:
# # #             response = HttpResponse(pdf, content_type='application/pdf')
# # #             filename = f"Invoice-{invoice.invoice_number_display or f'Draft-{invoice.pk}'}.pdf"

# # #             # Handle different PDF options based on query parameters
# # #             if request.GET.get('download'):
# # #                 response['Content-Disposition'] = f'attachment; filename="{filename}"'
# # #             else:
# # #                 response['Content-Disposition'] = f'inline; filename="{filename}"'

# # #             return response

# # #         return HttpResponse("Error generating PDF.", status=500)
    
    



# # # apps/parent_portal/views.py
# # # ... (existing imports: TemplateView, ParentLoginRequiredMixin, _, messages, logger, reverse, redirect, reverse_lazy) ...
# # from django.http import HttpResponseForbidden # For cases where session data is missing

# # class MockPaymentGatewayView(ParentLoginRequiredMixin, TemplateView):
# #     template_name = 'parent_portal/mock_payment_gateway.html'

# #     def get_context_data(self, **kwargs):
# #         context = super().get_context_data(**kwargs)
# #         context['view_title'] = _("Simulated Payment Gateway")
        
# #         payment_context = self.request.session.get('payment_context')
# #         if not payment_context:
# #             logger.warning(f"MockPaymentGatewayView: No payment_context in session for user {self.request.user.email}. Redirecting.")
# #             messages.error(self.request, _("Payment session expired or is invalid. Please try selecting invoices again."))
# #             # Redirecting to select_invoices might be better if that's where they came from
# #             # but needs a robust way to handle if that URL also requires session data.
# #             # For now, redirect to dashboard.
# #             # To prevent infinite loop if dashboard also has issues, use a known good URL.
# #             # This should ideally not happen if the flow is correct.
# #             # kwargs['bypass_to_url'] = reverse_lazy('parent_portal:select_invoices_for_payment') # This would cause redirect loop if no context
# #             kwargs['payment_error'] = True # Flag for template
# #             return context

# #         context['invoice_details'] = payment_context.get('invoice_details', [])
# #         context['total_amount_to_pay'] = payment_context.get('total_amount_to_pay', '0.00')
# #         context['currency'] = payment_context.get('currency', '$')
# #         context['parent'] = self.request.user # For display if needed

# #         logger.info(f"MockPaymentGatewayView: Displaying mock payment page for user {self.request.user.email}, amount {context['total_amount_to_pay']} {context['currency']}")
# #         return context

# #     def dispatch(self, request, *args, **kwargs):
# #         # Check for payment_context early in dispatch
# #         payment_context = request.session.get('payment_context')
# #         if not payment_context:
# #             logger.warning(f"MockPaymentGatewayView Dispatch: No payment_context in session for user {request.user.email}.")
# #             messages.error(request, _("Your payment session is invalid or has expired. Please start over by selecting invoices."))
# #             return redirect(reverse_lazy('parent_portal:select_invoices_for_payment')) # Redirect to selection page
# #         return super().dispatch(request, *args, **kwargs)





# # # apps/parent_portal/views.py
# # # ... (existing imports: View, ParentLoginRequiredMixin, _, messages, logger, reverse, redirect, reverse_lazy) ...
# # # We'll need a function to do the actual recording, let's call it record_parent_payment
# # from apps.payments.services import record_parent_payment # We will create this service function

# # class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Using base View for simple POST handling
    
# #     def post(self, request, *args, **kwargs):
# #         payment_context = request.session.get('payment_context')
# #         payment_outcome = request.POST.get('payment_outcome')

# #         if not payment_context:
# #             logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
# #             messages.error(request, _("Your payment session has expired. Please select invoices again."))
# #             return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

# #         # Clear the payment context from session after retrieving it
# #         # to prevent re-processing or issues if user hits back button.
# #         # Do this early, but make sure all needed data is extracted first.
# #         # For real gateways, this context might be tied to a PaymentIntent object.
        
# #         invoice_ids = payment_context.get('invoice_ids', [])
# #         total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
# #         total_amount_paid = Decimal(total_amount_paid_str) # Convert back to Decimal
        
# #         # Remove payment_context from session now that we have what we need
# #         if 'payment_context' in request.session:
# #             del request.session['payment_context']
# #             request.session.modified = True # Ensure session is saved

# #         if payment_outcome == 'success':
# #             logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
            
# #             # --- Call the Payment Recording Logic ---
# #             try:
# #                 # Pass necessary info to the recording service/function
# #                 # The service function will handle creating Payment, PaymentAllocations, JEs, etc.
# #                 payment_successful, message, payment_instance = record_parent_payment(
# #                     parent_user=request.user,
# #                     tenant=request.tenant, # Make sure request.tenant is available
# #                     invoice_pks_to_pay=invoice_ids,
# #                     amount_paid_by_parent=total_amount_paid,
# #                     payment_method_code="ONLINE_MOCK", # Specific code for mock payments
# #                     transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}" # Example mock ref
# #                 )

# #                 if payment_successful:
# #                     messages.success(request, message or _("Payment processed successfully!"))
# #                     # Redirect to a success page, perhaps showing payment details
# #                     # Or redirect to the payment history page
# #                     return redirect(reverse_lazy('parent_portal:mock_payment_success', kwargs={'payment_pk': payment_instance.pk if payment_instance else 0}))
# #                 else:
# #                     messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
# #                     return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

# #             except Exception as e:
# #                 logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
# #                 messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
# #                 return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

# #         elif payment_outcome == 'failure':
# #             logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
# #             messages.error(request, _("Your simulated payment failed as requested."))
# #             return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
# #         else:
# #             logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
# #             messages.error(request, _("Invalid payment simulation outcome."))
# #             return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
    
# #     class ProcessMockPaymentView(ParentLoginRequiredMixin, View): # Now 'View' is defined
# #         template_name = None # Not directly rendering a template, but handling POST and redirecting

# #     def post(self, request, *args, **kwargs):
# #         # ... your existing post logic from the previous response ...
# #         payment_context = request.session.get('payment_context')
# #         payment_outcome = request.POST.get('payment_outcome')

# #         if not payment_context:
# #             logger.warning(f"ProcessMockPaymentView: No payment_context in session for user {request.user.email} during POST. Aborting.")
# #             messages.error(request, _("Your payment session has expired. Please select invoices again."))
# #             return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))
        
# #         invoice_ids = payment_context.get('invoice_ids', [])
# #         total_amount_paid_str = payment_context.get('total_amount_to_pay', '0.00')
# #         total_amount_paid = Decimal(total_amount_paid_str)
        
# #         if 'payment_context' in request.session:
# #             del request.session['payment_context']
# #             request.session.modified = True

# #         if payment_outcome == 'success':
# #             logger.info(f"ProcessMockPaymentView: Simulating SUCCESSFUL payment for user {request.user.email}, amount {total_amount_paid_str}.")
# #             try:
# #                 payment_successful, message, payment_instance = record_parent_payment(
# #                     parent_user=request.user,
# #                     tenant=request.tenant,
# #                     invoice_pks_to_pay=invoice_ids,
# #                     amount_paid_by_parent=total_amount_paid,
# #                     payment_method_code="ONLINE_MOCK",
# #                     transaction_reference=f"MOCKPAY_{timezone.now().strftime('%Y%m%d%H%M%S')}"
# #                 )
# #                 if payment_successful:
# #                     messages.success(request, message or _("Payment processed successfully!"))
# #                     success_url_name = 'parent_portal:mock_payment_success'
# #                     # Pass payment_pk if payment_instance is not None
# #                     success_kwargs = {'payment_pk': payment_instance.pk} if payment_instance else {}
# #                     if not payment_instance: # If payment_instance is None, can't use pk. Maybe simple success page.
# #                         # This case should ideally be handled by record_parent_payment returning False earlier
# #                         logger.error("ProcessMockPaymentView: payment_instance is None after successful record_parent_payment call. This is unexpected.")
# #                         # Fallback to a generic success page if you have one, or dashboard.
# #                         return redirect(reverse_lazy('parent_portal:dashboard')) # Or a generic payment success page

# #                     return redirect(reverse_lazy(success_url_name, kwargs=success_kwargs))
# #                 else:
# #                     messages.error(request, message or _("There was an issue recording your payment. Please contact support."))
# #                     return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
# #             except Exception as e:
# #                 logger.error(f"ProcessMockPaymentView: Exception during record_parent_payment for user {request.user.email}: {e}", exc_info=True)
# #                 messages.error(request, _("A critical error occurred while processing your payment. Please contact support immediately."))
# #                 return redirect(reverse_lazy('parent_portal:mock_payment_failure'))

# #         elif payment_outcome == 'failure':
# #             logger.info(f"ProcessMockPaymentView: Simulating FAILED payment for user {request.user.email}.")
# #             messages.error(request, _("Your simulated payment failed as requested."))
# #             return redirect(reverse_lazy('parent_portal:mock_payment_failure'))
# #         else:
# #             logger.warning(f"ProcessMockPaymentView: Invalid payment_outcome '{payment_outcome}' for user {request.user.email}.")
# #             messages.error(request, _("Invalid payment simulation outcome."))
# #             return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))

# #     def get(self, request, *args, **kwargs):
# #         # POST-only view, redirect GET requests
# #         logger.warning(f"ProcessMockPaymentView: GET request received, redirecting. User: {request.user.email}")
# #         messages.error(request, _("This action must be performed via a form submission."))
# #         return redirect(reverse_lazy('parent_portal:select_invoices_for_payment'))








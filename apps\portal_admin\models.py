# apps/portal_admin/models.py

from django.db import models
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

class AdminActivityLog(models.Model):
    """
    Logs administrative and significant actions performed within a tenant's schema
    or by public administrators (associated with settings.AUTH_USER_MODEL).
    """

    # --- Action Type Constants (as class attributes for direct access) ---
    ACTION_CREATE = 'CREATE'
    ACTION_UPDATE = 'UPDATE'
    ACTION_DELETE = 'DELETE'
    ACTION_LOGIN_SUCCESS = 'LOGIN_SUCCESS'
    ACTION_LOGIN_FAILED = 'LOGIN_FAILED'
    ACTION_LOGOUT = 'LOGOUT'
    ACTION_PASSWORD_CHANGE = 'PASSWORD_CHANGE'
    ACTION_PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST'
    ACTION_PASSWORD_RESET_COMPLETE = 'PASSWORD_RESET_COMPLETE'
    ACTION_USER_REGISTERED = 'USER_REGISTERED'
    ACTION_PERMISSION_CHANGE = 'PERMISSION_CHANGE' # THIS IS THE CONSTANT
    ACTION_ROLE_ASSIGNMENT = 'ROLE_ASSIGNMENT'
    ACTION_ROLE_CREATED = 'ROLE_CREATED'
    ACTION_ROLE_UPDATED = 'ROLE_UPDATED'
    ACTION_ROLE_DELETED = 'ROLE_DELETED'
    ACTION_SCHOOL_PROFILE_UPDATE = 'SCHOOL_PROFILE_UPDATE'
    ACTION_ACADEMIC_YEAR_SETUP = 'ACADEMIC_YEAR_SETUP'
    ACTION_STAFF_CREATED = 'STAFF_CREATED'
    ACTION_STAFF_UPDATED = 'STAFF_UPDATED'
    ACTION_STAFF_DEACTIVATED = 'STAFF_DEACTIVATED'
    ACTION_STAFF_REACTIVATED = 'STAFF_REACTIVATED'
    ACTION_LEAVE_APPLIED = 'LEAVE_APPLIED'
    ACTION_LEAVE_APPROVED = 'LEAVE_APPROVED'
    ACTION_LEAVE_REJECTED = 'LEAVE_REJECTED'
    ACTION_LEAVE_CANCELLED = 'LEAVE_CANCELLED'
    ACTION_STUDENT_ADMITTED = 'STUDENT_ADMITTED'
    ACTION_STUDENT_UPDATED = 'STUDENT_UPDATED'
    ACTION_STUDENT_PROMOTED = 'STUDENT_PROMOTED'
    ACTION_STUDENT_WITHDRAWN = 'STUDENT_WITHDRAWN'
    ACTION_FEE_STRUCTURE_CREATED = 'FEE_STRUCTURE_CREATED'
    ACTION_FEE_STRUCTURE_UPDATED = 'FEE_STRUCTURE_UPDATED'
    ACTION_INVOICE_GENERATED = 'INVOICE_GENERATED'
    ACTION_INVOICE_SENT = 'INVOICE_SENT'
    ACTION_INVOICE_UPDATED = 'INVOICE_UPDATED'
    ACTION_INVOICE_CANCELLED = 'INVOICE_CANCELLED'
    ACTION_PAYMENT_RECORDED = 'PAYMENT_RECORDED'
    ACTION_PAYMENT_REFUNDED = 'PAYMENT_REFUNDED'
    ACTION_CONCESSION_APPLIED = 'CONCESSION_APPLIED'
    ACTION_EXPENSE_RECORDED = 'EXPENSE_RECORDED'
    ACTION_BUDGET_CREATED = 'BUDGET_CREATED'
    ACTION_BUDGET_UPDATED = 'BUDGET_UPDATED'
    ACTION_MANUAL_JE_CREATED = 'MANUAL_JE_CREATED'
    ACTION_SYSTEM_NOTIFICATION_SENT = 'SYSTEM_NOTIFICATION_SENT'
    ACTION_DATA_EXPORT = 'DATA_EXPORT'
    ACTION_DATA_IMPORT = 'DATA_IMPORT'
    ACTION_SETTINGS_CHANGED = 'SETTINGS_CHANGED'
    ACTION_SYSTEM_ERROR_LOGGED = 'SYSTEM_ERROR_LOGGED'
    ACTION_GENERIC_ADMIN_ACTION = 'GENERIC_ADMIN_ACTION'

    # --- Choices for action_type field (using the class-level constants) ---
    ACTION_TYPES_CHOICES = [
        (ACTION_CREATE, _('Object Created')),
        (ACTION_UPDATE, _('Object Updated')),
        (ACTION_DELETE, _('Object Deleted')),
        (ACTION_LOGIN_SUCCESS, _('Login Success')),
        (ACTION_LOGIN_FAILED, _('Login Failed')),
        (ACTION_LOGOUT, _('Logout')),
        (ACTION_PASSWORD_CHANGE, _('Password Changed')),
        (ACTION_PASSWORD_RESET_REQUEST, _('Password Reset Requested')),
        (ACTION_PASSWORD_RESET_COMPLETE, _('Password Reset Completed')),
        (ACTION_USER_REGISTERED, _('User Registered')),
        (ACTION_PERMISSION_CHANGE, _('Permissions Changed for Role/Group')), # Uses the constant
        (ACTION_ROLE_ASSIGNMENT, _('Role/Group Assignment to User')),
        (ACTION_ROLE_CREATED, _('Role/Group Created')),
        (ACTION_ROLE_UPDATED, _('Role/Group Updated')),
        (ACTION_ROLE_DELETED, _('Role/Group Deleted')),
        (ACTION_SCHOOL_PROFILE_UPDATE, _('School Profile Updated')),
        (ACTION_ACADEMIC_YEAR_SETUP, _('Academic Year/Term Setup Changed')),
        (ACTION_STAFF_CREATED, _('Staff Member Created')),
        (ACTION_STAFF_UPDATED, _('Staff Member Updated')),
        (ACTION_STAFF_DEACTIVATED, _('Staff Member Deactivated')),
        (ACTION_STAFF_REACTIVATED, _('Staff Member Reactivated')),
        (ACTION_LEAVE_APPLIED, _('Leave Applied')),
        (ACTION_LEAVE_APPROVED, _('Leave Approved')),
        (ACTION_LEAVE_REJECTED, _('Leave Rejected')),
        (ACTION_LEAVE_CANCELLED, _('Leave Cancelled')),
        (ACTION_STUDENT_ADMITTED, _('Student Admitted')),
        (ACTION_STUDENT_UPDATED, _('Student Profile Updated')),
        (ACTION_STUDENT_PROMOTED, _('Student Promoted/Class Changed')),
        (ACTION_STUDENT_WITHDRAWN, _('Student Withdrawn/Status Changed')),
        (ACTION_FEE_STRUCTURE_CREATED, _('Fee Structure Created')),
        (ACTION_FEE_STRUCTURE_UPDATED, _('Fee Structure Updated')),
        (ACTION_INVOICE_GENERATED, _('Invoice Generated')),
        (ACTION_INVOICE_SENT, _('Invoice Sent')),
        (ACTION_INVOICE_UPDATED, _('Invoice Updated/Adjusted')),
        (ACTION_INVOICE_CANCELLED, _('Invoice Cancelled')),
        (ACTION_PAYMENT_RECORDED, _('Payment Recorded')),
        (ACTION_PAYMENT_REFUNDED, _('Payment Refunded')),
        (ACTION_CONCESSION_APPLIED, _('Fee Concession Applied')),
        (ACTION_EXPENSE_RECORDED, _('Expense Recorded')),
        (ACTION_BUDGET_CREATED, _('Budget Created')),
        (ACTION_BUDGET_UPDATED, _('Budget Updated')),
        (ACTION_MANUAL_JE_CREATED, _('Manual Journal Entry Created')),
        (ACTION_SYSTEM_NOTIFICATION_SENT, _('System Notification Sent')),
        (ACTION_DATA_EXPORT, _('Data Exported')),
        (ACTION_DATA_IMPORT, _('Data Imported')),
        (ACTION_SETTINGS_CHANGED, _('System/School Settings Changed')),
        (ACTION_SYSTEM_ERROR_LOGGED, _('Significant System Error Logged')),
        (ACTION_GENERIC_ADMIN_ACTION, _('Generic Admin Action')),
    ]

    # --- Model Fields ---
    action_type = models.CharField(
        _("action type"),
        max_length=50,
        choices=ACTION_TYPES_CHOICES, # Correctly uses the list defined above
        db_index=True
    )
    timestamp = models.DateTimeField(_("timestamp"), default=timezone.now, db_index=True)
    
    user = models.ForeignKey( 
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='public_admin_activity_logs',
        help_text=_("The public admin user (platform owner/admin) who performed the action.")
    )
    staff_user = models.ForeignKey( 
        'schools.StaffUser', 
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name='staff_activity_logs',
        help_text=_("The tenant staff user who performed the action.")
    )
    actor_description = models.CharField(
        max_length=255, null=True, blank=True,
        help_text=_("Description of the actor if user/staff_user is not set (e.g., 'System Task', 'Unidentified User IP').")
    )

    tenant = models.ForeignKey(
        settings.TENANT_MODEL, 
        on_delete=models.CASCADE, 
        null=True, blank=True,   
        db_index=True,
        verbose_name=_("Associated School/Tenant")
    )
    ip_address = models.GenericIPAddressField(_("IP address"), null=True, blank=True)
    user_agent = models.TextField(_("user agent"), blank=True, help_text=_("Browser/client user agent string."))

    target_content_type = models.ForeignKey(
        ContentType, on_delete=models.SET_NULL, 
        null=True, blank=True,
        verbose_name=_("target model type")
    )
    target_object_id = models.CharField(
        _("target object ID"), 
        max_length=255, 
        null=True, blank=True, db_index=True
    ) 
    target_object = GenericForeignKey('target_content_type', 'target_object_id')
    
    target_object_repr = models.CharField(
        _("target object representation"),
        max_length=300, null=True, blank=True,
        help_text=_("A human-readable string representing the target object (e.g., 'Invoice INV-001', 'Student: John Doe').")
    )

    description = models.TextField(
        _("description"),
        blank=True, 
        help_text=_("Detailed description of the event, including what changed if applicable (e.g., 'Updated status from Active to Inactive').")
    )
    
    class Meta:
        verbose_name = _("Admin Activity Log")
        verbose_name_plural = _("Admin Activity Logs")
        ordering = ['-timestamp']

    def __str__(self):
        actor_display = self.get_actor_display()
        action_display = self.get_action_type_display() 
        target_repr_display = self.target_object_repr or (str(self.target_object) if self.target_object_id and self.target_object else _("N/A"))
        timestamp_formatted = self.timestamp.strftime('%Y-%m-%d %H:%M:%S') if self.timestamp else _("No timestamp")
        
        log_entry = f"{timestamp_formatted} - {actor_display} performed action '{action_display}'"
        if self.target_object_id: 
            log_entry += f" on '{target_repr_display[:100]}'" 
        if self.description:
            log_entry += f" - Details: {self.description[:100]}"
            if len(self.description) > 100:
                log_entry += "..."
        return log_entry

    def get_actor_display(self):
        if self.staff_user:
            name = self.staff_user.full_name if hasattr(self.staff_user, 'full_name') and self.staff_user.full_name else self.staff_user.email
            return _("Staff: %(name)s") % {'name': name}
        elif self.user: 
            name = self.user.get_full_name() if self.user.get_full_name() else self.user.get_username()
            return _("Admin: %(name)s") % {'name': name}
        elif self.actor_description:
            return self.actor_description
        return _("Unknown/System")
    
    
class PortalAdminPermissions(models.Model):
    class Meta:
        managed = False
        default_permissions = ()
        permissions = [
            ('view_setup_admin_module', _('Can view the main Setup & Admin module link')),
            ('assign_staff_roles', _('Can assign staff members to roles (groups)')),
        ]
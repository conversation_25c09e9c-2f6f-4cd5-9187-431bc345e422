#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import psycopg
from django.conf import settings
from django.core.management import call_command

def fix_migration_for_schema(schema_name):
    """Fix migration issues for a specific schema"""
    print(f"🔧 Fixing migrations for schema: {schema_name}")
    
    db_settings = settings.DATABASES['default']
    conn = psycopg.connect(
        host=db_settings['HOST'],
        port=db_settings['PORT'],
        dbname=db_settings['NAME'],
        user=db_settings['USER'],
        password=db_settings['PASSWORD']
    )
    conn.autocommit = True
    cur = conn.cursor()
    
    try:
        # Set search path to the tenant schema
        cur.execute(f'SET search_path TO "{schema_name}";')
        
        # Check if the problematic migration already exists
        cur.execute("""
            SELECT name FROM django_migrations 
            WHERE app = 'schools' AND name = '0003_remove_school_foreign_key'
        """)
        migration_exists = cur.fetchone()
        
        if migration_exists:
            print(f"  ✅ Migration schools.0003_remove_school_foreign_key already applied for {schema_name}")
        else:
            # Check if schools_schoolprofile table exists and has the problematic structure
            cur.execute("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = 'schools_schoolprofile'
                ORDER BY ordinal_position
            """, (schema_name,))
            columns = cur.fetchall()
            
            if columns:
                print(f"  📋 Found schools_schoolprofile table with {len(columns)} columns")
                
                # Check if we have the problematic primary key structure
                cur.execute("""
                    SELECT constraint_name, constraint_type 
                    FROM information_schema.table_constraints 
                    WHERE table_schema = %s AND table_name = 'schools_schoolprofile' 
                    AND constraint_type = 'PRIMARY KEY'
                """, (schema_name,))
                pk_constraints = cur.fetchall()
                
                if len(pk_constraints) > 1:
                    print(f"  ⚠️  Found {len(pk_constraints)} primary key constraints - fixing...")
                    
                    # Drop extra primary key constraints
                    for i, (constraint_name, _) in enumerate(pk_constraints[1:], 1):
                        try:
                            cur.execute(f'ALTER TABLE schools_schoolprofile DROP CONSTRAINT "{constraint_name}"')
                            print(f"    ✅ Dropped extra primary key constraint: {constraint_name}")
                        except Exception as e:
                            print(f"    ⚠️  Could not drop constraint {constraint_name}: {e}")
                
                # Mark the migration as applied
                cur.execute("""
                    INSERT INTO django_migrations (app, name, applied)
                    VALUES ('schools', '0003_remove_school_foreign_key', NOW())
                    ON CONFLICT (app, name) DO NOTHING
                """)
                print(f"  ✅ Marked schools.0003_remove_school_foreign_key as applied")
            
            else:
                print(f"  ℹ️  No schools_schoolprofile table found - will be created by migration")
        
        # Also check for other problematic migrations
        problematic_migrations = [
            ('fees', '0004_feepermissions_feestructure_total_amount'),
        ]
        
        for app, migration_name in problematic_migrations:
            cur.execute("""
                SELECT name FROM django_migrations 
                WHERE app = %s AND name = %s
            """, (app, migration_name))
            
            if not cur.fetchone():
                # Check if the table/column already exists
                if app == 'fees' and 'total_amount' in migration_name:
                    cur.execute("""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_schema = %s AND table_name = 'fees_feestructure' 
                        AND column_name = 'total_amount'
                    """, (schema_name,))
                    
                    if cur.fetchone():
                        # Column exists, mark migration as applied
                        cur.execute("""
                            INSERT INTO django_migrations (app, name, applied)
                            VALUES (%s, %s, NOW())
                            ON CONFLICT (app, name) DO NOTHING
                        """, (app, migration_name))
                        print(f"  ✅ Marked {app}.{migration_name} as applied (column exists)")
        
        print(f"  ✅ Migration fixes completed for {schema_name}")
        
    except Exception as e:
        print(f"  ❌ Error fixing migrations for {schema_name}: {e}")
    
    finally:
        conn.close()

def main():
    print("🔧 Tenant Migration Fixer")
    print("This script fixes common migration issues for tenant schemas")
    
    # Get schema name from command line or prompt
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
    else:
        schema_name = input("Enter the schema name to fix (or 'all' for all schemas): ").strip()
    
    if schema_name.lower() == 'all':
        # Fix all existing tenant schemas
        db_settings = settings.DATABASES['default']
        conn = psycopg.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD']
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        # Get all tenant schemas
        cur.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'public', 'pg_toast')
            ORDER BY schema_name
        """)
        schemas = [row[0] for row in cur.fetchall()]
        conn.close()
        
        print(f"Found {len(schemas)} tenant schemas: {schemas}")
        
        for schema in schemas:
            fix_migration_for_schema(schema)
    else:
        fix_migration_for_schema(schema_name)
    
    print("\n🎉 Migration fixes completed!")

if __name__ == '__main__':
    main()

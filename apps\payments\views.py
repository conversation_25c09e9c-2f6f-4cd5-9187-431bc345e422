# D:\school_fees_saas_v2\apps\payments\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View # DetailView if needed
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from django.db import transaction
from django.db.models import Sum, Q # Not directly used here, but good to keep if summaries added
from django.db import models
from django.utils import timezone
from django.http import HttpResponse, Http404
from django.conf import settings
from datetime import datetime, date, time # For combining date and time
from decimal import Decimal
from django import forms # Needed for PaymentFilterForm widget customization

# --- Authentication & Permissions ---
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.auth.decorators import login_required, permission_required # For function views

# --- Models ---
from .models import Payment, PaymentMethod
from .forms import PaymentForm, PaymentMethodForm, PaymentFilterForm # Ensure PaymentFilterForm is imported
from apps.fees.models import Invoice, AcademicYear # For initial data, invoice update
from apps.students.models import Student # For initial data
from apps.accounting.models import Account as ChartOfAccount # For JE
# from apps.accounting.utils import create_payment_journal_entry # JE Utility
from apps.schools.models import SchoolProfile, StaffUser # For receipt context and processed_by_staff
from apps.common.utils import render_to_pdf, PDF_AVAILABLE # PDF Util

from apps.accounting.utils import create_payment_journal_entry # Use the one we designede

# ========================================
# PaymentMethod CRUD Views
# ========================================
from django.urls import reverse_lazy
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin # Ensure both are imported

from .models import PaymentMethod # Assuming PaymentMethod is in the same app's models.py
# from apps.accounting.models import Account as ChartOfAccount # Import if needed for select_related, though your current select_related is fine

class PaymentMethodListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = PaymentMethod
    template_name = 'payments/payment_method_list.html'
    context_object_name = 'payment_methods'
    login_url = reverse_lazy('schools:staff_login') # Redirect to staff login if not authenticated or no permission
    permission_required = 'payments.view_paymentmethod'
    # permission_denied_message = "You do not have permission to view payment methods." # Optional
    # raise_exception = True # Or False, to redirect to login_url on permission denial

    def get_queryset(self):
        # select_related is good for performance if you access linked_account fields in template
        return PaymentMethod.objects.select_related('linked_account').order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Payment Methods"
        # You can add a form for creating a new payment method here if you want
        # from .forms import PaymentMethodForm # Example
        # context['form'] = PaymentMethodForm()
        return context



class PaymentMethodCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = PaymentMethod
    form_class = PaymentMethodForm
    template_name = 'payments/payment_method_form.html'
    success_url = reverse_lazy('payments:payment_method_list')
    success_message = "Payment Method '%(name)s' created."
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'payments.add_paymentmethod'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Add New Payment Method"
        return context

class PaymentMethodUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = PaymentMethod
    form_class = PaymentMethodForm
    template_name = 'payments/payment_method_form.html'
    success_url = reverse_lazy('payments:payment_method_list')
    success_message = "Payment Method '%(name)s' updated."
    context_object_name = 'payment_method' # For template
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'payments.change_paymentmethod'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Update Payment Method: {self.object.name}"
        return context

class PaymentMethodDeleteView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = PaymentMethod
    template_name = 'payments/payment_method_confirm_delete.html'
    success_url = reverse_lazy('payments:payment_method_list')
    context_object_name = 'payment_method' # For template
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'payments.delete_paymentmethod'

    # Add protection if payments use this method
    # def post(self, request, *args, **kwargs): ...

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Confirm Delete Payment Method"
        return context

    def form_valid(self, form): # For custom success message
        object_name = self.object.name
        response = super().form_valid(form)
        messages.success(self.request, f"Payment Method '{object_name}' deleted successfully.")
        return response

# ========================================
# Payment Views
# ========================================
class PaymentListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = Payment
    template_name = 'payments/payment_list.html'
    context_object_name = 'payments'
    paginate_by = 25
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'payments.view_payment'

    def get_queryset(self):
        queryset = Payment.objects.select_related(
            'student', 'payment_method', 'academic_year', 'processed_by_staff'
        ).order_by('-payment_date', '-created_at')
        # Initialize filter form and apply filters
        self.filter_form = PaymentFilterForm(self.request.GET, queryset=queryset, request=self.request) # Pass request
        return self.filter_form.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "View Payments"
        # Ensure filter_form is initialized if get_queryset wasn't fully processed or for initial GET
        if not hasattr(self, 'filter_form'):
            self.filter_form = PaymentFilterForm(self.request.GET, request=self.request)
        context['filter_form'] = self.filter_form
        return context
    
    
    
# apps/payments/views.py

from django.views.generic import CreateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin # Keep this
from django.db import transaction
from django.shortcuts import redirect, get_object_or_404
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal
from datetime import date, datetime # For type checking payment_date

from .models import Payment
from .forms import PaymentForm
from apps.fees.models import Invoice
from apps.students.models import Student
from apps.schools.models import AcademicYear, StaffUser, SchoolProfile # Import StaffUser, SchoolProfile
# from apps.accounting.models import Account # Already imported in your accounting util
from apps.accounting.utils import create_payment_journal_entry # Use the one we designed

from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin

import logging
logger = logging.getLogger(__name__)

class RecordPaymentView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'payments/record_payment_form.html' # Your template
    success_message = "Payment of %(amount).2f recorded successfully for %(student_name)s." # Ensure .2f for formatting
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'payments.add_payment'

    def get_initial(self):
        initial = super().get_initial()
        invoice_id = self.request.GET.get('invoice')
        student_id = self.request.GET.get('student') # Changed from 'student' to 'student_id' for consistency
                                                    # if your GET param is student_id

        if invoice_id:
            try:
                # No need to filter by tenant explicitly, get_object_or_404 on tenant model is scoped
                invoice = Invoice.objects.select_related('student', 'academic_year').get(pk=invoice_id)
                initial['student'] = invoice.student
                initial['academic_year'] = invoice.academic_year
                # Ensure balance_due property exists and is accurate on Invoice model
                initial['amount'] = invoice.balance_due
            except Invoice.DoesNotExist:
                messages.warning(self.request, _("Invalid invoice specified for pre-fill."))
        
        elif student_id: # Only if invoice_id was not provided
            try:
                initial['student'] = Student.objects.get(pk=student_id) # Tenant-scoped
            except Student.DoesNotExist:
                messages.warning(self.request, _("Invalid student specified for pre-fill."))

        if 'payment_date' not in initial: # Set if not already pre-filled
            initial['payment_date'] = timezone.now() # Model field is DateTimeField, so timezone.now() is fine

        if 'academic_year' not in initial:
            current_ay = AcademicYear.objects.filter(is_active=True).order_by('-start_date').first() # Prefer active AY
            if not current_ay: # Fallback to most recent if no 'is_active'
                current_ay = AcademicYear.objects.order_by('-start_date').first()
            if current_ay:
                initial['academic_year'] = current_ay
        
        if 'status' not in initial: # Default status for new payments
            initial['status'] = Payment.STATUS_COMPLETED

        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request 
        
        # Pass instances to form for potentially disabling fields or dynamic querysets
        invoice_id = self.request.GET.get('invoice')
        student_id = self.request.GET.get('student') # Or student_id

        if invoice_id:
            try:
                kwargs['invoice_instance'] = Invoice.objects.select_related('student').get(pk=invoice_id)
            except Invoice.DoesNotExist: pass # Form's initial can handle this warning
        elif student_id:
            try:
                kwargs['student_instance'] = Student.objects.get(pk=student_id)
            except Student.DoesNotExist: pass
        return kwargs

    def form_valid(self, form):
        logger.info(f"VIEW: Payment form is valid for user {self.request.user.email}. Processing...")
        payment = form.save(commit=False)

        # Set processed_by_staff (ensure request.user is the correct user type, e.g., StaffUser)
        if self.request.user.is_authenticated:
            # If your settings.AUTH_USER_MODEL is the public one, and StaffUser is separate:
            if StaffUser and isinstance(self.request.user, StaffUser):
                payment.processed_by_staff = self.request.user
            elif StaffUser: # If request.user is PublicUser, try to get their StaffUser equivalent
                try:
                    staff_user_equivalent = StaffUser.objects.get(email__iexact=self.request.user.email)
                    payment.processed_by_staff = staff_user_equivalent
                except StaffUser.DoesNotExist:
                    logger.warning(f"Could not find StaffUser equivalent for {self.request.user.email} to set processed_by_staff.")
                    # payment.processed_by_staff = None or a default system user if appropriate
            else: # Fallback if StaffUser model isn't available or if direct assignment is okay
                payment.processed_by_staff = self.request.user


        # Handle payment_date awareness (Model's DateTimeField should handle this if form widget gives naive)
        # Django's DateTimeField automatically handles making naive datetimes aware using settings.TIME_ZONE
        # The form widget `format='%Y-%m-%dT%H:%M'` for `datetime-local` produces naive datetime strings.
        # Django's form field `DateTimeField` should convert this to an aware datetime on clean.
        # No explicit conversion usually needed here if model field is DateTimeField and form field is DateTimeField.

        logger.debug(f"  VIEW: Payment object before save: Student PK {getattr(payment.student, 'pk', None)}, Amount {payment.amount}, Status {payment.status}")

        try:
            with transaction.atomic():
                payment.save() # Save the payment object
                logger.info(f"  VIEW: Payment PK {payment.pk} saved successfully.")

                # Handle payment allocation to invoice if specified
                invoice_to_apply = form.cleaned_data.get('invoice_to_apply_to')
                if invoice_to_apply and payment.amount > 0:
                    from .models import PaymentAllocation

                    # Create payment allocation - the signal will handle updating the invoice
                    allocation = PaymentAllocation.objects.create(
                        payment=payment,
                        invoice=invoice_to_apply,
                        amount_allocated=payment.amount,
                        allocation_date=payment.payment_date.date() if payment.payment_date else timezone.now().date()
                    )
                    logger.info(f"  VIEW: Payment allocation created: {allocation.amount_allocated} allocated to Invoice {invoice_to_apply.invoice_number}")
                    logger.info(f"  VIEW: Invoice update will be handled by PaymentAllocation signal")

                # If no specific invoice selected but student is specified, try to allocate to oldest outstanding invoice
                elif not invoice_to_apply and payment.student and payment.amount > 0:
                    from .models import PaymentAllocation

                    # Find the oldest outstanding invoice for this student
                    outstanding_invoices = Invoice.objects.filter(
                        student=payment.student,
                        status__in=['DRAFT', 'SENT', 'PARTIALLY_PAID', 'OVERDUE']
                    ).exclude(
                        status='PAID'
                    ).order_by('issue_date', 'created_at')

                    remaining_amount = payment.amount
                    for invoice in outstanding_invoices:
                        if remaining_amount <= 0:
                            break

                        # Calculate how much to allocate to this invoice
                        invoice_balance = invoice.balance_due
                        if invoice_balance > 0:
                            allocation_amount = min(remaining_amount, invoice_balance)

                            # Create payment allocation - signal will handle invoice updates
                            allocation = PaymentAllocation.objects.create(
                                payment=payment,
                                invoice=invoice,
                                amount_allocated=allocation_amount,
                                allocation_date=payment.payment_date.date() if payment.payment_date else timezone.now().date()
                            )
                            logger.info(f"  VIEW: Auto-allocated {allocation_amount} to Invoice {invoice.invoice_number}")
                            remaining_amount -= allocation_amount

                    if remaining_amount > 0:
                        logger.info(f"  VIEW: {remaining_amount} remains unallocated (overpayment or no outstanding invoices)")
                else:
                    logger.info(f"  VIEW: No allocation created - no invoice specified and no student or zero amount")

                # Create Journal Entry for completed payments
                if payment.status == Payment.STATUS_COMPLETED:
                    logger.debug(f"  VIEW: Payment PK {payment.pk} is COMPLETED. Attempting JE creation.")
                    # Get SchoolProfile (tenant settings for accounting)
                    school_profile = None
                    try:
                        school_profile = SchoolProfile.objects.get(school_id=self.request.tenant.pk)
                    except SchoolProfile.DoesNotExist:
                        logger.error(f"CRITICAL: SchoolProfile not found for tenant {self.request.tenant.schema_name}. Cannot create journal entry for payment {payment.pk}.")
                        messages.error(self.request, _("School accounting profile not configured. Payment recorded, but accounting entry NOT created."))
                        # This is a critical configuration error. Depending on policy, you might
                        # want to prevent payment saving if JE cannot be created (by raising Exception here to roll back).
                        # For now, it allows payment to be saved but logs error and informs user.
                    
                    if school_profile:
                        try:
                            entry = create_payment_journal_entry(
                                payment=payment,
                                school_profile=school_profile,
                                user=payment.processed_by_staff or self.request.user # User who performed action
                            )
                            payment.related_journal_entry = entry
                            payment.save(update_fields=['related_journal_entry'])
                            logger.info(f"  VIEW: Journal entry {getattr(entry, 'entry_number', entry.pk)} created for payment {payment.pk}")
                            # Success message will be handled by SuccessMessageMixin if JE part of main success
                        except Exception as je_error:
                            logger.error(f"  VIEW: Failed to create journal entry for payment {payment.pk}: {je_error}", exc_info=True)
                            messages.warning(self.request, _(f"Payment recorded successfully, but an error occurred creating the accounting journal entry: {je_error}"))
                else: # Payment not 'COMPLETED'
                    pass # Success message will be handled by SuccessMessageMixin

            # Prepare context for SuccessMessageMixin
            student_name_for_msg = payment.student.get_full_name() if payment.student else "N/A"
            success_message_context = {'amount': payment.amount, 'student_name': student_name_for_msg}
            
            # Manually trigger success message if not using form_valid's default redirect
            # However, SuccessMessageMixin works by hooking into successful form_valid completion
            # and uses self.object (which is `payment`).
            # The get_success_message method of the mixin will be called.

            if payment.student: # If student exists, redirect to student detail
                return redirect(reverse('students:student_detail', kwargs={'pk': payment.student.pk}))
            else: # Fallback to payment list
                return redirect(reverse('payments:payment_list'))

        except Exception as e:
            logger.error(f"VIEW: Error during payment (PK {payment.pk if payment.pk else 'New'}) form_valid processing: {e}", exc_info=True)
            # Add a generic error to the form to be displayed to the user
            form.add_error(None, _(f"An unexpected error occurred while saving the payment. Please try again or contact support. Error: {e}"))
            return self.form_invalid(form)



    def get_success_message(self, cleaned_data):
        # cleaned_data here is from the form that was just validated.
        # self.object is the saved Payment instance.
        student_name = self.object.student.get_full_name() if self.object.student else "N/A"
        return self.success_message % {
            'amount': self.object.amount,
            'student_name': student_name,
        }

    def get_success_url(self):
        # If self.object (the payment) has an invoice, redirect to its detail page
        if self.object and self.object.invoice:
            return reverse('fees:invoice_detail', kwargs={'pk': self.object.invoice.pk})
        # If it has a student but no invoice
        elif self.object and self.object.student:
            return reverse('students:student_detail', kwargs={'pk': self.object.student.pk})
        # Default fallback
        return reverse_lazy('payments:payment_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Record New Payment")
        
        # For displaying info about pre-filled invoice/student in the template
        invoice_instance_for_context = None
        student_instance_for_context = None
        
        invoice_id = self.request.GET.get('invoice') # Use 'invoice' as per your get_initial
        student_id = self.request.GET.get('student') # Use 'student'

        if invoice_id:
            try:
                invoice_instance_for_context = Invoice.objects.select_related('student').get(pk=invoice_id)
                context['view_title'] = _(f"Record Payment for Invoice {invoice_instance_for_context.invoice_number}")
                if invoice_instance_for_context.student:
                    student_instance_for_context = invoice_instance_for_context.student
            except Invoice.DoesNotExist:
                pass # Error already messaged in get_initial or get_form_kwargs
        elif student_id:
            try:
                student_instance_for_context = Student.objects.get(pk=student_id)
                context['view_title'] = _(f"Record Payment for Student {student_instance_for_context.get_full_name()}")
            except Student.DoesNotExist:
                pass
        
        context['invoice_for_context'] = invoice_instance_for_context
        context['student_for_context'] = student_instance_for_context
        return context




@login_required(login_url=reverse_lazy('schools:staff_login'))
@permission_required('payments.view_payment', raise_exception=True) # Or a specific 'print_receipt' perm
def generate_payment_receipt_pdf(request, pk):
    payment = get_object_or_404(
        Payment.objects.select_related(
            'student', 'payment_method', 'academic_year',
            'student__current_class', 'student__current_section', # Added for received from
            'processed_by_staff' # StaffUser who recorded it
        ).prefetch_related('student__parents'), # For parent info
        pk=pk
    )
    school_profile = SchoolProfile.objects.filter(school=request.tenant).first()
    if not school_profile:
        messages.warning(request, "School profile is not set up. PDF may be incomplete.")

    context = {
        'payment': payment,
        'school_profile': school_profile,
        'report_title': f"Receipt #{payment.receipt_number_display}", # For _pdf_base.html
        'current_datetime': timezone.now(), # For _pdf_base.html
        # Payment details for receipt generation
    }
    if not PDF_AVAILABLE:
        messages.error(request, "PDF generation library is not available.")
        return redirect(request.META.get('HTTP_REFERER', reverse('payments:payment_list')))

    pdf = render_to_pdf('payments/receipt_pdf_template.html', context) # Ensure template path is correct
    if pdf:
        response = HttpResponse(pdf, content_type='application/pdf')
        student_name_safe = payment.student.last_name if payment.student else "general"
        filename = f"receipt_{payment.receipt_number_display or payment.pk}_{student_name_safe}.pdf"
        content_disposition = f'inline; filename="{filename}"'
        response['Content-Disposition'] = content_disposition
        return response

    logger.error(f"PDF generation failed for Payment PK {pk} in tenant {request.tenant.schema_name}")
    messages.error(request, "Could not generate PDF Receipt.")
    # Redirect back to a relevant page
    if payment.student:
        return redirect(reverse('students:student_detail', kwargs={'pk': payment.student.pk}))
    else:
        return redirect(reverse('payments:payment_list')) # Or payment detail if you have one
    



# D:\school_fees_saas_v2\apps\payments\views.py

# D:\school_fees_saas_v2\apps\payments\views.py

from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.views import View
# ... other imports ...
from .models import Payment
from apps.schools.models import SchoolProfile # Ensure SchoolProfile is imported
from apps.common.utils import render_to_pdf, PDF_AVAILABLE # Assuming PDF util is here

class PaymentReceiptPDFView(View): # Staff-facing view
    """
    Generates a PDF receipt for a specific payment.
    """
    def get(self, request, *args, **kwargs):
        if not PDF_AVAILABLE:
            return HttpResponse("PDF generation is not available.", status=501)

        try:
            # Assumes this view is protected by a staff/permission mixin
            payment = get_object_or_404(
                Payment.objects.select_related(
                    'student', 'parent_payer', 'payment_method', 'processed_by_staff'
                ), 
                pk=self.kwargs.get('pk')
            )
        except Payment.DoesNotExist:
            return HttpResponse("Payment not found.", status=404)
        
        allocations = payment.allocations.select_related('invoice').all()
        
        # --- CORRECTED WAY TO FETCH SCHOOL PROFILE ---
        # When inside a tenant context, this query is automatically scoped.
        school_profile = SchoolProfile.objects.first()
        if not school_profile:
            logger.warning(f"No SchoolProfile found for tenant {request.tenant.schema_name} when generating staff receipt.")
            # return HttpResponse("School profile not configured for this tenant.", status=500)

        context = {
            'payment': payment,
            'allocations': allocations,
            'student': payment.student,
            'school_profile': school_profile, # Use the fetched object
            'tenant': request.tenant,
        }
        
        pdf = render_to_pdf('payments/pdf/payment_receipt_pdf.html', context) # Use full path for clarity
        
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"Receipt_P{payment.id}_{payment.student.get_full_name().replace(' ', '_')}_{payment.payment_date.strftime('%Y-%m-%d')}.pdf"
            content = f"inline; filename={filename}"
            response['Content-Disposition'] = content
            return response
            
        return HttpResponse("Failed to generate PDF.", status=500)


class PaymentReceiptPDFPublicView(View): # Parent-facing view
    """
    Generates a PDF receipt for a specific payment - accessible by parents.
    """
    def get(self, request, *args, **kwargs):
        if not PDF_AVAILABLE:
            return HttpResponse("PDF generation is not available.", status=501)

        try:
            payment = get_object_or_404(
                Payment.objects.select_related('student', 'parent_payer'),
                pk=self.kwargs.get('pk')
            )
        except Payment.DoesNotExist:
            return HttpResponse("Payment not found.", status=404)

        # Security check: Parent can only view receipts for their own payments or payments for their children
        if not request.user.is_authenticated:
            return HttpResponse("Access denied. Please log in.", status=403)

        # Debug information to help identify the issue
        debug_info = []
        debug_info.append(f"Current user: {request.user} (ID: {request.user.pk})")
        debug_info.append(f"Payment parent_payer: {payment.parent_payer} (ID: {payment.parent_payer.pk if payment.parent_payer else 'None'})")
        debug_info.append(f"Payment student: {payment.student} (ID: {payment.student.pk if payment.student else 'None'})")
        debug_info.append(f"User has children attr: {hasattr(request.user, 'children')}")

        if hasattr(request.user, 'children'):
            user_children = list(request.user.children.all())
            debug_info.append(f"User's children: {user_children}")
            if payment.student:
                is_child = request.user.children.filter(pk=payment.student.pk).exists()
                debug_info.append(f"Payment student is user's child: {is_child}")

        # Check if user is the parent who made the payment
        if payment.parent_payer and payment.parent_payer == request.user:
            # User is the parent who made this payment - allow access
            pass
        elif hasattr(request.user, 'children') and payment.student:
            # Check if the payment is for one of the user's children
            if not request.user.children.filter(pk=payment.student.pk).exists():
                debug_message = "Access denied. Debug info: " + " | ".join(debug_info)
                return HttpResponse(debug_message, status=403)
        else:
            debug_message = "Access denied. Debug info: " + " | ".join(debug_info)
            return HttpResponse(debug_message, status=403)

        # --- CORRECTED WAY TO FETCH SCHOOL PROFILE ---
        school_profile = SchoolProfile.objects.first()
        if not school_profile:
            logger.warning(f"No SchoolProfile found for tenant {request.tenant.schema_name} when generating parent receipt.")

        # Also get allocations to display in the receipt
        allocations = payment.allocations.select_related('invoice').all()

        context = {
            'payment': payment,
            'allocations': allocations,
            'school_profile': school_profile,
            'tenant': request.tenant,
        }

        # Use the correct template path for receipt generation
        pdf = render_to_pdf('payment_receipt_pdf.html', context)

        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"Receipt_P{payment.id}_{payment.student.get_full_name().replace(' ', '_')}_{payment.payment_date.strftime('%Y-%m-%d')}.pdf"
            content = f"inline; filename={filename}"
            response['Content-Disposition'] = content
            return response

        return HttpResponse("Failed to generate PDF.", status=500)

# # ... other imports ...
# from django.http import HttpResponse
# from django.shortcuts import get_object_or_404
# from django.views import View
# from .models import Payment
# from apps.common.utils import render_to_pdf, PDF_IS_AVAILABLE # Import your PDF utility

# class PaymentReceiptPDFView(View):
#     """
#     Generates a PDF receipt for a specific payment.
#     """
#     def get(self, request, *args, **kwargs):
#         if not PDF_AVAILABLE:
#             return HttpResponse("PDF generation is not available. Please install required libraries.", status=501)

#         try:
#             payment = get_object_or_404(
#                 Payment.objects.select_related(
#                     'student', 'parent_payer', 'payment_method', 'processed_by_staff'
#                 ), 
#                 pk=self.kwargs.get('pk')
#             )
#         except Payment.DoesNotExist:
#             return HttpResponse("Payment not found.", status=404)
        
#         # Get the related allocations to show what was paid
#         allocations = payment.allocations.select_related('invoice').all()

#         context = {
#             'payment': payment,
#             'allocations': allocations,
#             'student': payment.student,
#             'school_profile': request.tenant.schoolprofile, # Assumes schoolprofile is on tenant
#             'tenant': request.tenant,
#         }
        
#         pdf = render_to_pdf('payment_receipt_pdf.html', context)
        
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             # Make the PDF downloadable with a nice filename
#             filename = f"Receipt_P{payment.id}_{payment.student.get_full_name().replace(' ', '_')}_{payment.payment_date.strftime('%Y-%m-%d')}.pdf"
#             content = f"inline; filename={filename}" # Use "attachment" to force download
#             response['Content-Disposition'] = content
#             return response
            
#         return HttpResponse("Failed to generate PDF.", status=500)


# class PaymentReceiptPDFPublicView(View):
#     """
#     Generates a PDF receipt for a specific payment - accessible by parents for their own payments.
#     """
#     def get(self, request, *args, **kwargs):
#         if not PDF_AVAILABLE:
#             return HttpResponse("PDF generation is not available. Please install required libraries.", status=501)

#         try:
#             payment = get_object_or_404(
#                 Payment.objects.select_related(
#                     'student', 'parent_payer', 'payment_method', 'processed_by_staff'
#                 ),
#                 pk=self.kwargs.get('pk')
#             )
#         except Payment.DoesNotExist:
#             return HttpResponse("Payment not found.", status=404)

#         # Security check: Only allow access if user is the parent who made the payment
#         # or if user is staff with appropriate permissions
#         if hasattr(request.user, 'children'):  # Parent user
#             # Check if this payment belongs to one of the parent's children
#             if payment.parent_payer != request.user:
#                 return HttpResponse("Access denied. You can only view receipts for your own payments.", status=403)
#         elif hasattr(request.user, 'staffuser'):  # Staff user
#             # Staff can view all receipts if they have permission
#             if not request.user.has_perm('payments.view_payment'):
#                 return HttpResponse("Access denied. Insufficient permissions.", status=403)
#         else:
#             return HttpResponse("Access denied.", status=403)

#         school_profile = SchoolProfile.objects.filter(school=request.tenant).first()

#         context = {
#             'payment': payment,
#             'school_profile': school_profile,
#             'tenant': request.tenant,
#         }

#         pdf = render_to_pdf('payment_receipt_pdf.html', context)

#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             # Make the PDF viewable with a nice filename
#             filename = f"Receipt_P{payment.id}_{payment.student.get_full_name().replace(' ', '_')}_{payment.payment_date.strftime('%Y-%m-%d')}.pdf"
#             content = f"inline; filename={filename}"
#             response['Content-Disposition'] = content
#             return response

#         return HttpResponse("Failed to generate PDF.", status=500)


# Example view for staff to record a payment
# in apps/payments/views.py

from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from .forms import ManualPaymentForm # A form to select the invoice, amount, method etc.
from .utils import record_payment
from apps.fees.models import Invoice

# @login_required (or your permission mixin)
def record_manual_payment_view(request, invoice_pk):
    invoice = get_object_or_404(Invoice, pk=invoice_pk)
    
    if request.method == 'POST':
        form = ManualPaymentForm(request.POST) # This form would have fields for amount, method, date, notes
        if form.is_valid():
            payment_obj = record_payment(
                invoice=invoice,
                amount_paid=form.cleaned_data['amount'],
                payment_method=form.cleaned_data['payment_method'],
                payment_date=form.cleaned_data['payment_date'],
                notes=form.cleaned_data.get('notes', ''),
                reference_number=form.cleaned_data.get('reference_number', ''),
                processed_by=request.user # Assuming the logged-in staff user
            )
            
            if payment_obj:
                messages.success(request, f"Payment of {payment_obj.amount} recorded successfully for Invoice #{invoice.invoice_number_display}.")
            else:
                messages.error(request, "Failed to record payment. Please check system logs or configuration.")
            
            return redirect('fees:invoice_detail', pk=invoice.pk) # Redirect back to the invoice detail
    else:
        form = ManualPaymentForm(initial={'amount': invoice.balance_due})

    context = {
        'form': form,
        'invoice': invoice,
    }
    return render(request, 'payments/record_manual_payment_form.html', context)




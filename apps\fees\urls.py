# apps/fees/urls.py
from django.urls import path
from . import views

app_name = 'fees' # Namespace for this app

urlpatterns = [
    # --- Academic Year URLs ---
    path('academic-years/', views.AcademicYearListView.as_view(), name='academic_year_list'),
    path('academic-years/new/', views.AcademicYearCreateView.as_view(), name='academic_year_create'),
    path('academic-years/<int:pk>/edit/', views.AcademicYearUpdateView.as_view(), name='academic_year_update'),
    path('academic-years/<int:pk>/delete/', views.AcademicYearDeleteView.as_view(), name='academic_year_delete'),

    # --- Term URLs ---
    path('terms/', views.TermListView.as_view(), name='term_list'),
    path('terms/new/', views.TermCreateView.as_view(), name='term_create'),
    path('terms/<int:pk>/edit/', views.TermUpdateView.as_view(), name='term_update'),
    path('terms/<int:pk>/delete/', views.TermDeleteView.as_view(), name='term_delete'),

    # --- Fee Head URLs ---
    path('fee-heads/', views.FeeHeadListView.as_view(), name='fee_head_list'),
    path('fee-heads/new/', views.FeeHeadCreateView.as_view(), name='fee_head_create'),
    path('fee-heads/<int:pk>/edit/', views.FeeHeadUpdateView.as_view(), name='fee_head_update'),
    path('fee-heads/<int:pk>/delete/', views.FeeHeadDeleteView.as_view(), name='fee_head_delete'),

    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),

    path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
    path('invoices/<int:pk>/pdf/', views.GenerateInvoicePDFView.as_view(), name='invoice_pdf_view'),
    path('invoices/<int:pk>/email-pdf/', views.email_invoice_pdf, name='email_invoice_pdf'),
    path('invoices/<int:pk>/send-reminder/', views.send_invoice_reminder, name='send_invoice_reminder'),
    path('invoices/<int:pk>/void/', views.void_invoice_view, name='invoice_void'),
    path('invoices/<int:pk>/issue/', views.issue_invoice_view, name='invoice_issue'),
    
    path('invoices/create/', views.InvoiceCreateView.as_view(), name='invoice_create'),
    path('invoices/<int:pk>/update/', views.InvoiceUpdateView.as_view(), name='invoice_update'),
    path('invoices/<int:pk>/delete/', views.InvoiceDraftDeleteView.as_view(), name='invoice_delete'),
    
    path('invoices/generate-from-structure/', views.GenerateInvoicesFromStructureView.as_view(), name='generate_structure_invoices'),

    # --- AJAX URLs ---
    path('ajax/terms-for-year/', views.ajax_get_terms_for_year, name='ajax_get_terms_for_year'),

    # --- Concession Type URLs ---
    path('concessions/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
    path('concessions/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
    path('concessions/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
    path('concessions/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

    # --- Fee Structure URLs ---
    path('fee-structures/', views.FeeStructureListView.as_view(), name='fee_structure_list'),
    path('fee-structures/new/', views.FeeStructureCreateView.as_view(), name='fee_structure_create'),
    path('fee-structures/<int:pk>/edit/', views.FeeStructureUpdateView.as_view(), name='fee_structure_update'),
    path('fee-structures/<int:pk>/delete/', views.FeeStructureDeleteView.as_view(), name='fee_structure_delete'),
    #path('fee-structures/<int:structure_pk>/generate-invoices/', views.generate_invoices_for_structure, name='generate_invoices_for_structure'),

    # --- Student Fee Allocation URL ---
    # path('allocations/', views.StudentFeeAllocationListView.as_view(), name='allocation_list'), # Admin view
    # path('allocations/new/', views.StudentFeeAllocationCreateView.as_view(), name='allocation_create'), # If direct creation needed
    # path('allocations/<int:pk>/delete/', views.StudentFeeAllocationDeleteView.as_view(), name='allocation_delete'),

    # # Student Concession URLs
    # path('allocations/concessions/<int:pk>/delete/', views.StudentConcessionDeleteView.as_view(), name='student_concession_delete'),
    # path('allocations/concessions/<int:pk>/edit/', views.StudentConcessionUpdateView.as_view(), name='student_concession_update'), # <<< ADD THIS


    # Concession Type URLs
    path('concession-types/', views.ConcessionTypeListView.as_view(), name='concession_type_list'),
    path('concession-types/new/', views.ConcessionTypeCreateView.as_view(), name='concession_type_create'),
    path('concession-types/<int:pk>/edit/', views.ConcessionTypeUpdateView.as_view(), name='concession_type_update'),
    path('concession-types/<int:pk>/delete/', views.ConcessionTypeDeleteView.as_view(), name='concession_type_delete'),

    # Student Concession URLs
    # path('student-concessions/', views.StudentConcessionListView.as_view(), name='student_concession_list'), # Admin view
    # # path('student-concessions/new/', views.StudentConcessionCreateView.as_view(), name='student_concession_create'), # If direct creation needed
    # path('student-concessions/<int:pk>/edit/', views.StudentConcessionUpdateView.as_view(), name='student_concession_update'),
    # path('student-concessions/<int:pk>/delete/', views.StudentConcessionDeleteView.as_view(), name='student_concession_delete'),

]

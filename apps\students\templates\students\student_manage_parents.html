{# apps/students/templates/students/student_manage_parents.html #}
{% extends "tenant_base.html" %}
{% load static widget_tweaks i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle">
        <h1>{{ view_title }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">{% trans "Students" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'students:student_detail' student.pk %}">{{ student.get_full_name }}</a></li>
                <li class="breadcrumb-item active">{% trans "Manage Parents" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    {# Card 1: Search Parents (Submits via GET to reload this page with query params) #}
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="mb-0 py-1">{% trans "Find Parents to Link" %}</h5>
        </div>
        <div class="card-body pt-3">
            <form method="get" action=""> {# Action is empty to submit to the current URL #}
                <div class="row align-items-end">
                    <div class="col-md-9 mb-2 mb-md-0">
                        <label for="{{ form.search_query.id_for_label }}" class="form-label">{{ form.search_query.label }}</label>
                        {% render_field form.search_query class+="form-control form-control-sm" %}
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-sm btn-outline-primary w-100">
                            <i class="bi bi-search"></i> {% trans "Search" %}
                        </button>
                    </div>
                </div>
                {% if request.GET.search_query %}
                    <div class="mt-2">
                        <small class="text-muted">{% trans "Showing results for:" %} "{{ request.GET.search_query }}" - <a href="{{ request.path }}">{% trans "Clear Search" %}</a></small>
                    </div>
                {% endif %}
            </form>
        </div>
    </div>

    {# Card 2: Link/Unlink Parents (Submits via POST) #}
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0 py-1">{% trans "Link Parents to" %} {{ student.get_full_name }}</h5>
        </div>
        <div class="card-body pt-3">
            <form method="post" novalidate>
                {% csrf_token %}
                {# The form instance passed to this template from UpdateView is self.get_form() #}
                {# It should contain the 'parents' ModelMultipleChoiceField #}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}<p class="mb-0">{{ error }}</p>{% endfor %}
                    </div>
                {% endif %}
                
                <div class="mb-3">
                    <p class="form-label d-block"><strong>{{ form.parents.label }}:</strong></p>
                    <p class="small text-muted">{% trans "Check to link a parent, uncheck to unlink." %}</p>

                    {% if form.parents.field.choices %} {# Check if there are any choices (parents found by query) #}
                        <div class="list-group parent-selection-list border rounded" style="max-height: 300px; overflow-y: auto;">
                            {# Django's CheckboxSelectMultiple widget renders each choice as a label containing an input #}
                            {# Looping through form.parents directly will render the widget #}
                            {{ form.parents }}
                            {% comment %} {# Example of custom rendering if needed, but {{ form.parents }} is simpler:
                            {% for choice_value, choice_label in form.parents.field.choices %}
                                <label class="list-group-item list-group-item-action d-flex align-items-center">
                                    <input type="checkbox" name="{{ form.parents.html_name }}" value="{{ choice_value }}"
                                        class="form-check-input me-2"
                                        {% if choice_value|stringformat:"s" in form.parents.value|default_if_none:"" or choice_value in form.parents.initial_pks_for_template %}checked{% endif %}> 
                                        {# 'initial_pks_for_template' is a custom attr you might add in form __init__ if needed for manual check #}
                                    {{ choice_label }}
                                </label>
                            {% endfor %}
                            #} {% endcomment %}
                        </div>
                    {% else %}
                        <div class="alert alert-info mt-2">
                            {% if request.GET.search_query %}
                                {% blocktrans with query=request.GET.search_query %}No parents found matching "<strong>{{ query }}</strong>".{% endblocktrans %}
                            {% else %}
                                {% trans "No parent accounts found in the system based on current filters." %}
                            {% endif %}
                            {% trans "You can" %} <a href="{% url 'students:parentuser_create' %}?student_pk_to_link={{ student.pk }}&next={{ request.path|urlencode }}">{% trans "create a new parent account" %}</a>.
                        </div>
                    {% endif %}
                    {% for error in form.parents.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    {% if form.parents.help_text %}<small class="form-text text-muted mt-1 d-block">{{ form.parents.help_text }}</small>{% endif %}
                </div>

                <hr>
                <div class="text-end">
                    <a href="{% url 'students:student_detail' student.pk %}" class="btn btn-outline-secondary me-2">{% trans "Cancel" %}</a>
                    <button type="submit" name="update_links_button" class="btn btn-primary"> {# Give button a name if you need to distinguish POST actions #}
                        <i class="bi bi-save me-1"></i> {% trans "Update Parent Links" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    {# Section to "Create & Link New Parent" - This button is also present above if no parents are found #}
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h5 class="mb-0 py-1"><i class="bi bi-person-plus-fill me-2"></i>{% trans "Or, Create New Parent" %}</h5>
        </div>
        <div class="card-body pt-3">
            <p>{% blocktrans with student_full_name=student.get_full_name %}If the parent does not exist in the list above, you can create a new parent account. After creation, you can return here to confirm the link or it might be auto-linked to <strong>{{ student_full_name }}</strong> if the creation form supports it.{% endblocktrans %}</p>
            <a href="{% url 'students:parentuser_create' %}?student_pk_to_link={{ student.pk }}&next={{ request.path|urlencode }}" class="btn btn-success">
                <i class="bi bi-plus-circle me-1"></i> {% trans "Create New Parent Account" %}
            </a>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
{{ block.super }}
{# Add any JS specific to this page if needed #}
{% endblock %}




























{% comment %} {# apps/students/templates/students/student_manage_parents.html #}
{% extends "tenant_base.html" %}
{% load static widget_tweaks %} {# Assuming you use widget_tweaks for form styling #}

{% load i18n %}
{% block tenant_page_title %}{{ view_title }}{% endblock %}



{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle">
        <h1>{{ view_title }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">Students</a></li>
                <li class="breadcrumb-item"><a href="{% url 'students:student_detail' student.pk %}">{{ student.full_name }}</a></li>
                <li class="breadcrumb-item active">Manage Parents</li>
            </ol>
        </nav>
    </div>

    <div class="card">
        <div class="card-body pt-3">
            <form method="post" novalidate>
                {% csrf_token %}
                {{ form.management_form }} {# If using formsets, not needed for this simple M2M form #}

                <p class="mb-3">Select the parents/guardians associated with this student. Unchecking a parent will remove the link.</p>
                
                {# Optional: Search field - this would require AJAX or page reload to update choices #}
                <div class="mb-3 row">
                    <label for="{{ form.search_parent_email.id_for_label }}" class="col-sm-3 col-form-label">{{ form.search_parent_email.label }}</label>
                    <div class="col-sm-7">
                        {{ form.search_parent_email|add_class:"form-control form-control-sm" }}
                    </div>
                    <div class="col-sm-2">
                        <button type="submit" name="search_parent" class="btn btn-sm btn-outline-secondary w-100">Search</button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label d-block">{{ form.parents_to_link.label }}:</label>
                    {% if form.parents_to_link.field.queryset.exists %}
                        <div class="list-group parent-selection-list" style="max-height: 300px; overflow-y: auto; border: 1px solid #ced4da; border-radius: 0.25rem; padding: 0.5rem;">
                        {% for pk, choice_label in form.parents_to_link.field.widget.choices %}
                            <label class="list-group-item list-group-item-action">
                                <input type="checkbox" name="{{ form.parents_to_link.html_name }}" value="{{ pk }}" 
                                        class="form-check-input me-2"
                                        {% if pk|stringformat:"s" in form.parents_to_link.value or pk in form.parents_to_link.initial_value_pks %}checked{% endif %}>
                                {{ choice_label }}
                            </label>
                        {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No parent accounts found in the system. Please <a href="#">create parent accounts</a> first. 
                            {# Link to parent creation view #}
                        </div>
                    {% endif %}
                    {% if form.parents_to_link.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.parents_to_link.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>

                <hr>
                <div class="text-end">
                    <a href="{% url 'students:student_detail' student.pk %}" class="btn btn-outline-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Update Parent Links
                    </button>
                </div>
            </form>
        </div>
    </div>

    {# Section to "Create & Link New Parent" #}
    <div class="card mt-4">
        <div class="card-header">
            <h5><i class="bi bi-person-plus-fill"></i> Create & Link New Parent</h5>
        </div>
        <div class="card-body pt-3">
            <p>If the parent does not exist in the system, you can create a new parent account here and it will be automatically linked to <strong>{{ student.full_name }}</strong>.</p>
            {# This would typically be a separate form or redirect to a parent creation view with a 'next' parameter #}
            {# For simplicity, you might just link to a generic parent creation view #}
            <a href="{% url 'students:parentuser_create' %}?student_pk_to_link={{ student.pk }}&next={{ request.path|urlencode }}" class="btn btn-success">
                {% trans "Create New Parent Account" %}
            </a>
            <small class="form-text text-muted d-block mt-1">You will be redirected to the parent creation form.</small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_tenant_js %}
<script>
// Optional: JavaScript for enhancing the parent selection, e.g., live search if you implement it
// For now, the checkbox list will work.
</script>
{% endblock %}

 {% endcomment %}

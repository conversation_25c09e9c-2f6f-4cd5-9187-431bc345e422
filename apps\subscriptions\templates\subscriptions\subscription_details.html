{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\subscription_details.html #}
{% extends "tenant_base.html" %} {# Correct: This page is viewed within a tenant's portal context #}

{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:_("Subscription Details") }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .status-badge { font-size: 0.9em; font-weight: 600; }
        .feature-list li { padding-bottom: 0.3rem; }
        .label { /* For Bootstrap 5 form-label like display without a form */
            margin-bottom: .5rem;
            font-weight: 600;
            color: #495057; /* Or your theme's label color */
        }
        .admin-action-section {
            background-color: #f8f9fa; /* Light background for admin section */
            border: 1px dashed #adb5bd;
            padding: 1rem;
            border-radius: .25rem;
        }
    </style>
{% endblock %}


{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li> {# Assumes staff dashboard is the primary back link #}
            <li class="breadcrumb-item active">{% trans "Subscription Details" %}</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section profile">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if subscription %}
    <div class="row">
        <div class="col-xl-8">
            <div class="card shadow-sm">
                <div class="card-body pt-4"> {# Increased top padding #}
                    <h4 class="card-title text-primary">{% trans "Current Subscription Plan" %}: {{ subscription.plan.name }}</h4>
                    
                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Status" %}:</div>
                        <div class="col-lg-8 col-md-7">
                            <span class="badge 
                                {% if subscription.status == 'ACTIVE' %}bg-success{% endif %}
                                {% if subscription.status == 'TRIALING' %}bg-info text-dark{% endif %}
                                {% if subscription.status == 'PENDING' %}bg-secondary{% endif %}
                                {% if subscription.status == 'PAST_DUE' or subscription.status == 'UNPAID' %}bg-danger{% endif %}
                                {% if subscription.status == 'CANCELLED' or subscription.status == 'ENDED' %}bg-warning text-dark{% endif %}
                                {% if subscription.status == 'SUSPENDED' %}bg-dark{% endif %}
                                status-badge">
                                {{ subscription.get_status_display }}
                            </span>
                            {% if not subscription.is_usable and subscription.status != 'CANCELLED' and subscription.status != 'ENDED' and subscription.status != 'PENDING' %}
                                <p class="small text-danger mt-1 mb-0">
                                    <i class="bi bi-exclamation-triangle-fill"></i> {% trans "Your access to some features may be limited. Please resolve any pending payment issues." %}
                                </p>
                            {% elif subscription.status == 'PENDING' %}
                                <p class="small text-info mt-1 mb-0">
                                    <i class="bi bi-hourglass-split"></i> {% trans "Your subscription is pending activation or payment." %}
                                </p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Billing Cycle" %}:</div>
                        <div class="col-lg-8 col-md-7">{{ subscription.get_billing_cycle_display }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Subscribed Price" %}:</div>
                        <div class="col-lg-8 col-md-7">
                            {% if subscription.price_at_subscription is not None %}
                                {{ school_currency_symbol|default:'$' }}{{ subscription.price_at_subscription|floatformat:2|intcomma }}
                            {% else %} {# Fallback to current plan price if historical price not stored #}
                                {% if subscription.billing_cycle == 'MONTHLY' %}
                                    {{ school_currency_symbol|default:'$' }}{{ subscription.plan.price_monthly|floatformat:2|intcomma }}
                                {% elif subscription.billing_cycle == 'ANNUALLY' %}
                                    {{ school_currency_symbol|default:'$' }}{{ subscription.plan.price_annually|floatformat:2|intcomma }}
                                {% else %}
                                    N/A
                                {% endif %}
                            {% endif %}
                            / {{ subscription.get_billing_cycle_display|lower }}
                        </div>
                    </div>

                    {% if subscription.is_on_trial %}
                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Trial Ends" %}:</div>
                        <div class="col-lg-8 col-md-7">{{ subscription.trial_end_date|date:"D, d M Y" }} ({{ subscription.trial_end_date|timeuntil }})</div>
                    </div>
                    {% endif %}

                    {% if subscription.current_period_start and subscription.current_period_end and not subscription.is_on_trial %}
                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Current Billing Period" %}:</div>
                        <div class="col-lg-8 col-md-7">{{ subscription.current_period_start|date:"d M Y" }} - {{ subscription.current_period_end|date:"d M Y" }}</div>
                    </div>
                        {% if subscription.cancel_at_period_end %}
                            <div class="row mb-3">
                                <div class="col-lg-4 col-md-5 label text-warning">{% trans "Cancels On" %}:</div>
                                <div class="col-lg-8 col-md-7 text-warning">{{ subscription.current_period_end|date:"D, d M Y" }}</div>
                            </div>
                        {% endif %}
                    {% elif not subscription.is_on_trial and subscription.status != 'PENDING' and subscription.status != 'ENDED' and subscription.status != 'CANCELLED' %}
                    <div class="row mb-3">
                        <div class="col-lg-4 col-md-5 label">{% trans "Next Billing Date" %}:</div>
                        <div class="col-lg-8 col-md-7">{{ subscription.current_period_end|date:"D, d M Y"|default:"N/A (Pending Activation)" }}</div>
                    </div>
                    {% endif %}

                    <h5 class="card-title mt-4">{% trans "Features Included in Your Plan" %} ({{subscription.plan.name}})</h5>
                    {% if subscription.plan.features.all %}
                        <ul class="feature-list row">
                        {% for feature in subscription.plan.features.all %}
                            <li class="col-md-6"><i class="bi bi-check-circle-fill text-success me-2"></i>{{ feature.name }}</li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p>{% trans "This plan includes standard platform features." %}</p>
                    {% endif %}

                    <h5 class="card-title mt-4">{% trans "Usage Limits" %}</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "Students" %}:</strong> 
                                {% if subscription.plan.max_students is not None %}
                                    {% trans "Up to" %} {{ subscription.plan.max_students }}
                                    {# TODO: Placeholder for current count: (Currently: X) #}
                                {% else %}
                                    {% trans "Unlimited" %}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Staff" %}:</strong> 
                                {% if subscription.plan.max_staff is not None %}
                                    {% trans "Up to" %} {{ subscription.plan.max_staff }}
                                    {# TODO: Placeholder for current count: (Currently: Y) #}
                                {% else %}
                                    {% trans "Unlimited" %}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center border-top pt-3">
                        {# Actions for the tenant user #}
                        <a href="{% url 'subscriptions:update_payment_method' %}" class="btn btn-info me-2 disabled">
                            <i class="bi bi-credit-card-2-front-fill me-1"></i> {% trans "Update Payment Method" %}
                        </a>
                        <a href="{% url 'subscriptions:pricing_page' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-arrow-up-circle-fill me-1"></i> {% trans "View/Change Plans" %}
                        </a>
                        {# Add Cancel Subscription button/logic here if applicable #}
                        {# <a href="{% url 'subscriptions:cancel_subscription' %}" class="btn btn-outline-danger">{% trans "Cancel Subscription" %}</a> #}
                    </div>

                </div> {# End card-body #}
            </div> {# End card #}

            {# --- PLATFORM ADMIN ACTION SECTION --- #}
            {# This section is ONLY visible to platform superusers/staff viewing this tenant's subscription #}
            {% if request.user.is_authenticated and request.user.is_superuser and request.user.is_staff %}
                {% if subscription and subscription.status == 'PENDING' or subscription.status == 'TRIALING' or subscription.status == 'PAST_DUE' or subscription.status == 'UNPAID' %}
                <div class="admin-action-section mt-4">
                    <h6 class="text-muted"><i class="bi bi-shield-lock-fill me-1"></i> {% trans "Platform Admin Action:" %}</h6>
                    <p class="small text-muted">{% trans "This section is visible only to platform administrators." %}</p>
                    <form method="post" action="{% url 'subscriptions:admin_manual_approve' subscription_pk=subscription.pk %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success btn-sm">
                            <i class="bi bi-check-circle-fill me-1"></i> 
                            {% if subscription.status == 'TRIALING' %}
                                {% trans "Convert Trial to Active Paid" %}
                            {% else %}
                                {% trans "Manually Activate/Approve Payment" %}
                            {% endif %}
                        </button>
                    </form>
                    <small class="d-block text-muted mt-1">
                        {% if subscription.status == 'TRIALING' %}
                            {% trans "This will end the trial and start the paid subscription period." %}
                        {% else %}
                            {% trans "This will set the subscription to ACTIVE and start its billing period as if a payment was received." %}
                        {% endif %}
                    </small>
                    {# Add other admin actions like "Suspend Subscription", "Extend Trial" etc. later #}
                </div>
                {% endif %}
            {% endif %}
            {# --- END PLATFORM ADMIN ACTION SECTION --- #}

        </div>{# End col-xl-8 #}

        <div class="col-xl-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Billing & Support" %}</h5>
                    <p class="small">{% trans "For any questions regarding your subscription, billing, or plan changes, please contact our support team." %}</p>
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-light border disabled">
                            <i class="bi bi-file-earmark-text-fill me-1"></i> {% trans "View Billing History" %} <small>({% trans "Coming Soon" %})</small>
                        </a>
                        {% if subscription.status == 'PAST_DUE' or subscription.status == 'UNPAID' %}
                            <a href="#" class="btn btn-danger disabled">
                                <i class="bi bi-arrow-repeat me-1"></i> {% trans "Retry Failed Payment" %} <small>({% trans "Via Gateway" %})</small>
                            </a>
                        {% endif %}
                        <a href="mailto:{{ settings.SUPPORT_EMAIL|default:'<EMAIL>' }}" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope-fill me-1"></i> {% trans "Contact Support" %}
                        </a>
                    </div>
                </div>
            </div>
            {# You could add another card here for "How to upgrade/downgrade" instructions #}
        </div>{# End col-xl-4 #}
    </div>{# End row #}

    {% else %} {# No subscription object found #}
    <div class="alert alert-warning" role="alert">
    <h4 class="alert-heading">{% trans "No Subscription Information Found!" %}</h4>
    <p>{% trans "We could not find any subscription details for your school. If you have recently registered, your subscription might still be pending setup or activation." %}</p>
    <hr>
    <p class="mb-0">{% trans "Please" %} <a href="{% url 'subscriptions:pricing_page' %}" class="alert-link">{% trans "view our plans" %}</a> {% trans "to get started, or contact platform support if you believe this is an error." %}</p>
    {# If a tenant admin, give option to select a plan from here #}
    {% if request.user.is_authenticated and request.tenant and request.tenant.owner == request.user %}
    <div class="mt-3">
        <a href="{% url 'subscriptions:pricing_page' %}" class="btn btn-primary">{% trans "Choose a Plan" %}</a>
    </div>
    {% endif %}
    </div>
    {% endif %} {# End if subscription #}
</section>
{% endblock tenant_specific_content %}






















{# apps/fees/templates/fees/invoice_list_pdf.html - PDF template for invoice list export #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 15px;
        }
        
        .school-logo {
            max-height: 60px;
            margin-bottom: 10px;
        }
        
        .school-name {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-meta {
            font-size: 9px;
            color: #666;
        }
        
        .summary-section {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .summary-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #28a745;
        }
        
        .summary-grid {
            display: table;
            width: 100%;
        }
        
        .summary-row {
            display: table-row;
        }
        
        .summary-cell {
            display: table-cell;
            padding: 3px 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .summary-label {
            font-weight: bold;
            width: 30%;
        }
        
        .summary-value {
            text-align: right;
            width: 20%;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 9px;
        }
        
        .invoice-table th {
            background-color: #28a745;
            color: white;
            padding: 8px 4px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #1e7e34;
        }
        
        .invoice-table td {
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        .invoice-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .invoice-table tbody tr:hover {
            background-color: #e8f5e8;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .footer {
            position: fixed;
            bottom: 1cm;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 5px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        
        .amount {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .positive-amount {
            color: #28a745;
        }
        
        .negative-amount {
            color: #dc3545;
        }
        
        .zero-amount {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        {% if school_profile and school_profile.logo %}
            <img src="{{ school_profile.logo.url }}" alt="{{ school_profile.name }}" class="school-logo">
        {% endif %}
        
        <div class="school-name">
            {% if school_profile %}
                {{ school_profile.name }}
            {% else %}
                School Management System
            {% endif %}
        </div>
        
        <div class="report-title">{{ report_title }}</div>
        
        <div class="report-meta">
            Generated on: {{ report_generated_at|date:"F d, Y \a\t g:i A" }}
            {% if filter_applied %} | Filters Applied{% endif %}
        </div>
    </div>
    
    <div class="summary-section">
        <div class="summary-title">Report Summary</div>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell summary-label">Total Invoices:</div>
                <div class="summary-cell summary-value">{{ total_invoices|default:0 }}</div>
                <div class="summary-cell summary-label">Total Amount:</div>
                <div class="summary-cell summary-value amount positive-amount">{{ total_amount|floatformat:2 }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell summary-label">Total Paid:</div>
                <div class="summary-cell summary-value amount positive-amount">{{ total_paid|floatformat:2 }}</div>
                <div class="summary-cell summary-label">Balance Due:</div>
                <div class="summary-cell summary-value amount {% if total_balance > 0 %}negative-amount{% elif total_balance == 0 %}zero-amount{% else %}positive-amount{% endif %}">{{ total_balance|floatformat:2 }}</div>
            </div>
        </div>
    </div>
    
    {% if invoices %}
        <table class="invoice-table">
            <thead>
                <tr>
                    <th style="width: 10%;">Invoice #</th>
                    <th style="width: 15%;">Student Name</th>
                    <th style="width: 8%;">Admission #</th>
                    <th style="width: 10%;">Class</th>
                    <th style="width: 8%;">Academic Year</th>
                    <th style="width: 8%;">Term</th>
                    <th style="width: 8%;">Issue Date</th>
                    <th style="width: 8%;">Due Date</th>
                    <th style="width: 8%;">Status</th>
                    <th style="width: 8%;" class="text-right">Total Amount</th>
                    <th style="width: 8%;" class="text-right">Amount Paid</th>
                    <th style="width: 9%;" class="text-right">Balance Due</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in invoices %}
                    <tr>
                        <td>{{ invoice.invoice_number }}</td>
                        <td>
                            {% if invoice.student %}
                                {{ invoice.student.get_full_name }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.student %}
                                {{ invoice.student.admission_number }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.student and invoice.student.current_class %}
                                {{ invoice.student.current_class.name }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.academic_year %}
                                {{ invoice.academic_year.name }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.term %}
                                {{ invoice.term.name }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.issue_date %}
                                {{ invoice.issue_date|date:"M d, Y" }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.due_date %}
                                {{ invoice.due_date|date:"M d, Y" }}
                            {% else %}
                                <em>N/A</em>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge 
                                {% if invoice.status == 'PAID' %}status-paid
                                {% elif invoice.status == 'PENDING' %}status-pending
                                {% elif invoice.status == 'OVERDUE' %}status-overdue
                                {% elif invoice.status == 'CANCELLED' %}status-cancelled
                                {% endif %}">
                                {{ invoice.get_status_display }}
                            </span>
                        </td>
                        <td class="text-right amount positive-amount">{{ invoice.total_amount|floatformat:2 }}</td>
                        <td class="text-right amount positive-amount">{{ invoice.amount_paid|floatformat:2 }}</td>
                        <td class="text-right amount {% if invoice.balance_due > 0 %}negative-amount{% elif invoice.balance_due == 0 %}zero-amount{% else %}positive-amount{% endif %}">{{ invoice.balance_due|floatformat:2 }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="no-data">
            <strong>No invoices found</strong><br>
            {% if filter_applied %}
                Try adjusting your filter criteria to see more results.
            {% else %}
                No invoices have been created yet.
            {% endif %}
        </div>
    {% endif %}
    
    <div class="footer">
        <div>
            {{ report_title }} - Page <span class="page-number"></span>
            | Generated by School Management System
            {% if school_profile %}
                | {{ school_profile.name }}
            {% endif %}
        </div>
    </div>
</body>
</html>

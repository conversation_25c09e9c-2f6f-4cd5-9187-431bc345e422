{% extends "tenant_base.html" %}
{% load static %}

{% block title %}Generate Payslips{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .info-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9ff 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #e9ecef;
        text-align: center;
    }

    .info-section h5 {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .info-section p {
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .alert-info {
        background: linear-gradient(135deg, #cce7ff 0%, #b8daff 100%);
        color: #004085;
    }

    .warning-box {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 2px solid #ffc107;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .warning-box i {
        color: #856404;
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .warning-box h6 {
        color: #856404;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .warning-box p {
        color: #856404;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-file-invoice-dollar me-3"></i>
                            Generate Payslips
                        </h3>
                    </div>
                    <div class="premium-body">
                        <!-- Information Section -->
                        <div class="info-section">
                            <h5><i class="fas fa-info-circle me-2"></i>Payslip Generation</h5>
                            <p>Select the month and year to generate payslips for all active employees.</p>
                            <p><strong>This process will create payslips for all staff members with active salary structures.</strong></p>
                        </div>

                        <!-- Warning Box -->
                        <div class="warning-box">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h6>Important Notice</h6>
                            <p>This will skip employees for whom a payslip already exists for the selected period.</p>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Period Selection Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-calendar-alt me-2"></i>Select Payroll Period</h4>
                            </div>

                            <div class="row">
                                <!-- Month Selection -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select{% if form.period_month.errors %} is-invalid{% endif %}"
                                                id="{{ form.period_month.id_for_label }}"
                                                name="{{ form.period_month.name }}"
                                                {% if form.period_month.field.required %}required{% endif %}>
                                            <option value="">Select month</option>
                                            {% for choice in form.period_month.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.period_month.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.period_month.id_for_label }}">
                                            <i class="fas fa-calendar-day icon-input"></i>Month
                                        </label>
                                        {% if form.period_month.help_text %}
                                            <div class="form-text">{{ form.period_month.help_text }}</div>
                                        {% endif %}
                                        {% if form.period_month.errors %}
                                            <div class="invalid-feedback">{{ form.period_month.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Year Selection -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select{% if form.period_year.errors %} is-invalid{% endif %}"
                                                id="{{ form.period_year.id_for_label }}"
                                                name="{{ form.period_year.name }}"
                                                {% if form.period_year.field.required %}required{% endif %}>
                                            <option value="">Select year</option>
                                            {% for choice in form.period_year.field.choices %}
                                                {% if choice.0 %}
                                                    <option value="{{ choice.0 }}" {% if form.period_year.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                        <label for="{{ form.period_year.id_for_label }}">
                                            <i class="fas fa-calendar icon-input"></i>Year
                                        </label>
                                        {% if form.period_year.help_text %}
                                            <div class="form-text">{{ form.period_year.help_text }}</div>
                                        {% endif %}
                                        {% if form.period_year.errors %}
                                            <div class="invalid-feedback">{{ form.period_year.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-cogs me-2"></i>Generate Payslips
                                </button>
                                <a href="{% url 'schools:dashboard' %}" class="btn-secondary-premium">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-premium[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Payslips...';
            submitBtn.disabled = true;
        });
    }

    // Enhanced form validation feedback
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });

    // Auto-select current month and year if not already selected
    const monthSelect = document.querySelector('select[name="period_month"]');
    const yearSelect = document.querySelector('select[name="period_year"]');

    if (monthSelect && !monthSelect.value) {
        const currentMonth = new Date().getMonth() + 1;
        monthSelect.value = currentMonth;
    }

    if (yearSelect && !yearSelect.value) {
        const currentYear = new Date().getFullYear();
        yearSelect.value = currentYear;
    }

    // Form validation before submission
    if (form) {
        form.addEventListener('submit', function(e) {
            const month = monthSelect.value;
            const year = yearSelect.value;

            if (!month || !year) {
                e.preventDefault();
                alert('Please select both month and year before generating payslips.');

                if (!month) {
                    monthSelect.classList.add('is-invalid');
                    monthSelect.focus();
                }
                if (!year) {
                    yearSelect.classList.add('is-invalid');
                    if (!month) yearSelect.focus();
                }

                // Re-enable submit button
                submitBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>Generate Payslips';
                submitBtn.disabled = false;
            }
        });
    }
});
</script>
{% endblock %}
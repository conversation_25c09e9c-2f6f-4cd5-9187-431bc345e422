{% extends "tenant_base.html" %}
{% load humanize static %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
    
{% comment %} <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div> {% endcomment %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}
    {% include "./_report_filter_export_card.html" %}

    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Cash Flow Statement for Period: {{ report_data.start_date_filter|date:"d M Y" }} to {{ report_data.end_date_filter|date:"d M Y" }}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-8 offset-lg-2">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <td class="fw-semibold">Cash and Cash Equivalents, Beginning of Period</td>
                                <td class="text-end">{{ report_data.beginning_cash_balance|floatformat:2|intcomma }}</td>
                            </tr>
                            <tr><td colspan="2"> </td></tr>

                            {# Operating Activities - Placeholder #}
                            <tr class="table-light"><td colspan="2" class="fw-bold">Cash Flows from Operating Activities:</td></tr>
                            {% for item in report_data.operating_activities_items %}
                                <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
                            {% empty %}
                                <tr><td colspan="2" class="text-muted fst-italic">    Detailed operating activities not yet available.</td></tr>
                            {% endfor %}
                            <tr class="fw-semibold">
                                <td>Net Cash from Operating Activities</td>
                                <td class="text-end">{{ report_data.net_cash_from_operating|floatformat:2|intcomma }}</td>
                            </tr>
                            <tr><td colspan="2"> </td></tr>

                            {# Investing Activities - Placeholder #}
                            <tr class="table-light"><td colspan="2" class="fw-bold">Cash Flows from Investing Activities:</td></tr>
                            {% for item in report_data.investing_activities_items %}
                                <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
                            {% empty %}
                                <tr><td colspan="2" class="text-muted fst-italic">    Detailed investing activities not yet available.</td></tr>
                            {% endfor %}
                            <tr class="fw-semibold">
                                <td>Net Cash from Investing Activities</td>
                                <td class="text-end">{{ report_data.net_cash_from_investing|floatformat:2|intcomma }}</td>
                            </tr>
                            <tr><td colspan="2"> </td></tr>

                            {# Financing Activities - Placeholder #}
                            <tr class="table-light"><td colspan="2" class="fw-bold">Cash Flows from Financing Activities:</td></tr>
                            {% for item in report_data.financing_activities_items %}
                                <tr><td>    {{ item.description }}</td><td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td></tr>
                            {% empty %}
                                <tr><td colspan="2" class="text-muted fst-italic">    Detailed financing activities not yet available.</td></tr>
                            {% endfor %}
                            <tr class="fw-semibold">
                                <td>Net Cash from Financing Activities</td>
                                <td class="text-end">{{ report_data.net_cash_from_financing|floatformat:2|intcomma }}</td>
                            </tr>
                            <tr><td colspan="2"> </td></tr>

                            <tr class="fw-bold table-secondary">
                                <td>Net Increase / (Decrease) in Cash and Cash Equivalents</td>
                                <td class="text-end">{{ report_data.net_change_in_cash|floatformat:2|intcomma }}</td>
                            </tr>
                            <tr><td colspan="2"> </td></tr>
                            <tr class="fw-bolder fs-5 table-dark text-white">
                                <td>Cash and Cash Equivalents, End of Period</td>
                                <td class="text-end">{{ report_data.ending_cash_balance|floatformat:2|intcomma }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <p class="text-muted fst-italic mt-3 small">
                        Note: Detailed breakdown of cash flows from operating, investing, and financing activities is a complex calculation.
                        This report currently shows the net change based on beginning and ending cash balances.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


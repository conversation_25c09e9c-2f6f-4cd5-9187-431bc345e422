# apps/hr/templatetags/hr_tags.py
from django import template
from django.utils.html import format_html

register = template.Library()

@register.simple_tag
def get_leave_status_badge(status):
    status_lower = status.lower() if status else ''
    badge_class = 'secondary' # Default
    status_display = status.replace('_', ' ').title() if status else 'N/A'

    if status_lower == 'pending':
        badge_class = 'warning text-dark'
    elif status_lower == 'approved':
        badge_class = 'success'
    elif status_lower == 'rejected':
        badge_class = 'danger'
    elif status_lower == 'cancelled':
        badge_class = 'info text-dark'
    
    return format_html('<span class="badge bg-{}">{}</span>', badge_class, status_display)

# You might also want a filter for this if you prefer that syntax
@register.filter(name='leave_status_badge_filter')
def leave_status_badge_filter(status):
    return get_leave_status_badge(status) # Reuse the logic
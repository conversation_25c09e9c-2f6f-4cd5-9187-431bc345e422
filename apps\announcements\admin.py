from django.contrib import admin
from .models import Announcement

@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = (
        'title', 
        'author', 
        'is_published', 
        'is_sticky', 
        'publish_date', 
        'expiry_date', 
        'get_audience_display_for_admin', # Using a custom method for better display
        'tenant', # If you added this field
        'is_global', # If you added this field
        'created_at'
    )
    list_filter = (
        'is_published', 
        'is_sticky', 
        # 'target_all_staff',       # <<< OLD/INCORRECT NAME
        # 'target_all_parents',     # <<< OLD/INCORRECT NAME
        'target_all_tenant_staff',  # <<< CORRECTED NAME (example)
        'target_all_tenant_parents',# <<< CORRECTED NAME (example)
        'is_global',                # If you added this field
        'tenant',                   # If you added this field
        'publish_date', 
        'expiry_date',
        'target_global_audience_type', # If you added this field
    )
    search_fields = ('title', 'content', 'author__email', 'author__first_name', 'author__last_name')
    filter_horizontal = (
        # 'target_staff_groups',    # <<< OLD/INCORRECT NAME
        'target_tenant_staff_groups', # <<< CORRECTED NAME (example)
    )
    
    fieldsets = (
        (None, {
            'fields': ('title', 'content', 'author')
        }),
        ('Publication Status', {
            'fields': ('is_published', 'is_sticky', 'publish_date', 'expiry_date')
        }),
        ('Audience & Scope', { # New section for clarity
            'fields': ('tenant', 'is_global', 'target_global_audience_type') # If using consolidated model
        }),
        ('Tenant-Specific Audience', { # Fields for when is_global=False and tenant is set
            'classes': ('collapse',), # Optional: make collapsible
            'fields': ('target_all_tenant_staff', 'target_tenant_staff_groups', 'target_all_tenant_parents')
        }),
    )
    # readonly_fields = ('created_at', 'updated_at') # Make these read-only

    # To improve list_display for M2M fields or boolean icons
    def get_audience_display_for_admin(self, obj):
        return obj.get_audience_display()
    get_audience_display_for_admin.short_description = 'Target Audience'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        # Example: Limit author choices if needed, though usually not necessary if using settings.AUTH_USER_MODEL
        # if db_field.name == "author":
        #     kwargs["queryset"] = YourUserModel.objects.filter(is_staff=True) # Example
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def save_model(self, request, obj, form, change):
        if not obj.pk and not obj.author: # If creating and author not set
            obj.author = request.user # Default author to current user
        super().save_model(request, obj, form, change)
        
        








# # D:\school_fees_saas_v2\apps\announcements\admin.py
# from django.contrib import admin
# from django.utils.translation import gettext_lazy as _
# from .models import Announcement # Import your Announcement model
# import logging

# logger = logging.getLogger(__name__)

# # Attempt to import the StaffUser model for type checking and queryset filtering in admin
# # This should match the import logic or definition source in your models.py
# StaffUserFromSchools = None
# try:
#     from apps.schools.models import StaffUser as StaffUserFromSchoolsImport
#     StaffUserFromSchools = StaffUserFromSchoolsImport
#     STAFF_USER_MODEL_AVAILABLE = True
# except ImportError:
#     logger.warning(
#         "AnnouncementAdmin: StaffUser model from 'apps.schools.models' could not be imported. "
#         "Author field functionality in admin might be limited."
#     )
#     STAFF_USER_MODEL_AVAILABLE = False


# @admin.register(Announcement)
# class AnnouncementAdmin(admin.ModelAdmin):
#     list_display = (
#         'title', 
#         'author_display_name', 
#         'audience_summary', 
#         'publish_date', 
#         'expiry_date', 
#         'is_published', 
#         'is_sticky',
#         'created_at'
#     )
#     list_filter = (
#         'is_published', 
#         'is_sticky', 
#         'target_all_staff', 
#         'target_all_parents', 
#         'publish_date', 
#         'expiry_date', 
#         'author'
#     )
#     search_fields = (
#         'title', 
#         'content', 
#         'author__email', 
#         'author__first_name',
#         'author__last_name'
#     )
#     list_editable = ('is_published', 'is_sticky')
    
#     filter_horizontal = ('target_staff_groups',)

#     # Define fieldsets using a method to dynamically include author or readonly author
#     def get_fieldsets(self, request, obj=None):
#         base_fieldsets = [
#             (None, {'fields': ('title', 'content')}),
#             (_('Publication Details'), {
#                 'fields': [
#                     # 'author' or 'readonly_author' will be inserted here
#                     'publish_date', 'expiry_date', 'is_published', 'is_sticky'
#                 ]
#             }),
#             (_('Audience Targeting'), {
#                 'classes': ('collapse',),
#                 'fields': ('target_all_staff', 'target_staff_groups', 'target_all_parents')
#             }),
#             (_('Timestamps'), {
#                 'classes': ('collapse',),
#                 'fields': ('created_at', 'updated_at')
#             })
#         ]

#         publication_fields = base_fieldsets[1][1]['fields'] # Get the list of fields

#         if obj: # Editing existing
#             publication_fields.insert(0, 'readonly_author')
#         else: # Creating new
#             publication_fields.insert(0, 'author')
        
#         return base_fieldsets

#     def get_readonly_fields(self, request, obj=None):
#         if obj: # Editing an existing object
#             return ('created_at', 'updated_at', 'readonly_author')
#         return ('created_at', 'updated_at') # For new object

#     @admin.display(description=_('Author'))
#     def readonly_author(self, obj):
#         if obj.author:
#             # Assuming StaffUser has get_full_name and email
#             return obj.author.get_full_name() or obj.author.email
#         return "N/A"
#     readonly_author.short_description = _('Author (Cannot change)')


#     @admin.display(description=_('Author'), ordering='author__email')
#     def author_display_name(self, obj):
#         if obj.author:
#             return obj.author.get_full_name() or obj.author.email
#         return _("(Not set)")

#     @admin.display(description=_('Target Audience'))
#     def audience_summary(self, obj):
#         return obj.get_audience_display()

#     def save_model(self, request, obj, form, change):
#         if not obj.pk and not obj.author_id: # If new object and author not explicitly set in form
#             if STAFF_USER_MODEL_AVAILABLE and StaffUserFromSchools and isinstance(request.user, StaffUserFromSchools):
#                 obj.author = request.user
#             else:
#                 logger.warning(
#                     f"AnnouncementAdmin: Could not automatically set author for new announcement. "
#                     f"Logged-in user '{request.user}' is not the expected StaffUser type or StaffUser model import failed."
#                 )
#         super().save_model(request, obj, form, change)

#     def formfield_for_foreignkey(self, db_field, request, **kwargs):
#         if db_field.name == "author":
#             if STAFF_USER_MODEL_AVAILABLE and StaffUserFromSchools:
#                 # Optionally limit queryset, e.g., to staff of the current tenant if StaffUser has a school FK
#                 # kwargs["queryset"] = StaffUserFromSchools.objects.filter(school=request.tenant) # Example
#                 pass # Allow selection or rely on save_model
#         return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    
#!/usr/bin/env python
"""
Direct fix for migration dependency issue using Django ORM
"""
import os
import sys
import django
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model, get_public_schema_name

def fix_migration_dependency():
    """Fix the migration dependency issue"""
    print("🔧 Fixing migration dependency issue...")

    # Enable debug output
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    TenantModel = get_tenant_model()
    
    # Get all tenants except public
    tenants = TenantModel.objects.exclude(schema_name=get_public_schema_name())
    
    for tenant in tenants:
        print(f"\n--- Processing tenant: {tenant.schema_name} ---")
        
        # Switch to tenant schema
        connection.set_tenant(tenant)
        
        with connection.cursor() as cursor:
            # Check all fees and payments migrations
            cursor.execute("""
                SELECT app, name, applied
                FROM django_migrations
                WHERE app IN ('fees', 'payments')
                ORDER BY app, name, applied;
            """)
            results = cursor.fetchall()

            print(f"  All fees and payments migrations:")
            for row in results:
                print(f"    {row[0]}.{row[1]} - {row[2]}")

            # Check for missing migrations and add them
            existing_migrations = {(row[0], row[1]): row[2] for row in results}

            # Required migrations in order
            required_migrations = [
                ('fees', '0001_initial'),
                ('fees', '0002_initial'),
                ('fees', '0003_initial'),
                ('payments', '0001_initial'),
                ('payments', '0002_initial'),
                ('payments', '0003_initial'),
            ]

            # Find the latest timestamp to base new timestamps on
            latest_timestamp = None
            if results:
                latest_timestamp = max(row[2] for row in results)
            else:
                from django.utils import timezone
                latest_timestamp = timezone.now()

            # Add missing migrations
            for i, (app, name) in enumerate(required_migrations):
                if (app, name) not in existing_migrations:
                    # Calculate timestamp based on position in sequence
                    new_timestamp = latest_timestamp - timedelta(minutes=len(required_migrations) - i)

                    print(f"  🔧 Adding missing {app}.{name} migration...")
                    cursor.execute("""
                        INSERT INTO django_migrations (app, name, applied)
                        VALUES (%s, %s, %s)
                    """, [app, name, new_timestamp])

                    print(f"  ✅ Added {app}.{name} with timestamp {new_timestamp}")

            # Continue to check order if we have the required migrations
            cursor.execute("""
                SELECT app, name, applied
                FROM django_migrations
                WHERE (app = 'fees' AND name = '0003_initial')
                   OR (app = 'payments' AND name = '0003_initial')
                ORDER BY applied;
            """)
            target_results = cursor.fetchall()

            if len(target_results) != 2:
                print(f"  ⚠️  Still missing target migrations after fix")
                continue
            
            # Check the order
            first_migration = target_results[0]
            second_migration = target_results[1]
            
            print(f"  Current order:")
            print(f"    1. {first_migration[0]}.{first_migration[1]} - {first_migration[2]}")
            print(f"    2. {second_migration[0]}.{second_migration[1]} - {second_migration[2]}")
            
            # Check if payments comes before fees (which is wrong)
            if (first_migration[0] == 'payments' and first_migration[1] == '0003_initial' and
                second_migration[0] == 'fees' and second_migration[1] == '0003_initial'):
                
                print(f"  ❌ Issue found: payments.0003_initial applied before fees.0003_initial")
                
                # Fix by updating fees.0003_initial to have an earlier timestamp
                payments_timestamp = first_migration[2]
                
                # Calculate new timestamp (1 minute earlier)
                new_timestamp = payments_timestamp - timedelta(minutes=1)
                
                cursor.execute("""
                    UPDATE django_migrations 
                    SET applied = %s
                    WHERE app = 'fees' AND name = '0003_initial'
                """, [new_timestamp])
                
                print(f"  ✅ Fixed: Updated fees.0003_initial timestamp to {new_timestamp}")
                
                # Verify the fix
                cursor.execute("""
                    SELECT app, name, applied
                    FROM django_migrations
                    WHERE (app = 'fees' AND name = '0003_initial') 
                       OR (app = 'payments' AND name = '0003_initial')
                    ORDER BY applied;
                """)
                results = cursor.fetchall()
                
                print(f"  New order:")
                for i, row in enumerate(results, 1):
                    print(f"    {i}. {row[0]}.{row[1]} - {row[2]}")
                    
            elif (first_migration[0] == 'fees' and first_migration[1] == '0003_initial' and
                  second_migration[0] == 'payments' and second_migration[1] == '0003_initial'):
                print(f"  ✅ Migration order is already correct")
            else:
                print(f"  ⚠️  Unexpected migration combination")

if __name__ == "__main__":
    try:
        fix_migration_dependency()
        print("\n🎉 Migration dependency fix completed!")
        print("\nNow try running: python manage.py migrate_schemas")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

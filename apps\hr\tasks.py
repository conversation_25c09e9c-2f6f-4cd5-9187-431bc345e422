# D:\school_fees_saas_v2\apps\hr\tasks.py
import logging
from decimal import Decimal
from datetime import date
from django.utils import timezone
from django.db import transaction

from celery import shared_task
from django_tenants.utils import schema_context

from apps.tenants.models import School
from .models import LeaveType, LeaveBalance, LeaveBalanceLog, StaffUser

logger = logging.getLogger(__name__)

@shared_task(name="hr.run_leave_accrual_for_all_tenants")
def run_leave_accrual_for_all_tenants():
    """
    Celery task that iterates through all active tenants and runs the
    leave accrual process for each one.
    """
    active_tenants = School.objects.filter(is_active=True)
    logger.info(f"Starting leave accrual task for {active_tenants.count()} active tenants.")
    
    for tenant in active_tenants:
        run_leave_accrual_for_tenant.delay(tenant.schema_name)
    
    logger.info("Finished dispatching leave accrual tasks for all tenants.")

@shared_task(name="hr.run_leave_accrual_for_tenant")
def run_leave_accrual_for_tenant(schema_name):
    """
    Performs the leave accrual logic for a single tenant.
    """
    with schema_context(schema_name):
        logger.info(f"--- Running leave accrual for tenant: {schema_name} ---")
        
        today = timezone.now().date()
        
        # Find leave types that accrue monthly and should run today
        monthly_leave_types = LeaveType.objects.filter(
            accrues=True, 
            accrual_frequency=LeaveType.AccrualFrequency.MONTHLY
        )
        
        if monthly_leave_types.exists():
            process_accrual_for_types(monthly_leave_types, today)

        # Add similar logic for ANNUAL accrual if it's the right day of the year
        # if today.month == 1 and today.day == 1:
        #     annual_leave_types = LeaveType.objects.filter(...)
        #     process_accrual_for_types(annual_leave_types, today)

        logger.info(f"--- Finished leave accrual for tenant: {schema_name} ---")

def process_accrual_for_types(leave_types, processing_date):
    """Helper function to process a set of leave types for all active staff."""
    active_staff = StaffUser.objects.filter(is_active=True)
    
    for leave_type in leave_types:
        logger.info(f"Processing accrual for Leave Type: '{leave_type.name}'")
        
        for staff in active_staff:
            try:
                with transaction.atomic():
                    balance, created = LeaveBalance.objects.get_or_create(
                        staff_user=staff,
                        leave_type=leave_type,
                        defaults={'balance': Decimal('0.00')}
                    )
                    
                    amount_to_accrue = leave_type.accrual_rate
                    
                    # Handle proration for new hires if applicable
                    if leave_type.prorate_accrual and staff.date_hired and \
                       staff.date_hired.year == processing_date.year and \
                       staff.date_hired.month == processing_date.month:
                        # Simple proration: only accrue if hired in the first half of the month
                        if staff.date_hired.day > 15:
                            logger.info(f"  Skipping accrual for new hire '{staff.email}' (hired after 15th).")
                            continue
                        # A more complex proration could be implemented here
                        
                    new_balance = balance.balance + amount_to_accrue
                    
                    # Enforce max accrual limit
                    if leave_type.max_accrual_balance is not None and new_balance > leave_type.max_accrual_balance:
                        amount_to_accrue = leave_type.max_accrual_balance - balance.balance
                        new_balance = leave_type.max_accrual_balance
                        
                        if amount_to_accrue < 0: # Already over the limit
                            amount_to_accrue = 0
                            
                    if amount_to_accrue > 0:
                        balance.balance = new_balance
                        balance.save()
                        
                        LeaveBalanceLog.objects.create(
                            leave_balance=balance,
                            action=LeaveBalanceLog.Action.ACCRUAL,
                            change_amount=amount_to_accrue,
                            notes=f"Monthly accrual on {processing_date.strftime('%Y-%m-%d')}."
                        )
                        logger.info(f"  Accrued {amount_to_accrue} days for '{staff.email}'. New balance: {new_balance}")
                    else:
                        logger.info(f"  No accrual for '{staff.email}' (at or over max balance).")

            except Exception as e:
                logger.error(f"Failed to process accrual for staff '{staff.email}' and leave type '{leave_type.name}': {e}", exc_info=True)
                
                
                
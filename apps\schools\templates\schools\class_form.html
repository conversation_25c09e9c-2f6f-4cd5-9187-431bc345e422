{% extends "tenant_base.html" %}
{% load widget_tweaks %}

{% block title %}{{ view_title|default:"Class/Grade Management" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title|default:"Class/Grade Management" }}</h4>
                </div>
                <div class="card-body p-4">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="text-danger">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mt-4 d-flex justify-content-end">
                            <a href="{% url 'schools:class_list' %}" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-success">
                                {% if object %}Update{% else %}Create{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


{# D:\school_fees_saas_v2\apps\hr\templates\hr\process_payroll_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">{{ view_title }}</h1>
            </div>

            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading"><i class="bi bi-exclamation-triangle-fill me-2"></i>{% trans "Important Note" %}</h4>
                <p>{% trans "Processing payroll will generate payslips for ALL active staff members based on their current salary structures. This action cannot be easily undone." %}</p>
                <hr>
                <p class="mb-0">{% trans "Please ensure all salary details and leave records for the selected period are up to date before proceeding." %}</p>
            </div>

            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.month.id_for_label }}" class="form-label required">{% trans "Pay Period Month" %}</label>
                                {{ form.month }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.year.id_for_label }}" class="form-label required">{% trans "Pay Period Year" %}</label>
                                {{ form.year }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.payment_date.id_for_label }}" class="form-label required">{% trans "Salary Payment Date" %}</label>
                                {{ form.payment_date }}
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'hr:payroll_run_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-play-circle-fill me-2"></i>{% trans "Process Payroll for Selected Period" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



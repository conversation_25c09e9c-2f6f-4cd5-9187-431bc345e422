{# apps/students/templates/students/student_list_pdf.html - Student List PDF Export Template #}
{% load i18n %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{% trans "Student List Report" %}</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .school-logo {
            max-height: 60px;
            margin-bottom: 10px;
        }
        
        .school-name {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin: 5px 0;
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0 5px 0;
            color: #333;
        }
        
        .report-date {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .summary-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #007bff;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            margin: 5px;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .filters-applied {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 3px;
            padding: 8px;
            margin-bottom: 15px;
            font-size: 9px;
        }
        
        .filters-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #0056b3;
        }
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        .student-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        
        .student-table th {
            background-color: #007bff;
            color: white;
            padding: 8px 4px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #0056b3;
            font-size: 9px;
        }
        
        .student-table td {
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        .student-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .student-table tbody tr:hover {
            background-color: #e7f3ff;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-suspended {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-graduated {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .student-name {
            font-weight: bold;
            color: #007bff;
        }
        
        .admission-number {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 1px 3px;
            border-radius: 2px;
        }
        
        .class-section {
            white-space: nowrap;
        }
        
        .contact-info {
            font-size: 8px;
            color: #666;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        {% if school_profile.logo %}
            <img src="{{ school_profile.logo.url }}" alt="School Logo" class="school-logo">
        {% endif %}
        <div class="school-name">
            {{ school_profile.name|default:"School Management System" }}
        </div>
        <div class="report-title">{% trans "Student List Report" %}</div>
        <div class="report-date">
            {% trans "Generated on" %}: {{ current_date|date:"F d, Y \a\t H:i" }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="summary-title">{% trans "Summary Statistics" %}</div>
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-number">{{ total_students }}</span>
                <span class="stat-label">{% trans "Total Students" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ active_students }}</span>
                <span class="stat-label">{% trans "Active Students" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ total_classes }}</span>
                <span class="stat-label">{% trans "Classes" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ male_students }}</span>
                <span class="stat-label">{% trans "Male Students" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ female_students }}</span>
                <span class="stat-label">{% trans "Female Students" %}</span>
            </div>
        </div>
    </div>

    <!-- Filters Applied -->
    {% if filters_applied %}
    <div class="filters-applied">
        <div class="filters-title">{% trans "Filters Applied:" %}</div>
        {{ filters_applied }}
    </div>
    {% endif %}

    <!-- Student Table -->
    {% if students %}
    <div class="table-container">
        <table class="student-table">
            <thead>
                <tr>
                    <th style="width: 15%;">{% trans "Student Name" %}</th>
                    <th style="width: 10%;">{% trans "Admission No." %}</th>
                    <th style="width: 12%;">{% trans "Class & Section" %}</th>
                    <th style="width: 8%;">{% trans "Status" %}</th>
                    <th style="width: 8%;">{% trans "Gender" %}</th>
                    <th style="width: 10%;">{% trans "Date of Birth" %}</th>
                    <th style="width: 15%;">{% trans "Primary Guardian" %}</th>
                    <th style="width: 12%;">{% trans "Contact" %}</th>
                    <th style="width: 10%;">{% trans "Date Admitted" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for student in students %}
                <tr>
                    <td>
                        <div class="student-name">{{ student.full_name }}</div>
                    </td>
                    <td>
                        <span class="admission-number">{{ student.admission_number }}</span>
                    </td>
                    <td>
                        <div class="class-section">
                            {% if student.current_class %}
                                {{ student.current_class.name }}
                                {% if student.current_section %}
                                    - {{ student.current_section.name }}
                                {% endif %}
                            {% else %}
                                <em>{% trans "Not Assigned" %}</em>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-{{ student.status|lower }}">
                            {{ student.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {{ student.get_gender_display|default:"-" }}
                    </td>
                    <td>
                        {% if student.date_of_birth %}
                            {{ student.date_of_birth|date:"M d, Y" }}
                            {% if student.calculated_age %}
                                <br><small>({{ student.calculated_age }} yrs)</small>
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if student.parents.exists %}
                            {% for parent in student.parents.all|slice:":1" %}
                                <div>{{ parent.get_full_name }}</div>
                                <div class="contact-info">{{ parent.get_relationship_display|default:"Guardian" }}</div>
                            {% endfor %}
                        {% else %}
                            <em>{% trans "No Guardian" %}</em>
                        {% endif %}
                    </td>
                    <td>
                        {% if student.parents.exists %}
                            {% for parent in student.parents.all|slice:":1" %}
                                {% if parent.phone_number %}
                                    <div class="contact-info">{{ parent.phone_number }}</div>
                                {% endif %}
                                {% if parent.email %}
                                    <div class="contact-info">{{ parent.email }}</div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {{ student.date_of_admission|date:"M d, Y" }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="no-data">
        <i class="bi bi-people" style="font-size: 48px; color: #ccc;"></i>
        <p>{% trans "No students found matching the selected criteria." %}</p>
    </div>
    {% endif %}

    <div class="footer">
        <p>
            {% trans "This report contains" %} {{ students|length }} {% trans "student(s)" %} 
            {% if total_students != students|length %}
                {% trans "out of" %} {{ total_students }} {% trans "total students" %}
            {% endif %}
            | {% trans "Generated by" %} {{ school_profile.name|default:"School Management System" }}
        </p>
    </div>
</body>
</html>

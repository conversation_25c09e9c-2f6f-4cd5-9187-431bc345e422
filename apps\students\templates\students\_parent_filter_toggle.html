{# apps/students/templates/students/_parent_filter_toggle.html - Parent List Toggle Filter #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<style>
    /* Premium Parent Filter Toggle Design */
    .premium-parent-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-parent-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-parent-filter-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-radius: 1.5rem 1.5rem 0 0;
        position: relative;
        overflow: hidden;
    }

    .premium-parent-filter-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .premium-parent-filter-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .premium-parent-filter-header .toggle-icon {
        transition: transform 0.3s ease;
        font-size: 1.2rem;
    }

    .premium-parent-filter-header[aria-expanded="true"] .toggle-icon {
        transform: rotate(180deg);
    }

    .premium-parent-filter-body {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .premium-parent-form-group {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .premium-parent-form-group label {
        position: absolute;
        top: 0.75rem;
        left: 1rem;
        background: white;
        padding: 0 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 2;
    }

    .premium-parent-form-group input:focus + label,
    .premium-parent-form-group input:not(:placeholder-shown) + label,
    .premium-parent-form-group select:focus + label,
    .premium-parent-form-group select:not([value=""]) + label {
        top: -0.5rem;
        font-size: 0.75rem;
        color: #667eea;
        font-weight: 500;
    }

    .premium-parent-form-control {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
    }

    .premium-parent-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
        background: white;
    }

    .premium-parent-form-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 3;
        transition: color 0.3s ease;
    }

    .premium-parent-form-group:focus-within .premium-parent-form-icon {
        color: #667eea;
    }

    .premium-parent-action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .premium-parent-filter-actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .premium-parent-export-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .premium-parent-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        white-space: nowrap;
    }

    .premium-parent-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .premium-parent-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .premium-parent-btn-secondary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border: 1px solid #dee2e6;
    }

    .premium-parent-btn-secondary:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        transform: translateY(-1px);
        color: #495057;
    }

    .premium-parent-btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .premium-parent-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .premium-parent-btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .premium-parent-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        color: white;
    }

    .premium-parent-btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: #212529;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .premium-parent-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
        color: #212529;
    }

    .premium-parent-btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .premium-parent-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        color: white;
    }

    .premium-parent-btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    /* Loading states */
    .premium-parent-btn.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    .premium-parent-btn.loading::after {
        content: '';
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .premium-parent-filter-header,
        .premium-parent-filter-body {
            padding: 1rem;
        }

        .premium-parent-action-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .premium-parent-filter-actions,
        .premium-parent-export-actions {
            justify-content: center;
        }

        .premium-parent-btn {
            justify-content: center;
        }
    }

    /* Animation for collapse */
    .premium-parent-filter-collapse {
        transition: all 0.3s ease;
    }

    .premium-parent-filter-collapse.collapsing {
        transition: height 0.3s ease;
    }
</style>

<div class="premium-parent-filter-card">
    <div class="premium-parent-filter-header" 
         data-bs-toggle="collapse" 
         data-bs-target="#parentFilterCollapse" 
         aria-expanded="false" 
         aria-controls="parentFilterCollapse"
         role="button">
        <h5>
            <i class="bi bi-funnel-fill"></i>
            {% trans "Toggle Filters" %}
            <div class="ms-auto d-flex align-items-center gap-3">
                <!-- Export Actions in Header -->
                <div class="premium-parent-export-actions">
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=csv"
                       class="premium-parent-btn premium-parent-btn-success premium-parent-btn-sm"
                       title="{% trans 'Export as CSV' %}">
                        <i class="bi bi-filetype-csv"></i>
                        <span class="d-none d-md-inline">CSV</span>
                    </a>
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=excel"
                       class="premium-parent-btn premium-parent-btn-info premium-parent-btn-sm"
                       title="{% trans 'Export as Excel' %}">
                        <i class="bi bi-file-earmark-excel"></i>
                        <span class="d-none d-md-inline">Excel</span>
                    </a>
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=pdf"
                       class="premium-parent-btn premium-parent-btn-danger premium-parent-btn-sm"
                       title="{% trans 'Export as PDF' %}">
                        <i class="bi bi-filetype-pdf"></i>
                        <span class="d-none d-md-inline">PDF</span>
                    </a>
                    <button type="button"
                            class="premium-parent-btn premium-parent-btn-warning premium-parent-btn-sm"
                            data-bs-toggle="modal"
                            data-bs-target="#importParentModal"
                            title="{% trans 'Import from Excel' %}">
                        <i class="bi bi-upload"></i>
                        <span class="d-none d-md-inline">{% trans "Import" %}</span>
                    </button>
                </div>
                <i class="bi bi-chevron-down toggle-icon"></i>
            </div>
        </h5>
    </div>
    
    <div class="collapse premium-parent-filter-collapse" id="parentFilterCollapse">
        <div class="premium-parent-filter-body">
            <form method="get" id="parentFilterForm">
                <div class="row g-4">
                    {% for field in filter_form %}
                        <div class="col-md-4 col-sm-6">
                            <div class="premium-parent-form-group">
                                {% if field.field.widget.input_type == 'text' %}
                                    <i class="premium-parent-form-icon bi bi-search"></i>
                                {% elif field.field.widget.input_type == 'email' %}
                                    <i class="premium-parent-form-icon bi bi-envelope"></i>
                                {% elif field.field.widget.input_type == 'select' or field.field.widget.input_type == 'select_multiple' %}
                                    <i class="premium-parent-form-icon bi bi-list"></i>
                                {% else %}
                                    <i class="premium-parent-form-icon bi bi-filter"></i>
                                {% endif %}
                                
                                {{ field|add_class:"premium-parent-form-control" }}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <div class="premium-parent-action-buttons">
                    <div class="premium-parent-filter-actions">
                        <button type="submit" class="premium-parent-btn premium-parent-btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        {% if request.GET %}
                            <a href="{% url 'students:parentuser_list' %}" 
                               class="premium-parent-btn premium-parent-btn-secondary">
                                <i class="bi bi-x-circle"></i>
                                {% trans "Clear Filters" %}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form interactions
    const form = document.getElementById('parentFilterForm');
    const formControls = document.querySelectorAll('.premium-parent-form-control');
    
    // Add floating label behavior
    formControls.forEach(control => {
        // Set initial state
        if (control.value && control.value !== '') {
            control.classList.add('has-value');
        }
        
        // Handle input events
        control.addEventListener('input', function() {
            if (this.value && this.value !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
        
        // Handle change events for selects
        control.addEventListener('change', function() {
            if (this.value && this.value !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
    
    // Add loading states to buttons
    const buttons = document.querySelectorAll('.premium-parent-btn');
    buttons.forEach(button => {
        if (button.type === 'submit' || button.href) {
            button.addEventListener('click', function() {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    // Remove loading state after 3 seconds as fallback
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 3000);
                }
            });
        }
    });
    
    // Auto-submit form on filter change (optional)
    // Uncomment if you want instant filtering
    /*
    formControls.forEach(control => {
        control.addEventListener('change', function() {
            if (this.value !== '') {
                form.submit();
            }
        });
    });
    */
});
</script>
{% endif %}

# Generated by Django 5.1.9 on 2025-07-05 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0002_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='school',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Controlled by subscription status.', verbose_name='Platform Active Status'),
        ),
        migrations.AlterField(
            model_name='school',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly version of name. Auto-generated.', max_length=100, unique=True),
        ),
    ]

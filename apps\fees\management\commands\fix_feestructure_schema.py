"""
Management command to fix FeeStructure schema issue
Usage: python manage.py fix_feestructure_schema
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import get_tenant_model

class Command(BaseCommand):
    help = 'Fix FeeStructure schema by removing school_class_id column'

    def handle(self, *args, **options):
        self.stdout.write("🔧 FIXING FEESTRUCTURE SCHEMA ISSUE")
        self.stdout.write("=" * 50)
        
        # Get all tenants
        TenantModel = get_tenant_model()
        tenants = TenantModel.objects.all()
        
        self.stdout.write(f"📋 Found {tenants.count()} tenants")
        
        for tenant in tenants:
            self.stdout.write(f"\n🔍 Checking tenant: {tenant.schema_name}")
            
            # Switch to tenant schema
            connection.set_tenant(tenant)
            
            with connection.cursor() as cursor:
                # Check if fees_feestructure table exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name = 'fees_feestructure'
                    )
                """, [tenant.schema_name])
                
                if not cursor.fetchone()[0]:
                    self.stdout.write(f"  ⏭️  No fees_feestructure table found, skipping")
                    continue
                
                # Check current columns
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = 'fees_feestructure'
                    ORDER BY ordinal_position
                """, [tenant.schema_name])
                
                columns = cursor.fetchall()
                self.stdout.write(f"  📊 Current columns: {[col[0] for col in columns]}")
                
                # Check if school_class_id exists
                school_class_id_exists = any(col[0] == 'school_class_id' for col in columns)
                
                if school_class_id_exists:
                    self.stdout.write(f"  ⚠️  Found problematic school_class_id column!")
                    
                    try:
                        # Drop foreign key constraints first
                        cursor.execute("""
                            SELECT tc.constraint_name
                            FROM information_schema.table_constraints tc
                            JOIN information_schema.constraint_column_usage ccu
                            ON tc.constraint_name = ccu.constraint_name
                            WHERE tc.table_schema = %s
                            AND tc.table_name = 'fees_feestructure'
                            AND ccu.column_name = 'school_class_id'
                            AND tc.constraint_type = 'FOREIGN KEY'
                        """, [tenant.schema_name])
                        
                        constraints = cursor.fetchall()
                        for constraint in constraints:
                            constraint_name = constraint[0]
                            cursor.execute(f'ALTER TABLE fees_feestructure DROP CONSTRAINT "{constraint_name}"')
                            self.stdout.write(f"    ✅ Dropped constraint: {constraint_name}")
                        
                        # Drop the column
                        cursor.execute('ALTER TABLE fees_feestructure DROP COLUMN school_class_id')
                        self.stdout.write(f"    ✅ Dropped school_class_id column")
                        
                    except Exception as e:
                        self.stdout.write(f"    ❌ Error: {e}")
                
                else:
                    self.stdout.write(f"  ✅ No school_class_id column found - schema is correct")
        
        self.stdout.write(f"\n🎉 Schema fix completed!")
        self.stdout.write(f"💡 You can now create Fee Structures without the constraint error.")

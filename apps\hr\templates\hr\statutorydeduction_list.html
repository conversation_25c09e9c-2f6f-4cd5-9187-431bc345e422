{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="h3 mb-0">{{ view_title }}</h1>
            <p class="text-muted mb-0">Define rules for recurring payroll deductions like Pension, Social Security, etc.</p>
        </div>
        {% if perms.hr.add_statutorydeduction %}
        <a href="{% url 'hr:deduction_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Add New Deduction
        </a>
        {% endif %}
    </div>
    
    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Payslip Label</th>
                            <th>Deduction Name</th>
                            <th class="text-end">Employee Rate</th>
                            <th class="text-end">Employer Rate</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for deduction in deductions %}
                        <tr>
                            <td class="align-middle">{{ deduction.payslip_label|default:"-" }}</td>
                            <td class="align-middle"><strong>{{ deduction.name }}</strong></td>
                            <td class="align-middle text-end">{{ deduction.employee_contribution_rate }}%</td>
                            <td class="align-middle text-end">{{ deduction.employer_contribution_rate }}%</td>
                            <td class="align-middle text-center">
                                {% if deduction.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td class="align-middle text-center">
                                {% if perms.hr.change_statutorydeduction %}
                                <a href="{% url 'hr:deduction_update' deduction.pk %}" class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                {% endif %}
                                {% if perms.hr.delete_statutorydeduction %}
                                <a href="{% url 'hr:deduction_delete' deduction.pk %}" class="btn btn-sm btn-outline-danger ms-1" title="Delete">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center p-4">No statutory deduction rules have been defined yet.</td>
                            </tr>
                        {% endfor %}
                        {% comment %} {% empty %}
                        <tr>
                            <td colspan="5" class="text-center p-4">No statutory deduction rules have been defined yet.</td>
                        </tr>
                        {% endfor %} {% endcomment %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



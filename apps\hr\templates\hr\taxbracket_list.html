{# D:\school_fees_saas_v2\apps\hr\templates\hr\taxbracket_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:"Tax Brackets" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3">{{ view_title|default:"Tax Brackets" }}</h1>
        {% if perms.hr.add_taxbracket %}
        <a href="{% url 'hr:taxbracket_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Add New Bracket
        </a>
        {% endif %}
    </div>

    <div class="alert alert-info small">
        <i class="bi bi-info-circle-fill me-2"></i>
        These brackets define the progressive income tax calculation. They should be configured according to your local government regulations.
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h6 class="m-0">Configured Tax Brackets</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Bracket Name</th>
                            <th class="text-end">From Amount</th>
                            <th class="text-end">To Amount</th>
                            <th class="text-center">Rate</th>
                            <th class="text-end">Deduction</th>
                            <th class="text-center">Active</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bracket in object_list %} {# Default context name for ListView is 'object_list' #}
                        <tr>
                            <td><strong>{{ bracket.name }}</strong></td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ bracket.from_amount|intcomma }}</td>
                            <td class="text-end">{% if bracket.to_amount %}{{ school_profile.currency_symbol|default:'$' }}{{ bracket.to_amount|intcomma }}{% else %}∞ (and above){% endif %}</td>
                            <td class="text-center">{{ bracket.rate_percent }}%</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ bracket.deduction_amount|intcomma }}</td>
                            <td class="text-center">
                                {% if bracket.is_active %}<i class="bi bi-check-circle-fill text-success"></i>{% else %}<i class="bi bi-x-circle-fill text-danger"></i>{% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'hr:taxbracket_update' pk=bracket.pk %}" class="btn btn-sm btn-outline-primary" title="Edit"><i class="bi bi-pencil-fill"></i></a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center p-4">No tax brackets have been configured yet.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}



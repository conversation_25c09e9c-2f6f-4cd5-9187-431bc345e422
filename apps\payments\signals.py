# D:\school_fees_saas_v2\apps\payments\signals.py

import logging
from decimal import Decimal
from django.db import models, transaction
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver

# Import the necessary models
from .models import PaymentAllocation
from apps.fees.models import Invoice

logger = logging.getLogger(__name__)

@receiver(post_save, sender=PaymentAllocation)
def update_invoice_on_allocation_save(sender, instance, created, **kwargs):
    """
    Signal to update the related Invoice's amount_paid and status
    whenever a PaymentAllocation is created or updated.
    """
    if instance.invoice:
        logger.debug(f"Signal update_invoice_on_allocation_save triggered for Invoice PK {instance.invoice.pk} from Allocation PK {instance.pk}.")
        # Use a transaction to ensure this update is atomic
        try:
            with transaction.atomic():
                # Recalculate the total paid amount for the invoice from scratch
                # to ensure accuracy, especially if allocations are updated or deleted.
                invoice_to_update = Invoice.objects.select_for_update().get(pk=instance.invoice.pk)
                
                total_paid = invoice_to_update.allocations.aggregate(
                    total=models.Sum('amount_allocated')
                )['total'] or Decimal('0.00')
                
                invoice_to_update.amount_paid = total_paid
                # Call the model method to update status based on the new balance
                invoice_to_update.update_status_based_on_balance() 
                
                invoice_to_update.save(update_fields=['amount_paid', 'status'])
                logger.info(f"Successfully updated Invoice PK {invoice_to_update.pk}. New amount_paid: {invoice_to_update.amount_paid}, New status: {invoice_to_update.status}")
        
        except Invoice.DoesNotExist:
             logger.error(f"Invoice with PK {instance.invoice.pk} not found during signal processing.")
        except Exception as e:
            logger.error(f"Error in update_invoice_on_allocation_save signal for Invoice PK {instance.invoice.pk}: {e}", exc_info=True)


@receiver(pre_delete, sender=PaymentAllocation)
def update_invoice_on_allocation_delete(sender, instance, **kwargs):
    """
    Signal to update the related Invoice's amount_paid and status
    BEFORE a PaymentAllocation is deleted.
    """
    if instance.invoice:
        logger.debug(f"Signal update_invoice_on_allocation_delete triggered for Invoice PK {instance.invoice.pk} from Allocation PK {instance.pk}.")
        try:
            with transaction.atomic():
                invoice_to_update = Invoice.objects.select_for_update().get(pk=instance.invoice.pk)
                
                # Subtract the amount of the allocation being deleted
                invoice_to_update.amount_paid -= instance.amount_allocated
                
                # Ensure amount_paid doesn't go below zero
                if invoice_to_update.amount_paid < Decimal('0.00'):
                    invoice_to_update.amount_paid = Decimal('0.00')
                
                # Call the model method to update status based on the new balance
                invoice_to_update.update_status_based_on_balance()
                
                invoice_to_update.save(update_fields=['amount_paid', 'status'])
                logger.info(f"Successfully updated Invoice PK {invoice_to_update.pk} before deleting allocation. New amount_paid: {invoice_to_update.amount_paid}, New status: {invoice_to_update.status}")

        except Invoice.DoesNotExist:
            logger.error(f"Invoice with PK {instance.invoice.pk} not found during pre_delete signal processing.")
        except Exception as e:
            logger.error(f"Error in update_invoice_on_allocation_delete signal for Invoice PK {instance.invoice.pk}: {e}", exc_info=True)
            
            
            
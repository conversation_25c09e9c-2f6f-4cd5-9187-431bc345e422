{# D:\school_fees_saas_v2\templates\public_base.html #}
{% extends "base.html" %} {# Assumes base.html provides basic HTML shell, Bootstrap JS, etc. #}

{% load static core_tags %} {# For active_nav_link and other custom tags #}

{% block title %}{% block public_page_title %}School Fees Platform{% endblock public_page_title %}{% endblock title %}

{% block page_specific_css %}
    {{ block.super }} {# Inherit from base.html if it has this block #}
    {# Bootstrap Icons - Already included from base.html or here, ensure it's loaded once #}
    {# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"> #}
    
    {# Google Fonts - Good to have them here if specific to public site #}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    {# Core custom styles (already in base.html or ensure it's here) #}
    {# <link rel="stylesheet" href="{% static 'css/custom_base_styles.css' %}"> #}
    
    {# Styles specific to the overall public site layout #}
    <link rel="stylesheet" href="{% static 'css/public_site_styles.css' %}">
    
    {% block extra_public_css %}{% endblock extra_public_css %} {# For page-specific public CSS like public_home_hero.css #}

    <style>
        /* Styles for the welcome message bar - can be moved to public_site_styles.css */
        .welcome-message-bar {
            background-color: #0f5132; /* A darker, trustworthy green - adjust to your brand */
            color: white;
            padding: 0.6rem 1rem;
            text-align: center;
            font-size: 1rem; /* Slightly smaller for a sub-header feel */
            font-weight: 500;
            line-height: 1.5;
            /* box-shadow: inset 0 -2px 5px rgba(0,0,0,0.1); */ /* Optional subtle depth */
        }
        /* Styling for the main content area padding, if needed outside the container */
        .main-content-area-wrapper {
            padding-top: 2rem; /* Add some space if sections don't have their own top padding */
            padding-bottom: 2rem;
        }
        body { /* Ensure fonts are applied broadly if not already in base.html */
            font-family: 'Poppins', sans-serif;
        }
        h1, h2, h3, h4, h5, h6, .navbar-brand, .btn { /* Example for headings and key elements */
            font-family: 'Montserrat', sans-serif;
        }
    </style>
{% endblock page_specific_css %}

{% block body_content %} {# Assuming body_content is the main block in base.html that navbar, content etc. go into #}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-navbar shadow-sm">
    <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="{% url 'public_site:home' %}">
            School Fees SaaS
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#publicAppNavbar" aria-controls="publicAppNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="publicAppNavbar">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                {% active_nav_link 'public_site:home' 'Home' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-house-door-fill' %}
                {% active_nav_link 'public_site:features' 'Features' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-stars' %}
                {% active_nav_link 'public_site:pricing' 'Pricing' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-tags-fill' %}
                {% active_nav_link 'public_site:testimonial_list' 'Reviews' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-chat-quote-fill' %}
                {% active_nav_link 'public_site:about' 'About Us' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-info-circle-fill' %}
                {% active_nav_link 'public_site:contact' 'Contact' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-envelope-fill' %}
            </ul>
            <ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center">
                {% if user.is_authenticated %}
                    {% if user.is_superuser %} {# Check if the user is a platform superuser #}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="platformAdminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-lock-fill me-1"></i>Platform Admin
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="platformAdminDropdown">
                                <li><a class="dropdown-item" href="/admin/" target="_blank"><i class="bi bi-sliders me-2"></i>Django Admin</a></li>
                                <li><a class="dropdown-item" href="{% url 'platform_management:platform_announcement_list' %}"><i class="bi bi-megaphone-fill me-2"></i>Manage Announcements</a></li>
                                {# Add other platform-level admin links here, e.g., manage subscriptions, users #}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% if owned_school_for_public_admin %}{{ owned_school_for_public_admin.get_absolute_url }}{% else %}{% url 'users:select_tenant_dashboard' %}{% endif %}"><i class="bi bi-speedometer2 me-2"></i>My School Dashboard</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'users:school_admin_logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% elif user_type_flags.IS_PUBLIC_ADMIN_USER %} {# School Owner (not necessarily platform superuser) #}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdownPublic" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-fill me-1"></i>
                                <span>Welcome, {{ user.get_full_name|default:user.email }}!</span>
                                {% comment %} Welcome, {{ request.user.first_name|default:request.user.email|truncatechars:20 }} {% endcomment %}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdownPublic">
                                {% if owned_school_for_public_admin %}
                                    <li><a class="dropdown-item" href="{{ owned_school_for_public_admin.get_absolute_url }}"><i class="bi bi-speedometer2 me-2"></i>My School Dashboard</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                {% elif not is_public_schema_active and current_tenant and current_tenant.owner_id == request.user.pk %}
                                    {# Link to current tenant dashboard if they are on its domain #}
                                    <li><a class="dropdown-item" href="{% url 'schools:dashboard' %}"><i class="bi bi-speedometer2 me-2"></i>Current School Dashboard</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                {% else %}
                                    {# Link to a page where they can select which tenant dashboard to go to, or a default page #}
                                    <li><a class="dropdown-item" href="{% url 'users:select_tenant_dashboard' %}"><i class="bi bi-card-list me-2"></i>My Schools</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{% url 'users:school_admin_logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% endif %}
                {% else %} {# User is not logged in #}
                    <li class="nav-item me-2">
                        <a class="nav-link" href="{% url 'public_site:find_school' %}">
                            <i class="bi bi-search me-1"></i> Find School
                        </a>
                    </li>
                    <li class="nav-item me-2">
                        <a href="{% url 'tenants:register_school' %}" class="btn btn-warning btn-sm">
                        <i class="bi bi-building-add me-1"></i> Register School
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'users:school_admin_login' %}" class="btn btn-light btn-sm">
                            <i class="bi bi-person-check-fill me-1"></i> Admin Login
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
{% endblock navbar %}

{# WELCOME MESSAGE BAR AREA - Child templates can populate this #}
{% block welcome_message_area %}
{% endblock welcome_message_area %}

{# FULL WIDTH CONTENT AREA - For hero sections etc. #}
{% block content_full_width %}
    {# Child templates (like home.html) will override this #}
{% endblock content_full_width %}

    {# MAIN CONTENT AREA - For standard container-wrapped content #}
    <div class="main-content-area-wrapper"> {# Wrapper for consistent padding if needed #}
        {% block content %}
            {# Default content, typically wrapped in a <div class="container py-X"> by child pages if not full-width #}
            <div class="container py-4">
                <p>This is default content from public_base.html. Child templates should override the 'content' block, potentially with their own container.</p>
            </div>
        {% endblock content %}
    </div>

    {% block footer %}
    <footer class="footer mt-auto py-4 bg-dark text-white-50"> {# Changed to dark footer for contrast #}
        <div class="container text-center">
            <div class="mb-3">
                <a href="{% url 'public_site:home' %}" class="text-white-50 me-3">Home</a>
                <a href="{% url 'public_site:features' %}" class="text-white-50 me-3">Features</a>
                <a href="{% url 'public_site:pricing' %}" class="text-white-50 me-3">Pricing</a>
                <a href="{% url 'public_site:contact' %}" class="text-white-50 me-3">Contact Us</a>
            </div>
            <p class="mb-1">
                <a href="{% url 'public_site:terms' %}" class="text-white-50 small me-3">Terms of Service</a>
                <a href="{% url 'public_site:privacy' %}" class="text-white-50 small">Privacy Policy</a>
            </p>
            <p class="mb-0 small">© {% now "Y" %} School Fees SaaS Platform. All rights reserved. <br> Powered by YourCompanyName.</p>
        </div>
    </footer>
    {% endblock footer %}

{% endblock body_content %}

{% block page_specific_js %}
    {{ block.super }}
    {% block extra_public_js %}{% endblock extra_public_js %}
{% endblock page_specific_js %}











{% comment %} {# D:\school_fees_saas_v2\templates\public_base.html #}
{% extends "base.html" %} {# Assumes base.html provides basic HTML shell, Bootstrap JS, etc. #}
{% load static core_tags %} {# For active_nav_link and other custom tags #}

{% block title %}{% block public_page_title %}School Fees Platform{% endblock %}{% endblock %}

{% block page_specific_css %}
    {{ block.super }} {# Inherit from base.html if it has this block #}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{% static 'css/custom_base_styles.css' %}"> {# Your general custom styles #}
    <link rel="stylesheet" href="{% static 'css/public_site_styles.css' %}"> {# Styles specific to public site #}
    {% block extra_public_css %}{% endblock %} {# For page-specific public CSS #}

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    {% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-navbar shadow-sm"> {# sticky-navbar if defined in custom_base_styles.css #}
    <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="{% url 'public_site:home' %}">
            {# <img src="{% static 'img/platform_logo_light.png' %}" alt="Platform Logo" height="30" class="d-inline-block align-top me-2"> #}
            School Fees SaaS
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#publicAppNavbar" aria-controls="publicAppNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="publicAppNavbar">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">

                {% active_nav_link 'public_site:home' 'Home' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-house-door-fill' %}
                {% active_nav_link 'public_site:features' 'Features' nav_item_class='nav-item' link_class='nav-link' icon_class='bi bi-stars' %}
                {% comment %} {% active_nav_link 'public_site:home' 'Home' nav_item_class='nav-item' link_class='nav-link' %}
                {% active_nav_link 'public_site:features' 'Features' nav_item_class='nav-item' link_class='nav-link' %} ############
                {% active_nav_link 'public_site:pricing' 'Pricing' nav_item_class='nav-item' link_class='nav-link' %}
                {% active_nav_link 'public_site:testimonial_list' 'Reviews' nav_item_class='nav-item' link_class='nav-link' %}
                {% active_nav_link 'public_site:about' 'About Us' nav_item_class='nav-item' link_class='nav-link' %}
                {% active_nav_link 'public_site:contact' 'Contact' nav_item_class='nav-item' link_class='nav-link' %}
            </ul>
            <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                {# Use user_type_flags and owned_school_for_public_admin from context processor #}
                {% if user_type_flags.IS_AUTHENTICATED_USER %}
                    {% if user_type_flags.IS_PUBLIC_ADMIN_USER %}
                        {# This is a Public Admin/Owner #}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdownPublic" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-fill me-1"></i>
                                Welcome, {{ request.user.first_name|default:request.user.email }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdownPublic">
                                {% if owned_school_for_public_admin %}
                                    {# owned_school_for_public_admin already has .get_absolute_url() #}
                                    <li><a class="dropdown-item" href="{{ owned_school_for_public_admin.get_absolute_url }}">My School Dashboard</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="/admin/" target="_blank">Platform Admin</a></li> {# Link to public Django admin #}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'users:school_admin_logout' %}">Logout</a></li>
                            </ul>
                        </li>
                        {% else %}
                        {# User is not logged in #}
                        <li class="nav-item me-2"> {# Added me-2 for spacing #}
                            <a class="nav-link" href="{% url 'public_site:find_school' %}">
                                <i class="bi bi-search me-1"></i> Find School Portal
                            </a>
                        </li>
                        <li class="nav-item me-2"> {# Added me-2 for spacing #}
                            <a href="{% url 'tenants:register_school' %}" class="btn btn-outline-light btn-sm">Register School</a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'users:school_admin_login' %}" class="btn btn-light btn-sm">Admin Login</a>
                        </li>
                    {% endif %}

                {% else %}
                    {# User is not logged in #}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'public_site:find_school' %}">Find School Portal</a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'tenants:register_school' %}" class="btn btn-outline-light btn-sm me-2">Register School</a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'users:school_admin_login' %}" class="btn btn-light btn-sm">Admin Login</a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
{% endblock navbar %}


{% block content_full_width %}
    {# For hero sections etc. that should span full width before main container #}
    {# Child templates (like home.html) will override this #}
{% endblock content_full_width %}


{% block content %}
    {# Main content for most public pages, typically wrapped in a <div class="container py-4"> #}
    {# Child templates will override this #}
    <div class="container py-4 main-content-area"> {# Added main-content-area for padding #}
        <p>This is default content from public_base.html. Child templates should override this block.</p>
    </div>
{% endblock content %}


{% block footer %}
    <footer class="footer mt-auto py-3 bg-light border-top text-center">
        <div class="container">
            
            <p class="mb-1">
                <a href="{% url 'public_site:terms' %}" class="text-muted small me-3">Terms of Service</a>
                <a href="{% url 'public_site:privacy' %}" class="text-muted small">Privacy Policy</a>
            </p>
            <span class="text-muted">© {% now "Y" %} School Fees SaaS Platform. All rights reserved.</span>
        </div>
    </footer>
{% endblock footer %} {% endcomment %}


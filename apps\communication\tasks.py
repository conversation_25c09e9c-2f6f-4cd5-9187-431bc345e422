# D:\school_fees_saas_v2\apps\communication\tasks.py
import logging
from celery import shared_task, Celery # Celery for app instance if not using default auto-discovery fully
from django.conf import settings
from django.core.mail import EmailMultiAlternatives # Use this for HTML + Text emails
from django.template.loader import render_to_string
from django.utils.html import strip_tags # Good for generating plain text from HTML if needed
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name, get_tenant_model
from django.urls import reverse # For generating URLs within correct context (can be tricky in tasks)

from django.utils.translation import gettext_lazy as _ # For translatable strings

# Assuming CommunicationLog is in the same app's models.py
from .models import CommunicationLog 

# Models from other apps - tasks should ideally fetch by PK passed as argument
# but for type hinting or direct use if absolutely necessary (and stable):
TenantModel = get_tenant_model() # Your School model from tenants.models

logger = logging.getLogger(__name__)

# --- Robustly get PublicUser Model ---
PublicUser = None
try:
    # Best practice: Use Django's get_user_model() if settings.AUTH_USER_MODEL points to it
    # If AUTH_USER_MODEL is *not* your PublicUser, then direct import is needed.
    from django.contrib.auth import get_user_model
    _User = get_user_model()
    # Check if the AUTH_USER_MODEL is indeed your intended PublicUser from apps.users
    # This check assumes 'apps.users.models.User' is your path
    if _User._meta.app_label == 'users' and _User._meta.model_name == 'user':
        PublicUser = _User
    else: # Fallback to direct import if AUTH_USER_MODEL is something else (e.g. tenant user)
        from apps.users.models import User as DirectPublicUserImport
        PublicUser = DirectPublicUserImport
except ImportError:
    logger.critical(
        "CRITICAL: PublicUser model (expected from apps.users.models or settings.AUTH_USER_MODEL) "
        "could not be imported in communication.tasks. Welcome emails and other public user notifications WILL FAIL."
    )
    # Not raising error here to allow Celery worker to start, but tasks using PublicUser will fail.


# --- Generic Email Sending and Logging Task ---
@shared_task(bind=True, name="communication.send_and_log_email", max_retries=3, default_retry_delay=5*60) # 5 minutes
def send_and_log_email_task(self, subject, recipient_list, text_body, html_body=None, 
                            from_email=None, related_obj_info=None, 
                            log_in_schema_name=None, email_type_for_log=None):
    """
    Generic Celery task to send an email (HTML optional) and log it in CommunicationLog.
    - recipient_list: Can be a single email string or a list of email strings.
    - related_obj_info: Dict {'app_label': str, 'model_name': str (lowercase), 'object_pk': int/str}
    - log_in_schema_name: Schema where CommunicationLog should be created. If None, defaults to public.
    - email_type_for_log: A string/choice from CommunicationLog.MessageType, e.g., 'WELCOME', 'FEE_REMINDER'.
    """
    task_id_str = f"Task (ID: {self.request.id}) " if self.request.id else "Task "
    log_prefix = f"{task_id_str}'send_and_log_email_task': "
    
    logger.info(f"{log_prefix}Initiated. Subject='{subject}', To={recipient_list}, LogSchema='{log_in_schema_name or 'public'}'")

    if not from_email:
        from_email = settings.DEFAULT_FROM_EMAIL
    
    if not isinstance(recipient_list, list):
        recipient_list = [str(recipient_list)]
    else:
        recipient_list = [str(r) for r in recipient_list if r] # Filter out empty recipients

    if not recipient_list:
        logger.warning(f"{log_prefix}No valid recipients provided. Aborting email send for subject '{subject}'.")
        return "Skipped: No valid recipients."

    # --- Prepare CommunicationLog Data ---
    log_data = {
        'message_type': email_type_for_log or CommunicationLog.MessageType.GENERIC,
        'status': CommunicationLog.Status.PENDING,
        'recipient_email': ", ".join(recipient_list)[:CommunicationLog._meta.get_field('recipient_email').max_length],
        'subject': subject[:CommunicationLog._meta.get_field('subject').max_length],
        'body_preview': strip_tags(html_body or text_body)[:250] + '...' if (html_body or text_body) else "N/A",
    }

    if related_obj_info and all(k in related_obj_info for k in ['app_label', 'model_name', 'object_pk']):
        try:
            # ContentType is a shared table, so can be fetched without specific schema context for this part.
            ct = ContentType.objects.get(
                app_label=related_obj_info['app_label'].lower(), 
                model=related_obj_info['model_name'].lower()
            )
            log_data['content_type'] = ct
            log_data['object_id'] = related_obj_info['object_pk']
        except ContentType.DoesNotExist:
            logger.error(f"{log_prefix}ContentType not found for {related_obj_info}. Log will not have related object.")
        except Exception as e_ct:
            logger.error(f"{log_prefix}Error setting related_obj for log: {e_ct}")

    # --- Create CommunicationLog Entry ---
    log_entry = None
    effective_schema_for_log = log_in_schema_name or get_public_schema_name()
    
    try:
        if effective_schema_for_log == get_public_schema_name():
            log_entry = CommunicationLog.objects.create(**log_data)
        else:
            tenant_for_log = TenantModel.objects.get(schema_name=effective_schema_for_log)
            with schema_context(tenant_for_log):
                log_entry = CommunicationLog.objects.create(**log_data)
        logger.info(f"{log_prefix}CommunicationLog CREATED (ID: {log_entry.pk}, Status: PENDING) in schema '{effective_schema_for_log}'")
    except Exception as e_log_create:
        logger.error(f"{log_prefix}FAILED to create PENDING CommunicationLog in schema '{effective_schema_for_log}': {e_log_create}", exc_info=True)
        # Proceed to send email, will attempt to log failure/success later if log_entry is None.

    # --- Send Email ---
    try:
        if html_body:
            mail = EmailMultiAlternatives(subject, text_body, from_email, recipient_list)
            mail.attach_alternative(html_body, "text/html")
            mail.send(fail_silently=False)
        else:
            django_send_mail(
                subject, text_body, from_email, recipient_list,
                fail_silently=False,
            )
        logger.info(f"{log_prefix}Email sent successfully. Subject='{subject}', To={recipient_list}")
        
        if log_entry:
            log_entry.status = CommunicationLog.Status.SENT
            log_entry.sent_at = timezone.now()
            log_entry.error_message = None # Clear any previous error if retrying
            log_entry.save(update_fields=['status', 'sent_at', 'error_message'])
            logger.info(f"{log_prefix}CommunicationLog ID {log_entry.pk} updated to SENT.")
        # Create a log entry now if initial creation failed but email sent (less ideal)
        elif not log_entry and effective_schema_for_log:
            log_data['status'] = CommunicationLog.Status.SENT
            log_data['sent_at'] = timezone.now()
            # Create log after successful send
            if effective_schema_for_log == get_public_schema_name():
                CommunicationLog.objects.create(**log_data)
            else:
                tenant_for_log_retry = TenantModel.objects.get(schema_name=effective_schema_for_log)
                with schema_context(tenant_for_log_retry):
                    CommunicationLog.objects.create(**log_data)
            logger.info(f"{log_prefix}Post-send CommunicationLog CREATED (Status: SENT) in schema '{effective_schema_for_log}'")

        return f"Email sent to {recipient_list}"

    except Exception as exc:
        logger.error(f"{log_prefix}Email sending FAILED. Subject='{subject}', To={recipient_list}, Error: {type(exc).__name__} - {exc}", exc_info=True)
        if log_entry:
            log_entry.status = CommunicationLog.Status.FAILED
            log_entry.error_message = str(exc)[:CommunicationLog._meta.get_field('error_message').max_length]
            log_entry.save(update_fields=['status', 'error_message'])
            logger.info(f"{log_prefix}CommunicationLog ID {log_entry.pk} updated to FAILED.")
        # Create a log entry now if initial creation failed and email also failed
        elif not log_entry and effective_schema_for_log:
            log_data['status'] = CommunicationLog.Status.FAILED
            log_data['error_message'] = str(exc)[:CommunicationLog._meta.get_field('error_message').max_length]
            if effective_schema_for_log == get_public_schema_name():
                CommunicationLog.objects.create(**log_data)
            else:
                tenant_for_log_retry = TenantModel.objects.get(schema_name=effective_schema_for_log)
                with schema_context(tenant_for_log_retry):
                    CommunicationLog.objects.create(**log_data)
            logger.info(f"{log_prefix}Post-failed-send CommunicationLog CREATED (Status: FAILED) in schema '{effective_schema_for_log}'")

        # Celery's retry mechanism
        try:
            raise self.retry(exc=exc)
        except Exception as final_exc: # If max_retries exceeded
            logger.critical(f"{log_prefix}Max retries exceeded for email task. Final error: {final_exc}")
            # Celery will mark the task as FAILED.
            return f"Email FAILED after retries for {recipient_list}. Error: {final_exc}"


# --- Welcome Email Task ---
@shared_task(name="communication.send_welcome_email_on_registration", bind=True, max_retries=3, default_retry_delay=5*60)
def send_welcome_email_task(self, public_user_id, school_id, school_schema_name):
    log_prefix = f"Task (ID: {self.request.id}) 'send_welcome_email_task': "
    logger.info(f"{log_prefix}Initiated for UserID: {public_user_id}, SchoolID: {school_id}, Schema: {school_schema_name}")

    if not PublicUser:
        logger.critical(f"{log_prefix}PublicUser model not available. Aborting.")
        raise self.retry(exc=ImportError("PublicUser model is not available."), countdown=60) # Retry later

    try:
        public_user = PublicUser.objects.get(pk=public_user_id)
        school = TenantModel.objects.get(pk=school_id, schema_name=school_schema_name) # Ensure schema_name matches

        platform_name = getattr(settings, 'PLATFORM_NAME', 'School Fees Platform')
        subject = _("Welcome to %(platform_name)s, %(user_name)s!") % {
            'platform_name': platform_name, 
            'user_name': public_user.first_name or public_user.username or _('Valued User')
        }
        
        # Construct login URL safely
        tenant_domain_obj = school.domains.filter(is_primary=True).first() or school.domains.first()
        if not tenant_domain_obj:
            logger.error(f"{log_prefix}No domain found for school {school.name}. Cannot generate login URL.")
            # Potentially retry or fail, depending on how critical this URL is.
            # For now, we'll proceed without it if not found.
            login_url = None
        else:
            tenant_domain_with_port = tenant_domain_obj.domain # e.g., alpha.myapp.test:8000
            staff_login_path = "/portal/staff/login/" # Define this path consistently
            login_url = f"{settings.APP_SCHEME}://{tenant_domain_with_port}{staff_login_path}"
        
        context = {
            'user_first_name': public_user.first_name or _("there"),
            'school_name': school.name,
            'platform_name': platform_name,
            'tenant_login_url': login_url,
            'contact_email': getattr(settings, 'SUPPORT_EMAIL', settings.DEFAULT_FROM_EMAIL),
        }
        
        html_message = render_to_string('communication/emails/welcome_school_owner.html', context)
        plain_message = strip_tags(html_message) # Generate plain text from HTML
        
        related_obj_info = {
            'app_label': school._meta.app_label, 
            'model_name': school._meta.model_name, 
            'object_pk': school.pk
        }

        # Welcome email log should be in public schema
        send_and_log_email_task.delay(
            subject=subject,
            recipient_list=[public_user.email],
            text_body=plain_message,
            html_body=html_message,
            related_obj_info=related_obj_info,
            log_in_schema_name=get_public_schema_name(), # Explicitly log to public
            email_type_for_log=CommunicationLog.MessageType.WELCOME # Specific type
        )
        logger.info(f"{log_prefix}Successfully queued generic send for {public_user.email} (School: {school.name})")
        return f"Welcome email queued for {public_user.email}"

    except PublicUser.DoesNotExist:
        logger.error(f"{log_prefix}PublicUser with ID {public_user_id} not found.")
        return f"Failed (No Retry): PublicUser {public_user_id} not found." # Don't retry if user fundamentally doesn't exist
    except TenantModel.DoesNotExist:
        logger.error(f"{log_prefix}School (Tenant) with ID {school_id} / Schema {school_schema_name} not found.")
        return f"Failed (No Retry): School {school_id} not found."
    except Exception as e:
        logger.error(f"{log_prefix}Unhandled error for school ID {school_id}, user ID {public_user_id}: {e}", exc_info=True)
        raise self.retry(exc=e) # Let Celery handle retry based on task settings


# --- Tenant-Specific Notification Wrapper Task (Example) ---
@shared_task(name="communication.send_tenant_notification", bind=True, max_retries=3, default_retry_delay=5*60)
def send_tenant_notification_task(self, tenant_schema_name, recipient_email, subject_template_str, 
                                    text_template_path, html_template_path, context_data_dict, 
                                    related_obj_info=None, email_type_for_log=None):
    """
    Wrapper to send a templated email within a specific tenant's context and log it there.
    """
    task_id_str = f"Task (ID: {self.request.id}) " if self.request.id else "Task "
    log_prefix = f"{task_id_str}'send_tenant_notification_task': "
    logger.info(f"{log_prefix}Initiated for schema {tenant_schema_name}, recipient {recipient_email}")

    try:
        tenant = TenantModel.objects.get(schema_name=tenant_schema_name)
    except TenantModel.DoesNotExist:
        logger.error(f"{log_prefix}Tenant schema '{tenant_schema_name}' not found. Cannot send email.")
        return f"Failed (No Retry): Tenant {tenant_schema_name} not found."

    # --- Prepare Context and Render Templates WITHIN Tenant Context ---
    # (Although render_to_string doesn't inherently need schema_context if templates are globally accessible,
    # keeping it here if context_data_dict might involve tenant-specific model fetches in the future)
    with schema_context(tenant):
        if not isinstance(context_data_dict, dict):
            logger.error(f"{log_prefix}context_data_dict is not a dict. Got {type(context_data_dict)}. Using empty.")
            context_data_dict = {}

        # Add common tenant-specific context automatically
        context_data_dict.setdefault('tenant_name', tenant.name)
        context_data_dict.setdefault('current_year', timezone.now().year)
        # TODO: Add school_details (logo, colors, currency) to context_data_dict
        # from apps.schools.models import SchoolProfile
        # school_profile = SchoolProfile.objects.first() # Or get specific one
        # school_public_instance_for_details = tenant # It's already the TenantModel instance
        # school_details_for_email = get_school_details_from_profile_helper(school_profile, school_public_instance_for_details)
        # context_data_dict.update(school_details_for_email)
        
        subject = _(subject_template_str).format(**context_data_dict) # Use .format() for f-string like behavior with translations
        
        try:
            text_body = render_to_string(text_template_path, context_data_dict)
            html_body = render_to_string(html_template_path, context_data_dict) if html_template_path else None
        except Exception as e_render:
            logger.error(f"{log_prefix}Error rendering email template for schema {tenant_schema_name}: {e_render}", exc_info=True)
            raise self.retry(exc=ValueError(f"Template rendering error: {e_render}"))

    # Call the generic task to send and log, specifying the tenant's schema for the log
    send_and_log_email_task.delay(
        subject=subject,
        recipient_list=[recipient_email],
        text_body=text_body,
        html_body=html_body,
        related_obj_info=related_obj_info,
        log_in_schema_name=tenant_schema_name, # Log this communication within the tenant's schema
        email_type_for_log=email_type_for_log or CommunicationLog.MessageType.TENANT_GENERAL
    )
    logger.info(f"{log_prefix}Queued generic send for {recipient_email} in schema {tenant_schema_name}, Subject: {subject}")
    return f"Tenant-specific email queued for {recipient_email} in {tenant_schema_name}"


# You would then create specific tasks that call 'send_tenant_notification_task'
# Example: Task for Fee Due Reminder (this would replace parts of schedule_fee_due_reminders)

@shared_task(name="communication.send_fee_due_reminder_to_parent")
def send_fee_due_reminder_to_parent_task(parent_user_pk, school_schema_name, student_fee_data_list, overall_total_due):
    from apps.students.models import ParentUser # Import here to avoid circularity if tasks are in student app
    from apps.schools.models import SchoolProfile # For school specific details

    with schema_context(school_schema_name):
        try:
            parent = ParentUser.objects.get(pk=parent_user_pk)
            if not parent.email or not parent.is_active or not getattr(parent, 'receive_notifications', True):
                logger.info(f"Skipping fee reminder for parent {parent.email} (disabled/no email).")
                return "Skipped"

            school_public_instance = TenantModel.objects.get(schema_name=school_schema_name) # For get_school_details
            school_profile = SchoolProfile.objects.first() # Assuming one per tenant
            
            # This helper would need to be accessible or its logic duplicated/adapted
            # school_email_details = get_school_details_from_profile_helper(school_profile, school_public_instance)
            
            context = {
                'parent_name': parent.get_full_name() or parent.email,
                'student_specific_fees': student_fee_data_list,
                'overall_total_due': overall_total_due,
                # **school_email_details
                # Manually add for now if helper is complex to call from here:
                
                'school_name': school_public_instance.name,
                'school_currency_symbol': school_profile.currency_symbol if school_profile else '$',
                # ... other details ...
            }

            related_obj_info = {
                'app_label': parent._meta.app_label,
                'model_name': parent._meta.model_name,
                'object_pk': parent.pk
            }

            send_tenant_notification_task.delay(
                tenant_schema_name=school_schema_name,
                recipient_email=parent.email,
                subject_template_str=_("School Fee Reminder from {school_name}"), # Pass as f-string template
                text_template_path='communication/emails/fee_due_reminder.txt',
                html_template_path='communication/emails/fee_due_reminder.html',
                context_data_dict=context,
                related_obj_info=related_obj_info,
                email_type_for_log=CommunicationLog.MessageType.FEE_REMINDER
            )
        except ParentUser.DoesNotExist:
            logger.error(f"ParentUser {parent_user_pk} not found in schema {school_schema_name} for fee reminder.")
        except Exception as e:
            logger.error(f"Error queueing fee reminder for parent {parent_user_pk} in {school_schema_name}: {e}", exc_info=True)



# D:\school_fees_saas_v2\apps\communication\tasks.py

from celery import shared_task
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.translation import gettext_lazy as _ # For subject translation
from django.urls import reverse
from django_tenants.utils import schema_context, get_tenant_model

# Assuming these models are correctly located for your project
try:
    from apps.tenants.models import School # Your public tenant model
except ImportError:
    School = get_tenant_model() # Fallback if direct import fails

try:
    from apps.students.models import ParentUser
except ImportError:
    ParentUser = None # Will cause issues if None, indicates setup problem

try:
    from apps.schools.models import SchoolProfile # For school-specific details
except ImportError:
    SchoolProfile = None

import logging
logger = logging.getLogger(__name__)

# --- Helper to get school details (from previous discussion, ensure it's defined) ---
# This function needs to be robust based on your SchoolProfile setup
def get_school_details_from_profile(school_profile_instance, school_public_instance):
    details = {
        'school_name': school_public_instance.name,
        'school_logo_url': None,
        'school_address': None,
        'school_contact_info': None,
        'school_primary_color': getattr(settings, 'EMAIL_DEFAULT_PRIMARY_COLOR', '#007bff'),
        'school_secondary_color': getattr(settings, 'EMAIL_DEFAULT_SECONDARY_COLOR', '#28a745'),
        'school_currency_symbol': '$', # Default, should come from profile
        'payment_portal_url': None,
    }
    if school_profile_instance:
        if hasattr(school_profile_instance, 'logo') and school_profile_instance.logo:
            # For logo.url to work correctly in emails sent by Celery (outside request context),
            # ensure DEFAULT_FILE_STORAGE provides absolute URLs or MEDIA_URL is absolute.
            # Or, pass the full absolute URL to the task.
            # For simplicity now, assuming logo.url gives something usable or is handled by template.
            try:
                details['school_logo_url'] = school_profile_instance.logo.url
            except ValueError: # Handle cases where logo file might be missing even if field is populated
                logger.warning(f"Could not get logo URL for school profile {school_profile_instance.pk} of school {school_public_instance.name}")
        
        addr_parts = [
            getattr(school_profile_instance, 'address_line1', ''),
            getattr(school_profile_instance, 'address_line2', ''),
            getattr(school_profile_instance, 'city', ''),
            getattr(school_profile_instance, 'state_province', ''),
            getattr(school_profile_instance, 'postal_code', ''),
            # getattr(school_profile_instance, 'country_name', '') # Assuming country is a FK or simple field
        ]
        details['school_address'] = ", ".join(filter(None, addr_parts))
        
        contacts = []
        if getattr(school_profile_instance, 'contact_email', ''): contacts.append(f"Email: {school_profile_instance.contact_email}")
        if getattr(school_profile_instance, 'contact_phone', ''): contacts.append(f"Phone: {school_profile_instance.contact_phone}")
        details['school_contact_info'] = " | ".join(contacts)
        
        if hasattr(school_profile_instance, 'currency_symbol') and school_profile_instance.currency_symbol:
            details['school_currency_symbol'] = school_profile_instance.currency_symbol

    # Construct payment_portal_url (this part is tricky from a task without request)
    domain_obj = school_public_instance.domains.filter(is_primary=True).first() or school_public_instance.domains.first()
    if domain_obj:
        base_url = f"{settings.APP_SCHEME}://{domain_obj.domain}"
        try:
            # Reversing URLs within a tenant context from a Celery task can be complex.
            # It's often better to pass the full URL or specific path components to the task.
            # For now, we assume a known path or that the template will handle it.
            # If 'parent_portal:dashboard' is a common path:
            portal_path = reverse('parent_portal:dashboard') # This uses default urlconf
            details['payment_portal_url'] = f"{base_url}{portal_path}"
            # A more robust way for tenant URLs in tasks:
            # with schema_context(school_public_instance.schema_name):
            #     portal_path = reverse('parent_portal:dashboard') # Now reverses within tenant context
            # details['payment_portal_url'] = f"{base_url}{portal_path}"

        except Exception as e:
            logger.error(f"Could not reverse 'parent_portal:dashboard' for domain {domain_obj.domain}: {e}")
            details['payment_portal_url'] = base_url # Fallback to base domain
    return details

# ... (your existing send_fee_due_reminder_email_task and send_payment_confirmation_email_task) ...

@shared_task(name="send_invoice_issued_email")
def send_invoice_issued_email_task(parent_user_id, school_schema_name, invoice_details_dict, student_name_for_email):
    """
    Sends an email to a parent when a new invoice is issued for their student.

    Args:
        parent_user_id (int): PK of the ParentUser.
        school_schema_name (str): Schema name of the school/tenant.
        invoice_details_dict (dict): Dictionary containing invoice information, e.g.,
            {
                'invoice_number': 'INV001',
                'issue_date': '2025-06-20',
                'due_date': '2025-07-20',
                'amount_due': Decimal('150.00'),
                'items': [{'description': 'Tuition Fee', 'amount': Decimal('100.00')}, ...],
                'invoice_url': 'Optional full URL to view the invoice' 
            }
        student_name_for_email (str): Full name of the student.
    """
    logger.info(f"Attempting to send 'invoice issued' email. Parent ID: {parent_user_id}, School Schema: {school_schema_name}, Invoice: {invoice_details_dict.get('invoice_number')}")

    if ParentUser is None:
        logger.critical("send_invoice_issued_email_task: ParentUser model not imported. Cannot send email.")
        return "Error: ParentUser model not available."
    if SchoolProfile is None:
        logger.warning("send_invoice_issued_email_task: SchoolProfile model not imported. School details in email might be limited.")


    try:
        school_public_instance = School.objects.get(schema_name=school_schema_name)
        
        with schema_context(school_schema_name):
            try:
                parent = ParentUser.objects.get(pk=parent_user_id)
            except ParentUser.DoesNotExist:
                logger.error(f"send_invoice_issued_email_task: ParentUser with ID {parent_user_id} not found in schema {school_schema_name}.")
                return f"Error: ParentUser {parent_user_id} not found in schema {school_schema_name}."

            if not parent.email:
                logger.warning(f"send_invoice_issued_email_task: ParentUser {parent.email} (ID: {parent_user_id}) has no email address. Cannot send email.")
                return "Skipped: Parent has no email."
            
            if not parent.is_active or not getattr(parent, 'receive_notifications', True): # Check ParentUser.receive_notifications
                logger.info(f"Skipping invoice issued email for parent {parent.email} (inactive or notifications disabled).")
                return "Skipped: Parent inactive or notifications disabled."

            # Fetch tenant-specific SchoolProfile
            school_profile_instance = None
            if SchoolProfile:
                school_profile_instance = SchoolProfile.objects.first() # Assumes one profile per tenant

            school_details = get_school_details_from_profile(school_profile_instance, school_public_instance)
            
            context = {
                'recipient_name': parent.get_full_name() or parent.email,
                'student_name': student_name_for_email,
                'invoice_details': invoice_details_dict,
                # 'invoice_url' can be part of invoice_details_dict or separate
                **school_details 
            }
            
            subject = _("New Invoice Issued for %(student_name)s - %(school_name)s") % {
                'student_name': student_name_for_email,
                'school_name': school_details['school_name']
            }
            
            html_message = render_to_string('communication/emails/invoice_issued.html', context)
            plain_message = render_to_string('communication/emails/invoice_issued.txt', context)
            
            from_email = settings.DEFAULT_FROM_EMAIL 
            # <AUTHOR> <EMAIL>"
            
            mail = EmailMultiAlternatives(subject, plain_message, from_email, [parent.email])
            mail.attach_alternative(html_message, "text/html")
            mail.send(fail_silently=False) # Set fail_silently=False to raise errors during development
            
            logger.info(f"Successfully sent 'invoice issued' email to {parent.email} for invoice {invoice_details_dict.get('invoice_number')}, school {school_details['school_name']}.")
            return f"Email sent to {parent.email} for invoice {invoice_details_dict.get('invoice_number')}."

    except School.DoesNotExist:
        logger.error(f"send_invoice_issued_email_task: School with schema {school_schema_name} not found.")
        return f"Error: School schema {school_schema_name} not found."
    except Exception as e:
        logger.error(f"Unexpected error in send_invoice_issued_email_task for parent ID {parent_user_id}, school {school_schema_name}: {e}", exc_info=True)
        # For Celery, you might want to retry:
        # raise self.retry(exc=e, countdown=60*5) # Retry in 5 minutes
        return f"Failed to send email due to an error: {e}"



@shared_task(name="send_payment_confirmation_email")
def send_payment_confirmation_email_task(recipient_email, recipient_name, school_schema_name, payment_info_dict, student_name=None, bcc_to_admin=False):
    """
    Sends a payment confirmation email.

    Args:
        recipient_email (str): Email address of the primary recipient (e.g., parent).
        recipient_name (str): Name of the primary recipient.
        school_schema_name (str): Schema name of the school/tenant.
        payment_info_dict (dict): Dictionary with payment details, e.g.,
            {
                'payment_id': 'REC001', 'date': '2025-06-20', 'amount': Decimal('50.00'),
                'method': 'Credit Card', 'status': 'Completed', 
                'paid_for_student': 'John Doe (optional)',
                'invoices_covered': ['INV2023-001', 'INV2023-002'] (optional list)
            }
        student_name (str, optional): Name of the student payment is for.
        bcc_to_admin (bool, optional): If true, BCC a school admin email.
    """
    logger.info(f"Attempting to send payment confirmation. Recipient: {recipient_email}, School Schema: {school_schema_name}, Payment ID: {payment_info_dict.get('payment_id')}")

    if not recipient_email:
        logger.warning("send_payment_confirmation_email_task: No recipient email provided. Cannot send email.")
        return "Error: No recipient email."
    
    if SchoolProfile is None: # Check if SchoolProfile model was imported
        logger.warning("send_payment_confirmation_email_task: SchoolProfile model not imported. School details in email might be limited.")

    try:
        school_public_instance = School.objects.get(schema_name=school_schema_name)
        
        school_profile_instance = None
        if SchoolProfile: # Check if SchoolProfile model was imported
            with schema_context(school_schema_name):
                school_profile_instance = SchoolProfile.objects.first()

        school_details = get_school_details_from_profile(school_profile_instance, school_public_instance)
            
        context = {
            'recipient_name': recipient_name,
            'payment_info': payment_info_dict, # Use payment_info in template
            'student_name': student_name, 
            'school_details': school_details # Pass school_details as a dict
        }
        
        subject = _("Payment Confirmation - %(school_name)s") % {'school_name': school_details['school_name']}
        
        html_message = render_to_string('communication/emails/payment_confirmation.html', context)
        plain_message = render_to_string('communication/emails/payment_confirmation.txt', context)
        
        from_email = settings.DEFAULT_FROM_EMAIL
        to_emails = [recipient_email]
        bcc_emails = []

        if bcc_to_admin and school_profile_instance and getattr(school_profile_instance, 'accounts_email', None):
            bcc_emails.append(school_profile_instance.accounts_email) # Add 'accounts_email' field to SchoolProfile
        elif bcc_to_admin and getattr(settings, 'DEFAULT_ADMIN_BCC_EMAIL', None):
            bcc_emails.append(settings.DEFAULT_ADMIN_BCC_EMAIL)

        mail = EmailMultiAlternatives(subject, plain_message, from_email, to_emails, bcc=bcc_emails or None)
        mail.attach_alternative(html_message, "text/html")
        mail.send(fail_silently=False)
        
        logger.info(f"Successfully sent payment confirmation email to {recipient_email} for payment {payment_info_dict.get('payment_id')}, school {school_details['school_name']}.")
        return f"Payment confirmation sent to {recipient_email}"

    except School.DoesNotExist:
        logger.error(f"send_payment_confirmation_email_task: School with schema {school_schema_name} not found.")
        return f"Error: School schema {school_schema_name} not found."
    except Exception as e:
        logger.error(f"Unexpected error in send_payment_confirmation_email_task for {recipient_email}, school {school_schema_name}: {e}", exc_info=True)
        return f"Failed to send payment confirmation due to an error: {e}"
    
    

# Import StaffUser for this task
try:
    from apps.schools.models import StaffUser
except ImportError:
    StaffUser = None # Task will log critical error if this happens

@shared_task(name="send_leave_status_update_email")
def send_leave_status_update_email_task(staff_user_id, school_schema_name, leave_info_dict, manager_email_to_bcc=None):
    """
    Sends an email to a staff member about their leave request status update.

    Args:
        staff_user_id (int): PK of the StaffUser who applied for leave.
        school_schema_name (str): Schema name of the school/tenant.
        leave_info_dict (dict): Dictionary with leave request details, e.g.,
            {
                'leave_request_id': 123,
                'leave_type_name': 'Annual Leave',
                'start_date': '2025-07-01',
                'end_date': '2025-07-05',
                'num_days': 5, (optional)
                'applied_on': '2025-06-15',
                'status': 'APPROVED', # Or 'REJECTED', 'PENDING_APPROVAL'
                'status_display': 'Approved', # From get_status_display()
                'reason_for_status': 'Enjoy your time off!' (optional comment from manager)
                'processed_by': 'Manager Name (optional)'
            }
        manager_email_to_bcc (str, optional): Email of manager to BCC.
    """
    logger.info(f"Attempting to send leave status update. Staff ID: {staff_user_id}, School Schema: {school_schema_name}, Leave ID: {leave_info_dict.get('leave_request_id')}, New Status: {leave_info_dict.get('status')}")

    if StaffUser is None:
        logger.critical("send_leave_status_update_email_task: StaffUser model not imported. Cannot send email.")
        return "Error: StaffUser model not available."
    if SchoolProfile is None:
        logger.warning("send_leave_status_update_email_task: SchoolProfile model not imported. School details in email might be limited.")

    try:
        school_public_instance = School.objects.get(schema_name=school_schema_name)
        
        with schema_context(school_schema_name):
            try:
                staff_member = StaffUser.objects.get(pk=staff_user_id)
            except StaffUser.DoesNotExist:
                logger.error(f"send_leave_status_update_email_task: StaffUser with ID {staff_user_id} not found in schema {school_schema_name}.")
                return f"Error: StaffUser {staff_user_id} not found in schema {school_schema_name}."

            if not staff_member.email:
                logger.warning(f"send_leave_status_update_email_task: StaffUser {staff_member.id} has no email address. Cannot send email.")
                return "Skipped: Staff member has no email."
            
            # Add a receive_leave_notifications field to StaffUser model if you want per-user preference
            # if not staff_member.is_active or not getattr(staff_member, 'receive_leave_notifications', True):
            if not staff_member.is_active: # Basic check for active staff
                logger.info(f"Skipping leave status email for inactive staff {staff_member.email}.")
                return "Skipped: Staff member inactive."

            school_profile_instance = None
            if SchoolProfile:
                school_profile_instance = SchoolProfile.objects.first() 

            school_details = get_school_details_from_profile(school_profile_instance, school_public_instance)
            
            # Construct staff_portal_leave_url (example)
            staff_portal_leave_url = None
            domain_obj = school_public_instance.domains.filter(is_primary=True).first() or school_public_instance.domains.first()
            if domain_obj:
                base_url = f"{settings.APP_SCHEME}://{domain_obj.domain}"
                try:
                    # This path needs to be defined in your tenant HR/Schools URLs
                    leave_list_path = reverse('hr:staff_leaverequest_list') 
                    staff_portal_leave_url = f"{base_url}{leave_list_path}"
                except Exception as e:
                    logger.error(f"Could not reverse 'hr:staff_leaverequest_list' for domain {domain_obj.domain}: {e}")
            
            context = {
                'recipient_name': staff_member.get_full_name() or staff_member.email,
                'leave_info': leave_info_dict,
                'staff_portal_leave_url': staff_portal_leave_url,
                'school_details': school_details
            }
            
            subject_status = leave_info_dict.get('status_display', leave_info_dict.get('status', 'Updated')).title()
            subject = _("Your Leave Request has been %(status)s - %(school_name)s") % {
                'status': subject_status,
                'school_name': school_details['school_name']
            }
            
            html_message = render_to_string('communication/emails/leave_request_status_update.html', context)
            plain_message = render_to_string('communication/emails/leave_request_status_update.txt', context)
            
            from_email = settings.DEFAULT_FROM_EMAIL
            to_emails = [staff_member.email]
            bcc_emails = []
            if manager_email_to_bcc:
                bcc_emails.append(manager_email_to_bcc)
            
            mail = EmailMultiAlternatives(subject, plain_message, from_email, to_emails, bcc=bcc_emails or None)
            mail.attach_alternative(html_message, "text/html")
            mail.send(fail_silently=False)
            
            logger.info(f"Successfully sent leave status update email to {staff_member.email} for leave request {leave_info_dict.get('leave_request_id')}, new status: {leave_info_dict.get('status')}.")
            return f"Leave status update email sent to {staff_member.email}."

    except School.DoesNotExist:
        logger.error(f"send_leave_status_update_email_task: School with schema {school_schema_name} not found.")
        return f"Error: School schema {school_schema_name} not found."
    except Exception as e:
        logger.error(f"Unexpected error in send_leave_status_update_email_task for staff ID {staff_user_id}, school {school_schema_name}: {e}", exc_info=True)
        return f"Failed to send leave status update email due to an error: {e}"    

{# templates/payments/receipt_pdf_template.html #}
{% extends "reporting/pdf/_pdf_base.html" %}

{% load humanize %}

{% block pdf_title %}Payment Receipt #{{ payment.receipt_number_display }} - {{ school_profile.name|default:request.tenant.name }}{% endblock %}

{% block document_name_header %}PAYMENT RECEIPT{% endblock %}

{% block document_specific_header_info %}
    <p class="mb-0">Receipt No: <strong>{{ payment.receipt_number_display }}</strong></p>
    <p class="mb-0">Payment Date: {{ payment.payment_date|date:"d M Y, H:i" }}</p>
    {% if payment.allocations.exists %}
    <p class="mb-0">For Invoice No:
        {% for allocation in payment.allocations.all %}
            {{ allocation.invoice.invoice_number_display }}{% if not forloop.last %}, {% endif %}
        {% endfor %}
    </p>
    {% endif %}
{% endblock %}

{% block pdf_main_content %}
    {# Received From Section #}
    <table class="no-border" style="margin-bottom: 20px;">
        <tr>
            <td style="width: 50%;" class="no-border">
                <h4 class="fw-bold">Received From:</h4>
                {% if payment.student %}
                    <p class="mb-0"><strong>{{ payment.student.full_name }}</strong></p>
                    <p class="mb-0">Admission No: {{ payment.student.admission_number|default:"N/A" }}</p>
                    <p class="mb-0">Class: {{ payment.student.current_class.name|default:"N/A" }}
                        {% if payment.student.current_section %}- {{ payment.student.current_section.name }}{% endif %}
                    </p>
                    {% with parent=payment.student.parents.all.first %}
                        {% if parent %}
                            <p class="mb-0">Parent: {{ parent.full_name }}</p>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p>N/A</p>
                {% endif %}
            </td>
            <td style="width: 50%; text-align: right;" class="no-border">
                {# Optionally, "Received By" Staff member name #}
                {% if payment.processed_by_staff %}
                    <p class="mb-0" style="font-size: 8pt;">Received by: {{ payment.processed_by_staff.full_name|default:payment.processed_by_staff.email }}</p>
                {% endif %}
            </td>
        </tr>
    </table>

    {# Payment Details #}
    <h4 class="fw-bold">Payment Details:</h4>
    <table>
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th style="width: 70%;">Description</th>
                <th class="text-end" style="width: 30%;">Amount Paid</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    Payment for
                    {% if payment.allocations.exists %}
                        {% for allocation in payment.allocations.all %}
                            Invoice #{{ allocation.invoice.invoice_number_display }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    {% elif payment.payment_type_description %}
                        {{ payment.payment_type_description }}
                    {% else %}
                        School Fees / Dues
                    {% endif %}
                    {% if payment.reference_number %}
                        <br><small>Reference: {{ payment.reference_number }}</small>
                    {% endif %}
                </td>
                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:'$' }}{{ payment.amount|floatformat:2|intcomma }}</td>
            </tr>
            <tr>
                <td class="text-end no-border fw-bold">Payment Method:</td>
                <td class="text-end no-border">{{ payment.payment_method.name|default:"N/A" }}</td>
            </tr>
        </tbody>
    </table>

    <div style="margin-top: 20px;">
        <p>Received with thanks: <strong>{{ school_profile.currency_symbol|default:'$' }}{{ payment.amount|floatformat:2|intcomma }}</strong>.</p>
    </div>

    {% if payment.notes %}
        <div style="margin-top: 15px; border-top: 1px dashed #ccc; padding-top: 10px; font-size: 8pt;">
            <h4 class="fw-bold" style="font-size: 10pt;">Notes:</h4>
            <p>{{ payment.notes|linebreaksbr }}</p>
        </div>
    {% endif %}

    <div style="margin-top: 30px; text-align: center; font-size: 8pt;">
        <p>This is a computer-generated receipt and does not require a signature.</p>
        {% if school_profile.name %}<p>{{ school_profile.name }}</p>{% endif %}
    </div>
{% endblock %}

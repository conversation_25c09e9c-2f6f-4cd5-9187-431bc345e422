{% extends "base.html" %}
{% load static i18n %}

{% block title %}{{ event.title }} - {% trans "School Calendar" %}{% endblock %}

{% block extra_css %}
<style>
    .event-header {
        background: linear-gradient(135deg, {{ event.category.color|default:'#667eea' }} 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px 10px 0 0;
    }
    
    .event-meta {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .meta-item:last-child {
        margin-bottom: 0;
    }
    
    .meta-icon {
        width: 40px;
        height: 40px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: {{ event.category.color|default:'#007bff' }};
    }
    
    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }
    .priority-urgent { background: #f5c6cb; color: #721c24; }
    
    .rsvp-section {
        background: #e3f2fd;
        border: 2px solid #2196f3;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .attendee-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .stat-card {
        text-align: center;
        padding: 1rem;
        background: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:calendar' %}">{% trans "Calendar" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'school_calendar:event_list' %}">{% trans "Events" %}</a></li>
            <li class="breadcrumb-item active">{{ event.title }}</li>
        </ol>
    </nav>
    
    <div class="card shadow-sm">
        <!-- Event Header -->
        <div class="event-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-2">
                        {% if event.category %}
                        <i class="{{ event.category.icon }} me-2"></i>
                        {% endif %}
                        <span class="badge bg-light text-dark me-2">{{ event.get_event_type_display }}</span>
                        <span class="priority-badge priority-{{ event.priority|lower }}">
                            {{ event.get_priority_display }}
                        </span>
                    </div>
                    <h1 class="mb-2">{{ event.title }}</h1>
                    {% if event.location %}
                    <p class="mb-0 opacity-75">
                        <i class="bi bi-geo-alt me-1"></i>{{ event.location }}
                    </p>
                    {% endif %}
                </div>
                <div class="col-md-4 text-md-end">
                    {% if can_manage %}
                    <div class="btn-group">
                        <a href="{% url 'school_calendar:admin_event_edit' event.pk %}" class="btn btn-outline-light btn-sm">
                            <i class="bi bi-pencil me-1"></i>{% trans "Edit" %}
                        </a>
                        <a href="{% url 'school_calendar:admin_event_delete' event.pk %}" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash me-1"></i>{% trans "Delete" %}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="card-body">
            <div class="row">
                <!-- Event Details -->
                <div class="col-lg-8">
                    {% if event.description %}
                    <div class="mb-4">
                        <h5>{% trans "Description" %}</h5>
                        <div class="text-muted">
                            {{ event.description|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if event.venue_details %}
                    <div class="mb-4">
                        <h5>{% trans "Venue Details" %}</h5>
                        <div class="text-muted">
                            {{ event.venue_details|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Contact Information -->
                    {% if event.contact_person or event.contact_email or event.contact_phone %}
                    <div class="mb-4">
                        <h5>{% trans "Contact Information" %}</h5>
                        <div class="row">
                            {% if event.contact_person %}
                            <div class="col-md-4">
                                <strong>{% trans "Contact Person" %}</strong><br>
                                <span class="text-muted">{{ event.contact_person }}</span>
                            </div>
                            {% endif %}
                            {% if event.contact_email %}
                            <div class="col-md-4">
                                <strong>{% trans "Email" %}</strong><br>
                                <a href="mailto:{{ event.contact_email }}" class="text-decoration-none">{{ event.contact_email }}</a>
                            </div>
                            {% endif %}
                            {% if event.contact_phone %}
                            <div class="col-md-4">
                                <strong>{% trans "Phone" %}</strong><br>
                                <a href="tel:{{ event.contact_phone }}" class="text-decoration-none">{{ event.contact_phone }}</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- RSVP Section -->
                    {% if event.requires_rsvp %}
                    <div class="rsvp-section">
                        <h5 class="mb-3">
                            <i class="bi bi-person-check me-2"></i>
                            {% trans "RSVP Required" %}
                        </h5>
                        
                        {% if user_rsvp %}
                        <div class="alert alert-success">
                            <strong>{% trans "Your RSVP:" %}</strong> {{ user_rsvp.get_rsvp_status_display }}
                            {% if user_rsvp.notes %}
                            <br><small>{% trans "Notes:" %} {{ user_rsvp.notes }}</small>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        <form method="post" action="{% url 'school_calendar:rsvp_event' event.pk %}">
                            {% csrf_token %}
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label">{% trans "Your Response" %}</label>
                                    <select name="rsvp_status" class="form-select" required>
                                        <option value="">{% trans "Select Response" %}</option>
                                        <option value="ATTENDING" {% if user_rsvp.rsvp_status == 'ATTENDING' %}selected{% endif %}>
                                            {% trans "Attending" %}
                                        </option>
                                        <option value="NOT_ATTENDING" {% if user_rsvp.rsvp_status == 'NOT_ATTENDING' %}selected{% endif %}>
                                            {% trans "Not Attending" %}
                                        </option>
                                        <option value="MAYBE" {% if user_rsvp.rsvp_status == 'MAYBE' %}selected{% endif %}>
                                            {% trans "Maybe" %}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{% trans "Notes (Optional)" %}</label>
                                    <input type="text" name="notes" class="form-control" 
                                            value="{{ user_rsvp.notes|default:'' }}" 
                                            placeholder="{% trans 'Any additional notes' %}">
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                        {% trans "Submit RSVP" %}
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <!-- Attendee Statistics -->
                        {% if attendee_counts %}
                        <div class="attendee-stats">
                            <div class="stat-card">
                                <div class="stat-number text-success">{{ attendee_counts.attending }}</div>
                                <small class="text-muted">{% trans "Attending" %}</small>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number text-warning">{{ attendee_counts.maybe }}</div>
                                <small class="text-muted">{% trans "Maybe" %}</small>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number text-danger">{{ attendee_counts.not_attending }}</div>
                                <small class="text-muted">{% trans "Not Attending" %}</small>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number text-secondary">{{ attendee_counts.pending }}</div>
                                <small class="text-muted">{% trans "Pending" %}</small>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if event.max_attendees %}
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-people me-1"></i>
                                {% trans "Maximum attendees:" %} {{ event.max_attendees }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                
                <!-- Event Meta Information -->
                <div class="col-lg-4">
                    <div class="event-meta">
                        <h5 class="mb-3">{% trans "Event Details" %}</h5>
                        
                        <!-- Date and Time -->
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="bi bi-calendar-event"></i>
                            </div>
                            <div>
                                <strong>{% trans "Date" %}</strong><br>
                                {% if event.is_multi_day %}
                                    {{ event.start_date|date:"M j, Y" }} - {{ event.end_date|date:"M j, Y" }}
                                {% else %}
                                    {{ event.start_date|date:"l, M j, Y" }}
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if not event.is_all_day and event.start_time %}
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div>
                                <strong>{% trans "Time" %}</strong><br>
                                {{ event.start_time|time:"g:i A" }}
                                {% if event.end_time %}
                                    - {{ event.end_time|time:"g:i A" }}
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if event.location %}
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <div>
                                <strong>{% trans "Location" %}</strong><br>
                                {{ event.location }}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if event.category %}
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="{{ event.category.icon }}"></i>
                            </div>
                            <div>
                                <strong>{% trans "Category" %}</strong><br>
                                <span class="badge" style="background-color: {{ event.category.color }}">
                                    {{ event.category.name }}
                                </span>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Duration -->
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div>
                                <strong>{% trans "Duration" %}</strong><br>
                                {% if event.duration_days == 1 %}
                                    {% trans "Single day" %}
                                {% else %}
                                    {{ event.duration_days }} {% trans "days" %}
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Recurrence -->
                        {% if event.recurrence != 'NONE' %}
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="bi bi-arrow-repeat"></i>
                            </div>
                            <div>
                                <strong>{% trans "Recurrence" %}</strong><br>
                                {{ event.get_recurrence_display }}
                                {% if event.recurrence_end_date %}
                                    <br><small class="text-muted">{% trans "Until" %} {{ event.recurrence_end_date|date:"M j, Y" }}</small>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="mt-3 d-grid gap-2">
                        <a href="{% url 'school_calendar:calendar' %}?year={{ event.start_date.year }}&month={{ event.start_date.month }}" 
                            class="btn btn-outline-primary">
                            <i class="bi bi-calendar me-1"></i>{% trans "View in Calendar" %}
                        </a>
                        <a href="{% url 'school_calendar:event_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-list me-1"></i>{% trans "All Events" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

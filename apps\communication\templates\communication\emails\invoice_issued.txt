{% load i18n humanize %}
{% autoescape off %}
{% blocktrans with school_name=school_name %}New Invoice Issued - {{ school_name }}{% endblocktrans %}

{% blocktrans with recipient_name=recipient_name %}Dear {{ recipient_name }},{% endblocktrans %}

{% blocktrans with student_name=student_name school_name=school_name %}A new invoice has been issued for {{ student_name }} at {{ school_name }}. Please find the details below:{% endblocktrans %}

{% trans "Invoice Details" %}
--------------------
Invoice Number: {{ invoice_details.invoice_number }}
Issue Date: {{ invoice_details.issue_date|date:"d M Y" }}
Due Date: {{ invoice_details.due_date|date:"d M Y" }}

{% if invoice_details.items %}
Invoice Items:
{% for item in invoice_details.items %}
- {{ item.description }}: {{ school_currency_symbol|default:'$' }}{{ item.amount|floatformat:2|intcomma }}
{% endfor %}
{% endif %}

Total Amount Due: {{ school_currency_symbol|default:'$' }}{{ invoice_details.amount_due|floatformat:2|intcomma }}
--------------------

{% if invoice_details.invoice_url or payment_portal_url %}
You can view the full invoice and make a payment via the parent portal:
{{ invoice_details.invoice_url|default:payment_portal_url }}
{% endif %}

Please ensure payment is made by the due date. If you have any questions, please contact the school office.

Thank you,
The {{ school_name }} Administration

{{ school_name }}
{% if school_address %}{{ school_address }}{% endif %}
{% if school_contact_info %}{{ school_contact_info }}{% endif %}
© {% now "Y" %} {{ school_name }}.
{% endautoescape %}


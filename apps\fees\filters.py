# D:\school_fees_saas_v2\apps\fees\filters.py

import django_filters
from django import forms

# Import the models you need to filter on
from .models import Invoice
from apps.students.models import Student
from apps.schools.models import AcademicYear

class InvoiceFilter(django_filters.FilterSet):
    """
    FilterSet for the Invoice model to be used in the InvoiceListView.
    """
    # Filter by student - uses a ModelChoiceField (dropdown)
    student = django_filters.ModelChoiceFilter(
        queryset=Student.objects.all(), # This will be further filtered in the form's __init__
        field_name='student',
        label="Student",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )

    # Filter by status - uses a ChoiceField (dropdown)
    status = django_filters.ChoiceFilter(
        choices=Invoice.InvoiceStatus.choices, # Assumes InvoiceStatus enum on Invoice model
        label="Status",
        empty_label="-- All Statuses --",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )

    # Filter by academic year
    academic_year = django_filters.ModelChoiceFilter(
        queryset=AcademicYear.objects.all(),
        label="Academic Year",
        empty_label="-- All Years --",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    
    # Filter by a range of issue dates
    issue_date_from = django_filters.DateFilter(
        field_name='issue_date',
        lookup_expr='gte', # gte = greater than or equal to
        label='Issue Date From',
        widget=forms.DateInput(attrs={'class': 'form-control form-control-sm', 'type': 'date'})
    )
    issue_date_to = django_filters.DateFilter(
        field_name='issue_date',
        lookup_expr='lte', # lte = less than or equal to
        label='Issue Date To',
        widget=forms.DateInput(attrs={'class': 'form-control form-control-sm', 'type': 'date'})
    )

    # Filter by searching within the invoice number
    invoice_number_query = django_filters.CharFilter(
        field_name='invoice_number',
        lookup_expr='icontains', # icontains = case-insensitive contains
        label="Invoice Number",
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Search Invoice #'})
    )

    class Meta:
        model = Invoice
        # These are the fields the FilterSet will use to build the form
        fields = [
            'student', 
            'status', 
            'academic_year', 
            'issue_date_from', 
            'issue_date_to', 
            'invoice_number_query'
        ]

    def __init__(self, *args, **kwargs):
        """
        Override __init__ to dynamically filter the 'student' dropdown
        to only show students relevant to the current tenant.
        """
        # A tenant object can be passed from the view for this purpose
        self.tenant = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)

        if self.tenant:
            # If a tenant is provided, filter the student choices.
            # This assumes your view will pass request.tenant to the filter.
            # Note: In a tenant schema, this is often redundant if the Student model
            # is already scoped, but it's a robust pattern.
            self.filters['student'].queryset = Student.objects.all() # .filter(school=self.tenant) if Student was a SHARED_APP model
            
            
            
            
            
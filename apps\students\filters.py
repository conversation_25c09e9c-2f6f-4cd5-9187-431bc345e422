# D:\school_fees_saas_v2\apps\students\filters.py
import django_filters
from django import forms
from django.db.models import Q # <<< MAKE SURE THIS IMPORT IS PRESENT
from .models import Student, ParentUser # Assuming STUDENT_STATUS_CHOICES is on Student model or imported
from apps.schools.models import SchoolClass # <<< CORRECTED IMPORT if SchoolClass is in schools app
from django.utils.translation import gettext_lazy as _

# If STUDENT_STATUS_CHOICES is a separate list in models.py, uncomment the next line
# from .models import STUDENT_STATUS_CHOICES

class StudentFilterForm(django_filters.FilterSet):
    name = django_filters.CharFilter(
        method='filter_by_name_or_admission',
        label=_("Name or Adm. No."), # Use _() for translation
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': _('Name or Adm. No.')})
    )
    current_class = django_filters.ModelChoiceFilter(
        queryset=SchoolClass.objects.all(), # Will be refined in __init__ if needed
        label=_("Class/Grade"),
        empty_label=_("-- All Classes --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}) # Good to set base class here
    )
    # Example for current_section if you add it later
    # from apps.schools.models import Section
    # current_section = django_filters.ModelChoiceFilter(
    #     queryset=Section.objects.all(),
    #     label=_("Section"),
    #     empty_label=_("-- All Sections --"),
    #     widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    # )

    status = django_filters.ChoiceFilter(
        # Assuming STUDENT_STATUS_CHOICES is an attribute of the Student model:
        choices=Student.StudentStatus.choices,
        # OR if it's a separately imported list:
        # choices=STUDENT_STATUS_CHOICES,
        label=_("Status"),
        empty_label=_("-- All Statuses --"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )

    class Meta:
        model = Student
        # Since 'name', 'current_class', and 'status' are explicitly defined above,
        # we don't strictly need to list them here with lookup types.
        # An empty fields list or dict tells django-filter to only use explicitly defined filters.
        fields = []
        # Or, if you want to be explicit about how django-filter would treat them if it auto-generated:
        # fields = {
        #     'current_class': ['exact'],
        #     'status': ['exact'],
        # }
        # But 'name' should NOT be here as it's handled by the custom CharFilter.

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # Get request if passed from view
        super().__init__(*args, **kwargs)

        # Dynamically populate queryset for SchoolClass
        # For django-tenants, .all() on a tenant-specific model (like SchoolClass if defined in 'schools' app)
        # is automatically scoped to the current tenant.
        active_classes = SchoolClass.objects.filter(is_active=True).order_by('name') # Example: filter active
        self.filters['current_class'].queryset = active_classes

        # Example for current_section if you add it and it depends on current_class
        # if 'current_section' in self.filters:
        #     selected_class_id = self.data.get('current_class') # From submitted form data
        #     if selected_class_id:
        #         try:
        #             self.filters['current_section'].queryset = Section.objects.filter(
        #                 school_class_id=selected_class_id, is_active=True
        #             ).order_by('name')
        #         except ValueError:
        #             self.filters['current_section'].queryset = Section.objects.none()
        #     else:
        #         # Default if no class selected - show no sections or all active sections
        #         self.filters['current_section'].queryset = Section.objects.filter(is_active=True).order_by('name')


    def filter_by_name_or_admission(self, queryset, name_param_in_url, value): # name_param_in_url will be 'name'
        """
        Custom filter method to search by student's first name, last name,
        middle name, or admission number.
        """
        if value: # Only filter if a value is provided
            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(middle_name__icontains=value) | # Make sure Student model has middle_name if you search it
                Q(admission_number__icontains=value)
            ).distinct() # distinct() is good if joins could cause duplicates
        return queryset # Return original queryset if no value





















# # D:\school_fees_saas_v2\apps\students\filters.py
# import django_filters
# from django import forms
# from django.db import models # For Q objects
# from .models import Student, SchoolClass # Assuming SchoolClass is correctly imported or defined
# from .models import STUDENT_STATUS_CHOICES # Assuming this is imported from your student models

# class StudentFilterForm(django_filters.FilterSet):
#     name = django_filters.CharFilter(
#         method='filter_by_name_or_admission',
#         label="Student Name or Admission No.",
#         widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Name or Adm. No.'}) # Optional: add widget here
#     )
#     current_class = django_filters.ModelChoiceFilter(
#         queryset=SchoolClass.objects.all(), # This will be further refined in __init__
#         label="Class/Grade",
#         empty_label="-- All Classes --",
#         # widget=forms.Select(attrs={'class': 'form-select form-select-sm'}) # Can do styling in template too
#     )
#     # current_section (if you add it, similar to current_class)

#     status = django_filters.ChoiceFilter(
#         choices=STUDENT_STATUS_CHOICES,
#         label="Status",
#         empty_label="-- All Statuses --",
#         # widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
#     )

#     class Meta:
#         model = Student
#         fields = {
#             # 'name': ['icontains'], # <<<< REMOVE THIS LINE
#             'current_class': ['exact'],
#             # 'current_section': ['exact'], # Add if you implement section filter
#             'status': ['exact'],
#         }
#         # By removing 'name', django-filter will use your explicitly defined 'name' CharFilter.
#         # The remaining fields ('current_class', 'status') will have filters auto-generated
#         # by FilterSet if not explicitly defined above (but you have defined them, which is good).
#         # If you explicitly define ALL filters as class attributes, Meta.fields can be an empty list or dict.
#         # However, it's common to explicitly define complex ones and let Meta.fields handle simple ones.
#         # For clarity, since you've defined current_class and status above, you could even have:
#         # fields = [] # Or fields = {} if all are explicitly defined.
#         # But keeping them in Meta.fields doesn't hurt if they match the explicitly defined ones.
#         # The key is removing the 'name' clash.

#     def __init__(self, *args, **kwargs):
#         self.request = kwargs.pop('request', None)
#         super().__init__(*args, **kwargs)

#         # Dynamically populate queryset for SchoolClass, tenant-aware if needed
#         if SchoolClass: # Check if SchoolClass was imported
#             # If SchoolClass is a tenant model, .all() is already schema-specific.
#             # If SchoolClass is shared, you might filter by request.tenant if available:
#             # if self.request and hasattr(self.request, 'tenant') and self.request.tenant:
#             #     self.filters['current_class'].queryset = SchoolClass.objects.filter(school=self.request.tenant).order_by('name')
#             # else:
#             self.filters['current_class'].queryset = SchoolClass.objects.all().order_by('name')

#         # Style form fields if not done via widget_tweaks in template (optional)
#         # for field_name, field in self.fields.items():
#         #     if isinstance(field.widget, forms.Select):
#         #         field.widget.attrs.update({'class': 'form-select form-select-sm'})
#         #     else:
#         #         field.widget.attrs.update({'class': 'form-control form-control-sm'})


#     def filter_by_name_or_admission(self, queryset, name_param, value): # name_param will be 'name'
#         if value:
#             # Ensure you have 'from django.db.models import Q' at the top of filters.py
#             return queryset.filter(
#                 models.Q(first_name__icontains=value) |
#                 models.Q(last_name__icontains=value) |
#                 models.Q(middle_name__icontains=value) |
#                 models.Q(admission_number__icontains=value)
#             ).distinct()
#         return queryset




class ParentUserFilterForm(django_filters.FilterSet):
    """Filter form for ParentUser list view."""

    name = django_filters.CharFilter(
        method='filter_by_name_or_email',
        label=_("Name or Email"),
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': _('Name or Email')
        })
    )

    is_active = django_filters.BooleanFilter(
        label=_("Status"),
        widget=forms.Select(
            choices=[
                ('', _('-- All Status --')),
                (True, _('Active')),
                (False, _('Inactive'))
            ],
            attrs={'class': 'form-select form-select-sm'}
        )
    )

    has_children = django_filters.BooleanFilter(
        method='filter_by_children',
        label=_("Has Children"),
        widget=forms.Select(
            choices=[
                ('', _('-- All Parents --')),
                (True, _('With Children')),
                (False, _('Without Children'))
            ],
            attrs={'class': 'form-select form-select-sm'}
        )
    )

    class Meta:
        model = ParentUser
        fields = ['name', 'is_active', 'has_children']

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def filter_by_name_or_email(self, queryset, name, value):
        """Filter by first name, last name, or email."""
        if value:
            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(email__icontains=value)
            ).distinct()
        return queryset

    def filter_by_children(self, queryset, name, value):
        """Filter parents by whether they have linked children."""
        if value is True:
            return queryset.filter(children__isnull=False).distinct()
        elif value is False:
            return queryset.filter(children__isnull=True).distinct()
        return queryset
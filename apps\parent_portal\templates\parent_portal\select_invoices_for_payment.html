{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\select_invoices_for_payment.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title|default:_("Pay School Fees") }}{% endblock %}

{% block extra_parent_portal_css %}
    {{ block.super }}
    <style>
        .invoice-selection-item {
            border: 1px solid #eee;
            border-radius: .375rem; /* Bootstrap's default border-radius */
            padding: 1rem 1.25rem;
            margin-bottom: 1rem;
            background-color: #fff;
            transition: box-shadow .15s ease-in-out;
        }
        .invoice-selection-item:hover {
            box-shadow: 0 .25rem .75rem rgba(0,0,0,.075);
        }
        .invoice-selection-item input[type="checkbox"] {
            flex-shrink: 0; /* Prevent checkbox from shrinking */
            margin-top: 0.25rem; /* Align with first line of text */
            transform: scale(1.3); /* Make checkbox slightly larger */
        }
        .invoice-details {
            cursor: pointer;
        }
        .invoice-amount {
            font-weight: bold;
            font-size: 1.1em;
        }
        .total-payment-summary {
            position: sticky;
            top: 80px; /* Adjust based on your sticky navbar height */
            z-index: 100;
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: .375rem;
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.1);
        }
    </style>
{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i>{% trans "Back to Dashboard" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <form method="post">
        {% csrf_token %}
        <div class="row">
            <div class="col-lg-8">
                {% if form.selected_invoices.field.queryset.exists %}
                    <p class="text-muted">{% trans "Please select the invoices you wish to pay from the list below." %}</p>
                    {% for invoice in form.selected_invoices.field.queryset %} {# Iterate through the actual invoice objects #}
                        <div class="invoice-selection-item">
                            <div class="d-flex align-items-start">
                                <input type="checkbox" name="selected_invoices" value="{{ invoice.pk }}" id="id_selected_invoices_{{ forloop.counter0 }}" class="form-check-input" data-amount="{{ invoice.balance_due }}"{% if form.selected_invoices.value and invoice.pk in form.selected_invoices.value %} checked{% endif %}>
                                <label for="id_selected_invoices_{{ forloop.counter0 }}" class="invoice-details ms-2 flex-grow-1"> {# Added ms-2 for spacing from checkbox #}
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ invoice.invoice_number }} - {{ invoice.student.get_full_name }}</strong>
                                        <span class="invoice-amount text-danger">
                                            {{ school_profile.currency_symbol|default:"$" }}{{ invoice.balance_due|floatformat:2|intcomma }}
                                        </span>
                                    </div>
                                    <small class="text-muted d-block">
                                        {% trans "Issued" %}: {{ invoice.issue_date|date:"d M Y" }} |
                                        {% trans "Due" %}: {{ invoice.due_date|date:"d M Y" }}
                                        <a href="{% url 'parent_portal:invoice_detail' invoice.pk %}" class="btn btn-xs btn-outline-info ms-2" target="_blank" title="View Invoice Details" onclick="event.stopPropagation();">
                                            <i class="bi bi-eye"></i> {% trans "View" %}
                                        </a>
                                    </small>
                                    <small class="text-muted d-block">{{ invoice.notes_to_parent|truncatechars:80 }}</small>
                                </label>
                            </div>
                        </div>
                    {% endfor %}
                    {% if form.selected_invoices.errors %}
                        <div class="alert alert-danger mt-2">
                            {% for error in form.selected_invoices.errors %} {{ error }} {% endfor %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>{% trans "You have no outstanding invoices at this time." %}
                    </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="total-payment-summary">
                    <h5 class="mb-3">{% trans "Payment Summary" %}</h5>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{% trans "Number of Invoices Selected:" %}</span>
                        <strong id="selected-count">0</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>{% trans "Total Amount to Pay:" %}</span>
                        <strong id="total-amount">{{ school_profile.currency_symbol|default:"$" }}0.00</strong>
                    </div>
                    <button type="submit" class="btn btn-success btn-lg w-100" id="proceed-to-payment-btn" disabled>
                        <i class="bi bi-lock-fill me-2"></i>{% trans "Proceed to Payment" %}
                    </button>
                    {% for error in form.non_field_errors %}
                        <div class="alert alert-danger mt-2">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock parent_portal_main_content %}

{% block extra_parent_portal_js %}
    {{ block.super }}
    <script src="https://cdn.jsdelivr.net/npm/decimal.js@10.4.3/decimal.min.js"></script> 
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_invoices"]');
        const selectedCountEl = document.getElementById('selected-count');
        const totalAmountEl = document.getElementById('total-amount');
        const proceedButton = document.getElementById('proceed-to-payment-btn');
        const currencySymbol = "{{ school_profile.currency_symbol|default:'$'|escapejs }}";

        function updateTotalSummary() {
            let count = 0;
            let total = Decimal(0);
            checkboxes.forEach(function(checkbox) {
                if (checkbox.checked) {
                    count++;
                    // Get the balance amount from the data attribute
                    const balanceDue = Decimal(checkbox.dataset.amount || 0);
                    total = total.plus(balanceDue);
                }
            });
            selectedCountEl.textContent = count;
            totalAmountEl.textContent = currencySymbol + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'); // Add commas

            if (count > 0 && total.greaterThan(0)) {
                proceedButton.disabled = false;
            } else {
                proceedButton.disabled = true;
            }
        }

        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', updateTotalSummary);
        });
        updateTotalSummary(); // Initial calculation on page load
    });
    </script>
{% endblock %}





{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\platformannouncement_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Platform Announcement") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .form-check {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .form-check-input {
            width: 1.5rem;
            height: 1.5rem;
            margin-top: 0.125rem;
            border: 2px solid #667eea;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .fieldset-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            margin: 2rem 0 1.5rem 0;
            text-align: center;
        }

        .fieldset-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
    </style>
{% endblock %}















{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Platform Announcement") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:platformannouncement_list' %}">{% trans "Announcements" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}{% trans "Edit Announcement" %}{% else %}{% trans "New Announcement" %}{% endif %}
            </li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-megaphone-fill me-2"></i>
                        {% if object %}{% trans "Edit Platform Announcement" %}{% else %}{% trans "Create Platform Announcement" %}{% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        <!-- Announcement Information Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-info-circle-fill me-2"></i>{% trans "Announcement Information" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Title Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if form.title.errors %} is-invalid{% endif %}"
                                           id="{{ form.title.id_for_label }}"
                                           name="{{ form.title.name }}"
                                           value="{{ form.title.value|default:'' }}"
                                           placeholder="{% trans 'Enter announcement title' %}"
                                           {% if form.title.field.required %}required{% endif %}>
                                    <label for="{{ form.title.id_for_label }}">
                                        <i class="bi bi-card-heading icon-input"></i>{% trans "Title" %}
                                    </label>
                                    {% if form.title.help_text %}
                                        <div class="form-text">{{ form.title.help_text }}</div>
                                    {% endif %}
                                    {% if form.title.errors %}
                                        <div class="invalid-feedback">{{ form.title.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Content Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if form.content.errors %} is-invalid{% endif %}"
                                              id="{{ form.content.id_for_label }}"
                                              name="{{ form.content.name }}"
                                              placeholder="{% trans 'Enter announcement content' %}"
                                              style="height: 150px;"
                                              {% if form.content.field.required %}required{% endif %}>{{ form.content.value|default:'' }}</textarea>
                                    <label for="{{ form.content.id_for_label }}">
                                        <i class="bi bi-file-text icon-input"></i>{% trans "Content" %}
                                    </label>
                                    {% if form.content.help_text %}
                                        <div class="form-text">{{ form.content.help_text }}</div>
                                    {% endif %}
                                    {% if form.content.errors %}
                                        <div class="invalid-feedback">{{ form.content.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Publishing Settings Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-calendar-event-fill me-2"></i>{% trans "Publishing Settings" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Publish Date Field -->
                                <div class="form-floating">
                                    <input type="datetime-local"
                                           class="form-control{% if form.publish_date.errors %} is-invalid{% endif %}"
                                           id="{{ form.publish_date.id_for_label }}"
                                           name="{{ form.publish_date.name }}"
                                           value="{{ form.publish_date.value|date:'Y-m-d\TH:i'|default:'' }}"
                                           {% if form.publish_date.field.required %}required{% endif %}>
                                    <label for="{{ form.publish_date.id_for_label }}">
                                        <i class="bi bi-calendar-plus icon-input"></i>{% trans "Publish Date" %}
                                    </label>
                                    {% if form.publish_date.help_text %}
                                        <div class="form-text">{{ form.publish_date.help_text }}</div>
                                    {% endif %}
                                    {% if form.publish_date.errors %}
                                        <div class="invalid-feedback">{{ form.publish_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Expiry Date Field -->
                                <div class="form-floating">
                                    <input type="datetime-local"
                                           class="form-control{% if form.expiry_date.errors %} is-invalid{% endif %}"
                                           id="{{ form.expiry_date.id_for_label }}"
                                           name="{{ form.expiry_date.name }}"
                                           value="{{ form.expiry_date.value|date:'Y-m-d\TH:i'|default:'' }}"
                                           {% if form.expiry_date.field.required %}required{% endif %}>
                                    <label for="{{ form.expiry_date.id_for_label }}">
                                        <i class="bi bi-calendar-x icon-input"></i>{% trans "Expiry Date (Optional)" %}
                                    </label>
                                    {% if form.expiry_date.help_text %}
                                        <div class="form-text">{{ form.expiry_date.help_text }}</div>
                                    {% endif %}
                                    {% if form.expiry_date.errors %}
                                        <div class="invalid-feedback">{{ form.expiry_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Is Published Field -->
                                <div class="form-check">
                                    <input type="checkbox"
                                           class="form-check-input{% if form.is_published.errors %} is-invalid{% endif %}"
                                           id="{{ form.is_published.id_for_label }}"
                                           name="{{ form.is_published.name }}"
                                           value="1"
                                           {% if form.is_published.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ form.is_published.id_for_label }}">
                                        <i class="bi bi-eye-fill me-2"></i>{{ form.is_published.label }}
                                    </label>
                                    {% if form.is_published.help_text %}
                                        <div class="form-text">{{ form.is_published.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_published.errors %}
                                        <div class="invalid-feedback">{{ form.is_published.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'platform_management:platformannouncement_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}{% trans "Update Announcement" %}{% else %}{% trans "Create Announcement" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation and enhancement
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');

        // Form submission handling
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Date validation - ensure expiry date is after publish date
        const publishDateInput = document.getElementById('{{ form.publish_date.id_for_label }}');
        const expiryDateInput = document.getElementById('{{ form.expiry_date.id_for_label }}');

        if (publishDateInput && expiryDateInput) {
            function validateDates() {
                const publishDate = new Date(publishDateInput.value);
                const expiryDate = new Date(expiryDateInput.value);

                if (publishDateInput.value && expiryDateInput.value && expiryDate <= publishDate) {
                    expiryDateInput.setCustomValidity('{% trans "Expiry date must be after publish date" %}');
                    expiryDateInput.classList.add('is-invalid');
                } else {
                    expiryDateInput.setCustomValidity('');
                    expiryDateInput.classList.remove('is-invalid');
                }
            }

            publishDateInput.addEventListener('change', validateDates);
            expiryDateInput.addEventListener('change', validateDates);
        }

        // Auto-focus first input
        const firstInput = form.querySelector('input[type="text"], textarea');
        if (firstInput) {
            firstInput.focus();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    });
    </script>
{% endblock %}

{# D:\school_fees_saas_v2\templates\public_site\home.html #}
{% extends "public_base.html" %}
{% load static humanize %}

{% block public_page_title %}School Fees Collection & Management Platform{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/public_home_hero.css' %}">
    <style>
        /* Styles for the welcome message bar if using Option 2 */
        .welcome-message-bar-inline {
            background-color: #17a2b8; /* Example: Bootstrap info teal */
            color: white;
            padding: 0.75rem 1rem;
            text-align: center;
            font-size: 1.1rem;
            font-weight: 500;
            /* margin-bottom: 0; */ /* Ensure no gap if hero has top margin/padding */
        }
    </style>
{% endblock %}


{% block content_full_width %}
    {# INLINE WELCOME MESSAGE BAR #}
    <div class="welcome-message-bar-inline">
        <div class="container"> {# Optional: to constrain width #}
            Welcome to the School Fees Collection & Management Platform!
        </div>
    </div>
    
    {# HERO SECTION Start #}
    <section class="hero-section text-light" style="background: linear-gradient(rgba(44, 62, 80, 0.6), rgba(44, 62, 80, 0.75)), url('{% static "img/backgrounds/hero_background_generic.jpg" %}'); background-size: cover; background-position: center center;">
        {# Fallback solid color if image is not available or desired #}
        {# style="background-color: #007bff;" #}
        <div class="container py-5">
            <div class="row align-items-center">
                {# Text Content Column #}
                <div class="col-lg-7 col-md-12 text-center text-lg-start mb-4 mb-lg-0">
                    <h1 class="display-3 fw-bolder hero-title text-white animate__animated animate__fadeInDown">
                        Streamline Your School's Finances
                    </h1>
                    <p class="lead hero-subtitle text-white-75 mb-4 animate__animated animate__fadeInUp animate__delay-1s">
                        Our comprehensive platform simplifies fee collection, automates invoicing, tracks payments, and enhances communication with parents and staff. Focus on education, let us handle the administrative overhead.
                    </p>
                    <div class="d-grid gap-3 d-sm-flex justify-content-sm-center justify-content-lg-start animate__animated animate__fadeInUp animate__delay-2s">
                        <a href="{% url 'tenants:register_school' %}" class="btn btn-primary btn-lg px-4 gap-3 btn-hero">
                            <i class="bi bi-rocket-takeoff-fill me-2"></i>Get Started Today
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg px-4 btn-hero">
                            <i class="bi bi-eye-fill me-2"></i>Discover Features
                        </a>
                    </div>
                </div>

                {# Optional Image/Illustration Column - shown on larger screens #}
                <div class="col-lg-5 d-none d-lg-block text-center animate__animated animate__fadeInRight animate__delay-1s">
                    <img src="{% static 'img/illustrations/school_management_dashboard.svg' %}" 
                         alt="Fees Management Platform Illustration" 
                         class="img-fluid hero-image" 
                         style="max-height: 420px;">
                </div>
            </div>
        </div>
    </section>
    {# HERO SECTION End #}
{% endblock content_full_width %}


{# The rest of your page content will go into the standard 'content' block #}
{% block content %}
    {# SCHOOL FINDER Section #}
    <section class="school-finder-section py-5 bg-light-subtle">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-8">
                    <div class="card shadow-sm school-finder-card border-0">
                        <div class="card-body p-4 p-md-5">
                            <h2 class="card-title text-center mb-4 h3">Find Your School Portal</h2>
                            <form method="GET" action="{% url 'public_site:find_school' %}">
                                <div class="input-group input-group-lg mb-2">
                                    <span class="input-group-text bg-transparent border-end-0 text-secondary"><i class="bi bi-search"></i></span>
                                    <input type="text" name="query" class="form-control border-start-0" placeholder="Enter School Name or Unique Code" required value="{{ request.GET.query|default:'' }}" aria-label="School search query">
                                    <button class="btn btn-success" type="submit">
                                        Find Portal
                                    </button>
                                </div>
                                <small class="form-text text-muted d-block text-center mt-2">
                                    Quickly access your school's dedicated fee payment and information portal.
                                </small>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {# FEATURES Section #}
    <section id="features" class="features-section py-5"> {# Added id="features" #}
        <div class="container">
            <h2 class="text-center section-title mb-5">Why Choose Our Platform?</h2>
            <div class="row text-center g-4">
                <div class="col-lg-3 col-md-6"> {# Changed to col-lg-3 for up to 4 features #}
                    <div class="feature-card h-100 p-4 shadow-sm">
                        <div class="feature-icon-wrapper bg-primary text-white mb-3">
                            <i class="bi bi-receipt-cutoff"></i>
                        </div>
                        <h3 class="h5">Automated Billing</h3>
                        <p class="small">Generate and send invoices automatically. Track payments and send timely reminders.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card h-100 p-4 shadow-sm">
                        <div class="feature-icon-wrapper bg-success text-white mb-3">
                             <i class="bi bi-credit-card-2-front-fill"></i>
                        </div>
                        <h3 class="h5">Online Payments</h3>
                        <p class="small">Securely accept fees through various online payment gateways for parent convenience.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card h-100 p-4 shadow-sm">
                        <div class="feature-icon-wrapper bg-info text-white mb-3">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h3 class="h5">Parent & Staff Portals</h3>
                        <p class="small">Empower users with secure online access to information and self-service tools.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card h-100 p-4 shadow-sm">
                        <div class="feature-icon-wrapper bg-warning text-white mb-3">
                             <i class="bi bi-bar-chart-line-fill"></i>
                        </div>
                        <h3 class="h5">Insightful Reports</h3>
                        <p class="small">Gain clarity with comprehensive financial and operational reports for decision-making.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {# TESTIMONIALS Section #}
    {% if testimonials %}
    <section class="testimonials-section py-5 bg-light-subtle">
        <div class="container">
            <h2 class="text-center section-title mb-5">What Our Schools Say</h2>
            <div class="row">
                {% for testimonial in testimonials %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card testimonial-card shadow-sm h-100">
                        <div class="card-body">
                            <div class="text-center mb-2">
                                <img src="{% static 'img/avatars/default_avatar.png' %}" alt="{{testimonial.author_name}}" class="rounded-circle" width="60" height="60">
                            </div>
                            <p class="card-text fst-italic text-center">"{{ testimonial.quote|truncatewords:30 }}"</p>
                            {% if testimonial.rating %}
                                <div class="mt-2 text-center">
                                    {% for i in "12345"|make_list %}
                                        <i class="bi bi-star{% if testimonial.rating >= i|add:0 %}-fill text-warning{% else %}{% endif %}"></i>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-white text-center border-top-0 pt-0">
                            <small class="text-muted">
                                <strong class="d-block">{{ testimonial.author_name }}</strong>
                                <em>{{ testimonial.author_title_school }}</em>
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if testimonial_list_url and testimonial_add_url %}
            <div class="text-center mt-4">
                <a href="{{ testimonial_list_url }}" class="btn btn-outline-primary">More Testimonials</a>
                <a href="{{ testimonial_add_url }}" class="btn btn-success ms-2">Share Your Experience</a>
            </div>
            {% endif %}
        </div>
    </section>
    {% endif %}

    {# FINAL CALL TO ACTION Section #}
    <section class="cta-section py-5 text-center">
        <div class="container">
            <h2 class="section-title mb-3">Ready to Transform Your School's Fee Management?</h2>
            <p class="lead mb-4">Join hundreds of schools benefiting from a simpler, more efficient system.</p>
            <a href="{% url 'tenants:register_school' %}" class="btn btn-primary btn-lg">
                <i class="bi bi-check2-circle me-2"></i>Sign Up For Free Trial
            </a>
        </div>
    </section>

    {# ADMIN LOGIN LINK Section #}
    <section class="admin-login-section text-center py-3 bg-dark text-light-emphasis">
        <div class="container">
            <p class="mb-0">
                <a href="{% url 'users:school_admin_login' %}" class="text-light-emphasis small">Platform Administrator Login</a>
            </p>
        </div>
    </section>
{% endblock content %}






{% comment %} {# D:\school_fees_saas_v2\templates\public_site\home.html #}
{% extends "public_base.html" %}

{% load static humanize %}

{% block public_page_title %}School Fees Collection & Management Platform{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    {# We assume styles are now in public_site_styles.css #}
{% endblock %}


{# home.html - TEMPORARY TEST #}
{% block content_full_width %}
    <h1 style="color: red; font-size: 50px; background-color: yellow; border: 2px solid blue;">
        School Fees Collection & Management Platform -- TEST VISIBILITY
    </h1>
{% endblock %}


{% block content %}
<section class="school-finder-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-8">
                    <div class="card shadow-sm school-finder-card">
                        <div class="card-body">
                            <h2 class="card-title text-center">Find Your School Portal</h2>
                            <form method="GET" action="{% url 'public_site:find_school' %}">
                                <div class="input-group input-group-lg mb-2">
                                    <input type="text" name="query" class="form-control" placeholder="Enter School Name or Unique Code" required value="{{ request.GET.query|default:'' }}" aria-label="School search query">
                                    <button class="btn btn-success" type="submit">
                                        <i class="bi bi-search me-1"></i> Find
                                    </button>
                                </div>
                                <small class="form-text text-muted d-block text-center">
                                    Access your school's dedicated portal.
                                </small>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="features-section">
        <div class="container">
            <h2 class="text-center section-title">Why Choose Our Platform?</h2>
            <div class="row text-center g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="bi bi-cash-coin feature-icon"></i>
                        <h3>Automated Billing</h3>
                        <p>Generate and send invoices automatically. Track payments with ease and send timely reminders.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="bi bi-people-fill feature-icon"></i>
                        <h3>Parent & Staff Portals</h3>
                        <p>Empower parents and staff with secure online access to relevant information and self-service tools.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="bi bi-bar-chart-line-fill feature-icon"></i>
                        <h3>Insightful Reports</h3>
                        <p>Gain clarity with comprehensive financial and operational reports to make informed decisions.</p>
                    </div>
                </div>
                {# You can add more features or use a 2-column layout on smaller screens if needed #}
            </div>
        </div>
    </section>

    {% if testimonials %}
    <section class="testimonials-section">
        <div class="container">
            <h2 class="text-center section-title">What Our Schools Say</h2>
            <div class="row">
                {% for testimonial in testimonials %}
                <div class="col-lg-4 col-md-6"> {# Use col-lg-4 for 3 per row on large, col-md-6 for 2 on medium #}
                    <div class="card testimonial-card shadow-sm"> {# h-100 can be added via CSS #}
                        <div class="card-body">
                            <p class="card-text fst-italic">"{{ testimonial.quote|truncatewords:25 }}"</p> {# Shorter truncate #}
                            {% if testimonial.rating %}
                                <div class="mt-2 text-center">
                                    {% for i in "12345"|make_list %}
                                        <i class="bi bi-star{% if testimonial.rating >= i|add:0 %}-fill text-warning{% else %}{% endif %}"></i>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-light text-center">
                            <small class="text-muted">
                                <strong>{{ testimonial.author_name }}</strong><br>
                                <em>{{ testimonial.author_title_school }}</em>
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="text-center mt-3"> {# Reduced margin #}
                <a href="{% url 'public_site:testimonial_list' %}" class="btn btn-outline-primary btn-sm">More Testimonials</a>
                <a href="{% url 'public_site:testimonial_add' %}" class="btn btn-success btn-sm ms-2">Share Your Experience</a>
            </div>
        </div>
    </section>
    {% endif %}

    <section class="admin-login-section text-center">
        <div class="container">
            <p>
                <a href="{% url 'users:school_admin_login' %}" class="text-secondary small">Platform Administrator Login</a>
            </p>
        </div>
    </section>
{% endblock content %} {% endcomment %}












{% comment %} {# D:\school_fees_saas_v2\templates\public_site\home.html #}
{% extends "public_base.html" %}
{% load static humanize %}

{% block public_page_title %}Welcome - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .hero-section {
            padding: 4rem 1.5rem;
            background-color: #f0f2f5;
            border-bottom: 1px solid #dee2e6;
            text-align: center;
        }
        .hero-section .display-4 {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.75rem;
        }
        .hero-section .fs-4.lead {
            color: #34495e;
            max-width: 750px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 2rem;
        }
        .feature-icon { font-size: 2.5rem; color: #0d6efd; margin-bottom: 1rem; }
        .feature-card { border: none; background: none; padding: 1.5rem; }
        .testimonial-card { margin-bottom: 1.5rem; border: 1px solid #e9ecef; }
        .school-finder-card { background-color: #ffffff; border-radius: 0.5rem; }
    </style>
{% endblock %}

{# This block will replace the navbar from base.html if public_base.html defines content_full_width
It's better if public_base.html defines a "main_content_before_container" block for such full-width sections #}

{% block content_full_width %}
    <section class="hero-section text-white text-center" style="margin-top: 200px !important;"> {# Adjust 70px #}
        <div class="hero-overlay"></div>
        <div class="container position-relative">
            <h1 class="display-3 fw-bold hero-headline mb-3">
                School Fees Collection & Management Platform
            </h1>
            <p class="fs-4 hero-subheadline lead mb-5">
                Streamline your school's fee collection, tracking, and reporting with our comprehensive platform.
            </p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                <a href="{% url 'tenants:register_school' %}" class="btn btn-primary btn-lg px-4 gap-3 hero-btn-primary">
                    <i class="bi bi-pencil-square me-1"></i> Register Your School
                </a>
                <a href="{% url 'public_site:features' %}" class="btn btn-outline-light btn-lg px-4 hero-btn-secondary">
                    <i class="bi bi-card-list me-1"></i> Explore Features
                </a>
            </div>
        </div>
    </section>
{% endblock %}
{% comment %} {% block content_full_width %} {# Or a more specific block name like 'hero_section_block' defined in public_base.html #}
    <div class="hero-section">
        <div class="container">
            <h1 class="display-4">{{ view_title|default:"Welcome to the School Fees Management Platform" }}</h1>
            <p class="fs-4 lead mb-4">Streamline your school's fee collection, tracking, and reporting with our comprehensive platform.</p>
            <p>
                <a href="{% url 'tenants:register_school' %}" class="btn btn-primary btn-lg me-md-2 mb-2 mb-md-0">
                    <i class="bi bi-pencil-square me-1"></i> Register Your School
                </a>
                <a href="{% url 'public_site:features' %}" class="btn btn-outline-secondary btn-lg mb-2 mb-md-0">
                    <i class="bi bi-card-list me-1"></i> Learn More
                </a>
            </p>
        </div>
    </div>
{% endblock %} 

{% block content %} {# This assumes public_base.html has a standard 'content' block wrapped in a container #}
    {# School Finder Form Section - This will now be inside the main container #}
    <div class="row justify-content-center mt-5 mb-5"> {# Added mt-5 for spacing after hero #}
        <div class="col-md-10 col-lg-8">
            <div class="card shadow-sm school-finder-card">
                <div class="card-body p-4 p-md-5">
                    <h2 class="card-title text-center mb-4">Find Your School Portal</h2>
                    <form method="GET" action="{% url 'public_site:find_school' %}">
                        <div class="input-group input-group-lg mb-2">
                            <input type="text" name="query" class="form-control" placeholder="Enter School Name or Unique Code" required value="{{ request.GET.query|default:'' }}" aria-label="School search query">
                            <button class="btn btn-success" type="submit">
                                <i class="bi bi-search me-1"></i> Find School
                            </button>
                        </div>
                        <small class="form-text text-muted d-block text-center">
                            Already have an account with a school? Find its portal here.
                        </small>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {# Key Features/Benefits Section #}
    <div class="container py-4"> {# Ensure this content is within a container #}
        <div class="row text-center mb-5 g-4">
            <div class="col-lg-4">
                <div class="feature-card">
                    <i class="bi bi-cash-coin feature-icon"></i>
                    <h3 class="mt-3">Automated Billing</h3>
                    <p class="text-muted">Generate and send invoices automatically. Track payments with ease and send timely reminders.</p>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="feature-card">
                    <i class="bi bi-people-fill feature-icon"></i>
                    <h3 class="mt-3">Parent & Staff Portals</h3>
                    <p class="text-muted">Empower parents and staff with secure online access to relevant information and self-service tools.</p>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="feature-card">
                    <i class="bi bi-bar-chart-line-fill feature-icon"></i>
                    <h3 class="mt-3">Insightful Reports</h3>
                    <p class="text-muted">Gain clarity with comprehensive financial, academic, and operational reports to make informed decisions.</p>
                </div>
            </div>
        </div>

        {# Testimonials Section #}
        {% if testimonials %}
        <hr class="my-5">
        <h2 class="text-center mb-4">What Our Schools Say</h2>
        <div class="row">
            {% for testimonial in testimonials %}
            <div class="col-md-4">
                <div class="card testimonial-card shadow-sm h-100">
                    <div class="card-body d-flex flex-column">
                        <p class="card-text fst-italic mb-auto">"{{ testimonial.quote|truncatewords:30 }}"</p>
                        {% if testimonial.rating %}
                            <div class="mt-2 text-center">
                                {% for i in "12345"|make_list %}
                                    <i class="bi bi-star{% if testimonial.rating >= i|add:0 %}-fill text-warning{% else %}{% endif %}"></i> {# Removed -empty #}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-light text-center">
                        <small class="text-muted">
                            <strong>{{ testimonial.author_name }}</strong><br>
                            <em>{{ testimonial.author_title_school }}</em>
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{% url 'public_site:testimonial_list' %}" class="btn btn-outline-primary btn-sm">More Testimonials</a>
            <a href="{% url 'public_site:testimonial_add' %}" class="btn btn-success btn-sm ms-2">Share Your Experience</a>
        </div>
        {% endif %}
    </div>

    <div class="container text-center mt-5 mb-3">
        <p>
            <a href="{% url 'users:school_admin_login' %}" class="text-secondary small">Platform Administrator Login</a>
        </p>
    </div>
{% endblock content %} {% endcomment %}









{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\admin_manual_approve_confirm.html #}
{% extends "platform_management/platform_admin_base.html" %} {# Or your Django admin base #}
{% load i18n %}

{% block platform_admin_page_title %}{{ view_title }}{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
</div>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">{% trans "Confirm Manual Activation" %}</h5>
        <p>
            {% blocktrans with school_name=tenant_public_model_instance.name plan_name=subscription.plan.name status=subscription.get_status_display %}
            Are you sure you want to manually activate the <strong>{{ plan_name }}</strong> plan for school 
            <strong>{{ school_name }}</strong>?
            Current subscription status: <strong>{{ status }}</strong>.
            {% endblocktrans %}
        </p>
        <p>
            {% trans "This will set the subscription status to ACTIVE and establish a new billing period." %}
        </p>
        <form method="post">
            {% csrf_token %}
            <button type="submit" class="btn btn-success">{% trans "Yes, Activate Subscription" %}</button>
            <a href="{% url 'admin:subscriptions_subscription_change' subscription.pk %}" class="btn btn-secondary ms-2">{% trans "Cancel" %}</a>
        </form>
    </div>
</div>
{% endblock %}



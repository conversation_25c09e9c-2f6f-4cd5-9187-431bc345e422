{# D:\school_fees_saas_v2\templates\payments\payment_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize core_tags widget_tweaks %}

{% block title %}{{ view_title|default:"Manage Payments" }} - {{ request.tenant.name }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .status-badge { font-size: 0.8em; padding: 0.4em 0.7em; } /* If displaying status */
        .actions-column .btn { margin-right: 0.25rem; margin-bottom: 0.25rem; }
        .table th, .table td { vertical-align: middle; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="d-flex flex-wrap justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800 me-3">{{ view_title|default:"Manage Payments" }}</h1>
        <div class="d-flex align-items-center gap-2 mt-2 mt-md-0">
            {% if perms.payments.add_payment %}
                <a href="{% url 'payments:record_payment' %}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle-fill me-1"></i> Record New Payment
                </a>
            {% endif %}
            <a href="{% url 'payments:payment_method_list' %}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-credit-card-2-front-fill me-1"></i> Manage Payment Methods
            </a>
        </div>
    </div>

    {% include "partials/_messages.html" %}

    {# --- Include Filter Form and Export Card --- #}
    {# This partial expects 'filter_form', 'export_csv_url', etc. in context #}
    {# The PaymentListView and BaseReportViewMixin (if used) should provide these #}
    {% if filter_form %} {# Only include if filter_form is provided by the view #}
        {% include "reporting/_report_filter_export_card.html" with report_code="payment_list_export" %}
        {% comment %} {# Note: The export URLs in BaseReportViewMixin are generic.
        You might need to customize them or have specific export views for payments.
        For now, assuming the generic export links from the report card are okay or
        will be adapted in the PaymentListView.
        #} {% endcomment %}
    {% else %}
        <div class="alert alert-info">Filter functionality coming soon.</div>
    {% endif %}


    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h6 class="m-0 font-weight-bold text-primary">Payment Records</h6>
        </div>
        <div class="card-body">
            {% if payments %} {# 'payments' is context_object_name from ListView #}
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered align-middle table-sm">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Receipt #</th>
                                <th scope="col">Payment Date</th>
                                <th scope="col">Student</th>
                                <th scope="col">Invoice #</th>
                                <th scope="col" class="text-end">Amount</th>
                                <th scope="col">Method</th>
                                <th scope="col">Reference</th>
                                <th scope="col">Recorded By</th>
                                <th scope="col" class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>
                                    <a href="#"><strong>{{ payment.receipt_number|default:payment.pk }}</strong></a> {# Link to Payment Detail later #}
                                </td>
                                <td>{{ payment.payment_date|date:"d M Y, H:i" }}</td>
                                <td>
                                    {% if payment.student %}
                                        <a href="{% url 'students:student_detail' payment.student.pk %}">{{ payment.student.full_name }}</a>
                                        <small class="d-block text-muted">{{ payment.student.admission_number }}</small>
                                    {% elif payment.invoice and payment.invoice.student %}
                                        <a href="{% url 'students:student_detail' payment.invoice.student.pk %}">{{ payment.invoice.student.full_name }}</a>
                                        <small class="d-block text-muted">{{ payment.invoice.student.admission_number }}</small>
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.allocations.exists %}
                                        {% for allocation in payment.allocations.all %}
                                            <a href="{% url 'fees:invoice_detail' allocation.invoice.pk %}">{{ allocation.invoice.invoice_number_display }}</a>{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        Direct Payment
                                    {% endif %}
                                </td>
                                <td class="text-end fw-bold">{{ payment.amount|floatformat:2|intcomma }}</td>
                                <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                                <td>{{ payment.reference_number|default:"-" }}</td>
                                <td>{{ payment.processed_by_staff.short_name|default:payment.processed_by_staff.email|default:"System" }}</td>
                                <td class="text-center actions-column">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionsMenuPayment{{payment.pk}}" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionsMenuPayment{{payment.pk}}">
                                            {# <li><a class="dropdown-item" href="#">View Details</a></li> #} {# Link to Payment Detail View later #}
                                            <li><a class="dropdown-item" href="{% url 'payments:payment_receipt_pdf' payment.pk %}" target="_blank"><i class="bi bi-file-earmark-pdf me-2"></i>View/Print Receipt</a></li>
                                            {% if perms.payments.change_payment %}
                                                {# Update Payment - Complex due to JE, typically not allowed or only for drafts #}
                                                {# <li><a class="dropdown-item" href="#"><i class="bi bi-pencil-square me-2"></i>Edit Payment</a></li> #}
                                            {% endif %}
                                            {% if perms.payments.delete_payment %}
                                                {# Delete Payment - VERY Complex due to JE & Invoice status, usually "Void" instead #}
                                                {# <li><hr class="dropdown-divider"></li> #}
                                                {# <li> #}
                                                {#    <form action="#" method="post" class="d-inline">#}
                                                {#        {% csrf_token %}#}
                                                {#        <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Are you sure? Deleting payments can affect accounting records.');">#}
                                                {#            <i class="bi bi-trash3 me-2"></i>Delete/Void Payment#}
                                                {#        </button>#}
                                                {#    </form>#}
                                                {# </li> #}
                                            {% endif %}
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" %} {# Pass page_obj implicitly #}
            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    No payments found matching your criteria.
                    {% if perms.payments.add_payment and not request.GET.urlencode %}
                        <a href="{% url 'payments:record_payment' %}" class="alert-link">Record one now?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End card #}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a>
    </div>

</div> {# End container-fluid #}
{% endblock content %}



from django import template
from datetime import datetime, date

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """
    Template filter to lookup a value in a dictionary by key.
    Usage: {{ dict|lookup:key }}
    """
    if dictionary is None:
        return []
    
    # Handle date objects
    if isinstance(key, date):
        key = key.strftime('%Y-%m-%d')
    elif isinstance(key, datetime):
        key = key.strftime('%Y-%m-%d')
    
    # Convert date string to date object for lookup
    try:
        if isinstance(key, str) and len(key) == 10:  # YYYY-MM-DD format
            date_obj = datetime.strptime(key, '%Y-%m-%d').date()
            return dictionary.get(date_obj, [])
    except (ValueError, TypeError):
        pass
    
    return dictionary.get(key, [])

@register.filter
def get_item(dictionary, key):
    """
    Alternative lookup filter for dictionaries.
    Usage: {{ dict|get_item:key }}
    """
    return lookup(dictionary, key)

@register.simple_tag
def current_date():
    """
    Returns the current date.
    Usage: {% current_date %}
    """
    return date.today()

@register.filter
def format_date_key(value):
    """
    Formats a date for use as a dictionary key.
    Usage: {{ date|format_date_key }}
    """
    if isinstance(value, (date, datetime)):
        return value.strftime('%Y-%m-%d')
    return str(value)

@register.filter
def is_today(value):
    """
    Checks if a date is today.
    Usage: {{ date|is_today }}
    """
    today = date.today()
    
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            return False
    elif isinstance(value, datetime):
        value = value.date()
    
    return value == today if isinstance(value, date) else False

@register.filter
def is_weekend(value):
    """
    Checks if a date falls on a weekend (Saturday or Sunday).
    Usage: {{ date|is_weekend }}
    """
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            return False
    elif isinstance(value, datetime):
        value = value.date()
    
    if isinstance(value, date):
        return value.weekday() in [5, 6]  # Saturday = 5, Sunday = 6
    
    return False

@register.filter
def day_of_week(value):
    """
    Returns the day of week as a number (0=Monday, 6=Sunday).
    Usage: {{ date|day_of_week }}
    """
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            return None
    elif isinstance(value, datetime):
        value = value.date()
    
    if isinstance(value, date):
        return value.weekday()
    
    return None

@register.filter
def has_events(events_dict, date_key):
    """
    Checks if a date has any events.
    Usage: {{ events_by_date|has_events:date_key }}
    """
    events = lookup(events_dict, date_key)
    return len(events) > 0 if events else False


# your_app/templatetags/calendar_tags.py

# your_app/templatetags/calendar_tags.py

from django import template
import datetime

register = template.Library()

@register.simple_tag
def get_day_data(year, month, day, events_by_date, today):
    """
    Consolidates all logic for a calendar day into a single function.
    """
    if day == 0:
        return {'is_valid': False}

    try:
        current_date = datetime.date(year, month, day)
        date_key = current_date.strftime('%Y-%m-%d')
        events = events_by_date.get(date_key, [])
        
        return {
            'is_valid': True,
            'date': current_date,
            'day_number': day,
            'is_today': current_date == today,
            'is_weekend': current_date.weekday() in [5, 6],
            'events': events,
            'event_count': len(events), # <<< ADD THIS LINE
        }
    except (ValueError, TypeError):
        return {'is_valid': False}
    
    
# Keep your existing lookup filter if you use it elsewhere,
# otherwise the new tag replaces its need within the calendar.
@register.filter
def lookup(d, key):
    return d.get(key)



# Add this new filter
@register.filter
def split(value, arg):
    """
    Splits a string by the given argument.
    Usage: {{ "hello,world"|split:"," }}
    """
    if not isinstance(value, str):
        return value
    return value.split(arg)





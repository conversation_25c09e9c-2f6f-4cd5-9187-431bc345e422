{# D:\school_fees_saas_v2\apps\hr\templates\hr\salarycomponent_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:"Salary Components" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3">{{ view_title }}</h1>
        {% if perms.hr.add_salarycomponent %}
        <a href="{% url 'hr:salarycomponent_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Add New Component
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h6 class="m-0">Configured Earning & Deduction Types</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Component Name</th>
                            <th>Type</th>
                            <th class="text-center">Statutory</th>
                            <th class="text-center">Is Active</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for component in components %} {# 'components' is the context_object_name #}
                        <tr>
                            <td><strong>{{ component.name }}</strong></td>
                            <td>
                                {% if component.type == 'EARNING' %}
                                    <span class="badge bg-success">{{ component.get_type_display }}</span>
                                {% else %}
                                    <span class="badge bg-warning text-dark">{{ component.get_type_display }}</span>
                                {% endif %}
                            </td>
                            <td class="text-center">{% if component.is_statutory %}<i class="bi bi-check-lg text-primary"></i>{% endif %}</td>
                            <td class="text-center">
                                {% if component.is_active %}<i class="bi bi-check-circle-fill text-success"></i>{% else %}<i class="bi bi-x-circle-fill text-danger"></i>{% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'hr:salarycomponent_update' pk=component.pk %}" class="btn btn-sm btn-outline-primary" title="Edit"><i class="bi bi-pencil-fill"></i></a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center p-4">No salary components have been configured yet.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}



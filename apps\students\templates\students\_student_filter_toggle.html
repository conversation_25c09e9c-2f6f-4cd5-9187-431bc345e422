{# apps/students/templates/students/_student_filter_toggle.html - Student List Toggle Filter #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<style>
    /* Premium Student Filter Toggle Design */
    .premium-student-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-student-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-student-filter-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .premium-student-filter-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-student-filter-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .premium-student-filter-toggle {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .premium-student-filter-toggle:hover {
        color: white;
        text-decoration: underline;
    }

    .premium-student-filter-body {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .form-floating-student {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-floating-student .form-control,
    .form-floating-student .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem 0.25rem 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating-student .form-control:focus,
    .form-floating-student .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-floating-student > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating-student > .form-control:focus ~ label,
    .form-floating-student > .form-control:not(:placeholder-shown) ~ label,
    .form-floating-student > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #007bff;
    }

    .premium-student-field-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 5;
        pointer-events: none;
        transition: color 0.3s ease;
    }

    .form-floating-student .form-control:focus ~ .premium-student-field-icon,
    .form-floating-student .form-select:focus ~ .premium-student-field-icon {
        color: #007bff;
    }

    .btn-student-premium {
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        min-width: 140px;
    }

    .btn-student-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-student-premium:hover::before {
        left: 100%;
    }

    .btn-student-premium-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-student-premium-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        color: white;
    }

    .btn-student-premium-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-student-premium-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-export-student {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .btn-export-student:hover {
        background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        color: white;
    }

    .btn-import-student {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .btn-import-student:hover {
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .student-actions-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        border: 1px solid #dee2e6;
    }

    .export-import-buttons-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        z-index: 1;
    }

    @media (max-width: 768px) {
        .premium-student-filter-header {
            padding: 1rem;
        }

        .premium-student-filter-title {
            font-size: 1.1rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .premium-student-filter-body {
            padding: 1.5rem;
        }

        .btn-student-premium {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            min-width: 120px;
        }

        .export-import-buttons-container {
            flex-direction: column;
            align-items: stretch;
        }

        .btn-export-student,
        .btn-import-student {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    }
</style>

<div class="card premium-student-filter-card">
    <div class="premium-student-filter-header">
        <div class="premium-student-filter-title">
            <span>
                <i class="bi bi-people-fill me-2"></i>{% trans "Student Filters & Data Management" %}
            </span>
            <div class="d-flex align-items-center">
                <div class="export-import-buttons-container">
                    <a href="?export=csv" class="btn btn-export-student" title="{% trans 'Export to CSV' %}">
                        <i class="bi bi-file-earmark-spreadsheet me-1"></i>CSV
                    </a>
                    <a href="?export=excel" class="btn btn-export-student" title="{% trans 'Export to Excel' %}">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </a>
                    <a href="?export=pdf" class="btn btn-export-student" title="{% trans 'Export to PDF' %}" target="_blank">
                        <i class="bi bi-file-earmark-pdf me-1"></i>PDF
                    </a>
                    <button type="button" class="btn btn-import-student" data-bs-toggle="modal" data-bs-target="#importStudentsModal" title="{% trans 'Import from Excel' %}">
                        <i class="bi bi-upload me-1"></i>Import
                    </button>
                </div>
                <a class="premium-student-filter-toggle ms-3" data-bs-toggle="collapse" href="#studentFilterCollapse" role="button" aria-expanded="{% if request.GET and not request.GET.export %}true{% else %}false{% endif %}" aria-controls="studentFilterCollapse">
                    <i class="bi bi-chevron-down me-1"></i>{% trans "Toggle Filters" %}
                </a>
            </div>
        </div>
    </div>
    <div class="collapse {% if request.GET and not request.GET.export %}show{% endif %}" id="studentFilterCollapse">
        <div class="premium-student-filter-body">
            <form method="get" novalidate id="studentFilterForm">
                <div class="row g-4">
                    {# Loop through visible fields of the filter form #}
                    {% for field in filter_form.visible_fields %}
                        <div class="col-md-{% if filter_form.visible_fields|length > 4 %}3{% elif filter_form.visible_fields|length == 1 %}12{% elif filter_form.visible_fields|length == 2 %}6{% else %}4{% endif %}">
                            <div class="form-floating-student">
                                {% if field.field.widget.input_type == 'select' %}
                                    {% render_field field class+="form-select" placeholder=field.label %}
                                    <i class="premium-student-field-icon bi bi-chevron-down"></i>
                                {% elif field.field.widget.input_type == 'date' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-student-field-icon bi bi-calendar3"></i>
                                {% elif field.field.widget.input_type == 'number' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-student-field-icon bi bi-123"></i>
                                {% elif field.field.widget.input_type == 'text' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-student-field-icon bi bi-search"></i>
                                {% else %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-student-field-icon bi bi-people"></i>
                                {% endif %}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                                {% if field.help_text %}
                                    <small class="form-text text-muted mt-1">{{ field.help_text }}</small>
                                {% endif %}
                            </div>
                            {% for error in field.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>

                {# Action Buttons #}
                <div class="student-actions-container">
                    <div class="row g-3 justify-content-end">
                        <div class="col-auto">
                            <a href="{{ request.path }}" class="btn btn-student-premium btn-student-premium-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>{% trans "Reset Filters" %}
                            </a>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-student-premium btn-student-premium-primary" id="applyStudentFiltersBtn">
                                <i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            {% if filter_form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    {% for error in filter_form.non_field_errors %}{{ error }}{% if not forloop.last %}<br>{% endif %}{% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submission enhancement
    const form = document.getElementById('studentFilterForm');
    const submitBtn = document.getElementById('applyStudentFiltersBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Applying..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}';
            }, 5000);
        });
    }

    // Enhanced form field interactions
    const formControls = document.querySelectorAll('.form-floating-student .form-control, .form-floating-student .form-select');
    formControls.forEach(function(control) {
        // Focus enhancement
        control.addEventListener('focus', function() {
            this.closest('.form-floating-student').style.transform = 'translateY(-2px)';
            this.closest('.form-floating-student').style.boxShadow = '0 5px 15px rgba(0, 123, 255, 0.1)';
        });

        // Blur enhancement
        control.addEventListener('blur', function() {
            this.closest('.form-floating-student').style.transform = 'translateY(0)';
            this.closest('.form-floating-student').style.boxShadow = 'none';
        });
    });

    console.log('Student filter toggle initialized');
});
</script>

{% else %}
<div class="alert alert-info">
    <i class="bi bi-info-circle me-2"></i>{% trans "Filter functionality is not available." %}
</div>
{% endif %}

{# D:\school_fees_saas_v2\apps\schools\templates\schools\academic_year_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock tenant_page_title %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">{{ view_title }}</h1>
                    {% if object %}
                        <p class="text-muted mb-0">{% trans "Update the details for this academic year." %}</p>
                    {% else %}
                        <p class="text-muted mb-0">{% trans "Create a new academic year for your school." %}</p>
                    {% endif %}
                </div>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {# Display any non-field errors (e.g., from the clean() method) at the top #}
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-danger">{{ error }}</div>
                        {% endfor %}

                        {# --- Render form fields manually for better Bootstrap control --- #}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label required">{{ form.name.label }}</label>
                            {% render_field form.name class="form-control" %}
                            {% for error in form.name.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label required">{{ form.start_date.label }}</label>
                                {% render_field form.start_date class="form-control" %}
                                {% for error in form.start_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label required">{{ form.end_date.label }}</label>
                                {% render_field form.end_date class="form-control" %}
                                {% for error in form.end_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            {% render_field form.is_active class="form-check-input" role="switch" %}
                            <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                            <div class="form-text mt-1">{{ form.is_active.help_text }}</div>
                            {% for error in form.is_active.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <hr class="my-4">
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'schools:academic_year_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                {% if object %}
                                    {% trans "Update Academic Year" %}
                                {% else %}
                                    {% trans "Create Academic Year" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



{% extends "tenant_base.html" %}
{% load static %}

{% block tenant_page_title %}{{ view_title }}{% endblock tenant_page_title %}

{% block tenant_specific_content %}
<style>
    /* Premium Academic Year Form Design */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        max-width: 900px;
        margin: 2rem auto;
        position: relative;
    }

    .premium-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #20c997, #17a2b8, #6f42c1, #e83e8c, #fd7e14, #20c997);
        background-size: 300% 100%;
        animation: rainbow-border 3s ease-in-out infinite;
        z-index: 10;
    }

    @keyframes rainbow-border {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-header {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .premium-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shine 2s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .premium-header i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .premium-body {
        padding: 3rem 2rem;
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
    }

    .premium-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(32, 201, 151, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(23, 162, 184, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* Floating Labels */
    .form-floating {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem 0.75rem 0.5rem 0.75rem;
        background: #ffffff !important;
        background-color: #ffffff !important;
        background-image: none !important;
        transition: all 0.3s ease;
        font-size: 1rem;
        color: #333333;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #20c997;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
        transform: translateY(-2px);
        background: #ffffff !important;
        background-color: #ffffff !important;
        background-image: none !important;
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1.25rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #20c997;
        font-weight: 500;
        z-index: 2;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 1;
        transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
        color: #20c997;
        background: transparent;
        padding: 0;
        border-radius: 0;
    }

    .icon-input {
        color: #20c997;
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }

    /* Premium Buttons */
    .btn-premium {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
    }

    .btn-premium:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);
        color: white;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Form Switch Styling */
    .form-check-input:checked {
        background-color: #20c997;
        border-color: #20c997;
    }

    .form-check-input:focus {
        border-color: #20c997;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
    }

    /* Form validation */
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .premium-header {
            padding: 2rem 1rem;
        }

        .premium-body {
            padding: 2rem 1rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            height: calc(3rem + 2px);
            font-size: 0.9rem;
        }

        .form-floating > label {
            padding: 1.25rem 0.75rem;
            font-size: 0.9rem;
        }
    }
</style>

<div class="container-fluid">
    <div class="premium-card">
        <!-- Premium Header -->
        <div class="premium-header">
            <i class="fas fa-calendar-alt"></i>
            <h3 class="mb-2 fw-bold">{{ view_title|default:"Academic Year Management" }}</h3>
            <p class="mb-0 opacity-75">
                Create and manage academic years for your school's educational calendar and operations
            </p>
        </div>

        <!-- Form Body -->
        <div class="premium-body">
            {% include "partials/_messages.html" %}

            <form method="post" novalidate id="academicYearForm">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    {% for error in form.non_field_errors %}
                        <div class="alert alert-danger">{{ error }}</div>
                    {% endfor %}
                {% endif %}

                <!-- Academic Year Name -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.name }}
                            <label for="{{ form.name.id_for_label }}">
                                <i class="fas fa-graduation-cap icon-input"></i>{{ form.name.label }}
                            </label>
                        </div>
                        {% if form.name.help_text %}<div class="form-text text-muted" style="margin-top: -0.5rem; margin-bottom: 1rem;">{{ form.name.help_text }}</div>{% endif %}
                        {% if form.name.errors %}<div class="invalid-feedback d-block">{{ form.name.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <!-- Empty row for spacing -->
                <div class="row">
                    <div class="col-12">
                        <div style="height: 2rem;"></div>
                    </div>
                </div>

                <!-- Start Date -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.start_date }}
                            <label for="{{ form.start_date.id_for_label }}">
                                <i class="fas fa-calendar-plus icon-input"></i>{{ form.start_date.label }}
                            </label>
                        </div>
                        {% if form.start_date.help_text %}<div class="form-text text-muted" style="margin-top: -0.5rem; margin-bottom: 1rem;">{{ form.start_date.help_text }}</div>{% endif %}
                        {% if form.start_date.errors %}<div class="invalid-feedback d-block">{{ form.start_date.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <!-- Empty row for spacing -->
                <div class="row">
                    <div class="col-12">
                        <div style="height: 2rem;"></div>
                    </div>
                </div>

                <!-- End Date -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.end_date }}
                            <label for="{{ form.end_date.id_for_label }}">
                                <i class="fas fa-calendar-minus icon-input"></i>{{ form.end_date.label }}
                            </label>
                        </div>
                        {% if form.end_date.help_text %}<div class="form-text text-muted" style="margin-top: -0.5rem; margin-bottom: 1rem;">{{ form.end_date.help_text }}</div>{% endif %}
                        {% if form.end_date.errors %}<div class="invalid-feedback d-block">{{ form.end_date.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <!-- Empty row for spacing -->
                <div class="row">
                    <div class="col-12">
                        <div style="height: 2rem;"></div>
                    </div>
                </div>

                <!-- Is Active Toggle -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-center">
                        <div class="form-check form-switch">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                <i class="fas fa-toggle-on icon-input"></i>{{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.help_text %}<div class="form-text text-muted text-center mt-2">{{ form.is_active.help_text }}</div>{% endif %}
                        {% if form.is_active.errors %}<div class="invalid-feedback d-block">{{ form.is_active.errors|striptags }}</div>{% endif %}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-center gap-3 mt-5 pt-4 border-top">
                    <button type="submit" class="btn-premium">
                        <i class="fas fa-save me-2"></i>{% if object %}Update{% else %}Save{% endif %} Academic Year
                    </button>
                    <a href="{% url 'schools:academic_year_list' %}" class="btn-secondary-premium">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission handling
        const form = document.getElementById('academicYearForm');
        const submitButton = form.querySelector('button[type="submit"]');

        form.addEventListener('submit', function(e) {
            // Prevent double submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitButton.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>{% if object %}Update{% else %}Save{% endif %} Academic Year';
                submitButton.style.background = 'linear-gradient(135deg, #20c997 0%, #17a2b8 100%)';
            }, 10000);
        });

        // Date validation
        const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
        const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');

        if (startDateField && endDateField) {
            startDateField.addEventListener('change', function() {
                if (endDateField.value && this.value > endDateField.value) {
                    alert('Start date cannot be after end date.');
                    this.value = '';
                }
            });

            endDateField.addEventListener('change', function() {
                if (startDateField.value && this.value < startDateField.value) {
                    alert('End date cannot be before start date.');
                    this.value = '';
                }
            });
        }
    });
</script>
{% endblock %}




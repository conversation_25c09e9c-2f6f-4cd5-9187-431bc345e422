{# D:\school_fees_saas_v2\templates\fees\fee_structure_list.html #}
{% extends "tenant_base.html" %}

{% load static humanize core_tags %} {# humanize for intcomma, core_tags for active_nav_link if used #}

{% block title %}
    {{ view_title|default:"Manage Fee Structures" }} - {{ request.tenant.name }}
{% endblock %}

{% block content %}
<div class="container-fluid mt-4"> {# Use container-fluid for more width if table is wide #}
    <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
        <h1 class="h2">{{ view_title|default:"Manage Fee Structures" }}</h1>
        {# Check permission before showing add button #}
        {% if perms.fees.add_feestructure %}
            <a href="{% url 'fees:fee_structure_create' %}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> Add New Fee Structure
            </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %} {# Include your messages partial #}

    {# Optional: Add Filter Form here later if needed #}

    <div class="card shadow-sm">
        <div class="card-body p-0"> {# Remove padding for full-width table in card #}
            {% if fee_structures %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Name / Description</th>
                                <th>Academic Year</th>
                                <th>Class</th>
                                <th>Term</th>
                                <th class="text-end">Total Amount</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for structure in fee_structures %}
                            <tr>
                                <td>
                                    <strong>{{ structure.name }}</strong>
                                    {% if structure.description %}
                                        <small class.text-muted d-block>{{ structure.description|truncatewords:15 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ structure.academic_year.name }}</td>
                                <td>{{ structure.school_class.name }}</td>
                                <td>{{ structure.term.name|default:"N/A (Annual)" }}</td>
                                <td class="text-end">{{ structure.total_amount|default:"0.00"|intcomma }}</td>
                                <td class="text-center">
                                    {% if structure.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {# View/Detail Link (optional if you have a detail page) #}
                                    {# <a href="{% url 'fees:fee_structure_detail' structure.pk %}" class="btn btn-info btn-sm me-1" title="View Details"><i class="bi bi-eye"></i></a> #}

                                    {% if perms.fees.change_feestructure %}
                                    <a href="{% url 'fees:fee_structure_update' structure.pk %}" class="btn btn-outline-primary btn-sm me-1" title="Edit Structure">
                                        <i class="bi bi-pencil-square"></i> Edit
                                    </a>
                                    {% endif %}

                                    {% if perms.fees.delete_feestructure %}
                                    <a href="{% url 'fees:fee_structure_delete' structure.pk %}" class="btn btn-outline-danger btn-sm" title="Delete Structure">
                                        <i class="bi bi-trash"></i> Delete
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info m-3">
                    No fee structures have been created yet.
                    {% if perms.fees.add_feestructure %}
                        <a href="{% url 'fees:fee_structure_create' %}" class="alert-link">Create one now?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End card #}

    {# Pagination (if you set paginate_by in ListView) #}
    {% if is_paginated %}
        <div class="mt-3">
            {% include "partials/_pagination.html" %} {# Assuming you have a pagination partial #}
        </div>
    {% endif %}

    <div class="footer-actions mt-4">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left-circle"></i> Back to Dashboard
        </a>
    </div>

</div> {# End container-fluid #}
{% endblock content %}









{% comment %} {# D:\school_fees_saas_v2\templates\fees\fee_structure_list.html #}
{% extends "tenant_base.html" %}

{% load static humanize %}

{% block title %}{{ view_title }} - {{ request.tenant.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" %}
    
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ view_title }}</h4>
            {% if perms.fees.add_feestructure %}
                <a href="{% url 'fees:fee_structure_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle-fill me-1"></i> Add New Structure
                </a>
            {% endif %}
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            {% if fee_structures %}
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Name</th>
                                <th scope="col">Academic Year</th>
                                <th scope="col">Term</th>
                                <th scope="col">Class</th>
                                <th scope="col" class="text-end">Total Amount</th>
                                <th scope="col" class="text-center">Status</th>
                                <th scope="col" class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for structure in fee_structures %}
                            <tr>
                                <td>
                                    <strong>{{ structure.name }}</strong>
                                    {% if structure.description %}
                                        <small class="d-block text-muted">{{ structure.description|truncatewords:15 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ structure.academic_year.name }}</td>
                                <td>{{ structure.term.name|default:"-" }}</td>
                                <td>{{ structure.school_class.name }}</td>
                                <td class="text-end fw-bold">{{ structure.total_amount|default:"0.00"|intcomma }}</td>
                                <td class="text-center">
                                    {% if structure.is_active %}
                                        <span class="badge bg-success-soft text-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary-soft text-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        {% if perms.fees.change_feestructure %}
                                        <a href="{% url 'fees:fee_structure_update' structure.pk %}" class="btn btn-outline-primary" title="Edit {{ structure.name }}">
                                            <i class="bi bi-pencil-square"></i>
                                        </a>
                                        {% endif %}
                                        {% if perms.fees.delete_feestructure %}
                                        <a href="{% url 'fees:fee_structure_delete' structure.pk %}" class="btn btn-outline-danger" title="Delete {{ structure.name }}">
                                            <i class="bi bi-trash3"></i>
                                        </a>
                                        {% endif %}
                                        {# Add view detail button if you have a detail view #}
                                        {# <a href="#" class="btn btn-outline-info" title="View Details"><i class="bi bi-eye"></i></a> #}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" with page_obj=page_obj %}
            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    No fee structures have been configured yet.
                    {% if perms.fees.add_feestructure %}
                        <a href="{% url 'fees:fee_structure_create' %}" class="alert-link">Get started by adding one!</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_css %}
{# Custom soft badges - add to your custom_tenant_styles.css or similar #}
<style>
.badge.bg-success-soft { background-color: rgba(25, 135, 84, 0.15); }
.badge.bg-secondary-soft { background-color: rgba(108, 117, 125, 0.15); }
/* Add more soft badges as needed */
</style>
{% endblock %} {% endcomment %}


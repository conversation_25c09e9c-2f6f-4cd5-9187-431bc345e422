# D:\school_fees_saas_v2\apps\tenants\utils.py
import re
from django.utils.text import slugify
from django_tenants.utils import get_tenant_model # To check for existing schemas

def generate_safe_schema_name(name, max_length=63):
    """
    Generates a database-safe schema name from a given string.
    Ensures it's lowercase, uses underscores, and is within PostgreSQL's length limits.
    It also attempts to ensure uniqueness by appending a number if a conflict occurs.
    """
    if not name:
        return None # Or raise ValueError

    # Basic slugification (lowercase, hyphens for spaces, remove special chars)
    schema_name = slugify(name)
    
    # Replace hyphens with underscores (PostgreSQL prefers underscores for schema names)
    schema_name = schema_name.replace('-', '_')
    
    # Remove any characters not suitable for schema names (alphanumeric and underscore)
    # This regex keeps a-z, 0-9, and _
    schema_name = re.sub(r'[^a-z0-9_]', '', schema_name)
    
    # Ensure it doesn't start with a number or underscore (some DBs have restrictions)
    if schema_name and (schema_name[0].isdigit() or schema_name[0] == '_'):
        schema_name = 's_' + schema_name # Prepend a letter

    # Truncate to max_length (PostgreSQL default is 63 for identifiers)
    # We need to account for potential suffixes like "_1", "_2" if checking for uniqueness
    base_max_length = max_length - 5 # Reserve space for suffixes like "_9999"
    if len(schema_name) > base_max_length:
        schema_name = schema_name[:base_max_length]

    # Remove trailing underscores that might result from truncation
    schema_name = schema_name.rstrip('_')

    if not schema_name: # If all characters were stripped or name was too short
        # Fallback to a generic prefix or raise an error
        # This is unlikely if the input 'name' is reasonable.
        import uuid
        schema_name = 'tenant_' + str(uuid.uuid4())[:8]
        if len(schema_name) > max_length: # Should not happen with this uuid format
            schema_name = schema_name[:max_length]
        return schema_name.lower() # Ensure lowercase for the fallback too

    # Check for uniqueness (optional, but good practice if schemas must be unique)
    # This requires access to your Tenant model (e.g., School)
    TenantModel = get_tenant_model() # Usually School model from tenants.models
    
    original_schema_name = schema_name
    counter = 1
    while TenantModel.objects.filter(schema_name=schema_name).exists():
        suffix = f"_{counter}"
        # Ensure the name + suffix doesn't exceed max_length
        if len(original_schema_name) + len(suffix) > max_length:
            # Truncate original_schema_name further to make space for the suffix
            allowed_original_len = max_length - len(suffix)
            schema_name_candidate = original_schema_name[:allowed_original_len] + suffix
        else:
            schema_name_candidate = original_schema_name + suffix
        
        schema_name = schema_name_candidate
        counter += 1
        if counter > 1000: # Safety break to prevent infinite loops
            raise ValueError(f"Could not generate a unique schema name for '{name}' after 1000 attempts.")
            
    return schema_name.lower() # Ensure lowercase

# Example usage (you can test this function independently)
if __name__ == '__main__':
    print(generate_safe_schema_name("My Awesome School!"))
    print(generate_safe_schema_name("123 School"))
    print(generate_safe_schema_name("School @ Name"))
    print(generate_safe_schema_name("A very long school name that will definitely exceed the maximum length allowed"))
    # For uniqueness test, you'd need Django environment setup
    # print(generate_safe_schema_name("alpha")) # If 'alpha' already exists
    
    
    
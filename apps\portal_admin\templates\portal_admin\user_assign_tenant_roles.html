{# apps/portal_admin/templates/portal_admin/user_assign_tenant_roles.html - Premium Design Version #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block title %}{{ view_title }}{% endblock %}

{% block page_specific_css %}
{{ block.super }}
<style>
    /* Premium Form Design - User Assign Tenant Roles */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-card-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-card-header h3 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
    }

    .premium-card-body {
        padding: 2.5rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .breadcrumb-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: transparent;
    }

    .breadcrumb-item a {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .info-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #2196f3;
    }

    .info-section h5 {
        color: #1976d2;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .info-section p {
        color: #424242;
        margin: 0;
        line-height: 1.6;
    }

    .user-info-card {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #ffc107;
        text-align: center;
    }

    .user-info-card h5 {
        color: #856404;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .user-info-card .user-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #495057;
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        display: inline-block;
        margin-top: 0.5rem;
    }

    .roles-fieldset {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .roles-fieldset:hover {
        border-color: #007bff;
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
    }

    .roles-fieldset legend {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        border: none;
        width: auto;
        float: none;
    }

    .role-item {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid #e9ecef;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .role-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .role-item:hover {
        border-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.15);
    }

    .role-item:hover::before {
        left: 100%;
    }

    .role-item.checked {
        border-color: #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
    }

    .role-item.checked::after {
        content: '✓';
        position: absolute;
        top: 0.5rem;
        right: 0.75rem;
        color: #28a745;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .role-checkbox {
        margin-right: 0.75rem;
        transform: scale(1.3);
        accent-color: #007bff;
    }

    .role-checkbox:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .role-label {
        font-size: 1rem;
        color: #495057;
        font-weight: 500;
        line-height: 1.4;
        cursor: pointer;
        flex: 1;
        transition: color 0.3s ease;
    }

    .role-item:hover .role-label {
        color: #007bff;
    }

    .role-checkbox:checked + .role-label {
        color: #28a745;
        font-weight: 600;
    }

    .btn {
        border-radius: 0.75rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    .selection-summary {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
        text-align: center;
    }

    .selection-summary h6 {
        color: #155724;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .selection-counter {
        font-size: 1.5rem;
        font-weight: 700;
        color: #28a745;
    }

    .form-text {
        background: rgba(0, 123, 255, 0.1);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin-top: 1rem;
        color: #0056b3;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .premium-card-header {
            padding: 1.5rem;
        }

        .premium-card-header h3 {
            font-size: 1.5rem;
        }

        .premium-card-body {
            padding: 1.5rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
        }

        .role-item {
            padding: 0.75rem;
        }
    }

    @media (max-width: 576px) {
        .roles-fieldset {
            padding: 1rem;
        }

        .role-item {
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb-container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'schools:staff_detail' pk=target_user.pk %}">
                                <i class="bi bi-person-fill me-1"></i>{% trans "Staff Details" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            {% trans "Assign Roles" %}
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h5><i class="bi bi-shield-check me-2"></i>{% trans "Role Assignment" %}</h5>
                <p>
                    {% trans "Assign specific roles to this staff member to control their access and permissions within the system. Multiple roles can be assigned to provide comprehensive access control." %}
                </p>
            </div>

            <!-- User Information Card -->
            <div class="user-info-card">
                <h5><i class="bi bi-person-badge me-2"></i>{% trans "Assigning Roles for Staff Member" %}</h5>
                <div class="user-name">{{ target_user.full_name|default:target_user.email }}</div>
            </div>

            <!-- Premium Form Card -->
            <div class="card premium-card">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-people-fill me-2"></i>
                        {{ view_title }}
                    </h3>
                </div>
                <div class="premium-card-body">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="roleAssignmentForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <!-- Selection Summary -->
                        <div class="selection-summary" id="selectionSummary">
                            <h6><i class="bi bi-check-circle me-2"></i>{% trans "Selected Roles" %}</h6>
                            <div class="selection-counter" id="selectionCounter">0</div>
                            <small class="text-muted">{% trans "roles selected" %}</small>
                        </div>

                        <!-- Roles Fieldset -->
                        <fieldset class="roles-fieldset">
                            <legend>
                                <i class="bi bi-shield-lock me-2"></i>{{ form.tenant_roles.label|default:"Available Roles" }}
                            </legend>

                            {% for choice_widget in form.tenant_roles %}
                                <div class="role-item" data-role-id="{{ choice_widget.data.value }}">
                                    <div class="d-flex align-items-center">
                                        <input
                                            type="checkbox"
                                            class="role-checkbox"
                                            name="tenant_roles"
                                            value="{{ choice_widget.data.value }}"
                                            id="{{ choice_widget.id_for_label }}"
                                            {% if choice_widget.data.selected %}checked{% endif %}
                                        >
                                        <label class="role-label" for="{{ choice_widget.id_for_label }}">
                                            <i class="bi bi-person-gear me-2"></i>{{ choice_widget.choice_label }}
                                        </label>
                                    </div>
                                </div>
                            {% empty %}
                                <div class="text-center py-4">
                                    <i class="bi bi-info-circle text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2">{% trans "No roles available for assignment." %}</p>
                                </div>
                            {% endfor %}

                            {% if form.tenant_roles.help_text %}
                                <div class="form-text">
                                    <i class="bi bi-lightbulb me-1"></i>{{ form.tenant_roles.help_text }}
                                </div>
                            {% endif %}

                            {% if form.tenant_roles.errors %}
                                <div class="invalid-feedback d-block">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    {{ form.tenant_roles.errors|join:", " }}
                                </div>
                            {% endif %}
                        </fieldset>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'schools:staff_detail' pk=target_user.pk %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-shield-check me-2"></i>{% trans "Save Assigned Roles" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Role selection counter
    const checkboxes = document.querySelectorAll('.role-checkbox');
    const selectionCounter = document.getElementById('selectionCounter');
    const selectionSummary = document.getElementById('selectionSummary');

    function updateSelectionCounter() {
        const checkedCount = document.querySelectorAll('.role-checkbox:checked').length;
        selectionCounter.textContent = checkedCount;

        // Update summary card appearance based on selection
        if (checkedCount > 0) {
            selectionSummary.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            selectionSummary.style.borderLeftColor = '#28a745';
        } else {
            selectionSummary.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
            selectionSummary.style.borderLeftColor = '#6c757d';
        }
    }

    // Add event listeners to all checkboxes
    checkboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelectionCounter);
    });

    // Initial count
    updateSelectionCounter();

    // Form submission enhancement
    const form = document.getElementById('roleAssignmentForm');
    const submitBtn = document.getElementById('submitBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Saving..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-shield-check me-2"></i>{% trans "Save Assigned Roles" %}';
            }, 5000);
        });
    }

    // Enhanced role item interactions
    const roleItems = document.querySelectorAll('.role-item');
    roleItems.forEach(function(item) {
        const checkbox = item.querySelector('.role-checkbox');
        const label = item.querySelector('.role-label');

        // Click anywhere on the item to toggle checkbox
        item.addEventListener('click', function(e) {
            if (e.target !== checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Visual feedback for checked state
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                item.classList.add('checked');
            } else {
                item.classList.remove('checked');
            }
        });

        // Initial state
        if (checkbox.checked) {
            item.classList.add('checked');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+A to select all roles
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            const allCheckboxes = document.querySelectorAll('.role-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            allCheckboxes.forEach(function(cb) {
                cb.checked = !allChecked;
                cb.dispatchEvent(new Event('change'));
            });
        }

        // Escape to cancel
        if (e.key === 'Escape') {
            const cancelBtn = document.querySelector('a[href*="staff_detail"]');
            if (cancelBtn) {
                cancelBtn.click();
            }
        }
    });

    console.log('Role assignment form initialized with', checkboxes.length, 'roles');
});
</script>
{% endblock %}








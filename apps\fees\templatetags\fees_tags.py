# Example: apps/fees/templatetags/fees_tags.py
from django import template
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
# Assuming your InvoiceStatus enum/choices are accessible, e.g., from fees.models
# from ..models import InvoiceStatus # If InvoiceStatus is defined in fees.models

register = template.Library()

@register.simple_tag
def invoice_status_badge(status_value, plain_text=False): # <<< ADD plain_text=False HERE
    # Define your status mappings if not using get_FOO_display() directly
    # This is just an example, adapt to your actual InvoiceStatus choices/values
    
    status_map = {
        'DRAFT': {'label': _("Draft"), 'css_class': 'bg-secondary text-light'},
        'SENT': {'label': _("Sent"), 'css_class': 'bg-info text-dark'},
        'PARTIALLY_PAID': {'label': _("Partially Paid"), 'css_class': 'bg-warning text-dark'},
        'PAID': {'label': _("Paid"), 'css_class': 'bg-success text-light'},
        'OVERDUE': {'label': _("Overdue"), 'css_class': 'bg-danger text-light'},
        'VOID': {'label': _("Void"), 'css_class': 'bg-dark text-light'},
        'CANCELLED': {'label': _("Cancelled"), 'css_class': 'bg-dark text-light'},
        # Add all your InvoiceStatus choices
    }

    default_display = {'label': _(status_value.replace('_', ' ').title()), 'css_class': 'bg-light text-dark'}
    status_info = status_map.get(str(status_value).upper(), default_display)
    
    display_name = status_info['label']
    css_class = status_info['css_class']

    if plain_text:
        return str(display_name) # Return plain string for PDF
    else:
        # Original HTML badge for web display
        return format_html('<span class="badge {} status-badge">{}</span>', 
                            css_class, 
                            display_name)



# # apps/fees/templatetags/fees_tags.py
# from django import template
# from django.utils.translation import gettext_lazy as _
# from apps.fees.models import Invoice, InvoiceStatus # Import Invoice and InvoiceStatus

# register = template.Library()

# @register.inclusion_tag('fees/partials/_status_badge.html') # Path to your snippet
# def invoice_status_badge(invoice_status_value): # invoice_status_value is 'DRAFT', 'SENT', etc.
#     STATUS_DISPLAY_MAP = {
#         InvoiceStatus.DRAFT:           {'class': 'secondary',    'text': _('Draft')},
#         InvoiceStatus.SENT:            {'class': 'info',         'text': _('Sent')},
#         InvoiceStatus.PARTIALLY_PAID:  {'class': 'primary',      'text': _('Partially Paid')},
#         InvoiceStatus.PAID:            {'class': 'success',      'text': _('Paid')},
#         InvoiceStatus.OVERDUE:         {'class': 'warning',      'text': _('Overdue')},
#         InvoiceStatus.CANCELLED:       {'class': 'danger',       'text': _('Cancelled')},
#         InvoiceStatus.VOID:            {'class': 'dark',         'text': _('Void')},
#     }
    
#     display_info = STATUS_DISPLAY_MAP.get(str(invoice_status_value))

#     if display_info:
#         return {"badge_class": display_info['class'], "badge_text": display_info['text']}
#     else:
#         return {"badge_class": "light text-dark", "badge_text": str(invoice_status_value).title()}
    
    
    




# @register.simple_tag # Or @register.inclusion_tag if it renders a snippet
# def invoice_status_badge(invoice_status_value): # invoice_status_value is the actual DB status string e.g. 'PARTIAL'
#     # Define default badge appearance
#     default_badge = ("secondary", str(invoice_status_value).title()) # Default if status not in map

#     # Map status DB values to badge class and display text
#     # The keys here should be the ACTUAL DATABASE VALUES for the status
#     status_map = {
#         Invoice.STATUS_DRAFT: ("secondary", _("Draft")),
#         Invoice.STATUS_PENDING: ("warning text-dark", _("Pending")),
#         Invoice.STATUS_SENT: ("primary", _("Sent")),
#         Invoice.STATUS_PAID: ("success", _("Paid")),
#         Invoice.STATUS_PARTIALLY_PAID: ("info text-dark", _("Partially Paid")), # <<< CORRECTED CONSTANT
#         Invoice.STATUS_OVERDUE: ("danger", _("Overdue")),
#         Invoice.STATUS_CANCELLED: ("dark", _("Cancelled")),
#         Invoice.STATUS_VOID: ("light text-dark", _("Void")),
#     }
    
#     badge_class, display_text = status_map.get(str(invoice_status_value).upper(), default_badge) # Use .upper() if DB values are uppercase

#     # Ensure you return HTML or use an inclusion tag to render a snippet
#     # For a simple_tag returning HTML, you'd use format_html
#     from django.utils.html import format_html
#     return format_html('<span class="badge bg-{}">{}</span>', badge_class, display_text)








# # D:\school_fees_saas_v2\apps\fees\templatetags\fees_tags.py
# from django import template
# from django.utils.html import format_html
# # Import Invoice model correctly (assuming it's in fees/models.py)
# from ..models import Invoice # Relative import from parent models.py
# from django.utils.translation import gettext_lazy as _

# register = template.Library()

# @register.simple_tag
# def invoice_status_badge(status_code):
#     status_map = {
#         Invoice.STATUS_DRAFT: ("secondary", "Draft"),
#         Invoice.STATUS_PENDING: ("warning text-dark", "Pending"), # Added text-dark for better contrast on warning
#         Invoice.STATUS_PAID: ("success", "Paid"),
#         Invoice.STATUS_PARTIALLY_PAID: ("info text-dark", _("Partially Paid")), # Added text-dark
#         Invoice.STATUS_OVERDUE: ("danger", "Overdue"),
#         Invoice.STATUS_CANCELLED: ("dark", "Cancelled"),
#         Invoice.STATUS_VOID: ("light text-dark border", "Void"), # Added border for light
#     }
#     # Default if status_code not in map
#     default_color = "light text-dark border"
#     default_text = status_code.replace('_', ' ').title() if status_code else "Unknown"
    
#     color, text = status_map.get(status_code, (default_color, default_text))
    
#     return format_html('<span class="badge bg-{0} status-badge">{1}</span>', color, text)

# # Add other custom tags for the 'fees' app here if needed

    
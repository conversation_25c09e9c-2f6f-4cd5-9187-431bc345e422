{% extends "tenant_base.html" %}
{% load static %}
{% block title %}Confirm Cancel Leave Request{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>Confirm Cancellation</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:staff_leaverequest_list' %}">My Leave Requests</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:staff_leaverequest_detail' object.pk %}">Request #{{ object.pk }}</a></li>
                <li class="breadcrumb-item active">Confirm Cancel</li>
            </ol>
        </nav>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title text-warning">Cancel Leave Request: {{ object.leave_type.name }} ({{ object.start_date|date:"d M Y" }} - {{ object.end_date|date:"d M Y" }})</h5>
            
            {% if not can_cancel %}
                <div class="alert alert-danger">This leave request cannot be cancelled because its status is no longer 'Pending'.</div>
                <a href="{% url 'hr:staff_leaverequest_detail' object.pk %}" class="btn btn-secondary">Back to Details</a>
            {% else %}
                <p>Are you sure you want to cancel this leave request?</p>
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning">Yes, Cancel Request</button>
                    <a href="{% url 'hr:staff_leaverequest_detail' object.pk %}" class="btn btn-secondary">No, Keep It</a>
                </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}






#!/usr/bin/env python
"""
Simple fix for migration dependency issue
"""
import os
import sys
import django
import psycopg2
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def fix_migration_issue():
    """Fix the migration issue by updating the database directly"""
    print("🔧 Fixing migration dependency issue...")
    
    # Get database connection details
    db_config = settings.DATABASES['default']
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_config['HOST'],
        port=db_config['PORT'],
        database=db_config['NAME'],
        user=db_config['USER'],
        password=db_config['PASSWORD']
    )
    
    try:
        # Get all tenant schemas
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name NOT IN ('public', 'information_schema') 
                AND schema_name NOT LIKE 'pg_%'
            """)
            schemas = [row[0] for row in cursor.fetchall()]
            
        print(f"Found schemas: {schemas}")
        
        for schema in schemas:
            if schema == 'public':
                continue
                
            print(f"\n--- Processing schema: {schema} ---")
            
            with conn.cursor() as cursor:
                # Set search path to the tenant schema
                cursor.execute(f"SET search_path TO {schema}")
                
                # Check if django_migrations table exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = %s 
                        AND table_name = 'django_migrations'
                    )
                """, (schema,))
                
                if not cursor.fetchone()[0]:
                    print(f"  ⚠️  No django_migrations table in {schema}")
                    continue
                
                # Check current migration state
                cursor.execute("""
                    SELECT app, name, applied
                    FROM django_migrations
                    WHERE (app = 'fees' AND name = '0003_initial') 
                       OR (app = 'payments' AND name = '0003_initial')
                    ORDER BY applied;
                """)
                results = cursor.fetchall()
                
                if len(results) < 2:
                    print(f"  ⚠️  Not all migrations found in {schema}")
                    continue
                    
                print(f"  Current migration order:")
                for row in results:
                    print(f"    {row[0]}.{row[1]} - {row[2]}")
                
                # Check if payments.0003_initial comes before fees.0003_initial
                payments_time = None
                fees_time = None
                
                for app, name, applied in results:
                    if app == 'payments' and name == '0003_initial':
                        payments_time = applied
                    elif app == 'fees' and name == '0003_initial':
                        fees_time = applied
                
                if payments_time and fees_time and payments_time < fees_time:
                    print(f"  ❌ Issue found: payments.0003_initial applied before fees.0003_initial")
                    
                    # Update fees.0003_initial to have an earlier timestamp
                    cursor.execute("""
                        UPDATE django_migrations 
                        SET applied = %s - INTERVAL '1 minute'
                        WHERE app = 'fees' AND name = '0003_initial'
                    """, (payments_time,))
                    
                    conn.commit()
                    print(f"  ✅ Fixed: Updated fees.0003_initial timestamp to be earlier")
                    
                    # Verify the fix
                    cursor.execute("""
                        SELECT app, name, applied
                        FROM django_migrations
                        WHERE (app = 'fees' AND name = '0003_initial') 
                           OR (app = 'payments' AND name = '0003_initial')
                        ORDER BY applied;
                    """)
                    results = cursor.fetchall()
                    
                    print(f"  Updated migration order:")
                    for row in results:
                        print(f"    {row[0]}.{row[1]} - {row[2]}")
                else:
                    print(f"  ✅ Migration order is correct")
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    try:
        fix_migration_issue()
        print("\n🎉 Migration dependency fix completed!")
        print("\nNow you can run: python manage.py migrate_schemas")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

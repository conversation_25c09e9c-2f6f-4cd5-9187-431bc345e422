{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\payment_success.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:_("Subscription Confirmed") }}{% endblock %}

{% block tenant_specific_content %}
<div class="pagetitle">
    <h1>{{ view_title }}</h1>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card text-center shadow-sm border-success">
                <div class="card-body py-5">
                    <i class="bi bi-check-circle-fill text-success display-1 mb-3"></i>
                    <h4 class="card-title">
                        {% if subscription.status == 'TRIALING' %}
                            {% trans "Your Free Trial Has Started!" %}
                        {% else %}
                            {% trans "Subscription Activated Successfully!" %}
                        {% endif %}
                    </h4>
                    <p class="card-text">
                        {% if subscription.status == 'TRIALING' %}
                            {% blocktrans with plan_name=subscription.plan.name trial_end=subscription.trial_end_date|date:"D, d M Y" %}
                            You are now on a free trial of the <strong>{{ plan_name }}</strong> plan, valid until {{ trial_end }}.
                            Explore all the features and see how our platform can help your school.
                            {% endblocktrans %}
                        {% else %}
                            {% blocktrans with plan_name=subscription.plan.name %}
                            Your school is now subscribed to the <strong>{{ plan_name }}</strong> plan.
                            {% endblocktrans %}
                        {% endif %}
                    </p>
                    <p>{% trans "You can manage your subscription details from your dashboard." %}</p>
                    <div class="mt-4">
                        <a href="{% url 'schools:dashboard' %}" class="btn btn-primary mx-2">{% trans "Go to My Dashboard" %}</a>
                        <a href="{% url 'subscriptions:subscription_details' %}" class="btn btn-outline-secondary mx-2">{% trans "View Subscription Details" %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock tenant_specific_content %}



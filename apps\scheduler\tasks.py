# apps/fees/tasks.py (or a dedicated scheduler app)
from celery import shared_task
from django.utils import timezone
from django.db.models import Sum, F, DecimalField, Q
from django.db.models.functions import Coalesce
from decimal import Decimal
from datetime import timedelta

from apps.tenants.models import School
from apps.students.models import ParentUser, Student
from apps.fees.models import Invoice
from apps.communication.tasks import send_fee_due_reminder_email_task # Import your email task
from apps.schools.models import SchoolProfile # For school specific settings
from django_tenants.utils import schema_context, get_tenant_model

import logging
logger = logging.getLogger(__name__)

@shared_task(name="schedule_fee_due_reminders")
def schedule_fee_due_reminders():
    logger.info("Starting scheduled task: schedule_fee_due_reminders")
    # Iterate over all active tenants
    TenantModel = get_tenant_model() # Should be School
    for tenant in TenantModel.objects.filter(is_active=True): # Or however you mark active schools
        logger.info(f"Processing fee reminders for tenant: {tenant.name} (Schema: {tenant.schema_name})")
        with schema_context(tenant.schema_name):
            try:
                # Check if tenant has parent portal enabled and reminder settings
                school_profile = SchoolProfile.objects.first() # Assuming one profile per tenant
                if not school_profile or not getattr(school_profile, 'send_fee_reminders', False): # Add send_fee_reminders to SchoolProfile
                    logger.info(f"Fee reminders disabled for {tenant.name} via SchoolProfile.")
                    continue

                reminder_days_before_due = getattr(school_profile, 'reminder_days_before_due', 3) # e.g., 3 days before
                # also consider reminder for overdue invoices

                now = timezone.now().date()
                upcoming_due_date_threshold = now + timedelta(days=reminder_days_before_due)
                
                # Find parents with invoices due soon or overdue
                # This query can be complex and needs optimization
                
                # Get all parents in this tenant
                parents_to_notify = ParentUser.objects.filter(is_active=True, receive_notifications=True) # Ensure ParentUser has receive_notifications field

                for parent in parents_to_notify:
                    student_fee_data_list = []
                    overall_parent_total_due = Decimal('0.00')
                    
                    # Assuming ParentUser has a M2M to Student named 'children'
                    linked_active_students = parent.children.filter(is_active=True)
                    if not linked_active_students.exists():
                        continue

                    for student in linked_active_students:
                        # Invoices due soon for this student
                        # Invoices overdue for this student
                        outstanding_statuses = [
                            Invoice.InvoiceStatus.SENT,
                            Invoice.InvoiceStatus.PARTIALLY_PAID,
                            Invoice.InvoiceStatus.OVERDUE
                        ]
                        
                        # Query for invoices that are:
                        # 1. Overdue (due_date < now)
                        # 2. Due within the reminder threshold (now <= due_date <= upcoming_due_date_threshold)
                        student_invoices_to_remind = Invoice.objects.filter(
                            student=student,
                            status__in=outstanding_statuses,
                            # Optionally add a filter to avoid sending too many reminders for same invoice
                            # e.g., last_reminder_sent_at__lte=now - timedelta(days=X)
                        ).filter(
                            Q(due_date__lt=now) | # Overdue
                            Q(due_date__gte=now, due_date__lte=upcoming_due_date_threshold) # Due soon
                        ).annotate(
                            balance_due_calc=Coalesce(F('total_amount') - F('amount_paid'), Decimal('0.00'), output_field=DecimalField())
                        ).filter(balance_due_calc__gt=Decimal('0.00'))

                        if student_invoices_to_remind.exists():
                            student_invoices_data = []
                            student_total_due_for_reminders = Decimal('0.00')
                            for inv in student_invoices_to_remind:
                                balance = inv.total_amount - inv.amount_paid
                                student_invoices_data.append({
                                    'invoice_number': inv.invoice_number,
                                    'due_date': inv.due_date,
                                    'balance_due': balance,
                                })
                                student_total_due_for_reminders += balance
                            
                            if student_total_due_for_reminders > 0:
                                student_fee_data_list.append({
                                    'student_name': student.get_full_name(),
                                    'total_due': student_total_due_for_reminders,
                                    'invoices': student_invoices_data
                                })
                                overall_parent_total_due += student_total_due_for_reminders
                    
                    if overall_parent_total_due > 0:
                        logger.info(f"Queueing fee reminder for parent {parent.email} (ID: {parent.id}) in school {tenant.name}. Amount: {overall_parent_total_due}")
                        send_fee_due_reminder_email_task.delay(
                            parent.id, 
                            tenant.schema_name, 
                            student_fee_data_list, 
                            overall_parent_total_due
                        )
            except Exception as e:
                logger.error(f"Error processing reminders for tenant {tenant.name}: {e}", exc_info=True)
    logger.info("Finished scheduled task: schedule_fee_due_reminders")
    
    
    
    
    
{% extends "tenant_base.html" %}
{% load static %}

{% block title %}
    {% if object %}Edit Leave Request{% else %}Apply for Leave{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select,
    .form-floating > textarea {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > textarea {
        height: auto;
        min-height: calc(3.5rem + 2px);
    }

    .form-floating > input[type="date"],
    .form-floating > input[type="file"] {
        height: calc(3.5rem + 2px);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus,
    .form-floating > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label,
    .form-floating > textarea:focus ~ label,
    .form-floating > textarea:not(:placeholder-shown) ~ label,
    .form-floating > input[type="date"] ~ label,
    .form-floating > input[type="file"]:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .form-check {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-radius: 1rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .form-check:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .form-check-input {
        width: 2rem;
        height: 1rem;
        margin-top: 0.25rem;
        margin-right: 1rem;
        border: 2px solid #667eea;
        border-radius: 2rem;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    .form-check-label {
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .form-check:hover .form-check-label {
        color: #667eea;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .file-upload-wrapper {
        position: relative;
        overflow: hidden;
        display: inline-block;
        width: 100%;
    }

    .file-upload-wrapper input[type=file] {
        position: absolute;
        left: -9999px;
    }

    .file-upload-label {
        display: block;
        padding: 1rem 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        color: #6c757d;
    }

    .file-upload-label:hover {
        border-color: #667eea;
        background: #f8f9ff;
        color: #667eea;
    }

    .file-upload-label i {
        font-size: 2rem;
        display: block;
        margin-bottom: 0.5rem;
        color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-calendar-plus me-3"></i>
                            {% if object %}Edit Leave Request{% else %}Apply for Leave{% endif %}
                        </h3>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <form method="post" enctype="multipart/form-data" novalidate>
                            {% csrf_token %}

                            <!-- Leave Type Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-list-alt me-2"></i>Leave Type Selection</h4>
                            </div>

                            <!-- Leave Type Field -->
                            <div class="form-floating">
                                <select class="form-select{% if form.leave_type.errors %} is-invalid{% endif %}"
                                        id="{{ form.leave_type.id_for_label }}"
                                        name="{{ form.leave_type.name }}"
                                        {% if form.leave_type.field.required %}required{% endif %}>
                                    <option value="">Select leave type</option>
                                    {% for choice in form.leave_type.field.choices %}
                                        {% if choice.0 %}
                                            <option value="{{ choice.0 }}" {% if form.leave_type.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                <label for="{{ form.leave_type.id_for_label }}">
                                    <i class="fas fa-tags icon-input"></i>Leave Type
                                </label>
                                {% if form.leave_type.help_text %}
                                    <div class="form-text">{{ form.leave_type.help_text }}</div>
                                {% endif %}
                                {% if form.leave_type.errors %}
                                    <div class="invalid-feedback">{{ form.leave_type.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Date Range Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-calendar-week me-2"></i>Leave Duration</h4>
                            </div>

                            <!-- Date Fields Row -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="date"
                                               class="form-control{% if form.start_date.errors %} is-invalid{% endif %}"
                                               id="{{ form.start_date.id_for_label }}"
                                               name="{{ form.start_date.name }}"
                                               value="{{ form.start_date.value|default:'' }}"
                                               {% if form.start_date.field.required %}required{% endif %}>
                                        <label for="{{ form.start_date.id_for_label }}">
                                            <i class="fas fa-calendar-day icon-input"></i>Start Date
                                        </label>
                                        {% if form.start_date.help_text %}
                                            <div class="form-text">{{ form.start_date.help_text }}</div>
                                        {% endif %}
                                        {% if form.start_date.errors %}
                                            <div class="invalid-feedback">{{ form.start_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="date"
                                               class="form-control{% if form.end_date.errors %} is-invalid{% endif %}"
                                               id="{{ form.end_date.id_for_label }}"
                                               name="{{ form.end_date.name }}"
                                               value="{{ form.end_date.value|default:'' }}"
                                               {% if form.end_date.field.required %}required{% endif %}>
                                        <label for="{{ form.end_date.id_for_label }}">
                                            <i class="fas fa-calendar-check icon-input"></i>End Date
                                        </label>
                                        {% if form.end_date.help_text %}
                                            <div class="form-text">{{ form.end_date.help_text }}</div>
                                        {% endif %}
                                        {% if form.end_date.errors %}
                                            <div class="invalid-feedback">{{ form.end_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Half Day Options -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-clock me-2"></i>Half Day Options</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="{{ form.half_day_start.id_for_label }}"
                                               name="{{ form.half_day_start.name }}"
                                               {% if form.half_day_start.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.half_day_start.id_for_label }}">
                                            <i class="fas fa-sun text-warning me-2"></i>Half Day Start
                                        </label>
                                        {% if form.half_day_start.help_text %}
                                            <div class="form-text">{{ form.half_day_start.help_text }}</div>
                                        {% endif %}
                                        {% if form.half_day_start.errors %}
                                            <div class="invalid-feedback d-block">{{ form.half_day_start.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="{{ form.half_day_end.id_for_label }}"
                                               name="{{ form.half_day_end.name }}"
                                               {% if form.half_day_end.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.half_day_end.id_for_label }}">
                                            <i class="fas fa-moon text-info me-2"></i>Half Day End
                                        </label>
                                        {% if form.half_day_end.help_text %}
                                            <div class="form-text">{{ form.half_day_end.help_text }}</div>
                                        {% endif %}
                                        {% if form.half_day_end.errors %}
                                            <div class="invalid-feedback d-block">{{ form.half_day_end.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Reason Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-comment-alt me-2"></i>Leave Details</h4>
                            </div>

                            <!-- Reason Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.reason.errors %} is-invalid{% endif %}"
                                          id="{{ form.reason.id_for_label }}"
                                          name="{{ form.reason.name }}"
                                          placeholder="Enter reason for leave"
                                          style="height: 120px;"
                                          {% if form.reason.field.required %}required{% endif %}>{{ form.reason.value|default:'' }}</textarea>
                                <label for="{{ form.reason.id_for_label }}">
                                    <i class="fas fa-edit icon-input"></i>Reason for Leave
                                </label>
                                {% if form.reason.help_text %}
                                    <div class="form-text">{{ form.reason.help_text }}</div>
                                {% endif %}
                                {% if form.reason.errors %}
                                    <div class="invalid-feedback">{{ form.reason.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Attachment Field -->
                            <div class="form-floating">
                                <div class="file-upload-wrapper">
                                    <input type="file"
                                           class="form-control{% if form.attachment.errors %} is-invalid{% endif %}"
                                           id="{{ form.attachment.id_for_label }}"
                                           name="{{ form.attachment.name }}"
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <label for="{{ form.attachment.id_for_label }}" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Click to upload supporting document</span>
                                        <small class="d-block text-muted mt-1">PDF, DOC, DOCX, JPG, PNG files allowed</small>
                                    </label>
                                </div>
                                {% if form.attachment.help_text %}
                                    <div class="form-text">{{ form.attachment.help_text }}</div>
                                {% endif %}
                                {% if form.attachment.errors %}
                                    <div class="invalid-feedback d-block">{{ form.attachment.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    {% if object %}Update Request{% else %}Submit Request{% endif %}
                                </button>
                                <a href="{% url 'hr:staff_leaverequest_list' %}" class="btn-secondary-premium">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-premium[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
        });
    }

    // Enhanced form validation feedback
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });

    // File upload enhancement
    const fileInput = document.querySelector('input[type="file"]');
    const fileLabel = document.querySelector('.file-upload-label span');

    if (fileInput && fileLabel) {
        fileInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                fileLabel.textContent = this.files[0].name;
                fileLabel.parentElement.style.borderColor = '#667eea';
                fileLabel.parentElement.style.backgroundColor = '#f8f9ff';
            } else {
                fileLabel.textContent = 'Click to upload supporting document';
                fileLabel.parentElement.style.borderColor = '#e9ecef';
                fileLabel.parentElement.style.backgroundColor = 'white';
            }
        });
    }

    // Date validation - ensure end date is not before start date
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    if (startDateInput && endDateInput) {
        function validateDates() {
            if (startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);

                if (endDate < startDate) {
                    endDateInput.setCustomValidity('End date cannot be before start date');
                    endDateInput.classList.add('is-invalid');
                } else {
                    endDateInput.setCustomValidity('');
                    endDateInput.classList.remove('is-invalid');
                    if (endDateInput.value) {
                        endDateInput.classList.add('is-valid');
                    }
                }
            }
        }

        startDateInput.addEventListener('change', validateDates);
        endDateInput.addEventListener('change', validateDates);
    }

    // Half day logic - prevent both half days from being selected
    const halfDayStart = document.querySelector('input[name="half_day_start"]');
    const halfDayEnd = document.querySelector('input[name="half_day_end"]');

    if (halfDayStart && halfDayEnd) {
        halfDayStart.addEventListener('change', function() {
            if (this.checked && halfDayEnd.checked) {
                halfDayEnd.checked = false;
            }
        });

        halfDayEnd.addEventListener('change', function() {
            if (this.checked && halfDayStart.checked) {
                halfDayStart.checked = false;
            }
        });
    }
});
</script>
{% endblock %}
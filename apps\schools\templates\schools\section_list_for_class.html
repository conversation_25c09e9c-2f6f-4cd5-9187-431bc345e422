{# D:\school_fees_saas_v2\apps\schools\templates\schools\section_list_for_class.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Manage Sections" }}{% endblock %}

{% block page_specific_css %}
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title }}</h1>
        <a href="{% url 'schools:section_create' school_class.pk %}" class="btn btn-primary">Add New Section</a>
    </div>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:class_list' %}">Classes/Grades</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ school_class.name }} Sections</li>
        </ol>
    </nav>

    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    {% if sections %}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>Section Name</th>
                            <th>Class/Grade</th>
                            <th style="width: 150px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for section in sections %}
                        <tr>
                            <td>{{ section.name }}</td>
                            <td>{{ section.school_class.name }}</td>
                            <td>
                                <a href="{% url 'schools:section_update' class_pk=school_class.pk pk=section.pk %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                <a href="{% url 'schools:section_delete' class_pk=school_class.pk pk=section.pk %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                {% comment %} <form action="{% url 'schools:section_delete' section.pk %}" method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete section \'{{ section.name|escapejs }}\' from class \'{{ section.school_class.name|escapejs }}\'?');"> {% endcomment %}
                                    {% csrf_token %}
                                    {% comment %} <button type="submit" class="btn btn-danger btn-sm" title="Delete Section">Del</button> {% endcomment %}
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">No sections found for {{ school_class.name }}.</div>
    {% endif %}

    {% include "partials/_pagination.html" %}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:class_list' %}" class="btn btn-outline-secondary">Back to Classes/Grades List</a>
    </div>
</div>
{% endblock content %}
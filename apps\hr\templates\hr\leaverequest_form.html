{# D:\school_fees_saas_v2\apps\hr\templates\hr\leave_request_form.html #}
{% extends "tenant_base.html" %}

{% load core_tags %} {# Or any other tag libraries you use #}


{% block title %}{{ view_title|default:"Leave Application" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>{{ view_title }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:staff_leaverequest_list' %}">My Leave Requests</a></li>
                <li class="breadcrumb-item active">{{ view_title }}</li>
            </ol>
        </nav>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">{{ view_title }}</h5>
            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}
                {{ form|crispy }} {# Or render manually like LeaveType form #}
                <button type="submit" class="btn btn-primary mt-3">Submit Request</button>
                <a href="{% url 'hr:staff_leaverequest_list' %}" class="btn btn-secondary mt-3">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}

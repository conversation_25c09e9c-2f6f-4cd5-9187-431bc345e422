{% extends "public_base.html" %}
{% load static %}

{% block public_page_title %}{{ view_title }}{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title }}</h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if owned_schools %}
                        <p>Please select which school dashboard you would like to access:</p>
                        <div class="list-group">
                            {% for school in owned_schools %}
                                {# Ensure your School model has a get_absolute_url() method #}
                                {# that returns the full URL to its tenant dashboard #}
                                <a href="{{ school.get_absolute_url }}" class="list-group-item list-group-item-action">
                                    {{ school.name }} 
                                    <small class="text-muted">({{ school.schema_name }}.{{ request.get_host.split(':')[0] }})</small>
                                    {# Or construct domain manually if school.get_host() isn't what you want #}
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p>No active schools found for your account.</p>
                    {% endif %}
                    <hr>
                    <a href="{% url 'public_site:home' %}" class="btn btn-outline-secondary">Back to Platform Home</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



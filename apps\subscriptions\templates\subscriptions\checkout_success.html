{# D:\school_fees_saas_v2\apps\subscriptions\templates\subscriptions\checkout_success.html #}
{% extends "public_base.html" %} {# Or tenant_base.html if user is already in tenant context #}
{% load static i18n %}

{% block public_page_title %}{% trans "Subscription Process Started" %}{% endblock %}

{% block content %}
<div class="container py-5 text-center">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <i class="bi bi-check-circle-fill display-1 text-success mb-3"></i>
            <h1 class="display-5">{% trans "Thank You!" %}</h1>
            {% if messages %}
                {% for message in messages %}
                    <p class="lead {% if message.tags %}alert alert-{{ message.tags }}{% endif %}">{{ message }}</p>
                {% endfor %}
            {% else %}
                <p class="lead">{% trans "Your subscription process has started. You will be notified once fully active." %}</p>
            {% endif %}
            
            <p>{% trans "If your plan includes a trial, it has begun. Otherwise, your subscription will activate upon successful payment confirmation or administrative approval." %}</p>
            
            {% if request.tenant and request.tenant.schema_name != public_schema_name %}
                <a href="{% url 'schools:dashboard' %}" class="btn btn-primary btn-lg mt-3">{% trans "Go to Your School Dashboard" %}</a>
            {% else %}
                <a href="{% url 'public_site:home' %}" class="btn btn-primary btn-lg mt-3">{% trans "Back to Homepage" %}</a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}


{# D:\school_fees_saas_v2\apps\fees\templates\fees\confirm_delete.html #}
{% extends "tenant_base.html" %} {# Assumes this is a tenant-facing action #}
{% load static i18n core_tags %}

{% block tenant_page_title %}{{ view_title|default:_("Confirm Deletion") }}{% endblock tenant_page_title %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            
            <div class="card border-danger shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>{{ view_title|default:_("Confirm Deletion") }}</h4>
                </div>
                <div class="card-body p-4">
                    
                    <p class="lead">{% trans "Are you sure you want to delete the following object?" %}</p>
                    
                    {# Display the object being deleted #}
                    <div class="alert alert-light border p-3 mb-4">
                        <strong class="d-block">{{ object_type_name|default:object|class_name }}:</strong>
                        <h5 class="mb-0">{{ object }}</h5>
                    </div>
                    
                    {# Display related objects that will also be deleted or affected #}
                    {% if related_objects %}
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">{% trans "Warning: Related Items" %}</h6>
                            <p>{% trans "The following related items will also be deleted or affected:" %}</p>
                            <ul>
                                {% for item in related_objects %}
                                    <li>{{ item }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    {# The form for deletion #}
                    <form method="post" action="">
                        {% csrf_token %}
                        
                        <p class="text-muted">{% trans "This action cannot be undone." %}</p>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            {# The "Cancel" button should go back to a safe URL #}
                            <a href="{{ cancel_url|default:request.META.HTTP_REFERER|default:'..' }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            
                            {# The "Confirm" button submits the form #}
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash-fill me-1"></i> {% trans "Yes, I'm sure. Delete." %}
                            </button>
                        </div>
                    </form>
                    
                </div>
            </div>
            
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



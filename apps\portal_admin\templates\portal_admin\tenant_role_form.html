{# D:\school_fees_saas_v2\apps\portal_admin\templates\portal_admin\tenant_role_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block title %}{{ view_title|default:"Tenant Role Form" }}{% endblock %}

{% block page_specific_css %}
{{ block.super }}
<style>
    /* Premium Form Design - Tenant Role Form */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-card-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-card-header h3 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
    }

    .premium-card-body {
        padding: 2.5rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .form-floating {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.15);
        background: #ffffff;
        transform: translateY(-2px);
    }

    .form-floating > label {
        padding: 1rem 0.75rem;
        font-weight: 500;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select:focus ~ label,
    .form-floating > .form-select:not([value=""]) ~ label {
        color: #007bff;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .icon-input {
        color: #007bff;
        margin-right: 0.5rem;
        font-size: 1.1rem;
    }

    .btn {
        border-radius: 0.75rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .breadcrumb-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: transparent;
    }

    .breadcrumb-item a {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .info-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #2196f3;
    }

    .info-section h5 {
        color: #1976d2;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .info-section p {
        color: #424242;
        margin: 0;
        line-height: 1.6;
    }

    .character-counter {
        font-size: 0.875rem;
        color: #6c757d;
        text-align: right;
        margin-top: 0.25rem;
        transition: color 0.3s ease;
    }

    .character-counter.warning {
        color: #ffc107;
    }

    .character-counter.danger {
        color: #dc3545;
    }

    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    @media (max-width: 768px) {
        .premium-card-header {
            padding: 1.5rem;
        }

        .premium-card-header h3 {
            font-size: 1.5rem;
        }

        .premium-card-body {
            padding: 1.5rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-7">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb-container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'portal_admin:group_list' %}">
                                <i class="bi bi-people-fill me-1"></i>{% trans "Roles" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            {% if object %}
                                {% trans "Edit Role" %}
                            {% else %}
                                {% trans "Create Role" %}
                            {% endif %}
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h5><i class="bi bi-info-circle me-2"></i>{% trans "Role Management" %}</h5>
                <p>
                    {% if object %}
                        {% trans "Update the role name to modify this role. Role names should be descriptive and reflect the permissions and responsibilities of users assigned to this role." %}
                    {% else %}
                        {% trans "Create a new role for your organization. Roles help organize staff permissions and access levels. Choose a clear, descriptive name that reflects the role's purpose." %}
                    {% endif %}
                </p>
            </div>

            <!-- Premium Form Card -->
            <div class="card premium-card">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-person-badge me-2"></i>
                        {% if object %}
                            {% trans "Edit Role" %}
                        {% else %}
                            {% trans "Create New Role" %}
                        {% endif %}
                    </h3>
                </div>
                <div class="premium-card-body">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="tenantRoleForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}{% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Role Name Field -->
                        <div class="form-floating">
                            <input
                                type="text"
                                class="form-control{% if form.name.errors %} is-invalid{% endif %}"
                                id="{{ form.name.id_for_label }}"
                                name="{{ form.name.name }}"
                                value="{{ form.name.value|default:'' }}"
                                placeholder="{% trans 'Enter role name' %}"
                                maxlength="150"
                                required
                                autocomplete="off"
                            >
                            <label for="{{ form.name.id_for_label }}">
                                <i class="bi bi-person-badge icon-input"></i>{% trans "Role Name" %}
                            </label>
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.name.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="character-counter" id="nameCounter">
                                <span id="nameCount">{{ form.name.value|length|default:0 }}</span>/150 {% trans "characters" %}
                            </div>
                            {% if form.name.help_text %}
                                <small class="form-text text-muted mt-2">
                                    <i class="bi bi-lightbulb me-1"></i>{{ form.name.help_text }}
                                </small>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'portal_admin:group_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}
                                    {% trans "Update Role" %}
                                {% else %}
                                    {% trans "Create Role" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Character counter for role name
    const nameInput = document.getElementById('{{ form.name.id_for_label }}');
    const nameCounter = document.getElementById('nameCounter');
    const nameCount = document.getElementById('nameCount');

    if (nameInput && nameCounter && nameCount) {
        nameInput.addEventListener('input', function() {
            const length = this.value.length;
            nameCount.textContent = length;

            // Update counter color based on length
            nameCounter.classList.remove('warning', 'danger');
            if (length > 120) {
                nameCounter.classList.add('danger');
            } else if (length > 100) {
                nameCounter.classList.add('warning');
            }
        });
    }

    // Form validation enhancement
    const form = document.getElementById('tenantRoleForm');
    const submitBtn = document.getElementById('submitBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

            // Re-enable after 3 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>{% if object %}{% trans "Update Role" %}{% else %}{% trans "Create Role" %}{% endif %}';
            }, 3000);
        });

        // Real-time validation
        nameInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value.length < 2) {
                this.setCustomValidity('{% trans "Role name must be at least 2 characters long." %}');
                this.classList.add('is-invalid');
            } else if (value.length > 150) {
                this.setCustomValidity('{% trans "Role name cannot exceed 150 characters." %}');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });

        nameInput.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                this.classList.remove('is-invalid');
                this.setCustomValidity('');
            }
        });
    }

    // Enhanced focus effects
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(function(control) {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>
{% endblock %}






{% comment %} {% extends "tenant_base.html" %}

{% load static %}

{% block title %}{{ view_title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <div class="card">
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                {% endif %}

                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                    {{ form.name }}
                    {% if form.name.help_text %}<small class="form-text text-muted">{{ form.name.help_text }}</small>{% endif %}
                    {% for error in form.name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                </div>
                
                {% if tenant_role_prefix and not object %}
                <p class="form-text text-muted">
                    The role name will be saved as: <code>{{ tenant_role_prefix }}<span id="roleNamePreview">your-role-name</span></code>.
                    You only need to type "your-role-name".
                </p>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
                        const previewSpan = document.getElementById('roleNamePreview');
                        if (nameInput && previewSpan) {
                            nameInput.addEventListener('input', function() {
                                previewSpan.textContent = nameInput.value.trim() || 'your-role-name';
                            });
                        }
                    });
                </script>
                {% elif object and tenant_role_prefix %}
                <p class="form-text text-muted">
                    Current full role name: <code>{{ object.name }}</code>.
                </p>
                {% endif %}

                <button type="submit" class="btn btn-success">Save Role</button>
                <a href="{% url 'schools:group_list' %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}
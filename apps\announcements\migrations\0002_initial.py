# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('announcements', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('schools', '0001_initial'),
        ('tenants', '0003_alter_school_is_active_alter_school_slug'),
    ]

    operations = [
        migrations.AddField(
            model_name='announcement',
            name='author',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_announcements', to='schools.staffuser', verbose_name='Author'),
        ),
        migrations.AddField(
            model_name='announcement',
            name='target_tenant_staff_groups',
            field=models.ManyToManyField(blank=True, help_text='Select if not targeting all staff in this school.', related_name='group_targeted_announcements', to='auth.group', verbose_name='Target Specific Staff Groups in this School'),
        ),
        migrations.AddField(
            model_name='announcement',
            name='tenant',
            field=models.ForeignKey(blank=True, help_text='The specific school this announcement is for. Leave blank if platform-wide.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to='tenants.school'),
        ),
    ]

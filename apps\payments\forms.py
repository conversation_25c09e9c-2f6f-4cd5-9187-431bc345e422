# # apps/payments/forms.py

import django_filters # Import django_filters

from apps.accounting.models import Account, AccountType # For the payment account field

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal

from .models import Payment, PaymentMethod # Assuming PaymentMethod is in this app's models
from apps.students.models import Student
from apps.fees.models import Invoice # Import Invoice model from fees app
from apps.schools.models import AcademicYear # Assuming AcademicYear location

from django import forms
from apps.fees.models import Invoice

import logging
logger = logging.getLogger(__name__)


# apps/payments/forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from django.core.validators import MinValueValidator
from django.conf import settings # For STAFF_USER_MODEL

from .models import Payment, PaymentMethod # Assuming PaymentMethod is in current app
from apps.students.models import Student
from apps.fees.models import Invoice # Ensure Invoice and its InvoiceStatus are defined
from apps.schools.models import AcademicYear

import logging
logger = logging.getLogger(__name__)

STAFF_USER_MODEL = settings.STAFF_USER_MODEL # From settings


# D:\school_fees_saas_v2\apps\payments\forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db.models import Q, F  # Add missing imports for QuerySet operations
from decimal import Decimal

# Ensure correct model imports based on your project structure
from .models import Payment, PaymentMethod
from apps.students.models import Student, ParentUser
from apps.schools.models import AcademicYear
from apps.fees.models import Invoice # For invoice choice field if used for pre-fill or direct link
from apps.schools.models import StaffUser # For processed_by_staff

import logging
logger = logging.getLogger(__name__)

class PaymentForm(forms.ModelForm):
    # --- Fields that might need more specific widgets or querysets ---
    payment_date = forms.DateTimeField(
        initial=timezone.now, # Default to current date and time
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'form-control form-control-sm'}),
        label=_("Payment Date & Time")
    )
    # This field is for selecting an invoice to pre-fill data or to directly link (if your model retains a single invoice FK)
    # If Payment model has NO direct invoice FK, this field is purely for UI pre-fill.
    # Let's assume for now Payment model does NOT have a direct invoice FK.
    # This field can be used to help select the student and pre-fill amount.
    invoice_to_apply_to = forms.ModelChoiceField(
        queryset=Invoice.objects.none(), # Populated dynamically in __init__
        required=False,
        label=_("Apply to Specific Invoice (Optional)"),
        help_text=_("Selecting an invoice will pre-fill student and amount. Payment will be allocated later."),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-field'})
    )

    class Meta:
        model = Payment
        fields = [
            'parent_payer',         # Who is ultimately paying (Parent)
            'student',              # Which student is this payment primarily for (can be derived from invoice)
            # 'invoice_to_apply_to' - Custom field for invoice selection (not a model field, handled separately)
            'payment_method',
            'academic_year',
            'amount',
            'payment_date',
            'transaction_reference',
            'payment_type',
            'status',               # Staff can set status (e.g. 'Completed' for cash)
            'notes',
            # 'processed_by_staff' - set automatically in save() method
            # 'unallocated_amount' - usually not directly set by user in this form
            # 'created_by' - set automatically in save() method based on user type
        ]
        widgets = {
            'parent_payer': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'student': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'payment_method': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'academic_year': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm text-end', 'step': '0.01'}),
            'transaction_reference': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'payment_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'status': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'notes': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 3}),
            'processed_by_staff': forms.Select(attrs={'class': 'form-select form-select-sm select2-field'}),
        }
        labels = { # Customize labels if needed
            'parent_payer': _("Paying Parent (Optional)"),
            'student': _("Student this payment primarily concerns (Optional if Invoice selected)"),
            'academic_year': _("Payment for Academic Year"),
            'transaction_reference': _("Payment Reference (e.g., Txn ID, Cheque No)"),
            'processed_by_staff': _("Payment Processed/Recorded By Staff"),
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        # For pre-filling form from context (e.g., from an invoice detail page "Record Payment" button)
        initial_invoice_pk = kwargs.pop('initial_invoice_pk', None) 
        initial_student_pk = kwargs.pop('initial_student_pk', None)

        super().__init__(*args, **kwargs)

        # --- Set dynamic querysets ---
        if 'parent_payer' in self.fields:
            self.fields['parent_payer'].queryset = ParentUser.objects.all().order_by('last_name', 'first_name', 'email')
            self.fields['parent_payer'].required = False # Making it optional
            self.fields['parent_payer'].empty_label = _("Select Parent (Optional)")


        if 'student' in self.fields:
            self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
            self.fields['student'].required = False # Payment might be general from parent
            self.fields['student'].empty_label = _("Select Student (Optional)")


        if 'academic_year' in self.fields:
            # Assuming AcademicYear is tenant-specific by schema or has tenant FK handled by manager
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(is_active=True).order_by('-start_date')
            self.fields['academic_year'].empty_label = _("Select Academic Year")


        if 'payment_method' in self.fields:
            self.fields['payment_method'].queryset = PaymentMethod.objects.filter(
                is_active=True
            ).select_related('linked_account').order_by('name')
            
            self.fields['payment_method'].empty_label = _("Select Payment Method")

        if 'processed_by_staff' in self.fields:
            self.fields['processed_by_staff'].queryset = StaffUser.objects.filter(is_active=True).order_by('last_name', 'first_name')
            self.fields['processed_by_staff'].required = True # Usually staff recording is required
            if self.request and hasattr(self.request.user, 'staffuser_profile') and not self.initial.get('processed_by_staff'):
                try: # Check if request.user is a StaffUser
                    self.initial['processed_by_staff'] = self.request.user.staffuser_profile # Or just self.request.user if StaffUser is AUTH_USER_MODEL for staff
                except AttributeError: pass # request.user might not be staff

        # --- Populate Invoice Choice Field (for pre-fill assistance) ---
        # This field is not directly on the Payment model in the consolidated version
        # but helps in the UI to select student/amount.
        from apps.fees.models import InvoiceStatus
        outstanding_statuses = [InvoiceStatus.SENT, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE]
        invoice_queryset = Invoice.objects.filter(status__in=outstanding_statuses).exclude(
            Q(subtotal_amount__lte=F('amount_paid') + F('total_concession_amount'))
        ).select_related('student', 'student__current_class').order_by('student__last_name', '-issue_date')
        self.fields['invoice_to_apply_to'].queryset = invoice_queryset
        self.fields['invoice_to_apply_to'].empty_label = _("Select Invoice to Pre-fill (Optional)")


        # --- Set initial values if creating a new payment ---
        if not self.instance.pk: # If creating a new payment
            if 'payment_date' in self.fields and not self.initial.get('payment_date'):
                self.initial['payment_date'] = timezone.now().strftime('%Y-%m-%dT%H:%M')
            
            if initial_student_pk and 'student' in self.fields:
                self.initial['student'] = initial_student_pk
            
            if initial_invoice_pk and 'invoice_to_apply_to' in self.fields:
                try:
                    invoice_instance = Invoice.objects.get(pk=initial_invoice_pk)
                    self.initial['invoice_to_apply_to'] = invoice_instance.pk
                    if 'student' in self.fields and invoice_instance.student:
                        self.initial['student'] = invoice_instance.student.pk
                        if invoice_instance.student.parents.exists() and 'parent_payer' in self.fields: # Assuming M2M from Student to ParentUser
                            self.initial['parent_payer'] = invoice_instance.student.parents.first().pk
                    if 'amount' in self.fields and hasattr(invoice_instance, 'balance_due'):
                        self.initial['amount'] = invoice_instance.balance_due
                    if 'academic_year' in self.fields and invoice_instance.academic_year:
                        self.initial['academic_year'] = invoice_instance.academic_year.pk
                except Invoice.DoesNotExist:
                    pass
            
            # Default status for manually recorded payments is often 'COMPLETED'
            if 'status' in self.fields and not self.initial.get('status'):
                self.initial['status'] = Payment.STATUS_COMPLETED

        # Make fields optional/required based on model's `blank` attribute
        for field_name, field_obj in self.fields.items():
            if field_name in self.Meta.fields: # Process only model fields
                model_field = self.Meta.model._meta.get_field(field_name)
                if model_field.blank and field_obj.required:
                    field_obj.required = False
                    if isinstance(field_obj.widget, forms.Select):
                        field_obj.empty_label = _("--------- (Optional)")


    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount <= Decimal('0.00'):
            raise forms.ValidationError(_("Payment amount must be positive."))
        return amount

    def clean_payment_date(self):
        payment_date = self.cleaned_data.get('payment_date')
        # Allow payment date to be slightly in the future for back-office entry, but not too far.
        # Or restrict to today or past. For now, just check it exists.
        if payment_date and payment_date.date() > (timezone.now() + timezone.timedelta(days=7)).date(): # Example: not more than 7 days in future
            raise forms.ValidationError(_("Payment date cannot be too far in the future."))
        if payment_date and payment_date > timezone.now() + timezone.timedelta(minutes=15): # Generous grace for clock skew
            logger.warning(f"Payment date {payment_date} is in the future. Allowing with grace period.")
            # raise forms.ValidationError(_("Payment date and time cannot be in the future."))
        return payment_date

    def clean(self):
        cleaned_data = super().clean()
        invoice_to_apply = cleaned_data.get('invoice_to_apply_to') # This is the UI helper field
        student = cleaned_data.get('student')
        parent_payer = cleaned_data.get('parent_payer')
        amount = cleaned_data.get('amount')

        # If invoice_to_apply is selected, ensure student and parent_payer are consistent or set
        if invoice_to_apply:
            if not student and invoice_to_apply.student:
                cleaned_data['student'] = invoice_to_apply.student
                # Re-validate if student was changed:
                self.data = self.data.copy() # Make data mutable
                self.data[self.add_prefix('student')] = invoice_to_apply.student.pk
                
            student = cleaned_data.get('student') # Get updated student

            if student and invoice_to_apply.student != student:
                self.add_error('invoice_to_apply_to', 
                                _("This invoice belongs to %(invoice_student)s, not the selected student %(form_student)s.") % 
                                {'invoice_student': invoice_to_apply.student.get_full_name(), 'form_student': student.get_full_name()})
            
            # Try to set parent_payer from student if not already set
            if not parent_payer and student and student.parents.exists(): # Assuming Student has M2M to ParentUser called 'parents'
                cleaned_data['parent_payer'] = student.parents.first()

        final_parent_payer = cleaned_data.get('parent_payer')
        final_student = cleaned_data.get('student')

        if not final_parent_payer and not final_student:
            self.add_error(None, _("A payment must be associated with either a Paying Parent or a Student."))

        # Validation for overpayment against a single selected invoice (if invoice_to_apply_to is used meaningfully)
        if invoice_to_apply and amount is not None:
            if amount > invoice_to_apply.balance_due + Decimal('0.01'): # Small tolerance
                self.add_error('amount', _("Payment amount (%(amount)s) exceeds the selected invoice's balance (%(balance_due)s).") % {
                        'amount': amount, 
                        'balance_due': invoice_to_apply.balance_due
                    })
        return cleaned_data

    def save(self, commit=True):
        payment = super().save(commit=False)

        # Set the appropriate user field based on user type
        if self.request and hasattr(self.request, 'user') and self.request.user.is_authenticated:
            from apps.schools.models import StaffUser
            from apps.students.models import ParentUser
            from django.conf import settings

            # If user is a StaffUser, set processed_by_staff
            if isinstance(self.request.user, StaffUser):
                payment.processed_by_staff = self.request.user
                # Don't set created_by for staff users since it expects AUTH_USER_MODEL
            # If user is a ParentUser, set created_by (assuming ParentUser is AUTH_USER_MODEL or compatible)
            elif isinstance(self.request.user, ParentUser):
                payment.created_by = self.request.user
            # If user is platform User (AUTH_USER_MODEL), set created_by
            else:
                # This would be for platform administrators
                payment.created_by = self.request.user

        # If processed_by_staff is not set and request.user is staff, set it.
        # This assumes processed_by_staff is a FK to your StaffUser model.
        if not payment.processed_by_staff_id and self.request and hasattr(self.request.user, 'staffuser_profile'):
            try:
                # Check if self.request.user is an instance of your StaffUser model
                # StaffUserModelClass = payment._meta.get_field('processed_by_staff').remote_field.model
                # if isinstance(self.request.user, StaffUserModelClass):
                payment.processed_by_staff = self.request.user.staffuser_profile # Or just self.request.user
            except AttributeError:
                logger.warning(f"Could not set processed_by_staff for payment by {self.request.user.email}, user may not be staff or staff profile missing.")


        if commit:
            payment.save()
            # After payment is saved, you would typically proceed to an allocation step
            # if the 'invoice_to_apply_to' field was used or if it's a general payment.
            # For now, this form just saves the Payment record.
            # The service function record_parent_payment handles allocations for online flow.
            # For manual staff entry, a separate allocation UI might be needed after this.
        return payment
    
    

# D:\school_fees_saas_v2\apps\payments\forms.py
import logging
from django import forms

# Import your models
from .models import PaymentMethod
from apps.accounting.models import Account, AccountType

logger = logging.getLogger(__name__)

class PaymentMethodForm(forms.ModelForm):
    class Meta:
        model = PaymentMethod
        # Ensure the order of fields matches your intended form layout
        fields = ['name', 'type', 'linked_account', 'is_active', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'type': forms.Select(attrs={'class': 'form-select'}),
            'linked_account': forms.Select(attrs={'class': 'form-select select2-coa'}), # 'select2-coa' implies you might use Select2 JS library
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if 'linked_account' in self.fields:
            try:
                logger.info("--- PaymentMethodForm: Initializing 'linked_account' field ---")

                # Get suitable accounts for Payment Methods
                # These should be ASSET accounts with DR normal balance (Cash, Bank accounts, etc.)
                suitable_accounts = Account.objects.filter(
                    account_type__classification='ASSET',  # ASSET classification
                    account_type__normal_balance='DR',     # DR normal balance (correct for assets)
                    is_active=True                         # Only active accounts
                ).exclude(
                    name__icontains='control'              # Exclude control accounts
                ).select_related('account_type').order_by('code')

                self.fields['linked_account'].queryset = suitable_accounts

                count = suitable_accounts.count()
                logger.info(f"Found {count} suitable accounts for Payment Methods")

                if count == 0:
                    logger.warning("No suitable accounts found! Checking all ASSET accounts...")
                    # Debug: Check all ASSET accounts
                    all_asset_accounts = Account.objects.filter(
                        account_type__classification='ASSET',
                        is_active=True
                    ).select_related('account_type')

                    logger.info(f"Total ASSET accounts: {all_asset_accounts.count()}")
                    for acc in all_asset_accounts[:5]:  # Log first 5
                        logger.info(f"  Account: {acc.name} | Type: {acc.account_type.name} | Normal Balance: {acc.account_type.normal_balance}")

                self.fields['linked_account'].empty_label = "--- Select an Asset Account ---"

                # Custom label format: "Code - Name (Type)"
                def custom_label(obj):
                    code = getattr(obj, 'code', 'N/A')
                    name = obj.name
                    type_name = obj.account_type.name if obj.account_type else 'N/A Type'
                    return f"{code} - {name} ({type_name})"

                self.fields['linked_account'].label_from_instance = custom_label

            except Exception as e:
                logger.error(f"Error setting up linked_account field in PaymentMethodForm: {e}", exc_info=True)
                # Fallback: show all active accounts
                self.fields['linked_account'].queryset = Account.objects.filter(is_active=True).select_related('account_type').order_by('code')
                self.fields['linked_account'].empty_label = "--- Select an Account (Error occurred) ---"
                
                
                

class PaymentFilterForm(django_filters.FilterSet):
    # Define filters as they were in your views.py
    student = django_filters.ModelChoiceFilter(
        queryset=Student.objects.all(), # View's get_queryset might refine this further
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label="Student"
    )
    payment_method = django_filters.ModelChoiceFilter(
        queryset=PaymentMethod.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label="Payment Method"
    )
    payment_date = django_filters.DateFromToRangeFilter( # For filtering by date range
        widget=django_filters.widgets.RangeWidget(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        label="Payment Date Range"
    )
    # Add other filters as needed, e.g., invoice number if payments are linked
    # invoice_number = django_filters.CharFilter(field_name='invoice__invoice_number', lookup_expr='icontains', ...)

    class Meta:
        model = Payment
        # Fields from Payment model to filter on
        fields = ['student', 'payment_method', 'payment_date', 'payment_type'] # Add 'payment_type' if it exists

    def __init__(self, *args, **kwargs):
        # The view (FilterView) passes request to the FilterSet
        # No need to pop 'request' here if using FilterView's default instantiation
        super().__init__(*args, **kwargs)
        # Filter querysets based on current tenant if necessary,
        # though django-tenants should handle this if queries are made
        # after middleware has set the schema.
        # Example if direct filtering needed:
        if hasattr(self.request, 'tenant') and self.request.tenant:
            self.filters['student'].queryset = Student.objects.filter(is_active=True).order_by('last_name', 'first_name')
            self.filters['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
        else: # Fallback for safety, though shouldn't be hit in tenant views
            self.filters['student'].queryset = Student.objects.none()
            self.filters['payment_method'].queryset = PaymentMethod.objects.none()

        # Customize labels if needed (FilterSet uses field's verbose_name by default)
        self.filters['student'].field.label_from_instance = lambda obj: f"{obj.full_name_admission_id}"
        
        
# D:\school_fees_saas_v2\apps\payments\forms.py
from django import forms
from django.utils import timezone
from .models import PaymentMethod # Ensure this is imported
from decimal import Decimal

# ... your other forms like PaymentMethodForm ...

class ManualPaymentForm(forms.Form):
    """
    A form for staff to manually record a payment against a specific invoice.
    """
    amount = forms.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        required=True,
        min_value=Decimal('0.01'),
        label="Amount Paid",
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
    
    # THIS IS THE CRITICAL FIELD
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.none(), # Start with an empty queryset
        required=True,
        empty_label="--- Select Payment Method ---",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    payment_date = forms.DateField(
        initial=timezone.now().date(),
        required=True,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Cheque No, Transaction ID'})
    )
    notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Optional notes about the payment'}),
        required=False
    )

    def __init__(self, *args, **kwargs):
        # We expect the view to pass in the tenant object
        tenant = kwargs.pop('tenant', None)
        self.invoice = kwargs.pop('invoice', None)
        
        super().__init__(*args, **kwargs)

        # --- THIS IS THE KEY LOGIC ---
        # Populate the payment_method queryset based on the tenant.
        # This ensures you only see payment methods for the current school.
        if tenant:
            # Since PaymentMethod is a tenant-specific model, this query
            # will already be scoped to the current tenant's schema
            # if the view is correctly wrapped in schema_context.
            # No explicit filtering by tenant is needed here.
            self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
        
        # This part is for pre-filling the amount based on the invoice
        if self.invoice:
            self.fields['amount'].initial = self.invoice.balance_due
            self.fields['amount'].label = f"Amount to Pay (Balance Due: {self.invoice.balance_due})"
        
    def clean_amount(self):
        """
        Validate that the amount paid is not more than the balance due.
        """
        amount = self.cleaned_data.get('amount')
        if self.invoice and amount > self.invoice.balance_due:
            raise forms.ValidationError(
                f"Payment amount cannot be greater than the outstanding balance of {self.invoice.balance_due}."
            )
        return amount
    
    
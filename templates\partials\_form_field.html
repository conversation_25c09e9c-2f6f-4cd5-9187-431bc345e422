{# partials/_form_field.html #}
{% if field %}
    <div class="mb-3 {% if field.errors %}is-invalid{% endif %}">
        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}
            {% if field.field.required %}
                <span class="text-danger">*</span>
            {% endif %}
        </label>
        {{ field }} {# Renders the widget #}
        {% if field.help_text %}
            <small class="form-text text-muted helptext">{{ field.help_text|safe }}</small>
        {% endif %}
        {% for error in field.errors %}
            <div class="invalid-feedback d-block">
                {{ error }}
            </div>
        {% endfor %}
    </div>
{% endif %}



{% comment %} {# D:\school_fees_saas_v2\templates\partials\_form_field.html #}
{# Expects 'field' and 'col_class' (optional) in context #}
<div class="{{ col_class|default:'col-md-6' }} mb-3">
    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label_tag|safe }}</label>
    {{ field }} {# Renders widget with attrs from form definition #}
    {% if field.help_text %}<small class="form-text text-muted">{{ field.help_text }}</small>{% endif %}
    {% if field.errors %}
        <div class="invalid-feedback d-block">
            {% for error in field.errors %}{{ error }}{% endfor %}
        </div>
    {% endif %}
</div> {% endcomment %}
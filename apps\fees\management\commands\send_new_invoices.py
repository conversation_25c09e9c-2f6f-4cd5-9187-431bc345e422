# D:\school_fees_saas_v2\apps\fees\management\commands\send_new_invoices.py
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.conf import settings
from django.urls import reverse
from django.db import transaction

from django_tenants.utils import schema_context, get_tenant_model # For iterating tenants
from apps.fees.models import Invoice # Your Invoice model
from apps.students.models import ParentUser # Your ParentUser model
from apps.communication.tasks import send_invoice_issued_email_task # Your Celery task

import logging
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Scans for newly issued invoices (status=SENT, notification_sent_at=NULL) and queues email notifications to parents.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specify a single tenant schema_name to process (e.g., alpha). Processes all if not specified.',
            default=None
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate the process without actually sending emails or updating records.'
        )

    @transaction.atomic # Ensure all operations for a tenant or the whole run are atomic
    def handle(self, *args, **options):
        tenant_schema_name_arg = options['tenant']
        dry_run = options['dry_run']

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN: No emails will be sent and no records will be updated."))

        TenantModel = get_tenant_model() # Your public School model

        if tenant_schema_name_arg:
            tenants_to_process = TenantModel.objects.filter(schema_name=tenant_schema_name_arg)
            if not tenants_to_process.exists():
                raise CommandError(f"Tenant with schema_name '{tenant_schema_name_arg}' not found.")
        else:
            tenants_to_process = TenantModel.objects.filter(is_active=True) # Or your criteria for active schools

        self.stdout.write(f"Found {tenants_to_process.count()} tenant(s) to process for invoice notifications.")
        
        total_notifications_queued = 0
        total_invoices_processed = 0

        for tenant_public_instance in tenants_to_process:
            self.stdout.write(self.style.SUCCESS(f"\nProcessing Tenant: {tenant_public_instance.name} (Schema: {tenant_public_instance.schema_name})"))
            
            with schema_context(tenant_public_instance.schema_name):
                try:
                    # Find invoices that are SENT and for which notification_sent_at is NULL
                    invoices_to_notify = Invoice.objects.filter(
                        status=Invoice.InvoiceStatus.SENT, # Ensure this enum/choice exists
                        notification_sent_at__isnull=True # Crucial condition
                    ).select_related('student', 'student__school_class', 'student__current_class', 'student__current_section') # Optimize queries

                    if not invoices_to_notify.exists():
                        self.stdout.write("  No new invoices to notify for this tenant.")
                        continue

                    self.stdout.write(f"  Found {invoices_to_notify.count()} invoice(s) needing notification.")

                    for invoice in invoices_to_notify:
                        total_invoices_processed += 1
                        student = invoice.student
                        if not student:
                            logger.warning(f"Invoice {invoice.invoice_number} (PK: {invoice.pk}) in schema {tenant_public_instance.schema_name} has no student. Skipping.")
                            continue
                        
                        student_name_for_email = student.get_full_name()

                        # Prepare invoice_details dict
                        invoice_items_data = []
                        if hasattr(invoice, 'details') and callable(getattr(invoice, 'details').all):
                            invoice_items_data = [{'desc': detail.description, 'amount': detail.amount_display} for detail in invoice.details.all()]
                        
                        invoice_details_dict = {
                            'invoice_number': invoice.invoice_number,
                            'issue_date': invoice.issue_date,
                            'due_date': invoice.due_date,
                            'amount_due': invoice.balance_due, # Assuming balance_due property exists
                            'items': invoice_items_data,
                            # Constructing invoice_url. This can be tricky from a command.
                            # Best if the email template just links to the parent portal dashboard.
                            # 'invoice_url': f"{settings.APP_SCHEME}://{tenant_public_instance.domains.first().domain}{reverse('parent_portal:invoice_detail', args=[invoice.pk])}" if tenant_public_instance.domains.exists() else None
                        }

                        # Find parent(s) associated with invoice.student
                        parents_to_notify = student.guardians.filter(
                            is_active=True, 
                            receive_notifications=True, # Make sure ParentUser has this field
                            email__isnull=False
                        ).exclude(email__exact='')

                        if not parents_to_notify.exists():
                            logger.warning(f"No active parents with email & notifications enabled found for student {student} (Invoice: {invoice.invoice_number}) in schema {tenant_public_instance.schema_name}.")
                            continue

                        for parent_user_instance in parents_to_notify:
                            self.stdout.write(f"    - Queuing notification for Invoice {invoice.invoice_number} to Parent {parent_user_instance.email} (Student: {student_name_for_email})")
                            
                            if not dry_run:
                                send_invoice_issued_email_task.delay(
                                    parent_user_instance.id,
                                    tenant_public_instance.schema_name,
                                    invoice_details_dict,
                                    student_name_for_email
                                )
                                total_notifications_queued += 1
                            else:
                                self.stdout.write(f"      (Dry Run) Would send email to Parent ID {parent_user_instance.id} for schema {tenant_public_instance.schema_name}")
                        
                        if not dry_run:
                            # Mark invoice as notification sent
                            invoice.notification_sent_at = timezone.now()
                            invoice.save(update_fields=['notification_sent_at'])
                            self.stdout.write(f"      Marked Invoice {invoice.invoice_number} as notification_sent.")

                except Exception as e:
                    logger.error(f"Error processing invoice notifications for tenant {tenant_public_instance.name}: {e}", exc_info=True)
                    self.stderr.write(self.style.ERROR(f"  Error in tenant {tenant_public_instance.name}: {e}"))
        
        self.stdout.write(self.style.SUCCESS(f"\nCommand Finished. Processed {total_invoices_processed} invoice(s). Queued {total_notifications_queued} notification(s)."))
        
        
        
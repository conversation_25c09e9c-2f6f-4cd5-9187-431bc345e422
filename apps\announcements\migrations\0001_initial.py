# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('content', models.TextField(verbose_name='Content')),
                ('target_all_tenant_staff', models.BooleanField(default=False, verbose_name='Target All Staff in this School')),
                ('target_all_tenant_parents', models.BooleanField(default=False, verbose_name='Target All Parents in this School')),
                ('is_global', models.BooleanField(default=False, help_text='Check if this announcement is for multiple/all schools or platform admins.', verbose_name='Platform-Wide Announcement')),
                ('target_global_audience_type', models.CharField(blank=True, choices=[('ALL_TENANT_ADMINS', 'All School Admins/Owners'), ('ALL_PLATFORM_USERS', 'All Platform Users (Rare)')], help_text='Select audience if this is a platform-wide announcement.', max_length=50, null=True, verbose_name='Global Target Audience')),
                ('publish_date', models.DateTimeField(db_index=True, default=django.utils.timezone.now, help_text='Announcement becomes visible from this date/time.', verbose_name='Publish Date')),
                ('expiry_date', models.DateTimeField(blank=True, db_index=True, help_text='Optional: Announcement will be hidden after this date/time.', null=True, verbose_name='Expiry Date')),
                ('is_published', models.BooleanField(db_index=True, default=True, help_text='Uncheck to keep as a draft. Only published announcements are shown.', verbose_name='Is Published')),
                ('is_sticky', models.BooleanField(db_index=True, default=False, help_text='Sticky announcements may appear more prominently or at the top.', verbose_name='Is Sticky/Pinned')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Announcement',
                'verbose_name_plural': 'Announcements',
                'ordering': ['-is_sticky', '-publish_date', '-created_at'],
                'permissions': [('view_announcements_module', 'Can view the main Announcements module link')],
            },
        ),
    ]

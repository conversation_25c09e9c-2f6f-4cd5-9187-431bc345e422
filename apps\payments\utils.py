# D:\school_fees_saas_v2\apps\payments\utils.py

import logging
from decimal import Decimal
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Import necessary models ---
# Use try-except blocks for robustness in case apps are not fully loaded during certain checks
try:
    from apps.payments.models import Payment, PaymentAllocation, PaymentMethod
    from apps.fees.models import Invoice
    from apps.accounting.models import JournalEntry, JournalLine, Account
    from apps.schools.models import StaffUser
    from apps.students.models import ParentUser, Student
except ImportError as e:
    # This might happen during initial setup, but should not happen in a running app
    logging.critical(f"Critical import error in payments.utils: {e}")
    Payment = PaymentAllocation = PaymentMethod = Invoice = JournalEntry = JournalLine = Account = StaffUser = ParentUser = Student = None


logger = logging.getLogger(__name__)

def record_payment(
    invoice: Invoice,
    amount_paid: Decimal,
    payment_method: PaymentMethod,
    payment_date: timezone.datetime = None,
    notes: str = None,
    reference_number: str = None,
    processed_by: StaffUser = None,
    parent_payer: ParentUser = None,
    student_payer: Student = None
) -> Payment:
    """
    Records a payment against a specific invoice and creates the corresponding
    accounting journal entry.

    This function should be the single point of entry for recording all payments.
    It ensures data integrity by using a database transaction.

    Args:
        invoice (Invoice): The invoice being paid.
        amount_paid (Decimal): The amount of the payment.
        payment_method (PaymentMethod): The method used for payment.
        payment_date (datetime, optional): The date of the payment. Defaults to now().
        notes (str, optional): Any notes about the payment.
        reference_number (str, optional): A transaction ID or reference.
        processed_by (StaffUser, optional): The staff member who processed the payment.
        parent_payer (ParentUser, optional): The parent who made the payment.
        student_payer (Student, optional): The student for whom the payment was made (often same as invoice.student).

    Returns:
        Payment: The newly created Payment object, or None if an error occurred.
    """

    if not all([Payment, Invoice, JournalEntry, JournalLine, Account]):
        logger.error("record_payment: One or more critical models failed to import. Aborting.")
        # In a real scenario, you might want to raise a specific exception.
        return None

    if not payment_date:
        payment_date = timezone.now()
        
    if not student_payer:
        student_payer = invoice.student

    # Get the critical GL accounts from the linked payment method or default settings
    # This assumes payment_method.linked_account is the asset account (e.g., Bank, Cash)
    asset_account = payment_method.linked_account
    
    # This assumes you have a way to get the Accounts Receivable account.
    # A robust way is to link it in SchoolProfile or a global settings model.
    # For now, let's try to get it by its well-known code.
    try:
        # NOTE: Make sure an Account with code 'ACCOUNTS_RECEIVABLE' exists!
        # This was part of your '0005_seed_account_types.py' data.
        # It's better to fetch this from a setting on SchoolProfile.
        accounts_receivable_account = Account.objects.get(code='1200')
    except Account.DoesNotExist:
        logger.error(f"FATAL: The 'Accounts Receivable' control account (code: 1200) is not defined in the Chart of Accounts. Cannot record payment.")
        # In a real app, this should probably raise a configuration error.
        return None
        
    if not asset_account:
        logger.error(f"FATAL: The payment method '{payment_method.name}' is not linked to an asset account (e.g., Bank/Cash). Cannot record payment.")
        return None

    try:
        with transaction.atomic():
            # 1. Create the Payment record
            payment = Payment.objects.create(
                student=student_payer,
                parent_payer=parent_payer,
                amount=amount_paid,
                payment_date=payment_date,
                payment_method=payment_method,
                transaction_reference=reference_number,
                notes=notes,
                status=Payment.STATUS_COMPLETED, # Assume direct completion for this util
                processed_by_staff=processed_by,
            )
            logger.info(f"Created Payment object {payment.pk} for amount {amount_paid}.")

            # 2. Create the PaymentAllocation record
            PaymentAllocation.objects.create(
                payment=payment,
                invoice=invoice,
                amount_allocated=amount_paid
            )
            logger.info(f"Allocated payment {payment.pk} to invoice {invoice.pk}.")

            # 3. Update the Invoice
            invoice.amount_paid += amount_paid
            
            # Recalculate balance for precision, instead of invoice.balance_due -= amount_paid
            new_balance_due = invoice.total_amount - invoice.amount_paid
            
            if new_balance_due <= Decimal('0.00'):
                # Handle potential small floating point inaccuracies
                invoice.status = Invoice.InvoiceStatus.PAID
                logger.info(f"Invoice {invoice.pk} status updated to PAID.")
            else:
                invoice.status = Invoice.InvoiceStatus.PARTIALLY_PAID
                logger.info(f"Invoice {invoice.pk} status updated to PARTIALLY_PAID.")
                
            invoice.save(update_fields=['amount_paid', 'status'])
            
            # 4. Create the Accounting Journal Entry
            je_description = f"Payment for Invoice #{invoice.invoice_number} for {invoice.student.full_name}"
            journal_entry = JournalEntry.objects.create(
                date=payment_date.date() if hasattr(payment_date, 'date') else payment_date,
                narration=je_description,
                entry_type=JournalEntry.EntryTypeChoices.PAYMENT,
                status=JournalEntry.StatusChoices.POSTED,
                created_by=processed_by,
            )

            # Create Debit and Credit lines
            # Debit: Asset account (e.g., Bank) is increased
            JournalLine.objects.create(
                journal_entry=journal_entry,
                account=asset_account,
                debit_amount=amount_paid,
                credit_amount=Decimal('0.00'),
                description=f"Debit from Invoice #{invoice.invoice_number}"
            )
            # Credit: Accounts Receivable is decreased
            JournalLine.objects.create(
                journal_entry=journal_entry,
                account=accounts_receivable_account,
                debit_amount=Decimal('0.00'),
                credit_amount=amount_paid,
                description=f"Credit for Invoice #{invoice.invoice_number}"
            )
            logger.info(f"Created and posted Journal Entry {journal_entry.pk} for payment {payment.pk}.")

            return payment

    except Exception as e:
        logger.error(f"An error occurred during the record_payment transaction for Invoice {invoice.pk}: {e}", exc_info=True)
        # The transaction.atomic() block will automatically roll back all database changes.
        return None
    
    
    
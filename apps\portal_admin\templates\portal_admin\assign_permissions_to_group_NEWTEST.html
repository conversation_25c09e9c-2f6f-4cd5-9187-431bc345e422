{# apps/portal_admin/templates/portal_admin/assign_permissions_to_group_NEWTEST.html - Premium Design Version #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block title %}{{ view_title|default:"Assign Permissions" }}{% endblock %}

{% block page_specific_css %}
{{ block.super }}
<style>
    /* Premium Form Design - Assign Permissions to Group */
    .premium-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-card-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-card-header h3 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
    }

    .premium-card-body {
        padding: 2.5rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .breadcrumb-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: transparent;
    }

    .breadcrumb-item a {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .info-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #2196f3;
    }

    .info-section h5 {
        color: #1976d2;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .info-section p {
        color: #424242;
        margin: 0;
        line-height: 1.6;
    }

    .role-info-card {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #ffc107;
        text-align: center;
    }

    .role-info-card h5 {
        color: #856404;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .role-info-card .role-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #495057;
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        display: inline-block;
        margin-top: 0.5rem;
    }

    .permission-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .permission-category-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        height: fit-content;
        max-height: 400px;
        display: flex;
        flex-direction: column;
    }

    .permission-category-card:hover {
        border-color: #007bff;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.15);
    }

    .permission-category-header {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        padding: 1rem 1.25rem;
        font-weight: 600;
        font-size: 0.95rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .permission-category-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .permission-category-card:hover .permission-category-header::before {
        left: 100%;
    }

    .permission-count-badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .permission-category-body {
        padding: 1rem;
        overflow-y: auto;
        flex: 1;
        max-height: 300px;
    }

    .permission-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .permission-item:hover {
        background: rgba(0, 123, 255, 0.05);
        border-color: rgba(0, 123, 255, 0.2);
        transform: translateX(5px);
    }

    .permission-item:last-child {
        margin-bottom: 0;
    }

    .permission-checkbox {
        margin-right: 0.75rem;
        transform: scale(1.2);
        accent-color: #007bff;
    }

    .permission-checkbox:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .permission-label {
        font-size: 0.9rem;
        color: #495057;
        font-weight: 500;
        line-height: 1.4;
        cursor: pointer;
        flex: 1;
        transition: color 0.3s ease;
    }

    .permission-item:hover .permission-label {
        color: #007bff;
    }

    .permission-checkbox:checked + .permission-label {
        color: #007bff;
        font-weight: 600;
    }

    .btn {
        border-radius: 0.75rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    .selection-summary {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
        text-align: center;
    }

    .selection-summary h6 {
        color: #155724;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .selection-counter {
        font-size: 1.5rem;
        font-weight: 700;
        color: #28a745;
    }

    @media (max-width: 768px) {
        .premium-card-header {
            padding: 1.5rem;
        }

        .premium-card-header h3 {
            font-size: 1.5rem;
        }

        .premium-card-body {
            padding: 1.5rem;
        }

        .permission-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .permission-grid {
            margin-top: 1rem;
        }

        .permission-category-body {
            max-height: 250px;
        }
    }
</style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb-container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'portal_admin:group_list' %}">
                                <i class="bi bi-people-fill me-1"></i>{% trans "Roles" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            {% trans "Assign Permissions" %}
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h5><i class="bi bi-shield-check me-2"></i>{% trans "Permission Management" %}</h5>
                <p>
                    {% trans "Assign specific permissions to this role to control what actions users with this role can perform. Permissions are organized by application modules and models for easy management." %}
                </p>
            </div>

            <!-- Role Information Card -->
            <div class="role-info-card">
                <h5><i class="bi bi-person-badge me-2"></i>{% trans "Editing Permissions for Role" %}</h5>
                <div class="role-name">{{ group_obj.name }}</div>
            </div>

            <!-- Premium Form Card -->
            <div class="card premium-card">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-shield-lock me-2"></i>
                        {{ view_title|default:"Assign Permissions" }}
                    </h3>
                </div>
                <div class="premium-card-body">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate id="permissionAssignmentForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}{% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Selection Summary -->
                        <div class="selection-summary" id="selectionSummary">
                            <h6><i class="bi bi-check-circle me-2"></i>{% trans "Selected Permissions" %}</h6>
                            <div class="selection-counter" id="selectionCounter">0</div>
                            <small class="text-muted">{% trans "permissions selected" %}</small>
                        </div>

                        <!-- Permissions Grid -->
                        <div class="permission-grid">
                            {% for display_group_key, perms_list_from_view in grouped_perms_display_data.items %}
                                <div class="permission-category-card">
                                    <div class="permission-category-header">
                                        <span>
                                            <i class="bi bi-folder me-2"></i>{{ display_group_key }}
                                        </span>
                                        <span class="permission-count-badge">
                                            {{ perms_list_from_view|length }}
                                        </span>
                                    </div>
                                    <div class="permission-category-body">
                                        {% for perm_data_from_view in perms_list_from_view %}
                                            {% for checkbox_widget in form.permissions %}
                                                {% if checkbox_widget.data.value|stringformat:"s" == perm_data_from_view.id|stringformat:"s" %}
                                                <div class="permission-item">
                                                    <input
                                                        type="checkbox"
                                                        class="permission-checkbox"
                                                        name="permissions"
                                                        value="{{ perm_data_from_view.id }}"
                                                        id="{{ checkbox_widget.id_for_label }}"
                                                        {% if checkbox_widget.data.selected %}checked{% endif %}
                                                    >
                                                    <label class="permission-label" for="{{ checkbox_widget.id_for_label }}">
                                                        {{ perm_data_from_view.name|slice:"Can " }}
                                                    </label>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% empty %}
                                <div class="col-12">
                                    <div class="alert alert-info text-center">
                                        <i class="bi bi-info-circle me-2"></i>
                                        {% trans "No permissions available to assign in the defined groups." %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        {% if form.permissions.help_text %}
                            <div class="form-text text-muted mt-3">
                                <i class="bi bi-lightbulb me-1"></i>{{ form.permissions.help_text|safe }}
                            </div>
                        {% endif %}

                        {% if form.permissions.errors %}
                            <div class="invalid-feedback d-block mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {% for error in form.permissions.errors %}
                                    {{ error }}{% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'portal_admin:group_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-shield-check me-2"></i>{% trans "Save Permissions" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Permission selection counter
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    const selectionCounter = document.getElementById('selectionCounter');
    const selectionSummary = document.getElementById('selectionSummary');

    function updateSelectionCounter() {
        const checkedCount = document.querySelectorAll('.permission-checkbox:checked').length;
        selectionCounter.textContent = checkedCount;

        // Update summary card appearance based on selection
        if (checkedCount > 0) {
            selectionSummary.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            selectionSummary.style.borderLeftColor = '#28a745';
        } else {
            selectionSummary.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
            selectionSummary.style.borderLeftColor = '#6c757d';
        }
    }

    // Add event listeners to all checkboxes
    checkboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelectionCounter);
    });

    // Initial count
    updateSelectionCounter();

    // Form submission enhancement
    const form = document.getElementById('permissionAssignmentForm');
    const submitBtn = document.getElementById('submitBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Saving..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-shield-check me-2"></i>{% trans "Save Permissions" %}';
            }, 5000);
        });
    }

    // Enhanced checkbox interactions
    const permissionItems = document.querySelectorAll('.permission-item');
    permissionItems.forEach(function(item) {
        const checkbox = item.querySelector('.permission-checkbox');
        const label = item.querySelector('.permission-label');

        // Click anywhere on the item to toggle checkbox
        item.addEventListener('click', function(e) {
            if (e.target !== checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Visual feedback for checked state
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                item.classList.add('checked');
            } else {
                item.classList.remove('checked');
            }
        });

        // Initial state
        if (checkbox.checked) {
            item.classList.add('checked');
        }
    });

    // Category card interactions
    const categoryCards = document.querySelectorAll('.permission-category-card');
    categoryCards.forEach(function(card) {
        const checkboxes = card.querySelectorAll('.permission-checkbox');
        const header = card.querySelector('.permission-category-header');

        // Add select all functionality (double-click header)
        header.addEventListener('dblclick', function() {
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            checkboxes.forEach(function(cb) {
                cb.checked = !allChecked;
                cb.dispatchEvent(new Event('change'));
            });
        });

        // Add tooltip for double-click functionality
        header.setAttribute('title', '{% trans "Double-click to toggle all permissions in this category" %}');
        header.setAttribute('data-bs-toggle', 'tooltip');
        header.setAttribute('data-bs-placement', 'top');
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+A to select all permissions
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            const allCheckboxes = document.querySelectorAll('.permission-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            allCheckboxes.forEach(function(cb) {
                cb.checked = !allChecked;
                cb.dispatchEvent(new Event('change'));
            });
        }

        // Escape to cancel
        if (e.key === 'Escape') {
            const cancelBtn = document.querySelector('a[href*="group_list"]');
            if (cancelBtn) {
                cancelBtn.click();
            }
        }
    });

    // Search functionality (if needed in future)
    // This can be enhanced to add a search box to filter permissions

    console.log('Permission assignment form initialized with', checkboxes.length, 'permissions');
});
</script>
{% endblock %}










































{% comment %} {# D:\school_fees_saas_v2\apps\portal_admin\templates\portal_admin\assign_permissions_to_group.html - ULTRA RADICAL TEST #}
{% extends "tenant_base.html" %}
{% load core_tags %} {# Load this if you created the type_of filter in core_tags.py, otherwise remove #}

{% block title %}ULTRA RADICAL TEST - {{ view_title|default:"Assign Permissions" }}{% endblock %}

{% block tenant_specific_content %}

<div class="container mt-4">
    <h1>ULTRA RADICAL TEST - {{ view_title }}</h1>
    <p>Editing permissions for role: <strong>{{ group_obj.name }} (PK: {{ group_obj.pk }})</strong></p>
    
    {% include "partials/_messages.html" %} {# Assuming this partial exists for Django messages #}

    <hr>
    <h2 style="color: red;">RAW CONTEXT VARIABLE CHECKER (FROM TEMPLATE):</h2>

    <p><strong>view_title:</strong> {{ view_title }} (Type: {{ view_title|type_of }})</p>
    <p><strong>group_obj:</strong> {{ group_obj }} (Type: {{ group_obj|type_of }})</p>
    
    <p><strong>assigned_permission_ids:</strong> {{ assigned_permission_ids }} (Type: {{ assigned_permission_ids|type_of }}, Length: {{ assigned_permission_ids|length }})</p>
    <p style="font-size: 0.8em; word-break: break-all;">First 10 Assigned IDs: {% for id_val in assigned_permission_ids|slice:":10" %}{{ id_val }},{% endfor %}</p>

    <p><strong>grouped_permissions_dict:</strong> (Type: {{ grouped_permissions_dict|type_of }}, Items Length: {{ grouped_permissions_dict.items|length }})</p>
    {% if grouped_permissions_dict.items|length > 0 %}
        {% with first_item_tuple=grouped_permissions_dict.items|first %}
            <p style="font-size: 0.8em;">First Group Key in dict: {{ first_item_tuple.0 }}</p>
        {% endwith %}
    {% else %}
        <p style="font-size: 0.8em; color: orange;">grouped_permissions_dict seems empty or has no items.</p>
    {% endif %}
    
    <p><strong>form:</strong> (Type: {{ form|type_of }})</p>
    {% if form %}
        <p><strong>form.permissions (BoundField object):</strong> (Type: {{ form.permissions|type_of }})</p>
        {% if form.permissions and form.permissions.field %}
            <p><strong>form.permissions.field.choices length:</strong> {{ form.permissions.field.choices|length }}</p>
            <p><strong>form.permissions.initial (raw from form object):</strong> {{ form.permissions.initial }}</p>
            <p style="font-size: 0.8em; word-break: break-all;">First 5 Initial PKs from form.permissions.initial: 
                {% for p_init in form.permissions.initial|slice:":5" %}{{ p_init.pk }},{% endfor %}
                (Count: {{ form.permissions.initial|length }})
            </p>
        {% else %}
            <p style="color: orange;">form.permissions is present, but no .field attribute or .field.choices.</p>
        {% endif %}
    {% else %}
        <p style="color: red; font-weight: bold;">TEMPLATE ERROR: `form` IS NOT IN CONTEXT.</p>
    {% endif %}
    
    <hr>
    <h3>Trying to Render `{{ "{{ form.permissions " }}` directly:</h3>
    {% if form.permissions %}
        <div style="border:1px dashed blue; padding:10px; max-height: 200px; overflow-y: auto;">
            {{ form.permissions }} {# This should render all checkboxes from CheckboxSelectMultiple #}
        </div>
    {% else %}
        <p style="color:red;">Cannot render `{{ "{{ form.permissions " }}}` because 'form' or 'form.permissions' is not available.</p>
    {% endif %}
    <hr>

    <h3>Trying to loop `grouped_permissions_dict` (Simplified Checkboxes):</h3>
    {% if grouped_permissions_dict.items|length > 0 %}
        <p>Outer loop (`grouped_permissions_dict`) will run {{ grouped_permissions_dict.items|length }} times.</p>
        {% for display_group_key, perms_list_in_group in grouped_permissions_dict.items %}
            <div style="border: 1px solid #ccc; margin-bottom: 10px; padding: 5px;">
                <h4 style="color: navy;">Group Key: {{ display_group_key }} ({{ perms_list_in_group|length }} perms)</h4>
                {% if perms_list_in_group|length > 0 %}
                    {% for perm_data in perms_list_in_group|slice:":3" %} {# Show first 3 perms in each group for brevity #}
                        <div style="margin-left: 15px; border-bottom: 1px dotted #eee; padding-bottom: 5px;">
                            Perm ID: {{ perm_data.id }}, Name: {{ perm_data.name }} <br>
                            Is checked? (using `assigned_permission_ids` from context): 
                            {% if perm_data.id in assigned_permission_ids %}<strong>YES</strong>{% else %}NO{% endif %}
                            <br>
                            <input type="checkbox" name="permissions_test_loop" value="{{ perm_data.id }}" 
                                id="test_loop_id_perm_{{ perm_data.id }}"
                                {% if perm_data.id in assigned_permission_ids %}checked{% endif %}>
                            <label for="test_loop_id_perm_{{ perm_data.id }}">{{ perm_data.name|slice:"Can " }}</label>
                        </div>
                    {% endfor %}
                    {% if perms_list_in_group|length > 3 %}... and more in this group ...{% endif %}
                {% else %}
                    <p><em>No permissions listed in this specific display group: {{ display_group_key }}</em></p>
                {% endif %}
            </div>
        {% empty %}
            <p style="color:red; font-weight: bold;">TEMPLATE ERROR: Loop over `grouped_permissions_dict.items` was empty.</p>
        {% endfor %}
    {% else %}
        <p style="color:red; font-weight: bold;">TEMPLATE ERROR: `grouped_permissions_dict.items` is not available or is empty (checked by length).</p>
    {% endif %}
    
    <hr>
    <form method="post" class="mt-3">
        {% csrf_token %}
        <p><em>(Form submit button below is for testing the POST, actual checkboxes are above if rendered)</em></p>
        <button type="submit" class="btn btn-success mt-1">Test Submit</button>
        <a href="{% url 'portal_admin:group_list' %}" class="btn btn-secondary mt-1">Cancel</a>
    </form>
</div>

{# 
Custom filter 'type_of' (if you want to use it for (Type: ...) debug lines):
--------------------------------------------------------------------------
1. Create/Open: D:\school_fees_saas_v2\apps\core\templatetags\core_tags.py
(Ensure apps/core/templatetags/__init__.py exists and is empty)

2. Add to core_tags.py:
from django import template
register = template.Library()

@register.filter
def type_of(value):
    return value.__class__.__name__

3. At the top of THIS template, ensure you have: {% load core_tags %}
(It's already there in this pasted version)
-------------------------------------------------------------------------- 
#}
{% endblock %} {% endcomment %}













{% comment %} {# D:\school_fees_saas_v2\apps\portal_admin\templates\portal_admin\assign_permissions_to_group.html - RADICAL TEST #}
{% extends "tenant_base.html" %}

{% block title %}RADICAL TEST - {{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>RADICAL TEST - {{ view_title }}</h1>
    <p>Editing permissions for role: <strong>{{ group_obj.name }} (PK: {{ group_obj.pk }})</strong></p>
    {% include "partials/_messages.html" %}

    <hr>
    <h2>Context Data Dump (from Template):</h2>
    <p><strong>Group Object Name:</strong> {{ group_obj.name }}</p>
    <p><strong>Assigned Permission IDs Count (from context `assigned_permission_ids`):</strong> {{ assigned_permission_ids|length }}</p>
    <p><strong>First 5 Assigned IDs:</strong> 
        {% for id_val in assigned_permission_ids|slice:":5" %}{{ id_val }},{% endfor %}
    </p>
    <p><strong>Grouped Permissions Dict (Keys Count from `grouped_permissions_dict`):</strong> {{ grouped_permissions_dict.items|length }}</p>
    
    {% if grouped_permissions_dict %}
        {% with first_item_tuple=grouped_permissions_dict.items|first %}
            {% if first_item_tuple %}
                <p><strong>First Group Key:</strong> {{ first_item_tuple.0 }}</p> {# .0 for key #}
                {% with first_group_perms_list=first_item_tuple.1 %} {# .1 for value (list of perm dicts) #}
                    <p><strong>Permissions in First Group (Count):</strong> {{ first_group_perms_list|length }}</p>
                    {% if first_group_perms_list %}
                        <p><strong>First Permission in First Group (ID):</strong> {{ first_group_perms_list.0.id }}</p>
                        <p><strong>First Permission in First Group (Name):</strong> {{ first_group_perms_list.0.name }}</p>
                    {% else %}
                        <p><em>First group's permission list is empty.</em></p>
                    {% endif %}
                {% endwith %}
            {% else %}
                <p><em>Could not get first item from grouped_permissions_dict.items.</em></p>
            {% endif %}
        {% endwith %}
    {% else %}
        <p style="color: red;"><strong>TEMPLATE WARNING: `grouped_permissions_dict` is EMPTY or None.</strong></p>
    {% endif %}
    <hr>

    <h2>Form 'permissions' Field Debug (from Template):</h2>
    <p><strong>Is form.permissions available in template?</strong> {% if form.permissions %}YES{% else %}NO{% endif %}</p>
    {% if form.permissions %}
        <p><strong>Form `permissions` Field Initial Value (PKs from form.initial):</strong> 
            {% if form.initial.permissions %}
                {% for p_initial in form.initial.permissions %}{{ p_initial.pk }},{% endfor %}
                (Count: {{ form.initial.permissions.count }})
            {% else %}
                form.initial.permissions is empty or None.
            {% endif %}
        </p>
        <p><strong>Form `permissions` Field Total Choices Count:</strong> {{ form.permissions.field.choices|length }}</p>
        <p><strong>First 3 choices from `form.permissions` field:</strong></p>
        <ul>
        {% for choice_val, choice_label in form.permissions.field.choices|slice:":3" %}
            <li>Value: {{ choice_val }} (Type: {{ choice_val|type }}), Label: {{ choice_label }}</li>
        {% endfor %}
        </ul>
        
        {# Attempt to render the whole form field directly - this should show all 267 checkboxes if form is okay #}
        <h3>Direct rendering of {{ form.permissions.label_tag }} {{ form.permissions.name }}</h3>
        {{ form.permissions }} 
        {# This above line should render all checkboxes if the form field is correctly set up #}
        {# and its widget is CheckboxSelectMultiple #}

    {% endif %}
    <hr>

    <h3>Attempting to Iterate `grouped_permissions_dict` (Simplified Checkboxes):</h3>
    {% if grouped_permissions_dict.items|length > 0 %}
        <p>Outer loop (`grouped_permissions_dict`) will run {{ grouped_permissions_dict.items|length }} times.</p>
        {% for display_group_key, perms_list_in_group in grouped_permissions_dict.items %}
            <div style="border: 1px solid #ccc; margin-bottom: 10px; padding: 5px;">
                <h4 style="color: navy;">Group Key: {{ display_group_key }} ({{ perms_list_in_group|length }} perms)</h4>
                {% for perm_data in perms_list_in_group|slice:":3" %} 
                    <div style="margin-left: 15px;">
                        Perm ID: {{ perm_data.id }}, Name: {{ perm_data.name }}
                        - Checked?: {% if perm_data.id in assigned_permission_ids %}YES{% else %}NO{% endif %}
                        <br>
                        <input type="checkbox" name="permissions_test" value="{{ perm_data.id }}" 
                            id="test_id_perm_{{ perm_data.id }}"
                            {% if perm_data.id in assigned_permission_ids %}checked{% endif %}>
                        <label for="test_id_perm_{{ perm_data.id }}">{{ perm_data.name|slice:"Can " }}</label>
                    </div>
                {% endfor %}
            </div>
        {% empty %}
            <p style="color: red; font-weight: bold;">TEMPLATE ERROR: `grouped_permissions_dict` IS EMPTY (in loop `empty` block)!</p>
        {% endfor %}
    {% else %}
        <p style="color: red; font-weight: bold;">TEMPLATE ERROR: `grouped_permissions_dict` HAS NO ITEMS (checked by length)!</p>
    {% endif %}

    <form method="post" class="mt-3"> {# Keep form tags for CSRF and button if needed for other tests #}
        {% csrf_token %}
        <p><em>(Form submit button below is for testing the POST, actual checkboxes are above)</em></p>
        <button type="submit" class="btn btn-success mt-1">Save (Test Submit Button)</button>
        <a href="{% url 'portal_admin:group_list' %}" class="btn btn-secondary mt-1">Cancel</a>
    </form>
</div>
{% endblock %} {% endcomment %}




{% comment %} {# templates/portal_admin/assign_permissions_to_group.html #}
{% extends "tenant_base.html" %}

{% block title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Editing permissions for role: <strong>{{ group_obj.name }}</strong></p>
    {% include "partials/_messages.html" %}

    <form method="post">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}

        <div class="mb-3">
            <label class="form-label fw-bold">{{ form.permissions.label|default:"Permissions" }}:</label>

            {# --- TEMPLATE DEBUG - Visible on Page --- #}
            <div style="background-color: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin-bottom: 15px;">
                <h4>Template Debug Info (Visible on Page):</h4>
                <p><strong>Context `group_obj.name`:</strong> {{ group_obj.name }}</p>
                <p><strong>Context `assigned_permission_ids` Count:</strong> {{ assigned_permission_ids|length }}</p>
                <p><strong>Context `grouped_permissions_dict` Items Count:</strong> {{ grouped_permissions_dict.items|length }}</p>
                <p><strong>Context `form.permissions` Field Choices Count:</strong> {{ form.permissions.field.choices|length }}</p>
                {% if not grouped_permissions_dict.items %}
                    <p style="color: red;"><strong>WARNING: `grouped_permissions_dict` has no items!</strong></p>
                {% endif %}
                {% if not form.permissions.field.choices %}
                    <p style="color: red;"><strong>WARNING: `form.permissions` has no choices!</strong></p>
                {% endif %}
            </div>
            {# --- END TEMPLATE DEBUG --- #}

            {# Build a dictionary of checkbox widgets from the form for easier lookup #}
            {% with checkbox_widgets_map={} %}
                {% for cb_widget in form.permissions %}
                    {% Laufe an den Start ## Hier wird ein Dictionary erstellt, das Checkbox-Widgets anhand ihrer Werte (Permission-IDs) zuordnet. Dies geschieht einmal außerhalb der Hauptschleifen, um die Effizienz zu verbessern. ## Fix for POE thinking this is German: Build a dictionary that maps checkbox widgets by their values (permission IDs). This is done once outside the main loops to improve efficiency. %}
                    {% if checkbox_widgets_map.update({(cb_widget.data.value|stringformat:"s"): cb_widget}) %}{% endif %}
                {% endfor %}

                <div class="row">
                {% for display_group_key, perms_list_from_view in grouped_permissions_dict.items %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                {{ display_group_key }} 
                                <span class="badge bg-secondary rounded-pill">{{ perms_list_from_view|length }}</span>
                            </div>
                            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                                {% if not perms_list_from_view %}
                                    <p class="text-muted small px-2 py-1"><em>(No permissions in this category)</em></p>
                                {% endif %}
                                {% for perm_data in perms_list_from_view %} {# perm_data is {'id': ..., 'name': ...} #}
                                    {% with target_perm_id_str=perm_data.id|stringformat:"s" %}
                                        {% if target_perm_id_str in checkbox_widgets_map %}
                                            {% with checkbox_widget_to_render=checkbox_widgets_map|get_item:target_perm_id_str %}
                                                <div class="form-check">
                                                    {{ checkbox_widget_to_render.tag }} 
                                                    <label class="form-check-label small" for="{{ checkbox_widget_to_render.id_for_label }}">
                                                        {{ perm_data.name|slice:"Can " }}
                                                    </label>
                                                </div>
                                            {% endwith %}
                                        {% else %}
                                            <p style="color:orange; font-size:0.8em;">Warning: Perm ID {{ perm_data.id }} ('{{ perm_data.name }}') from view not found in form widgets map.</p>
                                        {% endif %}
                                    {% endwith %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <p class="alert alert-warning">No permission groups to display. `grouped_permissions_dict` might be empty.</p>
                    </div>
                {% endfor %} {# End of grouped_permissions_dict loop #}
                </div>
            {% endwith %} {# End of checkbox_widgets_map scope #}

            {% if form.permissions.help_text %}
                <div class="form-text text-muted">{{ form.permissions.help_text }}</div>
            {% endif %}
            {% if form.permissions.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.permissions.errors %}
                        {{ error }}<br>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <button type="submit" class="btn btn-success">Save Permissions</button>
        <a href="{% url 'portal_admin:group_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %} {% endcomment %}






{% comment %} {# templates/portal_admin/assign_permissions_to_group.html #}
{% extends "tenant_base.html" %}

{% block title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Editing permissions for role: <strong>{{ group_obj.name }}</strong></p>
    {% include "partials/_messages.html" %}

    <form method="post">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}

        <div class="mb-3">
            <label class="form-label fw-bold">{{ form.permissions.label|default:"Permissions" }}:</label>

            {# --- TEMPLATE DEBUG - Visible on Page --- #}
            <div style="background-color: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin-bottom: 15px;">
                <h4>Template Debug Info:</h4>
                <p><strong>Assigned Permission IDs Count (from context `assigned_permission_ids`):</strong> {{ assigned_permission_ids|length }}</p>
                <p><strong>Grouped Display Keys Count (from `grouped_permissions_for_display`):</strong> {{ grouped_permissions_for_display.items|length }}</p>
                {% if grouped_permissions_for_display.items|length > 0 %}
                    <p>Looping through grouped_permissions_for_display...</p>
                {% else %}
                    <p style="color: red;"><strong>WARNING: `grouped_permissions_for_display` is empty or has no items in template!</strong></p>
                {% endif %}
            </div>
            {# --- END TEMPLATE DEBUG --- #}

            <div class="row">
            {% for display_group_key, perms_list_in_group in grouped_permissions_for_display.items %}
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header bg-light">
                            {{ display_group_key }} 
                            <span class="badge bg-secondary rounded-pill">{{ perms_list_in_group|length }}</span>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            {% if not perms_list_in_group %}
                                <p class="text-muted small px-2 py-1"><em>(No permissions in this category)</em></p>
                            {% endif %}
                            {% for perm_data in perms_list_in_group %} {# perm_data is {'id': ..., 'name': ...} #}
                                <div class="form-check">
                                    <input type="checkbox" name="permissions" value="{{ perm_data.id }}"
                                        class="form-check-input" id="id_perm_{{ perm_data.id }}"
                                        {% if perm_data.id in assigned_permission_ids %}checked{% endif %} 
                                        >
                                    <label class="form-check-label small" for="id_perm_{{ perm_data.id }}">
                                        {{ perm_data.name|slice:"Can " }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col-12">
                    <p class="alert alert-warning">No permission groups to display. Check view's `grouped_permissions_for_display` context variable.</p>
                </div>
            {% endfor %} {# End of grouped_permissions_for_display loop #}
            </div>
            {% if form.permissions.help_text %}
                <div class="form-text text-muted">{{ form.permissions.help_text }}</div>
            {% endif %}
            {% if form.permissions.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.permissions.errors %}
                        {{ error }}<br>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <button type="submit" class="btn btn-success">Save Permissions</button>
        <a href="{% url 'portal_admin:group_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %} {% endcomment %}



















{% comment %} {# templates/portal_admin/assign_permissions_to_group.html #}
{% extends "tenant_base.html" %}

{% block title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Editing permissions for role: <strong>{{ group_obj.name }}</strong></p>
    {% include "partials/_messages.html" %}

    <form method="post">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}

        <div class="mb-3">
            <label class="form-label fw-bold">{{ form.permissions.label|default:"Permissions" }}:</label>

            {# --- TEMPLATE DEBUG --- #}
            <p style="background-color: #eee; padding: 5px;">
                DEBUG: Assigned Permission IDs Count (from context): {{ assigned_permission_ids|length }} <br>
                DEBUG: Grouped Display Keys Count: {{ grouped_permissions_for_display.items|length }}
            </p>
            {# --- END TEMPLATE DEBUG --- #}

            <div class="row">
            {% for display_group_key, perms_list_in_group in grouped_permissions_for_display.items %}
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header bg-light">{{ display_group_key }}</div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            {% if not perms_list_in_group %}
                                <p class="text-muted small"><em>No permissions in this display group.</em></p>
                            {% endif %}
                            {% for perm_data in perms_list_in_group %} {# perm_data is {'id': ..., 'name': ...} #}
                                <div class="form-check">
                                    <input type="checkbox" name="permissions" value="{{ perm_data.id }}"
                                        class="form-check-input" id="id_perm_{{ perm_data.id }}"
                                        {% if perm_data.id in assigned_permission_ids %}checked{% endif %} 
                                        >
                                    <label class="form-check-label small" for="id_perm_{{ perm_data.id }}">
                                        {{ perm_data.name|slice:"Can " }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% empty %} {# Fallback if grouped_permissions_for_display is empty #}
                <div class="col-12">
                    <p class="text-warning">No permissions available for display based on current filters.</p>
                </div>
            {% endfor %} {# End of grouped_permissions_for_display loop #}
            </div>
            {% if form.permissions.help_text %}
                <div class="form-text text-muted">{{ form.permissions.help_text }}</div>
            {% endif %}
            {% if form.permissions.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.permissions.errors %}
                        {{ error }}<br>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <button type="submit" class="btn btn-success">Save Permissions</button>
        <a href="{% url 'portal_admin:group_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %} {% endcomment %}











# apps/accounting/management/commands/rebuild_mptt_trees_custom.py
from django.core.management.base import BaseCommand
from django.apps import apps
from django.db import connection # To get current schema
from mptt.models import MPTTModel
from django_tenants.utils import schema_context, get_tenant_model, get_public_schema_name

class Command(BaseCommand):
    help = 'Rebuild MPTT trees for specified MPTT-enabled models FOR ALL TENANTS'

    def add_arguments(self, parser):
        parser.add_argument(
            'model_path',
            type=str,
            help='MPTT Model in format app_label.ModelName (e.g., accounting.Account)'
        )

    def handle(self, *args, **options):
        model_path_str = options['model_path']
        TenantModel = get_tenant_model()
        public_schema_name = get_public_schema_name()

        try:
            app_label, model_name = model_path_str.split('.')
        except ValueError:
            self.stderr.write(self.style.ERROR(f"Invalid model_path: '{model_path_str}'. Use 'app_label.ModelName'."))
            return

        try:
            model_class = apps.get_model(app_label, model_name)
        except LookupError:
            self.stderr.write(self.style.ERROR(f"Model '{model_path_str}' not found."))
            return

        if not issubclass(model_class, MPTTModel) or not hasattr(model_class.objects, 'rebuild'):
            self.stderr.write(self.style.ERROR(f"Model '{model_path_str}' is not a valid MPTT model or lacks rebuild()."))
            return

        tenants = TenantModel.objects.all()
        if not tenants.exists():
            self.stdout.write(self.style.WARNING("No tenants found. Skipping MPTT rebuild for tenant models."))
            # Optionally, you could try to rebuild if the model is in public schema here,
            # but Account is likely tenant-specific.
            return

        self.stdout.write(f"Found {tenants.count()} tenant(s).")

        for tenant in tenants:
            schema_name = tenant.schema_name
            with schema_context(schema_name):
                self.stdout.write(self.style.SUCCESS(
                    f"--- Rebuilding MPTT tree for {model_path_str} in schema '{schema_name}' ---"
                ))
                try:
                    # Access the model manager again within the schema_context
                    # to ensure it's scoped correctly, though model_class.objects should work
                    # if the connection's search_path is set by schema_context.
                    # current_model_manager = apps.get_model(app_label, model_name).objects
                    # current_model_manager.rebuild()
                    model_class.objects.rebuild() # This should work fine within schema_context
                    self.stdout.write(
                        self.style.SUCCESS(f"Successfully rebuilt MPTT tree for {model_path_str} in schema '{schema_name}'")
                    )
                except Exception as e:
                    self.stderr.write(self.style.ERROR(
                        f"An error occurred while rebuilding tree for {model_path_str} in schema '{schema_name}': {e}"
                    ))
        
        self.stdout.write(self.style.SUCCESS("--- MPTT Rebuild for all tenants completed. ---"))
        
        
        
{# D:\school_fees_saas_v2\templates\fees\fee_structure_confirm_delete.html #}
{% extends "tenant_base.html" %}

{% load static %}

{% block title %}
    Confirm Delete Fee Structure - {{ request.tenant.name }}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">Confirm Deletion</h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %} {# For displaying ProtectedError messages #}

                    {# Use object, as Delete<PERSON>iew provides the instance as 'object' by default #}
                    {# Or use fee_structure if you set context_object_name explicitly #}
                    <p>Are you sure you want to delete the Fee Structure:
                        <strong class="text-danger">{{ object.name }}</strong>?
                    </p>
                    <p>
                        <strong>Academic Year:</strong> {{ object.academic_year.name }}<br>
                        <strong>Class:</strong> {{ object.school_class.name }}<br>
                        {% if object.term %}
                            <strong>Term:</strong> {{ object.term.name }}<br>
                        {% endif %}
                        <strong>Total Amount:</strong> {{ object.total_amount|default:"0.00" }}
                    </p>

                    {% if not is_deletable %} {# is_deletable passed from view's get_context_data #}
                        <div class="alert alert-warning" role="alert">
                            <strong>Warning:</strong> This fee structure cannot be deleted because it is currently linked to student allocations or generated invoices. You must remove those links first.
                        </div>
                        <a href="{% url 'fees:fee_structure_list' %}" class="btn btn-secondary">Back to List</a>
                    {% else %}
                        <p class="text-danger fw-bold">This action will also delete all associated fee items and cannot be undone.</p>
                        <form method="post">
                            {% csrf_token %}
                            <div class="d-flex justify-content-end mt-3">
                                <a href="{% url 'fees:fee_structure_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
                                <button type="submit" class="btn btn-danger">Yes, Delete this Structure</button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}














{% comment %} {# D:\school_fees_saas_v2\templates\fees\fee_structure_confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - {{ request.tenant.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" %}

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>{{ view_title }}</h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %} {# For ProtectedError messages #}

                    <p class="lead">Are you sure you want to delete the fee structure: <strong>"{{ fee_structure.name }}"</strong>?</p>
                    <p>This will also delete all associated fee items within this structure. This action cannot be undone.</p>
                    
                    {% if fee_structure.student_allocations.exists %} {# Example check if it's used #}
                        <div class="alert alert-warning" role="alert">
                            <h5 class="alert-heading"><i class="bi bi-exclamation-circle-fill"></i> Warning!</h5>
                            This fee structure is currently allocated to one or more students. Deleting it might affect existing records.
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'fees:fee_structure_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash3-fill me-1"></i>Yes, Delete
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} {% endcomment %}


{# D:\school_fees_saas_v2\apps\platform_management\templates\platform_management\maintenance_mode_form.html #}
{% extends "platform_management/platform_admin_base.html" %}
{% load static i18n %}

{% block platform_admin_page_title %}{{ view_title|default:_("Maintenance Mode") }}{% endblock %}

{% block extra_platform_admin_css %}
    {{ block.super }}
    <style>
        .premium-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .premium-card-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            text-align: center;
        }

        .premium-card-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .premium-card-body {
            background: white;
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        }

        .form-floating > label {
            padding: 1rem 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }

        .icon-input {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .form-check {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .form-check-input {
            width: 1.5rem;
            height: 1.5rem;
            margin-top: 0.125rem;
            border: 2px solid #667eea;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
            border-radius: 1rem;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 0.5rem;
        }

        .btn {
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .fieldset-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            margin: 2rem 0 1.5rem 0;
            text-align: center;
        }

        .fieldset-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .maintenance-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .maintenance-warning h6 {
            color: #856404;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .maintenance-warning p {
            color: #664d03;
            margin: 0;
            font-size: 0.9rem;
        }

        .security-section {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .security-section h6 {
            color: #721c24;
            font-weight: 600;
            margin-bottom: 1rem;
        }
    </style>
{% endblock %}

{% block platform_admin_page_content %}
<div class="pagetitle">
    <h1>{{ view_title|default:_("Maintenance Mode") }}</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'platform_management:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'platform_management:maintenance_mode_list' %}">{% trans "Maintenance" %}</a></li>
            <li class="breadcrumb-item active">
                {% if object %}{% trans "Edit Maintenance Mode" %}{% else %}{% trans "Configure Maintenance Mode" %}{% endif %}
            </li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card premium-card shadow-lg">
                <div class="premium-card-header">
                    <h3>
                        <i class="bi bi-tools me-2"></i>
                        {% if object %}{% trans "Edit Maintenance Mode" %}{% else %}{% trans "Configure Maintenance Mode" %}{% endif %}
                    </h3>
                </div>

                <div class="premium-card-body">
                    <!-- Warning Section -->
                    <div class="maintenance-warning">
                        <h6><i class="bi bi-exclamation-triangle-fill me-2"></i>{% trans "Maintenance Mode Warning" %}</h6>
                        <p>{% trans "Enabling maintenance mode will make the platform inaccessible to all users except those with bypass permissions. Use this feature carefully during system updates or critical maintenance." %}</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}

                        {% for hidden_field in form.hidden_fields %}
                            {{ hidden_field }}
                        {% endfor %}

                        <!-- Maintenance Status Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-power me-2"></i>{% trans "Maintenance Status" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Is Enabled Field (Switch) -->
                                <div class="form-check form-switch">
                                    <input type="checkbox"
                                           class="form-check-input{% if form.is_enabled.errors %} is-invalid{% endif %}"
                                           id="{{ form.is_enabled.id_for_label }}"
                                           name="{{ form.is_enabled.name }}"
                                           value="1"
                                           {% if form.is_enabled.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ form.is_enabled.id_for_label }}">
                                        <i class="bi bi-toggle-on me-2"></i>{% trans "Enable Maintenance Mode" %}
                                    </label>
                                    {% if form.is_enabled.help_text %}
                                        <div class="form-text">{{ form.is_enabled.help_text }}</div>
                                    {% endif %}
                                    {% if form.is_enabled.errors %}
                                        <div class="invalid-feedback">{{ form.is_enabled.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Bypass Staff Field -->
                                <div class="form-check">
                                    <input type="checkbox"
                                           class="form-check-input{% if form.bypass_staff.errors %} is-invalid{% endif %}"
                                           id="{{ form.bypass_staff.id_for_label }}"
                                           name="{{ form.bypass_staff.name }}"
                                           value="1"
                                           {% if form.bypass_staff.value %}checked{% endif %}>
                                    <label class="form-check-label" for="{{ form.bypass_staff.id_for_label }}">
                                        <i class="bi bi-person-badge me-2"></i>{% trans "Allow Staff Access" %}
                                    </label>
                                    {% if form.bypass_staff.help_text %}
                                        <div class="form-text">{{ form.bypass_staff.help_text }}</div>
                                    {% endif %}
                                    {% if form.bypass_staff.errors %}
                                        <div class="invalid-feedback">{{ form.bypass_staff.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Display Information Section -->
                        <div class="fieldset-header">
                            <h5><i class="bi bi-display me-2"></i>{% trans "Display Information" %}</h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Title Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if form.title.errors %} is-invalid{% endif %}"
                                           id="{{ form.title.id_for_label }}"
                                           name="{{ form.title.name }}"
                                           value="{{ form.title.value|default:'' }}"
                                           placeholder="{% trans 'Enter maintenance title' %}"
                                           {% if form.title.field.required %}required{% endif %}>
                                    <label for="{{ form.title.id_for_label }}">
                                        <i class="bi bi-card-heading icon-input"></i>{% trans "Maintenance Title" %}
                                    </label>
                                    {% if form.title.help_text %}
                                        <div class="form-text">{{ form.title.help_text }}</div>
                                    {% endif %}
                                    {% if form.title.errors %}
                                        <div class="invalid-feedback">{{ form.title.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Site Name Override Field -->
                                <div class="form-floating">
                                    <input type="text"
                                           class="form-control{% if form.site_name_override.errors %} is-invalid{% endif %}"
                                           id="{{ form.site_name_override.id_for_label }}"
                                           name="{{ form.site_name_override.name }}"
                                           value="{{ form.site_name_override.value|default:'' }}"
                                           placeholder="{% trans 'e.g., Our Awesome Platform' %}"
                                           {% if form.site_name_override.field.required %}required{% endif %}>
                                    <label for="{{ form.site_name_override.id_for_label }}">
                                        <i class="bi bi-building icon-input"></i>{% trans "Site Name Override (Optional)" %}
                                    </label>
                                    {% if form.site_name_override.help_text %}
                                        <div class="form-text">{{ form.site_name_override.help_text }}</div>
                                    {% endif %}
                                    {% if form.site_name_override.errors %}
                                        <div class="invalid-feedback">{{ form.site_name_override.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- Message Field -->
                                <div class="form-floating">
                                    <textarea class="form-control{% if form.message.errors %} is-invalid{% endif %}"
                                              id="{{ form.message.id_for_label }}"
                                              name="{{ form.message.name }}"
                                              placeholder="{% trans 'Enter maintenance message for users' %}"
                                              style="height: 120px;"
                                              {% if form.message.field.required %}required{% endif %}>{{ form.message.value|default:'' }}</textarea>
                                    <label for="{{ form.message.id_for_label }}">
                                        <i class="bi bi-chat-text icon-input"></i>{% trans "Maintenance Message" %}
                                    </label>
                                    {% if form.message.help_text %}
                                        <div class="form-text">{{ form.message.help_text }}</div>
                                    {% endif %}
                                    {% if form.message.errors %}
                                        <div class="invalid-feedback">{{ form.message.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings Section -->
                        <div class="security-section">
                            <h6><i class="bi bi-shield-lock-fill me-2"></i>{% trans "Security & Bypass Settings" %}</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Bypass Key Field -->
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control{% if form.bypass_key.errors %} is-invalid{% endif %}"
                                               id="{{ form.bypass_key.id_for_label }}"
                                               name="{{ form.bypass_key.name }}"
                                               value="{{ form.bypass_key.value|default:'' }}"
                                               placeholder="{% trans 'Optional secret key for URL bypass' %}"
                                               {% if form.bypass_key.field.required %}required{% endif %}>
                                        <label for="{{ form.bypass_key.id_for_label }}">
                                            <i class="bi bi-key-fill icon-input"></i>{% trans "Bypass Key (Optional)" %}
                                        </label>
                                        {% if form.bypass_key.help_text %}
                                            <div class="form-text">{{ form.bypass_key.help_text }}</div>
                                        {% endif %}
                                        {% if form.bypass_key.errors %}
                                            <div class="invalid-feedback">{{ form.bypass_key.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Allowed IPs Field -->
                                    <div class="form-floating">
                                        <textarea class="form-control{% if form.allowed_ips.errors %} is-invalid{% endif %}"
                                                  id="{{ form.allowed_ips.id_for_label }}"
                                                  name="{{ form.allowed_ips.name }}"
                                                  placeholder="{% trans 'Enter IP addresses separated by commas or newlines' %}"
                                                  style="height: 100px;"
                                                  {% if form.allowed_ips.field.required %}required{% endif %}>{{ form.allowed_ips.value|default:'' }}</textarea>
                                        <label for="{{ form.allowed_ips.id_for_label }}">
                                            <i class="bi bi-router-fill icon-input"></i>{% trans "Allowed IP Addresses" %}
                                        </label>
                                        {% if form.allowed_ips.help_text %}
                                            <div class="form-text">{{ form.allowed_ips.help_text }}</div>
                                        {% endif %}
                                        {% if form.allowed_ips.errors %}
                                            <div class="invalid-feedback">{{ form.allowed_ips.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5">
                            <a href="{% url 'platform_management:maintenance_mode_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}{% trans "Update Maintenance Mode" %}{% else %}{% trans "Configure Maintenance Mode" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock platform_admin_page_content %}

{% block extra_platform_admin_js %}
    {{ block.super }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation and enhancement
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');
        const maintenanceSwitch = document.getElementById('{{ form.is_enabled.id_for_label }}');

        // Form submission handling with maintenance mode warning
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                if (maintenanceSwitch && maintenanceSwitch.checked) {
                    const confirmed = confirm('{% trans "Are you sure you want to enable maintenance mode? This will make the platform inaccessible to most users." %}');
                    if (!confirmed) {
                        e.preventDefault();
                        return false;
                    }
                }

                const originalText = submitButton.innerHTML;
                // Prevent double submission
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            });
        }

        // Maintenance mode switch visual feedback
        if (maintenanceSwitch) {
            maintenanceSwitch.addEventListener('change', function() {
                const warningSection = document.querySelector('.maintenance-warning');
                if (this.checked) {
                    warningSection.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
                    warningSection.style.borderColor = '#dc3545';
                } else {
                    warningSection.style.background = 'linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%)';
                    warningSection.style.borderColor = '#17a2b8';
                }
            });
        }

        // IP address validation
        const allowedIpsField = document.getElementById('{{ form.allowed_ips.id_for_label }}');
        if (allowedIpsField) {
            allowedIpsField.addEventListener('blur', function() {
                const ips = this.value.split(/[,\n]/).map(ip => ip.trim()).filter(ip => ip);
                const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/;

                let hasInvalidIp = false;
                for (let ip of ips) {
                    if (ip && !ipPattern.test(ip)) {
                        hasInvalidIp = true;
                        break;
                    }
                }

                if (hasInvalidIp) {
                    this.setCustomValidity('{% trans "Please enter valid IP addresses or CIDR blocks" %}');
                    this.classList.add('is-invalid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                }
            });
        }

        // Bypass key generation helper
        const bypassKeyField = document.getElementById('{{ form.bypass_key.id_for_label }}');
        if (bypassKeyField) {
            // Add generate button next to bypass key field
            const generateBtn = document.createElement('button');
            generateBtn.type = 'button';
            generateBtn.className = 'btn btn-outline-secondary btn-sm mt-2';
            generateBtn.innerHTML = '<i class="bi bi-shuffle me-1"></i>{% trans "Generate Random Key" %}';
            generateBtn.addEventListener('click', function() {
                const randomKey = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                bypassKeyField.value = randomKey;
            });
            bypassKeyField.parentElement.appendChild(generateBtn);
        }

        // Auto-focus first input
        const firstInput = form.querySelector('input[type="text"], textarea');
        if (firstInput) {
            firstInput.focus();
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    });
    </script>
{% endblock %}


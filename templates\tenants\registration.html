{# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}
{% load static i18n widget_tweaks tenant_extras %}

{% block title %}{% trans "Register Your School" %}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% trans "Register Your School" %}</h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">{{ form.non_field_errors|striptags }}</div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        <h5 class="mb-3">{% trans "School Information" %}</h5>

                        <div class="mb-3">
                            <label for="{{ form.school_name.id_for_label }}" class="form-label">{{ form.school_name.label }}</label>
                            {% render_field form.school_name class+="form-control" %}
                            {% if form.school_name.errors %}
                                <div class="text-danger">{{ form.school_name.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.subdomain.id_for_label }}" class="form-label">{{ form.subdomain.label }}</label>
                            {% render_field form.subdomain class+="form-control" %}
                            <div class="form-text">
                                {% trans "Your portal address will be: " %}
                                <strong>{{ form.subdomain.value|default_if_none:"yourprefix" }}.{% get_base_domain %}</strong>
                            </div>
                            {% if form.subdomain.errors %}
                                <div class="text-danger">{{ form.subdomain.errors }}</div>
                            {% endif %}
                        </div>

                        <h5 class="mb-3 mt-4">{% trans "Your Administrator Account" %}</h5>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.admin_first_name.id_for_label }}" class="form-label">{{ form.admin_first_name.label }}</label>
                                {% render_field form.admin_first_name class+="form-control" %}
                                {% if form.admin_first_name.errors %}
                                    <div class="text-danger">{{ form.admin_first_name.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.admin_last_name.id_for_label }}" class="form-label">{{ form.admin_last_name.label }}</label>
                                {% render_field form.admin_last_name class+="form-control" %}
                                {% if form.admin_last_name.errors %}
                                    <div class="text-danger">{{ form.admin_last_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.admin_email.id_for_label }}" class="form-label">{{ form.admin_email.label }}</label>
                            {% render_field form.admin_email class+="form-control" %}
                            {% if form.admin_email.errors %}
                                <div class="text-danger">{{ form.admin_email.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.admin_password1.id_for_label }}" class="form-label">{{ form.admin_password1.label }}</label>
                                {% render_field form.admin_password1 class+="form-control" %}
                                {% if form.admin_password1.errors %}
                                    <div class="text-danger">{{ form.admin_password1.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.admin_password2.id_for_label }}" class="form-label">{{ form.admin_password2.label }}</label>
                                {% render_field form.admin_password2 class+="form-control" %}
                                {% if form.admin_password2.errors %}
                                    <div class="text-danger">{{ form.admin_password2.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <h5 class="mb-3 mt-4">{% trans "Select Your Plan" %}</h5>

                        <div class="mb-4">
                            {% if form.plan.errors %}
                                <div class="text-danger">{{ form.plan.errors }}</div>
                            {% endif %}
                            {% for radio in form.plan %}
                            <div class="form-check mb-3 border rounded p-3">
                                {{ radio.tag }}
                                <label for="{{ radio.id_for_label }}" class="form-check-label">
                                    <strong>{{ radio.choice_label }}</strong>
                                    {% with plan_instance=radio.choice_value.instance %}
                                        <small class="d-block text-muted">
                                            {% if plan_instance.trial_period_days > 0 %}
                                                {% blocktrans with days=plan_instance.trial_period_days %}Includes a {{ days }}-day free trial.{% endblocktrans %}
                                            {% endif %}
                                            {{ plan_instance.description|truncatewords:20 }}
                                        </small>
                                        <ul class="list-unstyled small mt-1">
                                            {% for feature_item in plan_instance.features.all|slice:":3" %}
                                                <li><i class="bi bi-check-circle-fill text-success me-1"></i>{{ feature_item.name }}</li>
                                            {% empty %}
                                                <li>{% trans "Core Features" %}</li>
                                            {% endfor %}
                                            {% if plan_instance.features.all.count > 3 %}<li>& {% trans "more" %}...</li>{% endif %}
                                        </ul>
                                    {% endwith %}
                                </label>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <label class="form-label">{% trans "Billing Cycle" %}</label>
                            {% if form.billing_cycle.errors %}
                                <div class="text-danger">{{ form.billing_cycle.errors }}</div>
                            {% endif %}
                            {% for radio_cycle in form.billing_cycle %}
                            <div class="form-check">
                                {{ radio_cycle.tag }}
                                <label for="{{ radio_cycle.id_for_label }}" class="form-check-label">{{ radio_cycle.choice_label }}</label>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {% render_field form.agree_to_terms class+="form-check-input" %}
                                <label for="{{ form.agree_to_terms.id_for_label }}" class="form-check-label">{{ form.agree_to_terms.label }}</label>
                            </div>
                            {% if form.agree_to_terms.errors %}
                                <div class="text-danger">{{ form.agree_to_terms.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                {% trans "Complete Registration" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

# D:\school_fees_saas_v2\apps\subscriptions\urls.py
from django.urls import path
from . import views # Make sure views is imported

app_name = 'subscriptions'

urlpatterns = [
    path('pricing/', views.pricing_page_view, name='pricing_page'),
    
    # This is the one for Subscription Details:
    path('status/', views.SubscriptionDetailsView.as_view(), name='subscription_details'), # <<< ADD/ENSURE THIS LINE

    path('update-payment-method/', views.update_payment_method_view, name='update_payment_method'), # Placeholder
    path('manage/', views.manage_subscription_redirect_view, name='manage_subscription_redirect'), 

    path('select-plan/', views.PlanSelectionView.as_view(), name='select_plan'),
    path('checkout/initiate/<slug:plan_slug>/<str:billing_cycle>/', views.InitiateCheckoutView.as_view(), name='initiate_checkout'),
    path('checkout/success/', views.PaymentSuccessView.as_view(), name='checkout_success'), # Or name='payment_success'
    path('checkout/cancel/', views.PaymentCancelView.as_view(), name='checkout_cancel'),   # Or name='payment_cancel'
    
    path('webhooks/payment-gateway/', views.payment_gateway_webhook_view, name='payment_webhook'),

    # Admin manual approval 
    path('admin-approve/<int:subscription_pk>/', views.admin_manual_approve_subscription_view, name='admin_manual_approve'),
]












# # D:\school_fees_saas_v2\apps\subscriptions\urls.py
# from django.urls import path
# from . import views # Assuming you will create views in this app

# app_name = 'subscriptions' # <<< THIS DEFINES THE NAMESPACE

# urlpatterns = [
#     # Example URL that your middleware is looking for:
#     path('manage-redirect/', views.manage_subscription_redirect_view, name='manage_subscription_redirect'),
#     path('details/', views.SubscriptionDetailsView.as_view(), name='subscription_details'),
#     path('update-payment/', views.update_payment_method_view, name='update_payment_method'),
#     # ... other subscription-related URLs (pricing page, checkout, webhook handler) ...
    
    
#     path('pricing/', views.pricing_page_view, name='pricing_page'),
    
#     path('details/', views.SubscriptionDetailsView, name='subscription_details'),
#     path('update-payment-method/', views.update_payment_method_view, name='update_payment_method'),
#     path('manage/', views.manage_subscription_redirect_view, name='manage_subscription_redirect'),


#     # New URLs for plan selection and checkout flow
#     path('select-plan/', views.PlanSelectionView.as_view(), name='select_plan'), # For existing tenants to change/select plan
#     path('checkout/initiate/<slug:plan_slug>/<str:billing_cycle>/', views.InitiateCheckoutView, name='initiate_checkout'),
#     path('checkout/success/', views.PaymentSuccessView, name='payment_success'),
#     path('checkout/cancel/', views.PaymentCancelView.as_view(), name='payment_cancel'),
    
#     # URL for payment gateway webhooks
#     path('webhooks/payment-gateway/', views.payment_gateway_webhook_view, name='payment_webhook'),
    
#     # New URLs for Payment Flow
#     # path('checkout/initiate/<int:plan_pk>/<str:billing_cycle>/', views.InitiateCheckoutView.as_view(), name='initiate_checkout'),
#     # path('checkout/success/', views.PaymentSuccessView.as_view(), name='checkout_success'),
#     # path('checkout/cancel/', views.PaymentCancelView.as_view(), name='payment_cancel'),   # <<< CORRECTED LINE
    
#     # path('checkout/cancelled/', views.subscription_checkout_cancelled_view, name='checkout_cancelled'),
    
#     # path('webhooks/payment-gateway/', views.payment_gateway_webhook_view, name='payment_webhook'), # Already existed

#     # For manual admin approval (simulation)
#     path('subscription/<int:subscription_pk>/approve-manual/', views.admin_manual_approve_subscription_view, name='admin_manual_approve'),
    
    
# ]
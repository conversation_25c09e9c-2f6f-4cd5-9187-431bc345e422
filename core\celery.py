# D:\school_fees_saas_v2\core\celery.py

import os
from celery import Celery
from django.conf import settings # To access Django settings

# Set the default Django settings module for the 'celery' program.
# This must happen before creating the app instance.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

# Create the Celery application instance
# 'core' can be any name, but often matches the Django project name
app = Celery('core')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix in settings.py.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
# Celery will look for a tasks.py file in each app.
app.autodiscover_tasks()
# Or, if you prefer to list apps explicitly:
# app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)


# Optional: Example debug task (can be in any app's tasks.py too)
@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
    print('Celery debug_task is running!')



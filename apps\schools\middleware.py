# apps/schools/middleware.py
# This middleware can be added to help manage tenant-specific users

class TenantUserMiddleware:
    """
    Middleware to handle tenant-specific user types and ensure proper authentication
    """
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Process request before view is called
        if hasattr(request, 'user') and not request.user.is_anonymous:
            # Add debug information
            print(f"--- TenantUserMiddleware: Processing user {request.user} of type {type(request.user)} ---")
            
            # Store the user type in the request for easy access in views/templates
            from apps.schools.models import StaffUser
            from apps.accounts.models import ParentUser
            
            if isinstance(request.user, StaffUser):
                request.user_type = 'staff'
            elif isinstance(request.user, ParentUser):
                request.user_type = 'parent'
            else:
                request.user_type = 'unknown'
            
            print(f"--- TenantUserMiddleware: User type set to {request.user_type} ---")
            
        response = self.get_response(request)
        return response
    
    
    
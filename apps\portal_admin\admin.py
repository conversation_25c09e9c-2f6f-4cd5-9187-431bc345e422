# D:\school_fees_saas_v2\apps\portal_admin\admin.py
from django.contrib import admin
from django.urls import reverse, NoReverseMatch
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import AdminActivityLog

import logging
logger = logging.getLogger(__name__)

@admin.register(AdminActivityLog)
class AdminActivityLogAdmin(admin.ModelAdmin):
    # --- List View Configuration ---
    list_display = (
        'timestamp', 
        'get_actor_for_list_display', 
        'action_type_display',      
        'get_target_for_list_display', 
        'description_shortened',
        'tenant_link_for_list' 
    )
    list_filter = (
        'action_type', 
        'timestamp', 
        'tenant', 
        'target_content_type', 
        'staff_user__email', # Filter by Staff<PERSON><PERSON>'s email
        'user__email'        # CORRECTED: Filter by public User's email
    )
    search_fields = (
        # 'user__username', # Removed as 'username' doesn't exist on your public User
        'user__email',      # Search by public User's email
        'user__first_name', # Assuming your public User has first_name
        'user__last_name',  # Assuming your public User has last_name
        'staff_user__email', 
        'staff_user__first_name', 
        'staff_user__last_name', 
        'actor_description', 
        'target_object_repr', 
        'description', 
        'ip_address'
    )
    list_per_page = 25
    ordering = ('-timestamp',)

    # --- Detail/Change View Configuration (Readonly) ---
    # ... (readonly_fields and fieldsets look good based on your models) ...
    readonly_fields = (
        'timestamp', 'action_type', 'user_link', 'staff_user_link', 
        'actor_description', 'tenant_link', 'ip_address', 'user_agent', 
        'linked_target_object', 'target_object_repr', 'description',
    )

    fieldsets = (
        (_('Event Overview'), {'fields': ('timestamp', 'action_type', 'description')}),
        (_('Actor & Context'), {'fields': ('user_link', 'staff_user_link', 'actor_description', 'tenant_link', 'ip_address', 'user_agent')}),
        (_('Target of Action'), {'fields': ('linked_target_object', 'target_object_repr')}),
    )

    # --- Custom Display Methods (look good) ---
    # ... (all your @admin.display methods: get_actor_for_list_display, etc.) ...
    @admin.display(description=_('Actor'), ordering='staff_user__email') # Example ordering
    def get_actor_for_list_display(self, obj):
        return obj.get_actor_display()

    @admin.display(description=_('Action'), ordering='action_type')
    def action_type_display(self, obj):
        return obj.get_action_type_display()

    @admin.display(description=_('Target'), ordering='target_object_repr')
    def get_target_for_list_display(self, obj):
        target_repr = obj.target_object_repr or (str(obj.target_object) if obj.target_object_id and obj.target_object else "N/A")
        return (target_repr[:70] + '...') if len(target_repr) > 70 else target_repr

    @admin.display(description=_('Details'), ordering='description')
    def description_shortened(self, obj):
        return (obj.description[:70] + '...') if obj.description and len(obj.description) > 70 else obj.description

    @admin.display(description=_('User (Public Admin)'))
    def user_link(self, obj):
        if obj.user:
            try:
                # Ensure obj.user._meta.app_label is 'users' and model_name is 'user'
                # or whatever your AUTH_USER_MODEL is registered as in admin.
                link = reverse(f"admin:{obj.user._meta.app_label}_{obj.user._meta.model_name}_change", args=[obj.user.pk])
                return format_html('<a href="{}">{}</a>', link, obj.user) # obj.user uses its __str__
            except NoReverseMatch:
                return str(obj.user) 
        return "-"
    
    @admin.display(description=_('Staff User (Tenant)'))
    def staff_user_link(self, obj):
        if obj.staff_user:
            try:
                link = reverse(f"admin:{obj.staff_user._meta.app_label}_{obj.staff_user._meta.model_name}_change", args=[obj.staff_user.pk])
                return format_html('<a href="{}">{}</a>', link, obj.staff_user)
            except NoReverseMatch:
                return str(obj.staff_user)
        return "-"

    @admin.display(description=_('Tenant/School'))
    def tenant_link(self, obj):
        if obj.tenant:
            try:
                link = reverse(f"admin:{obj.tenant._meta.app_label}_{obj.tenant._meta.model_name}_change", args=[obj.tenant.pk])
                return format_html('<a href="{}">{}</a>', link, obj.tenant)
            except NoReverseMatch:
                return str(obj.tenant)
        return "-"
    tenant_link_for_list = tenant_link 

    @admin.display(description=_('Target Object Link'))
    def linked_target_object(self, obj):
        if obj.target_object: 
            try:
                if obj.target_content_type and obj.target_object_id:
                    link = reverse(
                        f"admin:{obj.target_content_type.app_label}_{obj.target_content_type.model}_change",
                        args=[obj.target_object_id]
                    )
                    return format_html('<a href="{}">View {} (ID: {})</a>', link, obj.target_content_type.name, obj.target_object_id)
                else:
                    return str(obj.target_object)
            except NoReverseMatch:
                return f"{str(obj.target_object)} (ID: {obj.target_object_id}) (No admin link)"
            except Exception as e:
                logger.error(f"Error creating target object link for log {obj.pk}: {e}")
                return "Error linking object"
        elif obj.target_object_repr: 
            return obj.target_object_repr
        return "N/A"

    # --- Permissions for Admin Interface (look good) ---
    def has_add_permission(self, request):
        return False
    def has_change_permission(self, request, obj=None):
        return False 
    def has_delete_permission(self, request, obj=None):
        if request.user.is_superuser or request.user.has_perm('portal_admin.delete_adminactivitylog'):
            return True
        return False
    
    
    





# # D:\school_fees_saas_v2\apps\portal_admin\admin.py
# from django.contrib import admin
# from django.urls import reverse, NoReverseMatch
# from django.utils.html import format_html
# from django.utils.translation import gettext_lazy as _

# from .models import AdminActivityLog

# import logging
# logger = logging.getLogger(__name__)

# @admin.register(AdminActivityLog)
# class AdminActivityLogAdmin(admin.ModelAdmin):
#     # --- List View Configuration ---
#     list_display = (
#         'timestamp', 
#         'get_actor_for_list_display', # Using a slightly different name for clarity if needed
#         'action_type_display',      # Using the model's get_action_type_display
#         'get_target_for_list_display', 
#         'description_shortened',
#         'tenant_link_for_list' # Optional: if you want to see tenant in list view
#     )
#     list_filter = ('action_type', 'timestamp', 'tenant', 'target_content_type', 'staff_user__email', 'user__username')
#     search_fields = (
#         'user__username', 'user__email', 
#         'staff_user__email', 'staff_user__first_name', 'staff_user__last_name', # Ensure StaffUser has these
#         'actor_description', 
#         'target_object_repr', 
#         'description', 
#         'ip_address'
#     )
#     list_per_page = 25
#     ordering = ('-timestamp',)

#     # --- Detail/Change View Configuration (Readonly) ---
#     readonly_fields = (
#         'timestamp', 
#         'action_type', 
#         'user_link', 
#         'staff_user_link', 
#         'actor_description',
#         'tenant_link', 
#         'ip_address', 
#         'user_agent', 
#         'linked_target_object', # Shows ContentType, PK, and a link
#         'target_object_repr',   # Shows the stored string representation
#         'description',
#         # 'target_content_type', # Usually shown via linked_target_object
#         # 'target_object_id',    # Usually shown via linked_target_object
#     )

#     fieldsets = (
#         (_('Event Overview'), {
#             'fields': ('timestamp', 'action_type', 'description')
#         }),
#         (_('Actor & Context'), {
#             'fields': ('user_link', 'staff_user_link', 'actor_description', 'tenant_link', 'ip_address', 'user_agent')
#         }),
#         (_('Target of Action'), {
#             'fields': ('linked_target_object', 'target_object_repr')
#         }),
#     )

#     # --- Custom Display Methods ---
#     @admin.display(description=_('Actor'), ordering='staff_user__email') # Add ordering if desired
#     def get_actor_for_list_display(self, obj):
#         return obj.get_actor_display() # Uses the model's method

#     @admin.display(description=_('Action'), ordering='action_type')
#     def action_type_display(self, obj):
#         return obj.get_action_type_display() # Uses model's built-in method for choices

#     @admin.display(description=_('Target'), ordering='target_object_repr')
#     def get_target_for_list_display(self, obj):
#         target_repr = obj.target_object_repr or (str(obj.target_object) if obj.target_object_id and obj.target_object else "N/A")
#         return (target_repr[:70] + '...') if len(target_repr) > 70 else target_repr

#     @admin.display(description=_('Details'), ordering='description')
#     def description_shortened(self, obj):
#         return (obj.description[:70] + '...') if obj.description and len(obj.description) > 70 else obj.description

#     @admin.display(description=_('User (Public Admin)'))
#     def user_link(self, obj):
#         if obj.user:
#             try:
#                 link = reverse(f"admin:{obj.user._meta.app_label}_{obj.user._meta.model_name}_change", args=[obj.user.pk])
#                 return format_html('<a href="{}">{}</a>', link, obj.user)
#             except NoReverseMatch:
#                 return str(obj.user) # Fallback if admin URL for user model isn't found
#         return "-"
    
#     @admin.display(description=_('Staff User (Tenant)'))
#     def staff_user_link(self, obj):
#         if obj.staff_user:
#             try:
#                 # Assuming StaffUser is registered with Django admin under 'schools' app
#                 link = reverse(f"admin:schools_staffuser_change", args=[obj.staff_user.pk])
#                 return format_html('<a href="{}">{}</a>', link, obj.staff_user)
#             except NoReverseMatch:
#                 # Fallback if StaffUser admin URL isn't 'schools_staffuser_change'
#                 try:
#                     link = reverse(f"admin:{obj.staff_user._meta.app_label}_{obj.staff_user._meta.model_name}_change", args=[obj.staff_user.pk])
#                     return format_html('<a href="{}">{}</a>', link, obj.staff_user)
#                 except NoReverseMatch:
#                     return str(obj.staff_user)
#         return "-"

#     @admin.display(description=_('Tenant/School'))
#     def tenant_link(self, obj):
#         if obj.tenant:
#             try:
#                 # Assuming School (tenant model) is registered with Django admin under 'tenants' app
#                 link = reverse(f"admin:tenants_school_change", args=[obj.tenant.pk])
#                 return format_html('<a href="{}">{}</a>', link, obj.tenant)
#             except NoReverseMatch:
#                 # Fallback
#                 try:
#                     link = reverse(f"admin:{obj.tenant._meta.app_label}_{obj.tenant._meta.model_name}_change", args=[obj.tenant.pk])
#                     return format_html('<a href="{}">{}</a>', link, obj.tenant)
#                 except NoReverseMatch:
#                     return str(obj.tenant)
#         return "-"
#     tenant_link_for_list = tenant_link # Alias for list_display if needed

#     @admin.display(description=_('Target Object Link'))
#     def linked_target_object(self, obj):
#         if obj.target_object: # This is the GenericForeignKey field
#             try:
#                 # Ensure content_type and object_id are present
#                 if obj.target_content_type and obj.target_object_id:
#                     link = reverse(
#                         f"admin:{obj.target_content_type.app_label}_{obj.target_content_type.model}_change",
#                         args=[obj.target_object_id]
#                     )
#                     return format_html('<a href="{}">View {} (ID: {})</a>', link, obj.target_content_type.name, obj.target_object_id)
#                 else:
#                     return str(obj.target_object) # Fallback if no content_type/object_id for linking
#             except NoReverseMatch:
#                 return f"{str(obj.target_object)} (ID: {obj.target_object_id}) (No admin link)"
#             except Exception as e:
#                 logger.error(f"Error creating target object link for log {obj.pk}: {e}")
#                 return "Error linking object"
#         elif obj.target_object_repr: # If only repr is stored (e.g. object deleted)
#             return obj.target_object_repr
#         return "N/A"

#     # --- Permissions for Admin Interface ---
#     def has_add_permission(self, request):
#         # Logs should be created by the application, not manually in admin
#         return False

#     def has_change_permission(self, request, obj=None):
#         # Logs are generally immutable from the admin interface
#         return False 

#     def has_delete_permission(self, request, obj=None):
#         # Allow deletion only for superusers or users with a specific "delete log" permission
#         if request.user.is_superuser or request.user.has_perm('portal_admin.delete_adminactivitylog'):
#             return True
#         return False
    
    
    
{# D:\school_fees_saas_v2\apps\hr\templates\hr\salarygrade_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title|default:"Salary Grades" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3">{{ view_title }}</h1>
        {% if perms.hr.add_salarygrade %}
        <a href="{% url 'hr:salarygrade_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Add New Salary Grade
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h6 class="m-0">Configured Salary Grades</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Grade Name</th>
                            <th>Description</th>
                            <th class="text-center">Is Active</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for grade in grades %} {# 'grades' is the context_object_name #}
                        <tr>
                            <td><strong>{{ grade.name }}</strong></td>
                            <td>{{ grade.description|truncatewords:15 }}</td>
                            <td class="text-center">
                                {% if grade.is_active %}
                                    <i class="bi bi-check-circle-fill text-success" title="Active"></i>
                                {% else %}
                                    <i class="bi bi-x-circle-fill text-danger" title="Inactive"></i>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'hr:salarygrade_update' pk=grade.pk %}" class="btn btn-sm btn-outline-primary" title="Edit"><i class="bi bi-pencil-fill"></i></a>
                                <a href="{% url 'hr:salarygrade_delete' pk=grade.pk %}" class="btn btn-sm btn-outline-danger" title="Delete"><i class="bi bi-trash-fill"></i></a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center p-4">
                                <p class="mb-1">No salary grades have been configured yet.</p>
                                <a href="{% url 'hr:salarygrade_create' %}" class="btn btn-sm btn-success">Create the first one</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if is_paginated %}
        <div class="card-footer">
            {% include "partials/_pagination.html" %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}



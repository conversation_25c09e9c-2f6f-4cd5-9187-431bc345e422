# Generated by Django 5.1.9 on 2025-07-06 13:47

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0005_alter_payslip_options_alter_paysliplineitem_options_and_more'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='payslip',
            options={'ordering': ['-payroll_run__payment_date', 'staff_member__last_name'], 'verbose_name': 'Payslip', 'verbose_name_plural': 'Payslips'},
        ),
        migrations.RenameField(
            model_name='payslip',
            old_name='generated_on',
            new_name='created_at',
        ),
        migrations.RenameField(
            model_name='payslip',
            old_name='staff_user',
            new_name='staff_member',
        ),
        migrations.AddField(
            model_name='payslip',
            name='allowances',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Sum of all allowances', max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='basic_salary',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='bonuses',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='loan_repayments',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='notes',
            field=models.TextField(blank=True, help_text='Notes specific to this payslip', null=True),
        ),
        migrations.AddField(
            model_name='payslip',
            name='other_deductions',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='pension_deductions',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='tax_deductions',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Tax (e.g., PAYE)'),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='gross_earnings',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='net_pay',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='total_deductions',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.CreateModel(
            name='PayrollRun',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pay_period_start', models.DateField()),
                ('pay_period_end', models.DateField()),
                ('payment_date', models.DateField()),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PROCESSED', 'Processed'), ('PAID', 'Paid'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payroll_runs', to='schools.staffuser')),
            ],
            options={
                'verbose_name': 'Payroll Run',
                'verbose_name_plural': 'Payroll Runs',
                'ordering': ['-payment_date'],
                'permissions': [('manage_payroll', 'Can create and process payroll runs')],
            },
        ),
        migrations.AlterUniqueTogether(
            name='payslip',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='payslip',
            name='payroll_run',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to='hr.payrollrun'),
        ),
        migrations.AlterUniqueTogether(
            name='payslip',
            unique_together={('payroll_run', 'staff_member')},
        ),
        migrations.RemoveField(
            model_name='payslip',
            name='paid_on',
        ),
        migrations.RemoveField(
            model_name='payslip',
            name='pay_period_end',
        ),
        migrations.RemoveField(
            model_name='payslip',
            name='pay_period_start',
        ),
        migrations.RemoveField(
            model_name='payslip',
            name='status',
        ),
    ]

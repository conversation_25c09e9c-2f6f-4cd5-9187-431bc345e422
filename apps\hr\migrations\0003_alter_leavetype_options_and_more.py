# Generated by Django 5.1.9 on 2025-07-06 06:19

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0002_initial'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='leavetype',
            options={'ordering': ['name'], 'permissions': [('view_hr_module', 'Can view the main HR module and navbar link'), ('approve_leave_requests', 'Can approve or reject leave requests for other staff')], 'verbose_name': 'leave type', 'verbose_name_plural': 'leave types'},
        ),
        migrations.RemoveField(
            model_name='leavetype',
            name='max_annual_days',
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_frequency',
            field=models.CharField(choices=[('NONE', 'No Accrual (Manually Granted)'), ('MONTHLY', 'Monthly'), ('ANNUALLY', 'Annually')], default='NONE', help_text="Choose 'No Accrual' for leave that is granted manually or all at once per year.", max_length=20, verbose_name='Accrual Frequency'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_rate',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='If accruing, number of days added per period (e.g., 1.75 for monthly).', max_digits=5, verbose_name='Accrual Rate'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_accrual_balance',
            field=models.PositiveIntegerField(blank=True, help_text='The maximum number of leave days a staff member can accumulate. Leave blank for no limit.', null=True, verbose_name='Maximum Balance'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_days_per_year_grant',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='For non-accruing leave types, how many days are granted per year (e.g., 15 sick days).', max_digits=5, null=True, verbose_name='Max Days Granted Annually'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='prorate_accrual',
            field=models.BooleanField(default=True, help_text='If accruing, new staff hired mid-period will receive a prorated amount for their first accrual.', verbose_name='Prorate for New Staff'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Is this leave type available for new requests?'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='name',
            field=models.CharField(help_text='Name of the leave type (e.g., Annual Leave, Sick Leave).', max_length=100, unique=True),
        ),
        migrations.CreateModel(
            name='LeaveBalanceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('ACCRUAL', 'Automatic Accrual'), ('ADJUSTMENT', 'Manual Adjustment'), ('APPLICATION', 'Leave Application'), ('RESET', 'Yearly Reset'), ('INITIAL', 'Initial Balance')], max_length=20, verbose_name='Action')),
                ('change_amount', models.DecimalField(decimal_places=2, help_text='The number of days added (positive) or removed (negative).', max_digits=5, verbose_name='Change Amount')),
                ('notes', models.CharField(blank=True, max_length=255, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, help_text='The admin who made a manual adjustment, or null for system actions.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_log_actions', to='schools.staffuser')),
                ('leave_balance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='hr.leavebalance')),
            ],
            options={
                'verbose_name': 'Leave Balance Log',
                'verbose_name_plural': 'Leave Balance Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]

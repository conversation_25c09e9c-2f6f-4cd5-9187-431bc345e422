{# Premium Report Filter Export Card Template #}
{% load widget_tweaks i18n %}

<style>
    /* Premium Report Filter Export Card Design */
    .premium-export-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-export-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-export-filter-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        color: white;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .premium-export-filter-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmerExport 3s infinite;
    }

    @keyframes shimmerExport {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-export-filter-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .premium-export-buttons {
        display: flex;
        gap: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .btn-export-premium {
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        min-width: 80px;
        font-size: 0.8rem;
    }

    .btn-export-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .btn-export-premium:hover::before {
        left: 100%;
    }

    .btn-export-csv {
        background: rgba(108, 117, 125, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-export-csv:hover {
        background: rgba(108, 117, 125, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }

    .btn-export-excel {
        background: rgba(25, 135, 84, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-export-excel:hover {
        background: rgba(25, 135, 84, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
        color: white;
    }

    .btn-export-pdf {
        background: rgba(220, 53, 69, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-export-pdf:hover {
        background: rgba(220, 53, 69, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .btn-export-pdf.disabled {
        background: rgba(108, 117, 125, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.5);
        cursor: not-allowed;
    }

    .premium-export-filter-body {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .form-floating-export {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-floating-export .form-control,
    .form-floating-export .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem 0.25rem 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating-export .form-control:focus,
    .form-floating-export .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-floating-export > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating-export > .form-control:focus ~ label,
    .form-floating-export > .form-control:not(:placeholder-shown) ~ label,
    .form-floating-export > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #6f42c1;
    }

    .premium-export-field-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 5;
        pointer-events: none;
        transition: color 0.3s ease;
    }

    .form-floating-export .form-control:focus ~ .premium-export-field-icon,
    .form-floating-export .form-select:focus ~ .premium-export-field-icon {
        color: #6f42c1;
    }

    .btn-premium-export-action {
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        min-width: 140px;
    }

    .btn-premium-export-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium-export-action:hover::before {
        left: 100%;
    }

    .btn-premium-export-primary {
        background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
    }

    .btn-premium-export-primary:hover {
        background: linear-gradient(135deg, #5a2d91 0%, #4c2a85 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
        color: white;
    }

    .btn-premium-export-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-premium-export-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .export-filter-actions-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        border: 1px solid #dee2e6;
    }

    @media (max-width: 768px) {
        .premium-export-filter-header {
            padding: 1rem;
        }

        .premium-export-filter-title {
            font-size: 1.1rem;
            flex-direction: column;
            gap: 1rem;
        }

        .premium-export-buttons {
            flex-wrap: wrap;
        }

        .premium-export-filter-body {
            padding: 1.5rem;
        }

        .btn-premium-export-action {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            min-width: 120px;
        }
    }

    @media (max-width: 576px) {
        .form-floating-export .form-control,
        .form-floating-export .form-select {
            height: calc(3rem + 2px);
            padding: 0.75rem 0.5rem 0.25rem 0.5rem;
        }

        .premium-export-field-icon {
            right: 0.75rem;
        }

        .btn-export-premium {
            min-width: 70px;
            font-size: 0.7rem;
            padding: 0.4rem 0.8rem;
        }
    }
</style>

<div class="card premium-export-filter-card">
    <div class="premium-export-filter-header">
        <div class="premium-export-filter-title">
            <span>
                <i class="bi bi-funnel-fill me-2"></i>{% trans "Filters & Export Options" %}
            </span>
            <div class="premium-export-buttons">
                {# Export Buttons - URLs provided by BaseReportViewMixin context #}
                {% if export_csv_url %}
                    <a href="{{ export_csv_url }}" class="btn btn-export-premium btn-export-csv" title="{% trans 'Export to CSV' %}">
                        <i class="bi bi-file-earmark-spreadsheet me-1"></i>CSV
                    </a>
                {% endif %}
                {% if export_excel_url %}
                    <a href="{{ export_excel_url }}" class="btn btn-export-premium btn-export-excel" title="{% trans 'Export to Excel' %}">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </a>
                {% endif %}
                {% if export_pdf_url %} {# pdf_available check removed, handled by URL presence #}
                    <a href="{{ export_pdf_url }}" class="btn btn-export-premium btn-export-pdf" title="{% trans 'Download PDF' %}" target="_blank">
                        <i class="bi bi-file-earmark-pdf me-1"></i>PDF
                    </a>
                {% elif not pdf_available and view.report_code %} {# Show disabled if PDF URL was intended but lib missing #}
                    <button type="button" class="btn btn-export-premium btn-export-pdf disabled" title="{% trans 'PDF library (xhtml2pdf) not available' %}">
                        <i class="bi bi-file-earmark-pdf me-1"></i>PDF
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="premium-export-filter-body">
        {# The filter_form is passed from the main report view's context #}
        {% if filter_form %}
            <form method="get" novalidate id="exportFilterForm">
                <div class="row g-4">
                    {# Iterate over visible fields of the filter form #}
                    {% for field in filter_form.form.visible_fields %}
                        <div class="col-md-{% if filter_form.form.visible_fields|length > 3 %}3{% elif filter_form.form.visible_fields|length == 1 %}12{% elif filter_form.form.visible_fields|length == 2 %}6{% else %}4{% endif %}">
                            <div class="form-floating-export">
                                {% if field.field.widget.input_type == 'select' %}
                                    {% render_field field class+="form-select" placeholder=field.label %}
                                    <i class="premium-export-field-icon bi bi-chevron-down"></i>
                                {% elif field.field.widget.input_type == 'date' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-export-field-icon bi bi-calendar3"></i>
                                {% elif field.field.widget.input_type == 'number' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-export-field-icon bi bi-123"></i>
                                {% elif field.field.widget.input_type == 'text' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-export-field-icon bi bi-search"></i>
                                {% else %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-export-field-icon bi bi-pencil"></i>
                                {% endif %}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                                {% if field.help_text %}
                                    <small class="form-text text-muted mt-1">{{ field.help_text }}</small>
                                {% endif %}
                            </div>
                            {% for error in field.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>

                {# Action Buttons #}
                <div class="export-filter-actions-container">
                    <div class="row g-3 justify-content-end">
                        <div class="col-auto">
                            <a href="{{ request.path }}" class="btn btn-premium-export-action btn-premium-export-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>{% trans "Reset Filters" %}
                            </a>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-premium-export-action btn-premium-export-primary" id="applyExportFiltersBtn">
                                <i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        {% else %}
            <div class="text-center py-4">
                <i class="bi bi-info-circle text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2 mb-0">{% trans "No filters available for this report." %}</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Form submission enhancement
    const form = document.getElementById('exportFilterForm');
    const submitBtn = document.getElementById('applyExportFiltersBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Applying..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}';
            }, 5000);
        });
    }

    // Enhanced form field interactions
    const formControls = document.querySelectorAll('.form-floating-export .form-control, .form-floating-export .form-select');
    formControls.forEach(function(control) {
        // Focus enhancement
        control.addEventListener('focus', function() {
            this.closest('.form-floating-export').style.transform = 'translateY(-2px)';
            this.closest('.form-floating-export').style.boxShadow = '0 5px 15px rgba(111, 66, 193, 0.1)';
        });

        // Blur enhancement
        control.addEventListener('blur', function() {
            this.closest('.form-floating-export').style.transform = 'translateY(0)';
            this.closest('.form-floating-export').style.boxShadow = 'none';
        });
    });

    // Export button enhancements
    const exportButtons = document.querySelectorAll('.btn-export-premium');
    exportButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!this.classList.contains('disabled')) {
                // Add loading state for export
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Exporting...';
                this.disabled = true;

                // Reset after 3 seconds
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 3000);
            }
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Enter to submit form
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            if (submitBtn) {
                submitBtn.click();
            }
        }

        // Escape to reset form
        if (e.key === 'Escape') {
            const resetBtn = document.querySelector('a[href="{{ request.path }}"]');
            if (resetBtn) {
                resetBtn.click();
            }
        }
    });

    console.log('Premium export filter form initialized');
});
</script>



{# D:\school_fees_saas_v2\templates\parent_portal_base.html #}
{# OR apps/parent_portal/templates/parent_portal_base.html if nested #}
{% extends "tenant_base.html" %}
{% load static i18n core_tags nav_utils %}

{% block title %}
    {% block parent_portal_page_title %}{% trans "Parent Portal" %}{% endblock parent_portal_page_title %} - {{ request.tenant.name|default:"School" }}
{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    {# If you create this file, uncomment: #}
    {# <link rel="stylesheet" href="{% static 'css/parent_portal_specific_styles.css' %}"> #}
    <style>
        body { /* Example: ensure parent portal uses consistent font if not already set higher up */
            /* font-family: 'Poppins', sans-serif; */
        }
        .parent-portal-main-content-area {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem; /* Or more if footer is not fixed */
            min-height: calc(100vh - 56px - 56px - 40px); /* Adjust based on navbar, footer, and potential message bar heights */
        }

        /* Fix dropdown issues */
        .navbar .dropdown-menu {
            z-index: 1050 !important;
            position: absolute !important;
            border: 1px solid rgba(0,0,0,.15);
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
            animation: none !important;
        }

        .navbar .dropdown-toggle::after {
            transition: transform 0.15s ease-in-out;
        }

        .navbar .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .navbar .dropdown {
            position: relative;
        }

        /* Ensure dropdown doesn't flicker */
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Add other parent-portal-base specific styles here */

        /* Give all nav links a consistent font-weight to start */
        .navbar-nav .nav-link {
            font-weight: 400; /* Corresponds to fw-normal */
            position: relative; /* Needed for the pseudo-element */
            padding-right: 1rem; /* Adjust padding as needed */
            padding-left: 1rem;
        }

        /* Create a "ghost" element that contains the bolded text */
        .navbar-nav .nav-link::before {
            content: attr(data-text); /* We will set this data-text attribute */
            font-weight: 600; /* Corresponds to fw-semibold */
            position: absolute;
            visibility: hidden; /* The ghost element is invisible */
            height: 0;
            overflow: hidden;
        }

        /* When the link is active, make the REAL text bold */
        .navbar-nav .nav-link.active {
            font-weight: 600;
        }
    </style>
    {% block extra_parent_portal_css %}{% endblock extra_parent_portal_css %}
{% endblock page_specific_css %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top border-bottom shadow-sm">
    <div class="container-fluid">
        <a class="navbar-brand" href="{% url 'parent_portal:dashboard' %}">
            {% if request.tenant.schoolprofile.logo %}
                <img src="{{ request.tenant.schoolprofile.logo.url }}" alt="{{ request.tenant.name }} Logo" height="30" class="d-inline-block align-top me-2 rounded">
            {% else %}
                <i class="bi bi-house-heart-fill me-2"></i>
            {% endif %}
            {{ request.tenant.name|default:"" }} {% trans "Parent Portal" %}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#parentPortalNavbar" aria-controls="parentPortalNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="parentPortalNavbar">
            
            {# --- MAIN NAVIGATION (for logged-in parents) --- #}
            {% if user.is_authenticated and user_type_flags.IS_TENANT_PARENT_USER %}
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">

                    {# My Dashboard Link #}
                    {% if navbar_flags.show_parent_dashboard_link %}
                    <li class="nav-item">
                        <a class="nav-link {% is_active_nav 'parent_portal:dashboard' %}" 
                            href="{% url 'parent_portal:dashboard' %}"
                            data-text="{% trans 'My Dashboard' %}">
                            <i class="bi bi-grid-fill me-1"></i>{% trans "My Dashboard" %}
                        </a>
                    </li>
                    {% endif %}

                    {# My Children's Fees Link #}
                    {% if navbar_flags.show_parent_children_fees_link %}
                    <li class="nav-item">
                        <a class="nav-link {% is_active_nav 'parent_portal:children_fees_summary' %}"
                            href="{% url 'parent_portal:children_fees_summary' %}"
                            data-text="{% trans 'My Dashboard' %}">
                            <i class="bi bi-cash-stack me-1"></i>{% trans "Children's Fees" %}
                        </a>
                    </li>
                    {% endif %}
                    
                    {# Pay Fees Online Link #}
                    {% if navbar_flags.show_parent_online_payment_link %}
                    <li class="nav-item">
                        <a class="nav-link {% is_active_nav 'parent_portal:select_invoices_for_payment' %}" 
                            href="{% url 'parent_portal:select_invoices_for_payment' %}"
                            data-text="{% trans 'My Dashboard' %}">
                            <i class="bi bi-credit-card-2-front-fill me-1"></i>{% trans "Pay Fees Online" %}
                        </a>
                    </li>
                    {% endif %}

                    {# Payment History Link #}
                    {% if navbar_flags.show_parent_payment_history_link %}
                    <li class="nav-item">
                        <a class="nav-link {% is_active_nav 'parent_portal:payment_history_all' %}" 
                            href="{% url 'parent_portal:payment_history_all' %}"
                            data-text="{% trans 'My Dashboard' %}">
                            <i class="bi bi-clock-history me-1"></i>{% trans "Payment History" %}
                        </a>
                    </li>
                    {% endif %}

                </ul>
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    {# User Dropdown #}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="parentUserDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i> {% firstof user.get_full_name user.email %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="parentUserDropdown">
                            <li><a class="dropdown-item {% is_active_nav 'parent_portal:profile_display' %}" href="{% url 'parent_portal:profile_display' %}"><i class="bi bi-person-badge me-2"></i>{% trans "My Profile" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'parent_portal:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>{% trans "Logout" %}</a></li>
                        </ul>
                    </li>
                </ul>
            {% else %}
                {# Fallback for non-authenticated users on a parent portal URL #}
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'parent_portal:login' %}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>{% trans "Parent Login" %}
                        </a>
                    </li>
                </ul>
            {% endif %}
        </div>
    </div>
</nav>
{% endblock navbar %}

{# This content block is inherited from tenant_base.html (which inherits from base.html) #}
{# We are overriding it to define the structure for all parent portal pages #}
{% block content %}
    <div class="parent-portal-main-content-area">
        {# Messages displayed above the main page-specific content #}
        {% if messages %}
            <div class="container"> {# Messages within a standard container for padding #}
                {% include "partials/_messages.html" %}
            </div>
        {% endif %}

        {# This is the block that specific parent portal pages (dashboard, profile, etc.) will fill #}
        {% block parent_portal_main_content %}
            <div class="container text-center py-5"> {# Default content if not overridden #}
                <h1 class="display-5">{% trans "Parent Portal Section" %}</h1>
                <p class="lead text-muted">{% trans "Specific page content should override the 'parent_portal_main_content' block." %}</p>
            </div>
        {% endblock parent_portal_main_content %}
    </div>
{% endblock content %}

{% block footer %}
    <footer class="footer mt-auto py-3 bg-light border-top text-center">
        <div class="container">
            <span class="text-muted">
                © {% now "Y" %} {{ request.tenant.name|default:"Your School" }}. {% trans "Parent Information Portal" %}.
            </span>
        </div>
    </footer>
{% endblock footer %}

{% block page_specific_js %}
    {{ block.super }}
    {# Simple fix for dropdown issues #}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Simple fix: ensure dropdowns work without interference
            const dropdowns = document.querySelectorAll('.dropdown-toggle');
            dropdowns.forEach(function(dropdown) {
                dropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close other dropdowns
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                        if (menu !== dropdown.nextElementSibling) {
                            menu.classList.remove('show');
                            menu.previousElementSibling.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // Toggle current dropdown
                    const menu = dropdown.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {
                        const isShown = menu.classList.contains('show');
                        if (isShown) {
                            menu.classList.remove('show');
                            dropdown.setAttribute('aria-expanded', 'false');
                        } else {
                            menu.classList.add('show');
                            dropdown.setAttribute('aria-expanded', 'true');
                        }
                    }
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                        menu.classList.remove('show');
                        menu.previousElementSibling.setAttribute('aria-expanded', 'false');
                    });
                }
            });
        });
    </script>
    {% block extra_parent_portal_js %}{% endblock extra_parent_portal_js %}
{% endblock page_specific_js %}



{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\journalentry_list.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize %} {# humanize for intcomma #}

{% block tenant_page_title %}{{ view_title|default:_("General Journal Entries") }}{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .status-badge-je-draft { background-color: var(--bs-warning); color: var(--bs-dark) !important; }
        .status-badge-je-posted { background-color: var(--bs-success); }
        .table th, .table td { vertical-align: middle; }
    </style>
{% endblock extra_tenant_css %}

{% block tenant_specific_content %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title|default:_("General Journal Entries") }}</h1>
        {% if perms.accounting.add_journalentry %} {# Check permission #}
        <a href="{% url 'accounting:journalentry_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle-fill me-1"></i> {% trans "New Manual Entry" %}
        </a>
        {% endif %}
    </div>

    {# Add filter form here later if needed #}

    {% if journal_entries %}
    <div class="card shadow-sm">
        <div class="card-body p-0"> {# Remove padding if table is directly inside #}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "JE ID" %}</th>
                            <th>{% trans "Date" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Type" %}</th>
                            <th class="text-end">{% trans "Total Debits" %}</th>
                            <th class="text-end">{% trans "Total Credits" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th>{% trans "Created By" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for je in journal_entries %}
                        <tr class="{% if not je.is_balanced and je.status == 'DRAFT' %}table-warning{% endif %}">
                            <td><a href="{% url 'accounting:journalentry_detail' pk=je.pk %}">JE-{{ je.pk|stringformat:"05d" }}</a></td>
                            <td>{{ je.date|date:"d M Y" }}</td>
                            <td>{{ je.description|truncatechars:60 }}</td>
                            <td><span class="badge bg-secondary rounded-pill">{{ je.get_entry_type_display }}</span></td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ je.total_debits|floatformat:2|intcomma }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ je.total_credits|floatformat:2|intcomma }}</td>
                            <td class="text-center">
                                <span class="badge rounded-pill status-badge-je-{{ je.status|lower }}">
                                    {{ je.get_status_display }}
                                </span>
                                {% if not je.is_balanced and je.status == 'DRAFT' %}
                                    <i class="bi bi-exclamation-triangle-fill text-danger ms-1" title="{% trans 'Unbalanced' %}"></i>
                                {% endif %}
                            </td>
                            <td>
                                {% if je.created_by %}
                                    {{ je.created_by.get_full_name|default:je.created_by.email|default:je.created_by.username }}
                                {% else %}
                                    {% trans "System" %}
                                {% endif %}
                            </td>
                            <td class="text-center actions-column">
                                <a href="{% url 'accounting:journalentry_detail' pk=je.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}"><i class="bi bi-eye-fill"></i></a>
                                {% if je.entry_type == 'MANUAL' and je.status == 'DRAFT' and perms.accounting.change_journalentry %}
                                    {# <a href="{% url 'accounting:journalentry_update' pk=je.pk %}" class="btn btn-sm btn-outline-primary" title="{% trans 'Edit Draft' %}"><i class="bi bi-pencil-fill"></i></a> #}
                                {% endif %}
                                {% if je.entry_type == 'MANUAL' and je.status == 'DRAFT' and perms.accounting.delete_journalentry %}
                                    {# <a href="{% url 'accounting:journalentry_delete' pk=je.pk %}" class="btn btn-sm btn-outline-danger needs-confirmation" data-confirm-message="Are you sure you want to delete this draft journal entry?" title="{% trans 'Delete Draft' %}"><i class="bi bi-trash-fill"></i></a> #}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% if is_paginated %}
        {% include "partials/_pagination.html" with page_obj=page_obj %}
    {% endif %}

    {% else %}
    <div class="alert alert-info mt-3" role="alert">
        <i class="bi bi-info-circle-fill me-2"></i> {% trans "No journal entries found." %}
        {% if perms.accounting.add_journalentry %}
            <a href="{% url 'accounting:journalentry_create' %}" class="alert-link">{% trans "Create the first manual journal entry." %}</a>
        {% endif %}
    </div>
    {% endif %}
{% endblock tenant_specific_content %}



















{% comment %} {% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title|default:_("General Journal Entries") }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-3"> {# Assuming tenant_base provides the outer container-fluid #}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title|default:_("General Journal Entries") }}</h1>
        {% if perms.accounting.add_journalentry %} {# Check permission before showing create button #}
        <a href="{% url 'accounting:journalentry_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle-fill me-2"></i> {% trans "New Manual Entry" %}
        </a>
        {% endif %}
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if journal_entries %}
    <div class="card shadow-sm">
        <div class="card-body p-0"> {# Removed padding for table to be flush #}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "JE ID" %}</th>
                            <th>{% trans "Date" %}</th>
                            <th>{% trans "Narration" %}</th>
                            <th>{% trans "Type" %}</th>
                            <th class="text-end">{% trans "Debits" %}</th>
                            <th class="text-end">{% trans "Credits" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for je in journal_entries %}
                        {# You can add a class to the row based on status for visual cues #}
                        <tr class="{% if je.status == je.StatusChoices.DRAFT %}table-warning{% elif je.status == je.StatusChoices.CANCELLED %}table-danger opacity-50{% endif %}">
                            <td><a href="{% url 'accounting:journalentry_detail' pk=je.pk %}">{{ je.entry_number|default:je.pk|stringformat:"05d" }}</a></td>
                            <td>{{ je.date|date:"d M Y" }}</td>
                            <td>{{ je.narration|truncatewords:10 }}</td>
                            <td>
                                <span class="badge rounded-pill 
                                    {% if je.entry_type == je.EntryTypeChoices.MANUAL %}bg-secondary
                                    {% elif je.entry_type == je.EntryTypeChoices.INVOICE %}bg-info text-dark
                                    {% elif je.entry_type == je.EntryTypeChoices.PAYMENT %}bg-success
                                    {% elif je.entry_type == je.EntryTypeChoices.EXPENSE %}bg-warning text-dark
                                    {% else %}bg-light text-dark border{% endif %}">
                                    {{ je.get_entry_type_display }}
                                </span>
                            </td>
                            <td class="text-end">{{ je.total_debits|floatformat:2|intcomma }}</td>
                            <td class="text-end">{{ je.total_credits|floatformat:2|intcomma }}</td>
                            <td class="text-center">
                                <span class="badge rounded-pill 
                                    {% if je.status == je.StatusChoices.POSTED %}bg-success
                                    {% elif je.status == je.StatusChoices.DRAFT %}bg-warning text-dark
                                    {% elif je.status == je.StatusChoices.CANCELLED %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {{ je.get_status_display }}
                                </span>
                            </td>
                            <td class="text-center actions-column"> {# Added actions-column class from tenant_base for consistent button spacing if desired #}
                                <a href="{% url 'accounting:journalentry_detail' pk=je.pk %}" class="btn btn-sm btn-outline-info" title="{% trans 'View Details' %}"><i class="bi bi-eye-fill"></i></a>
                                
                                {# --- ACTION BUTTONS FOR DRAFT, MANUAL ENTRIES --- #}
                                {% if je.status == je.StatusChoices.DRAFT and je.entry_type == je.EntryTypeChoices.MANUAL %}
                                    {% if perms.accounting.change_journalentry %}
                                    <a href="{% url 'accounting:journalentry_update' pk=je.pk %}" class="btn btn-sm btn-outline-primary mx-1" title="{% trans 'Edit Draft' %}"><i class="bi bi-pencil-fill"></i></a>
                                    {% endif %}
                                    {% if perms.accounting.delete_journalentry %}
                                    <a href="{% url 'accounting:journalentry_delete' pk=je.pk %}" class="btn btn-sm btn-outline-danger" title="{% trans 'Delete Draft' %}"><i class="bi bi-trash-fill"></i></a>
                                    {% endif %}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {# Pagination (ensure your view supports pagination and you have this partial) #}
    {% if is_paginated %}
        {% include "partials/_pagination.html" with page_obj=page_obj %}
    {% endif %}

    {% else %}
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle-fill me-2"></i>{% trans "No journal entries found." %}
        {% if perms.accounting.add_journalentry %}
            <a href="{% url 'accounting:journalentry_create' %}" class="alert-link">{% trans "Create the first one?" %}</a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock tenant_specific_content %} {% endcomment %}



<!-- apps/hr/templates/hr/staff_payslip_list.html -->

{% extends "base.html" %} {# Or your main base template #}

{% block title %}My Payslips{% endblock title %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">My Payslips</h1>

    <div class="card">
        <div class="card-body">
            {% if payslip_list %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Payroll Period</th>
                                <th>Status</th>
                                <th class="text-end">Net Pay</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payslip in payslip_list %}
                            <tr>
                                <td>
                                    {{ payslip.payroll_run.start_date|date:"F j, Y" }} - {{ payslip.payroll_run.end_date|date:"F j, Y" }}
                                </td>
                                <td>
                                    {% if payslip.payroll_run.status == 'PAID' %}
                                        <span class="badge bg-success">Paid</span>
                                    {% else %}
                                        <span class="badge bg-warning text-dark">Processed</span>
                                    {% endif %}
                                </td>
                                <td class="text-end fw-bold">
                                    ${{ payslip.net_pay|floatformat:2 }} {# Use your currency symbol #}
                                </td>
                                <td class="text-center">
                                    <a href="{% url 'hr:payslip_pdf' pk=payslip.pk %}" class="btn btn-primary btn-sm" target="_blank">
                                        <i class="bi bi-file-earmark-pdf"></i> View PDF
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">You do not have any payslips available yet.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock content %}




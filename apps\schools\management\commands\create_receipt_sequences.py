"""
Management command to create ReceiptSequence records for all tenants
that don't have one yet.
"""

from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.schools.models import ReceiptSequence


class Command(BaseCommand):
    help = 'Create ReceiptSequence records for all tenants that do not have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        # Get all tenant schools
        tenants = School.objects.all()
        
        if not tenants.exists():
            self.stdout.write(
                self.style.WARNING('No tenant schools found.')
            )
            return

        self.stdout.write(f'Found {tenants.count()} tenant(s)')
        
        created_count = 0
        existing_count = 0
        error_count = 0

        for tenant in tenants:
            self.stdout.write(f'\nProcessing tenant: {tenant.schema_name} ({tenant.name})')
            
            try:
                with schema_context(tenant.schema_name):
                    # Check if ReceiptSequence table exists and has records
                    try:
                        sequence = ReceiptSequence.objects.first()

                        if sequence:
                            existing_count += 1
                            current_padding = sequence.padding_digits
                            target_padding = 9  # For 13-digit format: RCT- (4) + 9 digits = 13

                            self.stdout.write(
                                self.style.SUCCESS(f'  ✓ ReceiptSequence exists (padding: {current_padding})')
                            )

                            # Update to 13-digit format if needed
                            if current_padding != target_padding:
                                if not dry_run:
                                    sequence.padding_digits = target_padding
                                    sequence.save(update_fields=['padding_digits'])
                                    self.stdout.write(
                                        self.style.SUCCESS(f'  ✓ Updated to 13-digit format')
                                    )
                                else:
                                    self.stdout.write(
                                        self.style.WARNING(f'  → Would update to 13-digit format')
                                    )
                        else:
                            # Create new ReceiptSequence
                            target_padding = 9  # 13-digit format

                            if not dry_run:
                                sequence = ReceiptSequence.objects.create(
                                    prefix='RCT-',
                                    last_number=0,
                                    padding_digits=target_padding
                                )
                                created_count += 1
                                self.stdout.write(
                                    self.style.SUCCESS(f'  ✓ Created 13-digit ReceiptSequence: {sequence}')
                                )
                            else:
                                created_count += 1
                                self.stdout.write(
                                    self.style.WARNING(f'  → Would create 13-digit ReceiptSequence with prefix "RCT-"')
                                )
                    except Exception as table_error:
                        self.stdout.write(
                            self.style.ERROR(f'  ❌ Table error: {table_error}')
                        )
                        self.stdout.write(
                            self.style.WARNING(f'  → Tenant schema may need migration: python manage.py migrate_schemas --schema={tenant.schema_name}')
                        )
                        error_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Error processing tenant {tenant.schema_name}: {e}')
                )
                error_count += 1

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write('SUMMARY:')
        self.stdout.write(f'  Tenants processed: {tenants.count()}')
        self.stdout.write(f'  ReceiptSequences created: {created_count}')
        self.stdout.write(f'  ReceiptSequences already existed: {existing_count}')
        self.stdout.write(f'  Errors: {error_count}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a DRY RUN - no changes were made.')
            )
            self.stdout.write(
                self.style.SUCCESS('Run without --dry-run to apply changes.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\nReceiptSequence setup completed!')
            )

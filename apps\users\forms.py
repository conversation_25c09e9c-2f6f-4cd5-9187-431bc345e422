# D:\school_fees_saas_v2\apps\users\forms.py
from django.contrib.auth.forms import AuthenticationForm
from django import forms # Import forms for widget attributes

class SchoolAdminLoginForm(AuthenticationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Customize labels and add placeholders for better UX
        self.fields['username'].label = "Administrator Email"
        self.fields['username'].widget = forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your administrator email address',
            'id': 'floatingAdminEmail',
            'autofocus': True,
            'autocomplete': 'email',
            'data-bs-toggle': 'tooltip',
            'data-bs-placement': 'top',
            'title': 'Enter the email address associated with your administrator account'
        })
        self.fields['password'].label = "Password"
        self.fields['password'].widget = forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your secure password',
            'id': 'floatingAdminPassword',
            'autocomplete': 'current-password',
            'data-bs-toggle': 'tooltip',
            'data-bs-placement': 'top',
            'title': 'Enter your administrator account password'
        })


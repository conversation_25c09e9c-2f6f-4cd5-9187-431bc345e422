{# D:\school_fees_saas_v2\templates\schools\parent_login.html #}
{% extends "tenant_base.html" %}

{% load static %}

{% block title %}{{ view_title|default:"Parent Login" }}{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    <style>.card-header.bg-parent-login { background-color: #17a2b8 !important; /* Info Blue */ }</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-header bg-parent-login text-white">
                    <h4 class="mb-0">{{ view_title }}</h4>
                    {% if request.tenant %}<p class="mb-0 text-white-50">School: {{ request.tenant.name }}</p>{% endif %}
                </div>
                <div class="card-body p-4">
                    {% include "partials/_messages.html" %} {# Assuming you have a messages partial #}
                    <form method="post" novalidate>{% csrf_token %} {{ form.as_p }}
                        <button type="submit" class="btn btn-info w-100 btn-lg mt-3">Login as Parent</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}



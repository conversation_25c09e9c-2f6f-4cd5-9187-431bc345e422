{# D:\school_fees_saas_v2\apps\reporting\templates\reporting\pdf\expense_report_pdf.html #}
{% extends "reporting/pdf/pdf_base.html" %} {# Assume you have a base PDF template #}
{% load i18n humanize %}

{% block pdf_title %}{{ report_title }}{% endblock %}

{% block pdf_content %}
    <div class="report-header">
        <h2>{{ report_title }}</h2>
        {% if school_profile %}<h4>{{ school_profile.name_on_reports|default:report_tenant_name }}</h4>{% endif %}
        <p>
            {% if filter_form_data.start_date and filter_form_data.end_date %}
                {% trans "Period:" %} {{ filter_form_data.start_date|date:"d-M-Y" }} {% trans "to" %} {{ filter_form_data.end_date|date:"d-M-Y" }}
            {% elif filter_form_data.start_date %}
                {% trans "From:" %} {{ filter_form_data.start_date|date:"d-M-Y" }}
            {% elif filter_form_data.end_date %}
                {% trans "Up to:" %} {{ filter_form_data.end_date|date:"d-M-Y" }}
            {% endif %}
        </p>
        <p class="generated-on">{% trans "Generated on:" %} {{ report_generated_at|date:"d-M-Y H:i" }}</p>
    </div>

    <hr>

    <table class="report-table-pdf">
        <thead>
            <tr>
                <th>{% trans "Date" %}</th>
                <th>{% trans "Description" %}</th>
                <th>{% trans "Category" %}</th>
                <th class="amount">{% trans "Amount" %}</th>
                <th>{% trans "Payment Method" %}</th>
            </tr>
        </thead>
        <tbody>
            {% for expense in report_items %} {# report_items is the queryset passed to PDF context #}
            <tr>
                <td>{{ expense.expense_date|date:"Y-m-d" }}</td>
                <td>{{ expense.description }}</td>
                <td>{{ expense.category.name|default:"N/A" }}</td>
                <td class="amount">{{ expense.amount|intcomma }}</td>
                <td>{{ expense.payment_method.name|default:"N/A" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="no-data">{% trans "No expense records found for this period." %}</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if report_items %}
        <tfoot>
            <tr>
                <td colspan="3" class="total-label"><strong>{% trans "Total Expenses" %}:</strong></td>
                <td class="amount total-value"><strong>{{ summary_total_expenses|intcomma }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
        {% endif %}
    </table>
{% endblock %}



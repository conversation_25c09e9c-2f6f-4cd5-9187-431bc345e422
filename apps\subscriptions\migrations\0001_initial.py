# Generated by Django 5.1.9 on 2025-06-18 20:41

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.Char<PERSON>ield(help_text='Short unique code for the feature (e.g., HR_MODULE, PARENT_PORTAL).', max_length=50, unique=True)),
                ('name', models.CharField(help_text='User-friendly name of the feature.', max_length=150)),
                ('description', models.TextField(blank=True, help_text='Optional description.')),
            ],
            options={
                'verbose_name': 'Plan Feature',
                'verbose_name_plural': 'Plan Features',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('TRIALING', 'Trialing'), ('ACTIVE', 'Active'), ('PAST_DUE', 'Past Due'), ('CANCELLED', 'Cancelled'), ('UNPAID', 'Unpaid'), ('INCOMPLETE', 'Incomplete'), ('INCOMPLETE_EXPIRED', 'Incomplete Expired')], default='INCOMPLETE', max_length=20)),
                ('billing_cycle', models.CharField(choices=[('MONTHLY', 'Monthly'), ('ANNUALLY', 'Annually')], default='MONTHLY', max_length=10)),
                ('pg_subscription_id', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('pg_customer_id', models.CharField(blank=True, max_length=100, null=True)),
                ('trial_end_date', models.DateTimeField(blank=True, null=True)),
                ('current_period_start', models.DateTimeField(blank=True, null=True)),
                ('current_period_end', models.DateTimeField(blank=True, null=True)),
                ('cancel_at_period_end', models.BooleanField(default=False)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'School Subscription',
                'verbose_name_plural': 'School Subscriptions',
                'ordering': ['school__name'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('price_monthly', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('price_annually', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('pg_price_id_monthly', models.CharField(blank=True, max_length=100, null=True)),
                ('pg_price_id_annually', models.CharField(blank=True, max_length=100, null=True)),
                ('max_students', models.PositiveIntegerField(blank=True, null=True)),
                ('max_staff', models.PositiveIntegerField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_public', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subscription Plan',
                'verbose_name_plural': 'Subscription Plans',
                'ordering': ['price_monthly', 'name'],
            },
        ),
    ]

# Generated by Django 5.1.9 on 2025-06-18 20:41

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactInquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('subject', models.<PERSON>r<PERSON>ield(max_length=200)),
                ('message', models.TextField()),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('is_addressed', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Contact Inquiries',
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('author_name', models.CharField(max_length=100)),
                ('author_title_school', models.CharField(help_text='e.g., Principal, XYZ School', max_length=150)),
                ('quote', models.TextField()),
                ('rating', models.PositiveSmallIntegerField(blank=True, help_text='Optional rating (1-5 stars)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('is_approved', models.BooleanField(default=False, help_text='Approve to display on site')),
                ('submitted_on', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Testimonial',
                'verbose_name_plural': 'Testimonials',
                'ordering': ['-submitted_on'],
            },
        ),
    ]

# D:\school_fees_saas_v2\apps\payments\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _  # <--- ADD THIS LINE
from .models import Payment, PaymentMethod, PaymentAllocation

# Inline for PaymentAllocations on Payment admin page (Optional but useful)
class PaymentAllocationInline(admin.TabularInline):
    model = PaymentAllocation
    extra = 0 # Don't show empty forms by default, add as needed
    autocomplete_fields = ['invoice'] # If you want to autocomplete invoice selection
    fields = ('invoice', 'amount_allocated', 'allocation_date')
    readonly_fields = ('allocation_date',) # Usually set automatically

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'linked_account', 'is_active', 'created_at')
    list_filter = ('type', 'is_active')
    search_fields = ('name', 'description')
    autocomplete_fields = ['linked_account']


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        'id', 
        'transaction_reference', # CORRECTED (was reference_number)
        'payment_date', 
        'parent_payer_display', # Custom method for display
        'student_display',      # Custom method for display
        'amount', 
        'payment_method', 
        'status', 
        'payment_type',
        'processed_by_staff_display', # Custom method for display
        'created_at'
    )
    list_filter = (
        'status', 
        'payment_type', 
        'payment_method', 
        'academic_year',
        'payment_date',
        'processed_by_staff', # CORRECTED (was recorded_by), ensure this field exists on Payment
        'parent_payer'        # If you added parent_payer
    )
    search_fields = (
        'transaction_reference', 
        'notes', 
        'parent_payer__user__email', # Example search on parent's email
        'parent_payer__user__first_name',
        'parent_payer__user__last_name',
        'student__user__email',      # Example search on student's user email
        'student__first_name',
        'student__last_name',
        'processed_by_staff__user__email' # Example search on staff's email
    )
    autocomplete_fields = (
        'parent_payer',       # If you added this
        'student',            # If you kept this
        'processed_by_staff', # CORRECTED (was recorded_by)
        'payment_method',
        'academic_year'
        # 'invoice' is REMOVED as it's no longer a direct FK
    )
    readonly_fields = ('created_at', 'updated_at', 'created_by', 'total_allocated', 'calculated_unallocated_amount') # Make created_by readonly
    
    fieldsets = (
        (None, {
            'fields': ('parent_payer', 'student', 'academic_year', 'payment_type', 'status')
        }),
        (_("Payment Details"), {
            'fields': ('payment_method', 'amount', 'payment_date', 'transaction_reference', 'notes')
        }),
        (_("Processing Info"), {
            'fields': ('processed_by_staff', 'unallocated_amount', 'total_allocated', 'created_by', 'created_at', 'updated_at')
        }),
    )
    
    inlines = [PaymentAllocationInline] # Add this to manage allocations directly on Payment page

    def parent_payer_display(self, obj):
        if obj.parent_payer:
            return obj.parent_payer.get_full_name() or obj.parent_payer.email
        return "-"
    parent_payer_display.short_description = _("Paying Parent")

    def student_display(self, obj):
        if obj.student:
            return obj.student.get_full_name()
        return "-"
    student_display.short_description = _("Student")

    def processed_by_staff_display(self, obj):
        if obj.processed_by_staff:
            return obj.processed_by_staff.get_full_name() or obj.processed_by_staff.email
        return "-"
    processed_by_staff_display.short_description = _("Processed By")


@admin.register(PaymentAllocation)
class PaymentAllocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'payment_link', 'invoice_link', 'amount_allocated', 'allocation_date')
    list_filter = ('allocation_date',)
    search_fields = ('payment__transaction_reference', 'invoice__invoice_number')
    autocomplete_fields = ['payment', 'invoice']
    readonly_fields = ('allocation_date',)

    def payment_link(self, obj):
        from django.utils.html import format_html
        from django.urls import reverse
        link = reverse("admin:payments_payment_change", args=[obj.payment.id])
        return format_html('<a href="{}">P-{}</a>', link, obj.payment.id)
    payment_link.short_description = _("Payment")

    def invoice_link(self, obj):
        from django.utils.html import format_html
        from django.urls import reverse
        if obj.invoice:
            link = reverse("admin:fees_invoice_change", args=[obj.invoice.id]) # Assuming 'fees' app for Invoice
            return format_html('<a href="{}">Inv-{}</a>', link, obj.invoice.invoice_number)
        return "-"
    invoice_link.short_description = _("Invoice")
    
    
    
    
    
    
    
    
    
    
    
    
    




# # D:\school_fees_saas_v2\apps\payments\admin.py
# from django.contrib import admin
# from .models import Payment, PaymentMethod
# # from apps.schools.models import StaffUser # For type hinting if needed

# @admin.register(PaymentMethod)
# class PaymentMethodAdmin(admin.ModelAdmin):
#     list_display = ('name', 'type', 'linked_account_display', 'is_active') # Assumes 'type' field added to model
#     list_filter = ('type', 'is_active', 'linked_account__account_type')    # Assumes 'type' field added to model
#     search_fields = ('name', 'linked_account__name')
#     autocomplete_fields = ['linked_account']
#     ordering = ('name',)

#     @admin.display(description='Linked Account (CoA)', ordering='linked_account__name')
#     def linked_account_display(self, obj):
#         return obj.linked_account.name if obj.linked_account else None


# @admin.register(Payment)
# class PaymentAdmin(admin.ModelAdmin):
#     list_display = (
#         'reference_number', 
#         'student_name_display', 
#         'amount_display', 
#         'payment_date_display', 
#         'payment_method_name_display', 
#         'status',               # Assumes 'status' field added to model
#         'payment_type',         # Existing field
#         'recorded_by_user_display'
#     )
#     search_fields = (
#         'reference_number', 
#         'student__first_name', 
#         'student__last_name',
#         'student__admission_number', # Good to search by this too
#         'recorded_by__email',   # Corrected from created_by
#         'recorded_by__first_name',
#         'recorded_by__last_name',
#     )
#     list_filter = (
#         'status',               # Assumes 'status' field added to model
#         'payment_type',
#         'payment_date', 
#         'payment_method',
#         'student__current_class',
#         'academic_year',
#         'recorded_by',
#     )
#     list_select_related = ('student', 'payment_method', 'recorded_by', 'invoice', 'academic_year')
#     autocomplete_fields = ['student', 'payment_method', 'recorded_by', 'invoice', 'academic_year']
#     date_hierarchy = 'payment_date'
#     readonly_fields = ('created_at', 'updated_at')
#     ordering = ('-payment_date',)

#     fieldsets = (
#         (None, {'fields': ('student', 'invoice', 'academic_year')}),
#         ('Payment Details', {'fields': ('payment_method', 'amount', 'payment_date', 'reference_number', 'payment_type', 'status')}), # Added status
#         ('Narration', {'fields': ('notes',)}),
#         ('Audit', {'fields': ('recorded_by', 'created_at', 'updated_at')}),
#     )

#     @admin.display(description='Student', ordering='student__last_name')
#     def student_name_display(self, obj):
#         return obj.student.full_name if obj.student else "N/A (e.g., Other Income)"
    
#     @admin.display(description='Amount Paid')
#     def amount_display(self, obj):
#         # TODO: Implement currency symbol fetching, e.g., from request.tenant.schoolprofile.currency_symbol
#         currency_symbol = "$" # Placeholder
#         return f"{currency_symbol}{obj.amount:,.2f}"

#     @admin.display(description='Payment Date', ordering='payment_date')
#     def payment_date_display(self, obj):
#         return obj.payment_date.strftime("%Y-%m-%d %H:%M") if obj.payment_date else None

#     @admin.display(description='Payment Method', ordering='payment_method__name')
#     def payment_method_name_display(self, obj):
#         return obj.payment_method.name if obj.payment_method else None
    
#     @admin.display(description='Recorded By', ordering='recorded_by__email')
#     def recorded_by_user_display(self, obj):
#         if obj.recorded_by:
#             return obj.recorded_by.get_full_name() or obj.recorded_by.email
#         return None























# management/commands/ensure_school_profiles.py
from django.core.management.base import BaseCommand
from django_tenants.utils import tenant_context
from apps.tenants.models import School # Your tenant model
from apps.schools.models import SchoolProfile # Your profile model

class Command(BaseCommand):
    help = 'Ensures every existing tenant has a SchoolProfile.'

    def handle(self, *args, **options):
        public_schema_tenants = School.objects.all() # Get all tenant objects
        for tenant_obj in public_schema_tenants:
            with tenant_context(tenant_obj): # Switch to tenant's schema
                profile, created = SchoolProfile.objects.get_or_create(
                    defaults={
                        'school_name_override': tenant_obj.name,
                        'school_name_on_reports': tenant_obj.name,
                        # Add any other essential defaults
                    }
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created SchoolProfile for {tenant_obj.name}'))
                else:
                    self.stdout.write(self.style.WARNING(f'SchoolProfile already existed for {tenant_obj.name}'))
                    
                    
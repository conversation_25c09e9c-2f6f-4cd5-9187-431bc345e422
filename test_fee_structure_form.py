#!/usr/bin/env python
"""
Test script to check fee structure form functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_tenants.utils import schema_context
from apps.fees.forms import FeeStructureForm, FeeStructureItemInlineFormSet
from apps.fees.models import FeeStructure, FeeHead
from apps.schools.models import AcademicYear, Term

def test_form():
    print("Testing Fee Structure Form...")
    
    with schema_context('alpha'):
        # Test form initialization
        form = FeeStructureForm()
        print(f"Form fields: {list(form.fields.keys())}")
        
        # Test formset initialization
        formset = FeeStructureItemInlineFormSet()
        print(f"Formset prefix: {formset.prefix}")
        print(f"Formset extra forms: {formset.extra}")
        print(f"Formset can_delete: {formset.can_delete}")
        
        # Check if fee heads exist
        fee_heads = FeeHead.objects.all()
        print(f"Available fee heads: {list(fee_heads.values_list('name', flat=True))}")
        
        # Check academic years and terms
        academic_years = AcademicYear.objects.all()
        print(f"Available academic years: {list(academic_years.values_list('name', flat=True))}")
        
        if academic_years.exists():
            first_year = academic_years.first()
            terms = Term.objects.filter(academic_year=first_year)
            print(f"Terms for {first_year.name}: {list(terms.values_list('name', flat=True))}")
        
        # Test form with data
        if academic_years.exists() and fee_heads.exists():
            first_year = academic_years.first()
            form_data = {
                'name': 'Test Fee Structure',
                'academic_year': first_year.id,
                'description': 'Test description',
                'is_active': True
            }
            
            form = FeeStructureForm(data=form_data)
            print(f"Form is valid: {form.is_valid()}")
            if not form.is_valid():
                print(f"Form errors: {form.errors}")
        
        print("Form test completed!")

if __name__ == "__main__":
    test_form()

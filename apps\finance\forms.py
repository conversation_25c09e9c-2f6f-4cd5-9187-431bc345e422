# D:\school_fees_saas_v2\apps\finance\forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone # For default dates
from decimal import Decimal

from django.db import models

# Import models from this app
from .models import ExpenseCategory, Vendor, Expense, BudgetItem, BudgetAmount

# Import models from other apps if needed for choices/linking
from apps.accounting.models import Account 
from apps.fees.models import AcademicYear, Term # For BudgetAmount

# from apps.finance.models import ChartOfAccount # If you use paid_from_account
from apps.payments.models import PaymentMethod

import logging
from django.db import models as django_db_models # For Q objects
from .models import BudgetItem # Make sure ChartOfAccount is imported

# ... other imports ...

logger = logging.getLogger(__name__)


class ExpenseCategoryForm(forms.ModelForm):
    class Meta:
        model = ExpenseCategory
        fields = ['name', 'description', 'expense_account']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Office Supplies, Utilities'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'expense_account': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'expense_account': _("Linked Expense Account (CoA)"),
        }
        help_texts = {
            'expense_account': _("Select the corresponding expense account from your Chart of Accounts."),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Correct way to filter:
        try:
            # Option 1: Filter by a known code for the 'Expense' AccountType
            # Ensure ACCOUNTING_EXPENSE_TYPE_CODE is defined in your settings
            # and an AccountType with this code exists.
            expense_account_type = AccountType.objects.get(code=settings.ACCOUNTING_EXPENSE_TYPE_CODE)
            self.fields['expense_account'].queryset = Account.objects.filter(
                account_type=expense_account_type,
                is_active=True
            ).order_by('code', 'name')
        except AccountType.DoesNotExist:
            # Fallback if the specific expense AccountType code isn't found
            # This might mean a configuration issue or the type doesn't exist for the tenant
            self.fields['expense_account'].queryset = Account.objects.filter(
                account_type__normal_balance='DR', # General filter for debit normal balance accounts
                account_type__is_control_account=False, # Typically don't link to control accounts
                is_active=True
            ).order_by('code', 'name')
            # You might want to add a warning message if the specific type isn't found
            print(f"WARNING: Could not find AccountType with code {settings.ACCOUNTING_EXPENSE_TYPE_CODE}. Falling back to broader filter for expense_account.")
        except AttributeError:
            # Fallback if ACCOUNTING_EXPENSE_TYPE_CODE is not in settings
            self.fields['expense_account'].queryset = Account.objects.filter(
                account_type__normal_balance='DR',
                is_active=True
            ).order_by('code', 'name')
            print(f"WARNING: settings.ACCOUNTING_EXPENSE_TYPE_CODE not found. Falling back to broader filter for expense_account.")

        # Ensure the field is optional if the model allows it
        if self.Meta.model._meta.get_field('expense_account').blank:
            self.fields['expense_account'].required = False
            self.fields['expense_account'].empty_label = "--------- (No specific account)"
            
            

class VendorForm(forms.ModelForm):
    class Meta:
        model = Vendor
        fields = [
            'name', 'contact_person', 'email', 'phone_number',
            'address_line1', 'address_line2', 'city', 'state_province',
            'postal_code', 'country', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'contact_person': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'state_province': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }




class ExpenseForm(forms.ModelForm):
    class Meta:
        model = Expense
        fields = [
            'expense_date', 'category', 'vendor', 'amount',
            'description', 'reference_number', 'payment_method',
        ]
        widgets = {
            'expense_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-select select2-category'}), # Added example class
            'vendor': forms.Select(attrs={'class': 'form-select select2-vendor'}),     # Added example class
            'amount': forms.NumberInput(attrs={'class': 'form-control text-end', 'step': '0.01', 'placeholder': '0.00'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'Detailed description of the expense'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Invoice #, Receipt #'}),
            'payment_method': forms.Select(attrs={'class': 'form-select select2-payment-method'}), # Added example class
        }
        labels = {
            'category': _("Expense Category"),
            'reference_number': _("Reference / Bill Number"),
            'payment_method': _("Paid Using (Payment Method)"),
        }
        
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # If you pass request for tenant context
        self.tenant = kwargs.pop('tenant', None) # Or tenant directly
        super().__init__(*args, **kwargs)

        # These queries run within the tenant schema context
        # Ensure they are efficient and don't hold cursors unnecessarily.
        # .all() at the end forces evaluation for ModelChoiceField.
        self.fields['category'].queryset = ExpenseCategory.objects.all().order_by('name')
        self.fields['vendor'].queryset = Vendor.objects.all().order_by('name')
        self.fields['vendor'].required = False # Vendor is optional
        self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
    
    # def __init__(self, *args, **kwargs):
    #     # VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV
    #     # Pop 'tenant' BEFORE calling super()
    #     self.tenant = kwargs.pop('tenant', None) # Store it if you need it later in __init__
    #     # ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

    #     super().__init__(*args, **kwargs) # Now 'tenant' is NOT in kwargs

    #     # Filter choices for ForeignKeys.
    #     # django-tenants should automatically scope these to the current tenant if models are tenant-aware.
    #     self.fields['category'].queryset = ExpenseCategory.objects.all().order_by('name')
    #     self.fields['vendor'].queryset = Vendor.objects.filter(is_active=True).order_by('name')
    #     self.fields['vendor'].required = False

    #     self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
    #     self.fields['payment_method'].required = False # Allows recording as Accounts Payable

        if not self.instance.pk: # For new expenses
            self.fields['expense_date'].initial = timezone.now().date()


from .models import BudgetItem, BudgetAmount # Ensure ChartOfAccount is imported if used
from apps.schools.models import AcademicYear, Term # Ensure these are imported
from django.db import models as django_db_models

from django import forms
from django.utils.translation import gettext_lazy as _
from django.db import models as django_db_models # For Q objects, though not strictly needed with __in

# To import ChartOfAccount, if it's in apps.accounting.models:
from apps.accounting.models import Account
# If ChartOfAccount is in apps.finance.models, then use:
# from .models import ChartOfAccount 


# D:\school_fees_saas_v2\apps\finance\forms.py
import logging
from django import forms
from django.utils.translation import gettext_lazy as _
from .models import BudgetItem # ChartOfAccount will be imported from accounting
from apps.accounting.models import Account, AccountType # Import AccountType

logger = logging.getLogger(__name__)

class BudgetItemForm(forms.ModelForm):
    class Meta:
        model = BudgetItem
        fields = [ # These are fields of BudgetItem model
            'name',
            'description',
            'linked_coa_account', # This is the ForeignKey to ChartOfAccount
            'budget_item_type',   # This is 'INCOME'/'EXPENSE' on BudgetItem itself
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter budget item name (e.g., Tuition Fees, Staff Salaries)',
                'id': 'floatingBudgetName',
                'autocomplete': 'off'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Provide a detailed description of this budget item...',
                'id': 'floatingBudgetDescription',
                'rows': 4,
                'style': 'resize: vertical;'
            }),
            'linked_coa_account': forms.Select(attrs={
                'class': 'form-select',
                'id': 'floatingLinkedAccount'
            }),
            'budget_item_type': forms.Select(attrs={
                'class': 'form-select',
                'id': 'floatingBudgetType'
            }),
        }
        labels = {
            'name': 'Budget Item Name',
            'description': 'Description',
            'linked_coa_account': 'Linked Chart of Accounts',
            'budget_item_type': 'Budget Type',
        }
        help_texts = {
            'name': 'Enter a clear, descriptive name for this budget item',
            'description': 'Provide additional context about this budget item\'s purpose and scope',
            'linked_coa_account': 'Select the corresponding account from your Chart of Accounts',
            'budget_item_type': 'Choose whether this is an income or expense item',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add premium styling and validation to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({
                'data-bs-toggle': 'tooltip',
                'data-bs-placement': 'top'
            })

            # Add specific tooltips for each field
            if field_name == 'name':
                field.widget.attrs['title'] = 'Enter a unique, descriptive name for this budget item'
            elif field_name == 'description':
                field.widget.attrs['title'] = 'Provide detailed information about this budget item\'s purpose'
            elif field_name == 'linked_coa_account':
                field.widget.attrs['title'] = 'Link this budget item to the appropriate Chart of Accounts entry'
            elif field_name == 'budget_item_type':
                field.widget.attrs['title'] = 'Specify whether this represents income or expenses'

        if 'linked_coa_account' in self.fields:
            logger.info("--- BudgetItemForm: Initializing 'linked_coa_account' field ---")
            
            actual_coa_code_field_name = 'code' # From Account model (not 'account_code')
            
            try:
                # Get the AccountType objects for INCOME, EXPENSE, COGS
                # Assuming 'classification' on AccountType model holds 'REVENUE', 'EXPENSE', 'COGS'
                # Or if 'name' on AccountType model holds 'Income', 'Expense', 'Cost of Goods Sold'
                # Let's assume AccountType.classification is what we need:
                desired_classifications = ['REVENUE', 'EXPENSE', 'COGS']
                
                # You might need to create these AccountType instances in your database first
                # via Django Admin or a data migration if they don't exist.
                # Example: AccountType.objects.create(name="Operating Income", classification="REVENUE")
                #          AccountType.objects.create(name="Salaries", classification="EXPENSE")

                logger.info(
                    f"Attempting to filter Account: "
                    f"account_type__classification__in={desired_classifications}, is_active=True, "
                    f"ordering by '{actual_coa_code_field_name}'"
                )
                
                filtered_queryset = Account.objects.filter(
                    account_type__classification__in=desired_classifications, # Filter by classification on related AccountType
                    is_active=True
                ).order_by(actual_coa_code_field_name).select_related('account_type') # select_related for efficiency

                count = filtered_queryset.count()
                logger.info(f"Filter successful! Found {count} Account records for dropdown.")
                
                self.fields['linked_coa_account'].queryset = filtered_queryset
                
            except Exception as e:
                logger.error(f"!!! Error during Account filter: {e}", exc_info=True)
                # Fallback logic as before...
                self.fields['linked_coa_account'].queryset = Account.objects.filter(is_active=True).order_by('code')


            self.fields['linked_coa_account'].label_from_instance = lambda obj: (
                f"{getattr(obj, actual_coa_code_field_name, 'N/A_CODE')} - "
                f"{obj.name} "
                f"({obj.account_type.name if obj.account_type else 'N/A Type'})" # Display AccountType's name
            )
            logger.info("--- BudgetItemForm: 'linked_coa_account' field initialization complete ---")
            
            
class BudgetAmountForm(forms.ModelForm):
    class Meta:
        model = BudgetAmount
        fields = ['budget_item', 'academic_year', 'term', 'budgeted_amount', 'notes']
        widgets = {
            'budget_item': forms.Select(attrs={'class': 'form-select'}),
            'academic_year': forms.Select(attrs={'class': 'form-select'}),
            'term': forms.Select(attrs={'class': 'form-select'}),
            'budgeted_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'placeholder': '0.00'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter choices
        self.fields['budget_item'].queryset = BudgetItem.objects.all().order_by('name')
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        self.fields['term'].queryset = Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date')
        self.fields['term'].required = False # Term is optional for annual budgets
        self.fields['term'].label_from_instance = lambda obj: f"{obj.name} ({obj.academic_year.name})"

        # Make academic_year required if not already
        self.fields['academic_year'].required = True
        
        
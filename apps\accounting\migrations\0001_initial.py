# Generated by Django 5.1.9 on 2025-07-05 16:35

import django.db.models.deletion
import django.utils.timezone
import mptt.fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('tenants', '0003_alter_school_is_active_alter_school_slug'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='User-friendly name (e.g., Cash, Accounts Payable, Sales Income)', max_length=100, unique=True, verbose_name='name')),
                ('code', models.CharField(blank=True, db_index=True, help_text='Short unique code for system reference, e.g., 1001, 4100. Can be based on standard COA numbering.', max_length=20, null=True, unique=True, verbose_name='code')),
                ('classification', models.CharField(choices=[('ASSET', 'Asset'), ('LIABILITY', 'Liability'), ('EQUITY', 'Equity'), ('REVENUE', 'Revenue'), ('EXPENSE', 'Expense'), ('COGS', 'Cost of Goods Sold')], help_text='The financial statement classification of this account type.', max_length=20, verbose_name='classification')),
                ('normal_balance', models.CharField(choices=[('DEBIT', 'Debit'), ('CREDIT', 'Credit')], help_text='The normal balance (Debit or Credit) for accounts of this type. Determines how transactions affect the balance.', max_length=6, verbose_name='Normal Balance')),
                ('statement_section', models.CharField(blank=True, choices=[('BS', 'Balance Sheet'), ('IS', 'Income Statement')], help_text='Which financial statement this account type primarily appears on.', max_length=2, null=True, verbose_name='Statement Section')),
                ('description', models.TextField(blank=True, null=True, verbose_name='description')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'verbose_name': 'Account Type',
                'verbose_name_plural': 'Account Types',
                'ordering': ['classification', 'name'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(blank=True, help_text='System-generated unique journal entry number.', max_length=50, unique=True, verbose_name='entry number')),
                ('date', models.DateField(db_index=True, default=django.utils.timezone.now, verbose_name='Entry Date')),
                ('narration', models.TextField(help_text='Overall purpose of this journal entry.', verbose_name='Narration/Description')),
                ('entry_type', models.CharField(choices=[('MANUAL', 'Manual Entry'), ('INVOICE', 'Invoice Posting'), ('PAYMENT', 'Payment Received'), ('EXPENSE', 'Expense Recorded'), ('ADJUSTMENT', 'Adjustment Entry'), ('OPENING_BALANCE', 'Opening Balance'), ('YEAR_END', 'Year End Closing')], default='MANUAL', max_length=20, verbose_name='Entry Type')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('POSTED', 'Posted'), ('CANCELLED', 'Cancelled')], db_index=True, default='DRAFT', max_length=10, verbose_name='Status')),
                ('posted_at', models.DateTimeField(blank=True, help_text='Timestamp when the entry was posted.', null=True, verbose_name='posted at')),
                ('is_reversing_entry', models.BooleanField(default=False, help_text='Indicates if this JE is a reversing entry.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Journal Entry',
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('debit', models.DecimalField(blank=True, decimal_places=2, default=Decimal('0.00'), max_digits=12, null=True, verbose_name='debit')),
                ('credit', models.DecimalField(blank=True, decimal_places=2, default=Decimal('0.00'), max_digits=12, null=True, verbose_name='credit')),
                ('description', models.CharField(blank=True, max_length=255, verbose_name='description')),
            ],
            options={
                'verbose_name': 'Journal Entry Item',
                'verbose_name_plural': 'Journal Entry Items',
                'ordering': ['pk'],
            },
        ),
        migrations.CreateModel(
            name='JournalLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Debit Amount')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Credit Amount')),
                ('description', models.CharField(blank=True, help_text='Optional description for this specific line item.', max_length=255, verbose_name='Line Description')),
            ],
            options={
                'verbose_name': 'Journal Line',
                'verbose_name_plural': 'Journal Lines',
                'ordering': ['pk'],
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the account (e.g., Cash in Bank, Tuition Fees, Salaries Expense).', max_length=150)),
                ('code', models.CharField(blank=True, help_text='Unique account code/number (e.g., 1010, 4000, 5010). Can be blank.', max_length=20, null=True)),
                ('description', models.TextField(blank=True, help_text='Optional description for the account.', null=True)),
                ('is_active', models.BooleanField(db_index=True, default=True, help_text='Is this account currently active and usable for posting?', verbose_name='Is Active?')),
                ('is_control_account', models.BooleanField(default=False, help_text='Control accounts summarize sub-accounts. Direct posting to them might be restricted.', verbose_name='Is Control Account?')),
                ('can_be_used_in_je', models.BooleanField(default=True, help_text='Can this account be directly selected in a manual journal entry line?', verbose_name='Usable in Manual Journal Entry?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent_account', mptt.fields.TreeForeignKey(blank=True, help_text='Select if this is a sub-account of another account (for hierarchical CoA).', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_accounts', to='accounting.account', verbose_name='Parent Account')),
                ('tenant', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='chart_of_accounts_entries', to='tenants.school')),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='accounting.accounttype', verbose_name='Account Type')),
            ],
            options={
                'verbose_name': 'Chart of Account Entry',
                'verbose_name_plural': 'Chart of Accounts',
                'ordering': ['tenant', 'tree_id', 'lft'],
            },
        ),
        migrations.CreateModel(
            name='GeneralLedger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_date', models.DateField(db_index=True)),
                ('description', models.CharField(max_length=255)),
                ('transaction_type', models.CharField(choices=[('DEBIT', 'Debit'), ('CREDIT', 'Credit')], max_length=6)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Always positive. Type indicates effect.', max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='gl_entries', to='accounting.account')),
            ],
            options={
                'verbose_name': 'General Ledger Transaction',
                'verbose_name_plural': 'General Ledger Transactions',
                'ordering': ['transaction_date', 'id'],
            },
        ),
    ]

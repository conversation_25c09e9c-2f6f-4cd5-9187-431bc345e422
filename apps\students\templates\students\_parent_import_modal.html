{# apps/students/templates/students/_parent_import_modal.html - Parent Import Modal #}
{% load i18n %}

<!-- Parent Import Modal -->
<div class="modal fade" id="importParentModal" tabindex="-1" aria-labelledby="importParentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="importParentModalLabel">
                    <i class="bi bi-upload me-2"></i>
                    {% trans "Import Parents from Excel" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Import Instructions -->
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle me-2"></i>{% trans "Import Instructions" %}</h6>
                    <ul class="mb-0">
                        <li>{% trans "Download the Excel template below and fill in parent information" %}</li>
                        <li>{% trans "Required fields: Email, First Name, Last Name" %}</li>
                        <li>{% trans "Email addresses must be unique" %}</li>
                        <li>{% trans "Phone numbers should include country code if international" %}</li>
                        <li>{% trans "Status: Use 'Active' or 'Inactive'" %}</li>
                    </ul>
                </div>

                <!-- Template Download -->
                <div class="mb-4">
                    <h6>{% trans "Step 1: Download Template" %}</h6>
                    <a href="{% url 'students:download_parent_import_template' %}" 
                       class="btn btn-outline-success">
                        <i class="bi bi-download me-2"></i>
                        {% trans "Download Excel Template" %}
                    </a>
                </div>

                <!-- File Upload Form -->
                <form id="parentImportForm" method="post" enctype="multipart/form-data" action="{% url 'students:import_parents' %}">
                    {% csrf_token %}
                    
                    <h6>{% trans "Step 2: Upload Completed File" %}</h6>
                    
                    <div class="mb-3">
                        <label for="parentImportFile" class="form-label">{% trans "Select Excel File" %}</label>
                        <input type="file" 
                               class="form-control" 
                               id="parentImportFile" 
                               name="import_file" 
                               accept=".xlsx,.xls" 
                               required>
                        <div class="form-text">{% trans "Accepted formats: .xlsx, .xls (Max size: 10MB)" %}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{% trans "Import Options" %}</label>
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="radio" 
                                   name="import_mode" 
                                   id="createOnly" 
                                   value="create_only" 
                                   checked>
                            <label class="form-check-label" for="createOnly">
                                {% trans "Create new parents only (skip existing emails)" %}
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="radio" 
                                   name="import_mode" 
                                   id="updateExisting" 
                                   value="update_existing">
                            <label class="form-check-label" for="updateExisting">
                                {% trans "Update existing parents and create new ones" %}
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="radio" 
                                   name="import_mode" 
                                   id="validateOnly" 
                                   value="validate_only">
                            <label class="form-check-label" for="validateOnly">
                                {% trans "Validate only (don't save, just check for errors)" %}
                            </label>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="sendWelcomeEmails" 
                               name="send_welcome_emails">
                        <label class="form-check-label" for="sendWelcomeEmails">
                            {% trans "Send welcome emails to new parents" %}
                        </label>
                    </div>
                </form>

                <!-- Progress Bar -->
                <div id="importProgress" class="d-none">
                    <h6>{% trans "Import Progress" %}</h6>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             style="width: 0%"
                             aria-valuenow="0" 
                             aria-valuemin="0" 
                             aria-valuemax="100">0%</div>
                    </div>
                    <div id="importStatus" class="text-muted">{% trans "Preparing import..." %}</div>
                </div>

                <!-- Results -->
                <div id="importResults" class="d-none">
                    <h6>{% trans "Import Results" %}</h6>
                    <div id="importSummary"></div>
                    <div id="importErrors"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% trans "Close" %}
                </button>
                <button type="button" 
                        class="btn btn-primary" 
                        id="startImportBtn"
                        onclick="startParentImport()">
                    <i class="bi bi-upload me-2"></i>
                    {% trans "Start Import" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function startParentImport() {
    const form = document.getElementById('parentImportForm');
    const fileInput = document.getElementById('parentImportFile');
    const progressDiv = document.getElementById('importProgress');
    const resultsDiv = document.getElementById('importResults');
    const startBtn = document.getElementById('startImportBtn');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const statusDiv = document.getElementById('importStatus');
    
    // Validate file selection
    if (!fileInput.files.length) {
        alert('{% trans "Please select a file to import." %}');
        return;
    }
    
    // Validate file type
    const file = fileInput.files[0];
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];
    
    if (!allowedTypes.includes(file.type)) {
        alert('{% trans "Please select a valid Excel file (.xlsx or .xls)." %}');
        return;
    }
    
    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        alert('{% trans "File size must be less than 10MB." %}');
        return;
    }
    
    // Show progress and hide results
    progressDiv.classList.remove('d-none');
    resultsDiv.classList.add('d-none');
    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Importing..." %}';
    
    // Create FormData
    const formData = new FormData(form);
    
    // Start import
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        // Update progress to 100%
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        statusDiv.textContent = '{% trans "Import completed!" %}';
        
        // Show results
        setTimeout(() => {
            progressDiv.classList.add('d-none');
            resultsDiv.classList.remove('d-none');
            displayImportResults(data);
            
            // Reset button
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Start Import" %}';
        }, 1000);
    })
    .catch(error => {
        console.error('Import error:', error);
        statusDiv.textContent = '{% trans "Import failed. Please try again." %}';
        statusDiv.className = 'text-danger';
        
        // Reset button
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Start Import" %}';
    });
    
    // Simulate progress (since we don't have real-time progress)
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) {
            progress = 90;
            clearInterval(progressInterval);
        }
        progressBar.style.width = progress + '%';
        progressBar.textContent = Math.round(progress) + '%';
        
        if (progress < 30) {
            statusDiv.textContent = '{% trans "Reading Excel file..." %}';
        } else if (progress < 60) {
            statusDiv.textContent = '{% trans "Validating parent data..." %}';
        } else if (progress < 90) {
            statusDiv.textContent = '{% trans "Saving parents..." %}';
        }
    }, 200);
}

function displayImportResults(data) {
    const summaryDiv = document.getElementById('importSummary');
    const errorsDiv = document.getElementById('importErrors');
    
    // Display summary
    let summaryHtml = '<div class="alert alert-success">';
    summaryHtml += '<h6><i class="bi bi-check-circle me-2"></i>{% trans "Import Summary" %}</h6>';
    summaryHtml += '<ul class="mb-0">';
    summaryHtml += `<li>{% trans "Total processed:" %} ${data.total_processed || 0}</li>`;
    summaryHtml += `<li>{% trans "Successfully created:" %} ${data.created_count || 0}</li>`;
    summaryHtml += `<li>{% trans "Successfully updated:" %} ${data.updated_count || 0}</li>`;
    summaryHtml += `<li>{% trans "Skipped (duplicates):" %} ${data.skipped_count || 0}</li>`;
    summaryHtml += `<li>{% trans "Errors:" %} ${data.error_count || 0}</li>`;
    summaryHtml += '</ul></div>';
    
    summaryDiv.innerHTML = summaryHtml;
    
    // Display errors if any
    if (data.errors && data.errors.length > 0) {
        let errorsHtml = '<div class="alert alert-warning">';
        errorsHtml += '<h6><i class="bi bi-exclamation-triangle me-2"></i>{% trans "Import Errors" %}</h6>';
        errorsHtml += '<ul class="mb-0">';
        data.errors.forEach(error => {
            errorsHtml += `<li>${error}</li>`;
        });
        errorsHtml += '</ul></div>';
        errorsDiv.innerHTML = errorsHtml;
    } else {
        errorsDiv.innerHTML = '';
    }
    
    // If successful, offer to reload the page
    if (data.created_count > 0 || data.updated_count > 0) {
        setTimeout(() => {
            if (confirm('{% trans "Import completed successfully! Would you like to reload the page to see the new parents?" %}')) {
                window.location.reload();
            }
        }, 2000);
    }
}

// Reset modal when closed
document.getElementById('importParentModal').addEventListener('hidden.bs.modal', function () {
    // Reset form
    document.getElementById('parentImportForm').reset();
    
    // Hide progress and results
    document.getElementById('importProgress').classList.add('d-none');
    document.getElementById('importResults').classList.add('d-none');
    
    // Reset button
    const startBtn = document.getElementById('startImportBtn');
    startBtn.disabled = false;
    startBtn.innerHTML = '<i class="bi bi-upload me-2"></i>{% trans "Start Import" %}';
    
    // Reset progress bar
    const progressBar = document.querySelector('#importProgress .progress-bar');
    progressBar.style.width = '0%';
    progressBar.textContent = '0%';
    
    // Reset status
    const statusDiv = document.getElementById('importStatus');
    statusDiv.textContent = '{% trans "Preparing import..." %}';
    statusDiv.className = 'text-muted';
});
</script>

from django import forms
from django.utils.translation import gettext_lazy as _

# Import the Subscription model to get access to its BillingCycle choices
from .models import Subscription

class PlanCycleSelectionForm(forms.Form):
    """
    A simple form used on the public pricing page to allow users to toggle
    between viewing monthly and annual prices for subscription plans.
    """
    
    # This is the field that was causing the error before.
    # We now correctly get the choices from the Subscription model.
    billing_cycle = forms.ChoiceField(
        choices=Subscription.BillingCycle.choices,
        
        # The widget makes these appear as radio buttons.
        # The 'btn-check' class is for Bootstrap's toggle button styling.
        widget=forms.RadioSelect(attrs={'class': 'btn-check', 'autocomplete': 'off'}),
        
        # Set the default selected value to 'MONTHLY'.
        initial=Subscription.BillingCycle.MONTHLY
    )

# You can add other forms related to the 'subscriptions' app in this file later.





# # D:\school_fees_saas_v2\apps\subscriptions\forms.py
# from django import forms
# from django.utils.translation import gettext_lazy as _
# from .models import SubscriptionPlan

# class PlanCycleSelectionForm(forms.Form):
#     billing_cycle = forms.ChoiceField(
#         choices=SubscriptionPlan.BILLING_CYCLE_CHOICES,
#         label=_("Select Billing Cycle"),
#         widget=forms.RadioSelect,
#         initial='MONTHLY',
#         required=True
#     )
#     # You don't need a plan field here if the plan is chosen via URL or button value

#     def __init__(self, *args, **kwargs):
#         self.plan = kwargs.pop('plan', None) # Pass the selected plan to the form
#         super().__init__(*args, **kwargs)

#         if self.plan:
#             # Dynamically update label or choices if needed based on the plan
#             # For example, if a plan only supports annual billing
#             if not self.plan.price_monthly or self.plan.price_monthly <= 0:
#                 self.fields['billing_cycle'].choices = [('ANNUALLY', _('Annually'))]
#                 self.fields['billing_cycle'].initial = 'ANNUALLY'
#             elif not self.plan.price_annually or self.plan.price_annually <= 0:
#                 self.fields['billing_cycle'].choices = [('MONTHLY', _('Monthly'))]
#                 self.fields['billing_cycle'].initial = 'MONTHLY'
                
                
                
                
{# templates/public_site/pricing.html #}
{% extends "public_base.html" %}
{% load humanize %}
{% block title %}{{ view_title }}{% endblock %}
{% block page_specific_css %}
<style>
    .card-pricing .list-unstyled li { padding: .5rem 0; }
    .card-pricing .card-title { font-size: 1.75rem; }
</style>
{% endblock %}
{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1>{{ view_title }}</h1>
        <p class="lead">Choose the perfect plan for your school's needs. Simple, transparent pricing.</p>
    </div>
    <div class="row row-cols-1 row-cols-md-3 g-4 justify-content-center">
        {% for plan in plans %}
        <div class="col">
            <div class="card h-100 shadow-sm card-pricing">
                <div class="card-header text-center py-3">
                    <h4 class="my-0 fw-normal">{{ plan.name }}</h4>
                </div>
                <div class="card-body">
                    <h1 class="card-title pricing-card-title text-center">${{ plan.price_monthly|intcomma }}/mo</h1>
                    {% if plan.price_annually > 0 %}
                        <p class="text-center text-muted">(or ${{ plan.price_annually|intcomma }}/year)</p>
                    {% endif %}
                    <p class="text-muted small">{{ plan.description|default:"" }}</p>
                    <ul class="list-unstyled mt-3 mb-4">
                        {% if plan.max_students %}
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>Up to {{ plan.max_students }} students</li>
                        {% else %}
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>Unlimited Students</li>
                        {% endif %}
                        {% if plan.max_staff %}
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>Up to {{ plan.max_staff }} staff users</li>
                        {% else %}
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>Unlimited Staff Users</li>
                        {% endif %}

                        {# Display features from ManyToMany field #}
                        {% for feature in plan.features.all %}
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>{{ feature.name }}</li>
                        {% empty %}
                            <li>Core Features Included</li>
                        {% endfor %}
                        {# You can also hardcode some key features based on plan name if needed #}
                    </ul>
                    <a href="{% url 'tenants:register_school' %}?plan={{ plan.pk }}" class="w-100 btn btn-lg btn-primary">Get Started with {{ plan.name }}</a>
                </div>
            </div>
        </div>
        {% empty %}
        <p>No subscription plans are currently available. Please check back soon!</p>
        {% endfor %}
    </div>
</div>
{% endblock %}



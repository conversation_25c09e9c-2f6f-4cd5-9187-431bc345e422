# apps/fees/management/commands/fix_fee_head_accounts.py

from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.fees.models import FeeHead
from apps.accounting.models import Account, AccountType
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix Fee Heads that do not have linked income accounts by creating and linking them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specific tenant schema to fix (optional, if not provided will fix all tenants)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        tenant_schema = options.get('tenant')
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))
        
        if tenant_schema:
            # Fix specific tenant
            try:
                tenant = School.objects.get(schema_name=tenant_schema)
                self.fix_tenant_fee_heads(tenant, dry_run)
            except School.DoesNotExist:
                self.stderr.write(self.style.ERROR(f"Tenant '{tenant_schema}' not found"))
                return
        else:
            # Fix all tenants
            tenants = School.objects.all()
            self.stdout.write(f"Processing {tenants.count()} tenants...")
            
            for tenant in tenants:
                self.stdout.write(f"\n🏫 Processing tenant: {tenant.schema_name} ({tenant.name})")
                try:
                    self.fix_tenant_fee_heads(tenant, dry_run)
                except Exception as e:
                    self.stderr.write(f"  ❌ Error processing tenant {tenant.schema_name}: {str(e)}")
                    continue
        
        self.stdout.write(self.style.SUCCESS("✅ Fee Head account linking completed!"))

    def fix_tenant_fee_heads(self, tenant, dry_run=False):
        """Fix Fee Heads for a specific tenant."""
        self.stdout.write(f"\n🏫 Processing tenant: {tenant.schema_name} ({tenant.name})")
        
        with schema_context(tenant.schema_name):
            # Get Fee Heads without linked income accounts
            fee_heads_without_accounts = FeeHead.objects.filter(income_account_link__isnull=True)
            
            if not fee_heads_without_accounts.exists():
                self.stdout.write(f"  ✅ All Fee Heads already have linked income accounts")
                return
            
            self.stdout.write(f"  📋 Found {fee_heads_without_accounts.count()} Fee Heads without income accounts")
            
            # Get appropriate account type for fee income
            income_account_type = self.get_fee_income_account_type()
            if not income_account_type:
                self.stderr.write(f"  ❌ No suitable revenue account type found for tenant {tenant.schema_name}")
                return
            
            fixed_count = 0
            created_count = 0
            
            for fee_head in fee_heads_without_accounts:
                try:
                    if dry_run:
                        self.stdout.write(f"  🔍 Would create income account for: {fee_head.name}")
                        continue
                    
                    with transaction.atomic():
                        # Create income account
                        account_name = f"{fee_head.name} Income"
                        account_code = self.generate_next_fee_income_account_code()
                        
                        income_account, account_created = Account.objects.get_or_create(
                            code=account_code,
                            defaults={
                                'name': account_name,
                                'account_type': income_account_type,
                                'is_active': True,
                                'description': f"Income account for {fee_head.name} fee head"
                            }
                        )
                        
                        # Link to fee head
                        fee_head.income_account_link = income_account
                        fee_head.save()
                        
                        if account_created:
                            created_count += 1
                            self.stdout.write(f"  ✅ Created and linked account '{account_name}' (Code: {account_code}) for '{fee_head.name}'")
                        else:
                            self.stdout.write(f"  🔗 Linked existing account '{account_name}' (Code: {account_code}) for '{fee_head.name}'")
                        
                        fixed_count += 1
                        
                except Exception as e:
                    self.stderr.write(f"  ❌ Failed to fix Fee Head '{fee_head.name}': {e}")
            
            if not dry_run:
                self.stdout.write(f"  📊 Summary: Fixed {fixed_count} Fee Heads, Created {created_count} new accounts")

    def get_fee_income_account_type(self):
        """Get the appropriate account type for fee income accounts."""
        account_type_names = [
            'Tuition Revenue',
            'Other Fee Revenue', 
            'Revenue Control',
            'Miscellaneous Revenue'
        ]
        
        for type_name in account_type_names:
            try:
                account_type = AccountType.objects.get(name__iexact=type_name)
                return account_type
            except AccountType.DoesNotExist:
                continue
        
        # Fallback: try to get any revenue type
        try:
            account_type = AccountType.objects.filter(classification='REVENUE').first()
            if account_type:
                return account_type
        except Exception:
            pass
        
        return None

    def generate_next_fee_income_account_code(self):
        """Generate the next available account code for fee income accounts."""
        # Look for existing fee income accounts in the 4xxx range
        existing_codes = Account.objects.filter(
            code__startswith='4',
            code__regex=r'^4\d{3}$'  # 4 followed by exactly 3 digits
        ).values_list('code', flat=True).order_by('code')
        
        # Convert to integers and find the next available code
        used_numbers = []
        for code in existing_codes:
            try:
                used_numbers.append(int(code))
            except (ValueError, TypeError):
                continue
        
        # Start from 4100 for fee income accounts
        start_code = 4100
        
        # Find the next available code
        for i in range(start_code, 4999):
            if i not in used_numbers:
                return str(i)
        
        # Fallback if all codes are used (unlikely)
        return str(max(used_numbers) + 1) if used_numbers else str(start_code)

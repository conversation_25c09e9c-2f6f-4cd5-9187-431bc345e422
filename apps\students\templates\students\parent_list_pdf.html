{# apps/students/templates/students/parent_list_pdf.html - Parent List PDF Export Template #}
{% load i18n humanize %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{% trans "Parent List Report" %}</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 18px;
            margin: 0 0 5px 0;
            font-weight: bold;
        }
        
        .header .school-info {
            font-size: 12px;
            color: #666;
            margin: 5px 0;
        }
        
        .header .report-info {
            font-size: 10px;
            color: #888;
            margin-top: 10px;
        }
        
        .summary-section {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }
        
        .summary-title {
            font-size: 12px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            margin: 5px;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 9px;
            color: #6c757d;
            text-transform: uppercase;
        }
        
        .filters-section {
            margin-bottom: 15px;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 3px;
        }
        
        .filters-title {
            font-size: 10px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .filter-item {
            display: inline-block;
            margin-right: 15px;
            font-size: 9px;
            color: #6c757d;
        }
        
        .filter-label {
            font-weight: bold;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        
        .data-table th {
            background-color: #007bff;
            color: white;
            padding: 8px 4px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #0056b3;
            font-size: 9px;
        }
        
        .data-table td {
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .children-list {
            font-size: 8px;
            color: #6c757d;
        }
        
        .child-badge {
            display: inline-block;
            background-color: #e9ecef;
            color: #495057;
            padding: 1px 4px;
            border-radius: 2px;
            margin: 1px;
            font-size: 7px;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        
        /* Responsive adjustments for PDF */
        @media print {
            .data-table {
                font-size: 8px;
            }
            
            .data-table th,
            .data-table td {
                padding: 4px 2px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <h1>{% trans "Parent List Report" %}</h1>
        <div class="school-info">
            {% if request.tenant.school_name %}
                {{ request.tenant.school_name }}
            {% else %}
                {% trans "School Management System" %}
            {% endif %}
        </div>
        <div class="report-info">
            {% trans "Generated on" %}: {{ current_datetime|date:"F d, Y \a\t H:i" }} | 
            {% trans "Total Parents" %}: {{ parents.paginator.count|default:0 }}
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-section">
        <div class="summary-title">{% trans "Summary Statistics" %}</div>
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-number">{{ total_parents|default:0 }}</span>
                <span class="stat-label">{% trans "Total Parents" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ active_parents|default:0 }}</span>
                <span class="stat-label">{% trans "Active Parents" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ inactive_parents|default:0 }}</span>
                <span class="stat-label">{% trans "Inactive Parents" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ parents_with_children|default:0 }}</span>
                <span class="stat-label">{% trans "With Children" %}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ parents_without_children|default:0 }}</span>
                <span class="stat-label">{% trans "Without Children" %}</span>
            </div>
        </div>
    </div>

    <!-- Applied Filters -->
    {% if applied_filters %}
    <div class="filters-section">
        <div class="filters-title">{% trans "Applied Filters" %}:</div>
        {% for filter_name, filter_value in applied_filters.items %}
            <span class="filter-item">
                <span class="filter-label">{{ filter_name }}:</span> {{ filter_value }}
            </span>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Parent Data Table -->
    {% if parents %}
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 20%;">{% trans "Full Name" %}</th>
                    <th style="width: 20%;">{% trans "Email Address" %}</th>
                    <th style="width: 12%;">{% trans "Phone Number" %}</th>
                    <th style="width: 25%;">{% trans "Linked Children" %}</th>
                    <th style="width: 8%;">{% trans "Status" %}</th>
                    <th style="width: 10%;">{% trans "Date Joined" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for parent_obj in parents %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>
                        <strong>{{ parent_obj.get_full_name|default:parent_obj.email }}</strong>
                    </td>
                    <td>{{ parent_obj.email }}</td>
                    <td>{{ parent_obj.phone_number|default:"N/A" }}</td>
                    <td>
                        <div class="children-list">
                            {% for child in parent_obj.children.all %}
                                <span class="child-badge">{{ child.get_full_name|truncatechars:20 }}</span>
                            {% empty %}
                                <span style="color: #6c757d; font-style: italic;">{% trans "No children linked" %}</span>
                            {% endfor %}
                        </div>
                    </td>
                    <td>
                        {% if parent_obj.is_active %}
                            <span class="status-badge status-active">{% trans "Active" %}</span>
                        {% else %}
                            <span class="status-badge status-inactive">{% trans "Inactive" %}</span>
                        {% endif %}
                    </td>
                    <td>{{ parent_obj.date_joined|date:"M d, Y" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="no-data">
            <h3>{% trans "No Parent Data Available" %}</h3>
            <p>{% trans "No parents match the current filter criteria or no parents have been added to the system yet." %}</p>
        </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>
            {% trans "This report was generated automatically by the School Management System." %}<br>
            {% trans "For questions or support, please contact the school administration." %}
        </p>
    </div>
</body>
</html>

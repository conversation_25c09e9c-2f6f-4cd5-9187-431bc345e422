{# D:\school_fees_saas_v2\templates\schools\parent_dashboard.html #}
{% extends "tenant_base.html" %}
{% load static %}

{% block title %}{{ view_title|default:"Parent Dashboard" }} - {{ request.tenant.name }}{% endblock %}

{% block content %}

<div class="container mt-4">
    {% include "partials/messages.html" %}

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">{{ view_title }}</h1>
                    <p class="text-muted mb-0">Welcome, {{ request.user.get_full_name|default:request.user.email }}!</p>
                </div>
                <a href="{% url 'schools:parent_logout' %}" class="btn btn-outline-danger">
                    <i class="bi bi-box-arrow-right me-1"></i>Logout
                </a>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="row">
        <!-- Announcements Section -->
        <div class="col-lg-8 mb-4">
            {% include "announcements/widgets/parent_announcement_widget.html" with announcements=announcements %}
        </div>

        <!-- Quick Info Section -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-hearts me-2"></i>
                        My Children
                    </h5>
                </div>
                <div class="card-body">
                    {% if linked_students %}
                        <div class="list-group list-group-flush">
                            {% for student in linked_students %}
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-bold">{{ student.get_full_name }}</h6>
                                        {% if student.current_class %}
                                            <small class="text-muted">
                                                <i class="bi bi-mortarboard me-1"></i>
                                                {{ student.current_class.name }}{% if student.current_section %} - {{ student.current_section.name }}{% endif %}
                                            </small>
                                        {% endif %}
                                    </div>
                                    <span class="badge bg-success">Active</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-person-plus display-4 text-muted mb-2"></i>
                            <p class="text-muted mb-0">No children linked to your account.</p>
                            <small class="text-muted">Contact the school administration for assistance.</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Row -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        Parent Portal Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="bi bi-bell-fill text-primary display-6 mb-2"></i>
                                <h6>Stay Informed</h6>
                                <p class="text-muted small">Receive important announcements and updates from the school.</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="bi bi-calendar-check text-success display-6 mb-2"></i>
                                <h6>Track Progress</h6>
                                <p class="text-muted small">Monitor your child's academic progress and attendance.</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="bi bi-chat-dots text-info display-6 mb-2"></i>
                                <h6>Stay Connected</h6>
                                <p class="text-muted small">Communicate with teachers and school administration.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}


{# D:\school_fees_saas_v2\apps\portal_admin\templates\portal_admin\tenant_role_list.html #}
{% extends "tenant_base.html" %}
{% load static core_tags %} {# Assuming core_tags is where active_nav_link might live #}

{% block title %}{{ view_title|default:"Manage Tenant Roles" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Manage Tenant Roles" }}</h1>
        {% if perms.portal_admin.add_tenantrole %}
        <a href="{% url 'portal_admin:tenant_role_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Create New Role
        </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %} {# For success/error messages #}

    {% if roles %} {# Context object name from TenantRoleListView is 'roles' #}
    <div class="card shadow-sm">
        <div class="list-group list-group-flush">
            {% for role_obj in roles %} {# Changed loop variable to role_obj to avoid conflict #}
            <div class="list-group-item d-flex justify-content-between align-items-center py-3">
                <div>
                    <h5 class="mb-1">{{ role_obj.name }}</h5>
                    {% if role_obj.description %}<small class="text-muted">{{ role_obj.description|truncatechars:80 }}</small>{% endif %}
                </div>
                <div class="actions-column">
                    {% if perms.portal_admin.change_tenantrole %}
                    <a href="{% url 'portal_admin:tenant_role_assign_permissions' pk=role_obj.pk %}" class="btn btn-sm btn-info me-1" title="Assign System Permissions"><i class="bi bi-shield-check"></i> Permissions</a>
                    <a href="{% url 'portal_admin:tenant_role_edit' pk=role_obj.pk %}" class="btn btn-sm btn-secondary me-1" title="Edit Role"><i class="bi bi-pencil-square"></i> Edit</a>
                    {% endif %}
                    {% if perms.portal_admin.delete_tenantrole %}
                    <a href="{% url 'portal_admin:tenant_role_delete' pk=role_obj.pk %}" class="btn btn-sm btn-danger" title="Delete Role"><i class="bi bi-trash3"></i> Delete</a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">No roles have been created for this school yet.</div>
    {% endif %}

    {% include "partials/_pagination.html" %} {# If pagination is added to view #}

    <div class="footer-actions mt-4">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard
        </a>
    </div>
</div>
{% endblock %}






{% comment %} {% extends "tenant_base.html" %}
{% block title %}{{ view_title }}{% endblock %}
{% block content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <a href="{% url 'schools:role_create' %}" class="btn btn-primary mb-3"><i class="bi bi-plus-circle"></i> Create New Role</a>
    {% if roles %}
    <div class="card">
        <div class="list-role list-role-flush">
            {% for role in roles %}
            <div class="list-role-item d-flex justify-content-between align-items-center">
                {{ role.display_name }}  {# Name without prefix #}
                <div>
                    <a href="{% url 'schools:role_assign_permissions' pk=role.pk %}" class="btn btn-sm btn-info" title="Assign Permissions"><i class="bi bi-shield-check"></i> Permissions</a>
                    <a href="{% url 'schools:role_edit' pk=role.pk %}" class="btn btn-sm btn-secondary" title="Edit Role"><i class="bi bi-pencil-square"></i> Edit</a>
                    <a href="{% url 'schools:role_delete' pk=role.pk %}" class="btn btn-sm btn-danger" title="Delete Role"><i class="bi bi-trash"></i> Delete</a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <p>No roles (roles) have been created for this school yet.</p>
    {% endif %}
</div>
{% endblock %} {% endcomment %}
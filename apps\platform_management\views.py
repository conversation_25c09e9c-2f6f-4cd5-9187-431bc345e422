# D:\school_fees_saas_v2\apps\platform_management\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import user_passes_test
from django.contrib.admin.views.decorators import staff_member_required # Good for admin-like views
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.conf import settings # For settings.LOGIN_URL

from django.http import HttpResponse

from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

from .models import PlatformSetting, SystemNotification, AuditLog, MaintenanceMode, PlatformAnnouncement
from .forms import PlatformSettingForm, SystemNotificationForm, MaintenanceModeForm, PlatformAnnouncementForm

# D:\school_fees_saas_v2\apps\platform_management\views.py
from django.views.generic import TemplateView
from django.contrib.auth.mixins import UserPassesTestMixin # More specific than LoginRequired

import django # For Django version

from django.contrib.auth import get_user_model

from apps.announcements.models import Announcement # Assuming it's here           # Assuming it's here
from apps.tenants.models import School                   # Assuming it's here
from apps.subscriptions.models import Subscription       # Assuming it's here

# D:\school_fees_saas_v2\apps\platform_management\views.py
import logging
from django.views.generic import TemplateView

# --- Setup Logger ---
logger = logging.getLogger(__name__) # Use __name__ for module-specific logger

# --- Attempt to Import Models ---
# Initialize to None so we can check if they loaded successfully
School = None
Subscription = None
Announcement = None
SystemNotification = None
PlatformSetting = None
AdminActivityLog = None

MODULE_NAME_FOR_LOG = "PlatformMgmtViews" # For easier log filtering

try:
    from apps.tenants.models import School as SchoolModel
    School = SchoolModel
    logger.info(f"{MODULE_NAME_FOR_LOG}: School model imported successfully.")
except ImportError as e:
    logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import School model: {e}. Stats may be affected.")
except Exception as e:
    logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing School model: {e}", exc_info=True)


try:
    from apps.subscriptions.models import Subscription as SubscriptionModel
    Subscription = SubscriptionModel
    logger.info(f"{MODULE_NAME_FOR_LOG}: Subscription model imported successfully.")
except ImportError as e:
    logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import Subscription model: {e}. Stats may be affected.")
except Exception as e:
    logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing Subscription model: {e}", exc_info=True)


try:
    from apps.announcements.models import Announcement as PAModel
    Announcement = PAModel
    logger.info(f"{MODULE_NAME_FOR_LOG}: Announcement model imported successfully.")
except ImportError as e:
    logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import Announcement model: {e}. Announcement stats will be 0.")
except Exception as e:
    logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing Announcement model: {e}", exc_info=True)


# ... (similar try-except blocks for SystemNotification, PlatformSetting, AdminActivityLog) ...
logger.debug(f"{MODULE_NAME_FOR_LOG}: Initializing SystemNotification to None.")
try:
    from .models import SystemNotification as SNModel # <<< CORRECTED: Relative import from current app
    SystemNotification = SNModel
    if SystemNotification is None:
        logger.error(f"{MODULE_NAME_FOR_LOG}: SystemNotification imported as SNModel from .models, BUT SNModel IS NONE.")
    else:
        logger.info(f"{MODULE_NAME_FOR_LOG}: Successfully imported SystemNotification as SNModel from .models.")
except ImportError as e_imp:
    logger.error(f"{MODULE_NAME_FOR_LOG}: ImportError for SystemNotification from .models: {e_imp}.")
except Exception as e_gen:
    logger.error(f"{MODULE_NAME_FOR_LOG}: General Exception importing SystemNotification from .models: {e_gen}", exc_info=True)

# try:
#     from .models import SystemNotification as SNModel
#     SystemNotification = SNModel
#     logger.info(f"{MODULE_NAME_FOR_LOG}: SystemNotification model imported successfully.")
# except ImportError as e:
#     logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import SystemNotification model: {e}. Notification stats will be 0.")
# except Exception as e:
#     logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing SystemNotification model: {e}", exc_info=True)



print("About to import models...")
try:
    from . import models
    print(f"Models module imported: {models}")
    print(f"Available attributes in models: {dir(models)}")
    
    PlatformSetting = getattr(models, 'PlatformSetting', None)
    print(f"PlatformSetting from getattr: {PlatformSetting}")
    
except Exception as e:
    print(f"Error importing models: {e}")
    import traceback
    traceback.print_exc()

# logger.debug(f"{MODULE_NAME_FOR_LOG}: Initializing PlatformSetting to None.")
# try:
#     # This import is failing for some reason:
#     from apps.common.models import PlatformSetting as PSModel # <<< IS THIS THE CORRECT PATH?
#     PlatformSetting = PSModel
#     if PlatformSetting is None:
#         logger.error(f"{MODULE_NAME_FOR_LOG}: PlatformSetting imported as PSModel, BUT PSModel IS NONE.")
#     else:
#         logger.info(f"{MODULE_NAME_FOR_LOG}: Successfully imported PlatformSetting as PSModel.")
# except ImportError as e_imp:
#     # THIS BLOCK (or the general Exception block) IS LIKELY BEING EXECUTED
#     logger.error(f"{MODULE_NAME_FOR_LOG}: ImportError for PlatformSetting from its expected location: {e_imp}.")
# except Exception as e_gen:
#     logger.error(f"{MODULE_NAME_FOR_LOG}: General Exception importing PlatformSetting: {e_gen}", exc_info=True)


# try:
#     from apps.common.models import PlatformSetting as PSModel # Assuming PlatformSetting is in common.models
#     PlatformSetting = PSModel
#     logger.info(f"{MODULE_NAME_FOR_LOG}: PlatformSetting model imported successfully.")
# except ImportError as e:
#     logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import PlatformSetting model: {e}. Settings stats/logic will be affected.")
# except Exception as e:
#     logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing PlatformSetting model: {e}", exc_info=True)


try:
    from apps.portal_admin.models import AdminActivityLog as AALModel # Assuming AdminActivityLog is in portal_admin.models
    AdminActivityLog = AALModel
    logger.info(f"{MODULE_NAME_FOR_LOG}: AdminActivityLog model imported successfully.")
except ImportError as e:
    logger.error(f"{MODULE_NAME_FOR_LOG}: FAILED to import AdminActivityLog model: {e}. Audit logs will be empty.")
except Exception as e:
    logger.critical(f"{MODULE_NAME_FOR_LOG}: UNEXPECTED error importing AdminActivityLog model: {e}", exc_info=True)


# --- View Definition ---
class PlatformAdminDashboardView(UserPassesTestMixin, TemplateView):
    template_name = 'platform_management/dashboard.html'

    def test_func(self):
        # Ensure user is authenticated and a superuser to access this platform dashboard
        return self.request.user.is_authenticated and self.request.user.is_superuser

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Platform Dashboard")
        logger.debug(f"{MODULE_NAME_FOR_LOG} (Dashboard): Preparing context...")

        # --- Gather Stats (with checks for model availability) ---
        context['total_settings'] = 0
        if PlatformSetting:
            try:
                context['total_settings'] = PlatformSetting.objects.count()
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting PlatformSetting objects: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): PlatformSetting model not loaded. total_settings is 0.")

        context['active_platform_announcements'] = 0
        if Announcement: # Check if the model was successfully imported
            try:
                # Add your full filter logic here, e.g., for date ranges
                context['active_platform_announcements'] = PlatformAnnouncement.objects.filter(is_published=True).count()
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting PlatformAnnouncement objects: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): PlatformAnnouncement model not loaded. active_platform_announcements is 0.")
        
        context['active_system_notifications'] = 0
        if SystemNotification:
            try:
                context['active_system_notifications'] = SystemNotification.objects.filter(sent_at__isnull=False, is_active=True).count() # Example
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting SystemNotification objects: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): SystemNotification model not loaded. active_system_notifications is 0.")

        maintenance_setting_value = False
        if PlatformSetting:
            try:
                maintenance_setting = PlatformSetting.objects.filter(key__iexact='MAINTENANCE_MODE_ENABLED').first() # Case-insensitive key
                if maintenance_setting and isinstance(maintenance_setting.value, str): # Ensure value is a string before 'True' comparison
                    maintenance_setting_value = maintenance_setting.value.lower() == 'true'
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error fetching maintenance_mode_status: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): PlatformSetting model not loaded. Maintenance mode status defaulted.")
        context['maintenance_mode_status'] = {'is_enabled': maintenance_setting_value}

        context['recent_audit_logs'] = [] # Default to empty list
        if AdminActivityLog:
            try:
                context['recent_audit_logs'] = AdminActivityLog.objects.select_related('user').order_by('-timestamp')[:5]
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error fetching AdminActivityLog objects: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): AdminActivityLog model not loaded. recent_audit_logs is empty.")

        context['total_tenants'] = "N/A"
        if School:
            try:
                context['total_tenants'] = School.objects.count()
            except Exception as e:
                logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting School objects: {e}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): School model not loaded. total_tenants is N/A.")
        
        active_sub_count = "N/A"
        if Subscription:
            try:
                active_sub_count = Subscription.objects.filter(status__in=['ACTIVE', 'TRIALING']).count()
            except Exception as e: 
                logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting active subscriptions (trying fallback): {e}")
                try:
                    active_sub_count = Subscription.objects.filter(is_active=True).count()
                except Exception as e2:
                    logger.error(f"{MODULE_NAME_FOR_LOG} (Dashboard): Error counting active subscriptions (fallback failed): {e2}", exc_info=True)
        else:
            logger.warning(f"{MODULE_NAME_FOR_LOG} (Dashboard): Subscription model not loaded. active_subscriptions is N/A.")
        context['active_subscriptions'] = active_sub_count
        
        context['django_version'] = django.get_version()
        
        logger.info(f"{MODULE_NAME_FOR_LOG} (Dashboard): Context preparation complete.")
        return context
    
    

# --- Mixin for Platform Admin Views (for CBVs) ---
class PlatformAdminRequiredMixin(LoginRequiredMixin, UserPassesTestMixin):
    """Ensures user is logged in and is a superuser/staff for platform management."""
    login_url = reverse_lazy('admin:login') # Redirect to Django admin login by default
    permission_denied_message = _("You do not have permission to access this platform area.")

    def test_func(self):
        # Platform admins should be superusers. Staff status is also usually required for admin access.
        return self.request.user.is_active and self.request.user.is_superuser and self.request.user.is_staff

    def handle_no_permission(self):
        messages.error(self.request, self.permission_denied_message)
        if self.request.user.is_authenticated:
            # If authenticated but not authorized, redirect to a safe place (e.g., public home)
            # Or, if you have a specific platform admin dashboard they *can* see:
            # return redirect(reverse_lazy('platform_management:dashboard')) 
            return redirect(getattr(settings, 'LOGIN_REDIRECT_URL', '/')) 
        return super().handle_no_permission() # Redirects to login_url

# --- Helper for FBVs ---
def is_platform_admin(user):
    return user.is_authenticated and user.is_active and user.is_superuser and user.is_staff

# --- Platform Dashboard ---
@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def platform_dashboard_view(request):
    maintenance_obj = MaintenanceMode.get_status() # Use classmethod
    active_platform_announcements_qs = Announcement.objects.filter(
        is_published=True, publish_date__lte=timezone.now()
    ).exclude(expiry_date__isnull=False, expiry_date__lt=timezone.now())

    active_system_notifications_qs = SystemNotification.objects.filter(
        is_active=True, publish_date__lte=timezone.now()
    ).exclude(expires_at__isnull=False, expires_at__lt=timezone.now())


    context = {
        'view_title': _("Platform Management Dashboard"),
        # 'total_settings': PlatformSetting.objects.count(),
        'active_platform_announcements_count': active_platform_announcements_qs.count(),
        'recent_platform_announcements': active_platform_announcements_qs.order_by('-publish_date')[:3],
        'active_system_notifications_count': active_system_notifications_qs.count(),
        'recent_system_notifications': active_system_notifications_qs.order_by('-publish_date')[:3],
        'recent_audit_logs': AuditLog.objects.select_related('user').order_by('-timestamp')[:10],
        'maintenance_mode_status': maintenance_obj,
    }
    logger.info(f"Platform dashboard accessed by {request.user.email}")
    return render(request, 'platform_management/dashboard.html', context)

# --- PlatformAnnouncement CRUD (CBVs) ---
class PlatformAnnouncementListView(PlatformAdminRequiredMixin, ListView):
    model = Announcement
    template_name = 'platform_management/platformannouncement_list.html'
    context_object_name = 'announcements'
    paginate_by = 15
    
    def get_queryset(self):
        # Example: return only published announcements
        return Announcement.objects.filter(is_published=True).order_by('-publish_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Platform Announcements")
        return context

class PlatformAnnouncementCreateView(PlatformAdminRequiredMixin, SuccessMessageMixin, CreateView):
    model = Announcement
    form_class = PlatformAnnouncementForm
    template_name = 'platform_management/platformannouncement_form.html' # Re-use form template
    success_url = reverse_lazy('platform_management:platformannouncement_list')
    success_message = _("Platform announcement '%(title)s' created successfully.")

    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, self.get_success_message(form.cleaned_data)) # Call explicitly for CreateView
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Create Platform Announcement")
        context['form_mode'] = "create"
        return context

class PlatformAnnouncementUpdateView(PlatformAdminRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Announcement
    form_class = PlatformAnnouncementForm
    template_name = 'platform_management/platformannouncement_form.html' # Re-use form template
    success_url = reverse_lazy('platform_management:platformannouncement_list')
    success_message = _("Platform announcement '%(title)s' updated successfully.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Edit: %(title)s") % {'title': self.object.title}
        context['form_mode'] = "update"
        return context

class PlatformAnnouncementDeleteView(PlatformAdminRequiredMixin, DeleteView):
    model = Announcement
    template_name = 'platform_management/platformannouncement_confirm_delete.html' # Re-use confirm_delete
    success_url = reverse_lazy('platform_management:platformannouncement_list')
    
    def form_valid(self, form):
        title = self.object.title
        response = super().form_valid(form)
        messages.success(self.request, _("Platform announcement '%(title)s' deleted successfully.") % {'title': title})
        return response
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Confirm Delete: %(title)s") % {'title': self.object.title}
        return context



# --- PlatformSetting Views (Renamed your FBVs for consistency, added Create) ---
@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def platform_setting_list_view(request):
    
    print(f"PlatformSetting type: {type(PlatformSetting)}")
    print(f"PlatformSetting value: {PlatformSetting}")
    
    if PlatformSetting is None:
        print("PlatformSetting is None - check imports!")
        return HttpResponse("Import error - check logs")
    
    search_query = request.GET.get('q', '') # Changed from 'search' to 'q' for convention
    settings_obj_list = PlatformSetting.objects.all().order_by('setting_name')
    if search_query:
        settings_obj_list = settings_obj_list.filter(
            Q(setting_name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(setting_value__icontains=search_query)
        )
    paginator = Paginator(settings_obj_list, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    context = {
        'view_title': _("Platform Settings"),
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'platform_management/platformsetting_list.html', context)


@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def platform_setting_create_view(request):
    if request.method == 'POST':
        form = PlatformSettingForm(request.POST)
        if form.is_valid():
            setting = form.save()
            AuditLog.objects.create(user=request.user, action_type='PLATFORM_SETTING_CHANGE', model_name='PlatformSetting', object_pk=str(setting.pk), description=f"Created setting: {setting.setting_name}")
            messages.success(request, _('Setting "%(name)s" created successfully!') % {'name': setting.setting_name})
            return redirect('platform_management:platformsetting_list')
    else:
        form = PlatformSettingForm()
    context = {
        'view_title': _("Create Platform Setting"),
        'form': form,
        'form_mode': 'create',
    }
    return render(request, 'platform_management/platformsetting_form.html', context)


@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def platform_setting_update_view(request, pk): # Changed from setting_detail to reflect action
    setting_obj = get_object_or_404(PlatformSetting, pk=pk)
    if request.method == 'POST':
        form = PlatformSettingForm(request.POST, instance=setting_obj)
        if form.is_valid():
            form.save()
            AuditLog.objects.create(user=request.user, action_type='PLATFORM_SETTING_CHANGE', model_name='PlatformSetting', object_pk=str(setting_obj.pk), description=f"Updated setting: {setting_obj.setting_name}")
            messages.success(request, _('Setting "%(name)s" updated successfully!') % {'name': setting_obj.setting_name})
            return redirect('platform_management:platformsetting_list')
    else:
        form = PlatformSettingForm(instance=setting_obj)
    context = {
        'view_title': _("Edit Setting: %(name)s") % {'name': setting_obj.setting_name},
        'form': form,
        'setting_obj': setting_obj, # Pass object for display if needed
        'form_mode': 'update',
    }
    return render(request, 'platform_management/platformsetting_form.html', context)


class PlatformSettingDeleteView(PlatformAdminRequiredMixin, DeleteView): # SuccessMessageMixin doesn't work directly with DeleteView
    model = PlatformSetting
    template_name = 'platform_management/platformsetting_confirm_delete.html' # New template needed
    success_url = reverse_lazy('platform_management:platformsetting_list')
    context_object_name = 'setting' # To refer to it as 'setting' in the template

    def form_valid(self, form):
        setting_name = self.object.setting_name # Get name before deletion for the message
        setting_pk = self.object.pk

        # Log audit BEFORE deleting
        AuditLog.objects.create(
            user=self.request.user, 
            action_type='DELETE', 
            object_pk=str(setting_pk), 
            model_name='PlatformSetting', 
            description=f"Attempting to delete platform setting: {setting_name} (PK: {setting_pk})"
        )
        
        response = super().form_valid(form) # This performs the deletion
        
        messages.success(self.request, _("Platform setting '%(name)s' deleted successfully.") % {'name': setting_name})
        logger.info(f"PlatformSetting '{setting_name}' (PK: {setting_pk}) deleted by {self.request.user.username}")
        return response
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Confirm Delete Setting: ") + self.object.setting_name
        return context



# --- SystemNotification Views (Your FBVs are fine, just renamed create_notification for consistency) ---
@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def system_notification_list_view(request): # Renamed
    notifications = SystemNotification.objects.select_related('created_by').order_by('-publish_date', '-created_at')
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    context = {
        'view_title': _("System Notifications"),
        'page_obj': page_obj,
    }
    return render(request, 'platform_management/systemnotification_list.html', context)

@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def system_notification_create_view(request): # Renamed
    if request.method == 'POST':
        form = SystemNotificationForm(request.POST)
        if form.is_valid():
            notification = form.save(commit=False)
            notification.created_by = request.user
            notification.save()
            form.save_m2m() 
            AuditLog.objects.create(user=request.user, action_type='CREATE', model_name='SystemNotification', object_pk=str(notification.pk), description=f"Created system notification: {notification.title}")
            messages.success(request, _('Notification "%(title)s" created successfully!') % {'title': notification.title})
            return redirect('platform_management:systemnotification_list')
    else:
        form = SystemNotificationForm()
    context = {
        'view_title': _("Create System Notification"),
        'form': form,
        'form_mode': 'create',
    }
    return render(request, 'platform_management/systemnotification_form.html', context)

class SystemNotificationUpdateView(PlatformAdminRequiredMixin, SuccessMessageMixin, UpdateView):
    model = SystemNotification
    form_class = SystemNotificationForm
    template_name = 'platform_management/systemnotification_form.html' # Can reuse the create form template
    success_url = reverse_lazy('platform_management:systemnotification_list')
    success_message = _("System notification '%(title)s' updated successfully.")
    context_object_name = 'notification' # To match what template might expect for object

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['request'] = self.request # If your form needs the request
        return kwargs

    def form_valid(self, form):
        # created_by should not change on update, it's set on creation.
        # If you need to track who updated it, add an 'updated_by' field to the model.
        logger.info(f"SystemNotification '{form.instance.title}' (PK: {form.instance.pk}) updated by {self.request.user.username}")
        # Log audit
        AuditLog.objects.create(
            user=self.request.user, 
            action_type='UPDATE', 
            object_pk=str(self.object.pk), 
            model_name='SystemNotification', 
            description=f"Updated system notification: {self.object.title}"
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Edit System Notification: ") + self.object.title
        context['form_mode'] = "update" # For the generic_form.html template
        return context

class SystemNotificationDeleteView(PlatformAdminRequiredMixin, DeleteView):
    model = SystemNotification
    template_name = 'platform_management/systemnotification_confirm_delete.html' # Needs this template
    success_url = reverse_lazy('platform_management:systemnotification_list')
    context_object_name = 'notification'

    def form_valid(self, form):
        title = self.object.title # Get title before deletion for the message
        pk = self.object.pk
        response = super().form_valid(form)
        messages.success(self.request, _("System notification '%(title)s' deleted successfully.") % {'title': title})
        logger.info(f"SystemNotification '{title}' (PK: {pk}) deleted by {self.request.user.username}")
        # Log audit
        AuditLog.objects.create(
            user=self.request.user, 
            action_type='DELETE', 
            object_pk=str(pk), # Use stored pk as self.object is gone
            model_name='SystemNotification', 
            description=f"Deleted system notification: {title}"
        )
        return response
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Confirm Delete Notification: ") + self.object.title
        return context




# --- AuditLog View (Your FBV is good) ---
@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def audit_log_list_view(request): # Renamed
    logs = AuditLog.objects.select_related('user').order_by('-timestamp')
    action_type_filter = request.GET.get('action_type', '')
    user_id_filter = request.GET.get('user_id', '')
    search_query = request.GET.get('q', '')

    if action_type_filter:
        logs = logs.filter(action_type=action_type_filter)
    if user_id_filter:
        try:
            logs = logs.filter(user_id=int(user_id_filter))
        except ValueError:
            pass # Ignore invalid user_id
    if search_query:
        logs = logs.filter(
            Q(description__icontains=search_query) |
            Q(user__username__icontains=search_query) | # Adjust for your AUTH_USER_MODEL
            Q(user__email__icontains=search_query) |
            Q(ip_address__icontains=search_query)
        )
    
    paginator = Paginator(logs, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    from django.contrib.auth import get_user_model
    User = get_user_model()

    context = {
        'view_title': _("Platform Audit Logs"),
        'page_obj': page_obj,
        'action_types': AuditLog.ACTION_TYPES, # For filter dropdown
        'platform_users': User.objects.filter(is_staff=True).order_by('email'),# 'platform_users': User.objects.filter(is_staff=True).order_by('username'), # For filter dropdown
        'selected_action': action_type_filter,
        'selected_user_id': user_id_filter,
        'search_query': search_query,
    }
    return render(request, 'platform_management/auditlog_list.html', context)

# --- MaintenanceMode View (Your FBV is good) ---
@user_passes_test(is_platform_admin, login_url=reverse_lazy('admin:login'))
def maintenance_mode_update_view(request): # Renamed
    maintenance_obj = MaintenanceMode.get_status() # Use classmethod
    
    if request.method == 'POST':
        form = MaintenanceModeForm(request.POST, instance=maintenance_obj)
        if form.is_valid():
            updated_maintenance = form.save(commit=False)
            
            # Log who enabled/disabled it, only if state changed
            if updated_maintenance.is_enabled != maintenance_obj.is_enabled or created: # 'created' doesn't apply here
                if updated_maintenance.is_enabled:
                    updated_maintenance.enabled_by = request.user
                    # enabled_at is set by model's save method
                else:
                    # Optionally log who disabled it, or clear enabled_by
                    updated_maintenance.enabled_by = request.user # Still log who last changed it
                    # enabled_at is cleared by model's save method
            elif form.changed_data: # If other fields like message changed
                updated_maintenance.enabled_by = request.user # Log who made the change

            updated_maintenance.save()
            
            status_log = "ENABLED" if updated_maintenance.is_enabled else "DISABLED"
            AuditLog.objects.create(
                user=request.user,
                action_type='MAINTENANCE_MODE_TOGGLE', # Use specific type
                model_name='MaintenanceMode',
                object_pk=str(updated_maintenance.pk),
                description=f"Maintenance mode set to {status_log}. Message: {updated_maintenance.message[:50]}",
            )
            
            messages.success(request, _(f'Maintenance mode settings updated successfully. Mode is now {status_log}.'))
            return redirect('platform_management:maintenance_mode_update')
    else:
        form = MaintenanceModeForm(instance=maintenance_obj)
    
    context = {
        'view_title': _("Manage Platform Maintenance Mode"),
        'form': form,
        'maintenance_status': maintenance_obj,
    }
    return render(request, 'platform_management/maintenance_mode_form.html', context)






# apps/platform_management/views.py
from django.shortcuts import redirect, reverse
from django.contrib.admin.views.decorators import staff_member_required # or superuser_required
from django.contrib import messages
from .models import MaintenanceMode # Assuming you have this model

@staff_member_required # Or your PlatformAdminRequiredMixin
def maintenance_mode_toggle_view(request):
    if not MaintenanceMode: # Check if model imported
        messages.error(request, "Maintenance Mode model not available.")
        return redirect('platform_management:dashboard') # Or some other safe redirect

    mode, created = MaintenanceMode.objects.get_or_create(pk=1) # Assuming singleton
    if request.method == 'POST':
        mode.is_active = not mode.is_active
        mode.save()
        messages.success(request, f"Maintenance mode {'activated' if mode.is_active else 'deactivated'}.")
        return redirect('platform_management:dashboard') # Redirect back to dashboard
    
    # Should not be reached via GET if it's a toggle action via POST button
    # Or render a page with a button if it's a dedicated page
    return redirect('platform_management:dashboard')

from django.core.management.base import BaseCommand, CommandError
from django.db import connection, transaction
from django_tenants.utils import get_tenant_model
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix migration dependency issue between fees and payments apps'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-schema',
            type=str,
            default='alpha',
            help='Tenant schema name to fix (default: alpha)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        tenant_schema = options['tenant_schema']
        dry_run = options['dry_run']

        # Define all dependencies for payments.0001_initial and their chain dependencies
        required_dependencies = [
            ('accounting', '0005_seed_account_types'),
            ('fees', '0001_initial'),
            ('schools', '0002_initial'),
            ('students', '0002_initial'),  # Required for students.0003_student_created_by
            ('students', '0003_student_created_by'),
        ]

        try:
            # Get the tenant
            TenantModel = get_tenant_model()
            tenant = TenantModel.objects.get(schema_name=tenant_schema)

            # Switch to tenant schema
            connection.set_tenant(tenant)

            self.stdout.write(f"Working on tenant schema: {tenant_schema}")

            # Check current migration state
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT app, name, applied
                    FROM django_migrations
                    ORDER BY applied;
                """)
                results = cursor.fetchall()
                applied_migrations = {(row[0], row[1]) for row in results}

                self.stdout.write("Current migration state for relevant apps:")
                for row in results:
                    if row[0] in ['fees', 'payments', 'students', 'schools', 'accounting']:
                        self.stdout.write(f"  {row[0]}.{row[1]} - {row[2]}")

                # Check if payments.0001_initial is applied
                payments_applied = ('payments', '0001_initial') in applied_migrations

                if not payments_applied:
                    self.stdout.write(self.style.SUCCESS("No payments.0001_initial migration found - nothing to fix"))
                    return

                # Check which dependencies are missing
                missing_dependencies = []
                for app, migration in required_dependencies:
                    if (app, migration) not in applied_migrations:
                        missing_dependencies.append((app, migration))

                if not missing_dependencies:
                    self.stdout.write(self.style.SUCCESS("All dependencies are applied - no issues found"))
                    return

                self.stdout.write(self.style.WARNING(f"Found missing dependencies for payments.0001_initial:"))
                for app, migration in missing_dependencies:
                    self.stdout.write(f"  - {app}.{migration}")

                if dry_run:
                    self.stdout.write(self.style.WARNING("DRY RUN: Would fix by marking missing dependencies as applied"))
                    return

                # Fix by marking missing dependencies as applied
                with transaction.atomic():
                    for app, migration in missing_dependencies:
                        cursor.execute("""
                            INSERT INTO django_migrations (app, name, applied)
                            VALUES (%s, %s, NOW())
                        """, [app, migration])

                        self.stdout.write(self.style.SUCCESS(f"Fixed: Marked {app}.{migration} as applied"))

                    # Verify the fix
                    cursor.execute("""
                        SELECT app, name, applied
                        FROM django_migrations
                        WHERE app IN ('fees', 'payments', 'students', 'schools', 'accounting')
                        ORDER BY applied;
                    """)
                    results = cursor.fetchall()

                    self.stdout.write("Updated migration state:")
                    for row in results:
                        self.stdout.write(f"  {row[0]}.{row[1]} - {row[2]}")

        except Exception as e:
            raise CommandError(f"Error fixing migration dependency: {e}")

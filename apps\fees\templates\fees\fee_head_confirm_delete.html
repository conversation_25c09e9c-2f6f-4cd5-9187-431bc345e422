{# D:\school_fees_saas_v2\apps\fees\templates\fees\fee_head_confirm_delete.html #}
{% extends "tenant_base.html" %}
{% load static %}

{% block title %}Confirm Delete Fee Head{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">Confirm Deletion</h4>
                </div>
                <div class="card-body">
                    <p>Are you sure you want to delete the Fee Head: <strong>"{{ object.name }}"</strong>?</p>
                    {% if object.description %}
                        <p><em>Description: {{ object.description }}</em></p>
                    {% endif %}
                    <p class="text-danger">This action cannot be undone.</p>
                    <form method="post">
                        {% csrf_token %}
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'fees:fee_head_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash3-fill me-1"></i> Yes, Delete
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


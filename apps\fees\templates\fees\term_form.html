{# D:\school_fees_saas_v2\templates\fees\term_form.html #}
{% extends "tenant_base.html" %}
{% load core_tags widget_tweaks %} {# Load widget_tweaks if you use render_field #}

{% block title %}{{ view_title|default:"Term/Semester Form" }}{% endblock %}

{% block tenant_specific_content %} {# Or your main content block name #}
<div class="container mt-4">
    <h1>{{ view_title|default:"Term/Semester Form" }}</h1>

    {% include "partials/_messages.html" %} {# If you have a messages partial #}

    <form method="post" novalidate>
        {% csrf_token %}

        {# Using render_field for Bootstrap styling, similar to academic_year_form #}
        <div class="mb-3">
            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
            {% render_field form.name class+="form-control form-control-sm" placeholder="e.g., Term 1, Fall Semester" %}
            {% for error in form.name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>

        <div class="mb-3">
            <label for="{{ form.academic_year.id_for_label }}" class="form-label">{{ form.academic_year.label }}</label>
            {% render_field form.academic_year class+="form-select form-select-sm" %}
            {% for error in form.academic_year.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                {% render_field form.start_date class+="form-control form-control-sm" type="date" %}
                {% for error in form.start_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>
            <div class="col-md-6 mb-3">
                <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                {% render_field form.end_date class+="form-control form-control-sm" type="date" %}
                {% for error in form.end_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
            </div>
        </div>

        <div class="mb-3 form-check">
            {% render_field form.is_active class+="form-check-input" %}
            <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
            {% for error in form.is_active.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>

        <div class="mb-3 form-check">
            {% render_field form.is_current_term class+="form-check-input" %}
            <label for="{{ form.is_current_term.id_for_label }}" class="form-check-label">{{ form.is_current_term.label }}</label>
            {% if form.is_current_term.help_text %}<small class="form-text text-muted d-block">{{ form.is_current_term.help_text }}</small>{% endif %}
            {% for error in form.is_current_term.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
        </div>


        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}<br>
                {% endfor %}
            </div>
        {% endif %}

        <button type="submit" class="btn btn-success">Save Term/Semester</button>
        <a href="{% url 'fees:term_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %}
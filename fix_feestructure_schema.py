"""
Fix FeeStructure schema issue - remove school_class_id column that shouldn't exist
"""

import psycopg
from django.conf import settings
from django_tenants.utils import schema_context

def fix_feestructure_schema():
    """Fix the FeeStructure table schema by removing the school_class_id column"""
    
    print("🔧 FIXING FEESTRUCTURE SCHEMA ISSUE")
    print("=" * 50)
    
    # Connect to database
    db_config = settings.DATABASES['default']
    conn = psycopg.connect(
        host=db_config['HOST'],
        port=db_config['PORT'],
        dbname=db_config['NAME'],
        user=db_config['USER'],
        password=db_config['PASSWORD']
    )
    
    try:
        with conn.cursor() as cur:
            # Get all tenant schemas
            cur.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'public')
                AND schema_name NOT LIKE 'pg_%'
                ORDER BY schema_name
            """)
            schemas = [row[0] for row in cur.fetchall()]
            
            print(f"📋 Found {len(schemas)} tenant schemas: {schemas}")
            
            for schema in schemas:
                print(f"\n🔍 Checking schema: {schema}")
                
                # Check if fees_feestructure table exists
                cur.execute("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name = 'fees_feestructure'
                    )
                """, (schema,))
                
                if not cur.fetchone()[0]:
                    print(f"  ⏭️  No fees_feestructure table found, skipping")
                    continue
                
                # Check current columns in fees_feestructure
                cur.execute("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = 'fees_feestructure'
                    ORDER BY ordinal_position
                """, (schema,))
                
                columns = cur.fetchall()
                print(f"  📊 Current columns:")
                for col_name, data_type, nullable, default in columns:
                    print(f"    - {col_name} ({data_type}) {'NULL' if nullable == 'YES' else 'NOT NULL'}")
                
                # Check if school_class_id column exists
                school_class_id_exists = any(col[0] == 'school_class_id' for col in columns)
                
                if school_class_id_exists:
                    print(f"  ⚠️  Found problematic school_class_id column!")
                    
                    # Check if there are any constraints on this column
                    cur.execute("""
                        SELECT constraint_name, constraint_type 
                        FROM information_schema.table_constraints tc
                        JOIN information_schema.constraint_column_usage ccu 
                        ON tc.constraint_name = ccu.constraint_name
                        WHERE tc.table_schema = %s 
                        AND tc.table_name = 'fees_feestructure'
                        AND ccu.column_name = 'school_class_id'
                    """, (schema,))
                    
                    constraints = cur.fetchall()
                    print(f"    Constraints on school_class_id: {constraints}")
                    
                    # Drop foreign key constraints first
                    for constraint_name, constraint_type in constraints:
                        if constraint_type == 'FOREIGN KEY':
                            try:
                                cur.execute(f'ALTER TABLE "{schema}".fees_feestructure DROP CONSTRAINT "{constraint_name}"')
                                print(f"    ✅ Dropped foreign key constraint: {constraint_name}")
                            except Exception as e:
                                print(f"    ⚠️  Could not drop constraint {constraint_name}: {e}")
                    
                    # Now drop the column
                    try:
                        cur.execute(f'ALTER TABLE "{schema}".fees_feestructure DROP COLUMN school_class_id')
                        print(f"    ✅ Dropped school_class_id column")
                        
                        # Commit the changes for this schema
                        conn.commit()
                        
                    except Exception as e:
                        print(f"    ❌ Could not drop school_class_id column: {e}")
                        conn.rollback()
                
                else:
                    print(f"  ✅ No school_class_id column found - schema is correct")
                
                # Verify the applicable_classes many-to-many table exists
                cur.execute("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name = 'fees_feestructure_applicable_classes'
                    )
                """, (schema,))
                
                if cur.fetchone()[0]:
                    print(f"  ✅ fees_feestructure_applicable_classes table exists (correct)")
                else:
                    print(f"  ⚠️  fees_feestructure_applicable_classes table missing")
            
            print(f"\n🎉 Schema fix completed!")
            print(f"💡 The school_class_id column has been removed from all schemas.")
            print(f"📋 FeeStructure now uses the applicable_classes ManyToManyField as intended.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    fix_feestructure_schema()

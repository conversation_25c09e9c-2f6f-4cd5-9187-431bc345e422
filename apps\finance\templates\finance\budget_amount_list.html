{# D:\school_fees_saas_V2\apps\finance\templates\finance\budget_amount_list.html #}
{% extends "tenant_base.html" %}

{% load core_tags humanize %}

{% block title %}{{ view_title|default:"Manage Budget Amounts" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title }}</h1>
        {% if perms.finance.add_budgetamount %}
        <a href="{% url 'finance:budget_amount_create' %}" class="btn btn-primary"><i class="bi bi-plus-circle me-1"></i> Set New Budget Amount</a>
        {% endif %}
    </div>
    {% include "partials/_messages.html" %}
    {% if budget_amounts %}
        <div class="card shadow-sm"><div class="card-body p-0"><div class="table-responsive">
        <table class="table table-striped table-hover table-sm mb-0">
            <thead class="table-light"><tr><th>Budget Item</th><th>Academic Year</th><th>Term</th><th class="text-end">Budgeted Amount</th><th>Notes</th><th>Actions</th></tr></thead>
            <tbody>
            {% for ba in budget_amounts %}
                <tr>
                    <td>{{ ba.budget_item.name }}</td>
                    <td>{{ ba.academic_year.name }}</td>
                    <td>{{ ba.term.name|default:"Annual" }}</td>
                    <td class="text-end">{{ ba.budgeted_amount|intcomma }}</td>
                    <td>{{ ba.notes|truncatewords:10|default:"-" }}</td>
                    <td class="actions-column">
                        {% if perms.finance.change_budgetamount %}
                        <a href="{% url 'finance:budget_amount_update' ba.pk %}" class="btn btn-sm btn-outline-primary me-1" title="Edit"><i class="bi bi-pencil-square"></i></a>
                        {% endif %}
                        {% if perms.finance.delete_budgetamount %}
                        <form method="post" action="{% url 'finance:budget_amount_delete' ba.pk %}" class="d-inline" onsubmit="return confirm('Delete this budget amount for \'{{ ba.budget_item.name|escapejs }}\'?');">
                            {% csrf_token %}<button type="submit" class="btn btn-sm btn-outline-danger" title="Delete"><i class="bi bi-trash3"></i></button>
                        </form>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        </div></div></div>
    {% else %}
        <div class="alert alert-info">No budget amounts have been set yet.</div>
    {% endif %}
    {% include "partials/_pagination.html" %}
    <div class="footer-actions mt-3"><a href="{% url 'schools:dashboard' %}" class="btn btn-secondary"><i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard</a></div>
</div>
{% endblock %}


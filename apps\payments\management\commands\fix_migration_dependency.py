from django.core.management.base import BaseCommand, CommandError
from django.db import connection, transaction
from django_tenants.utils import get_tenant_model, get_public_schema_name
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix migration dependency issue between payments.0003_initial and fees.0003_initial'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-schema',
            type=str,
            help='Specific tenant schema to fix (optional, will fix all tenants if not specified)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )

    def handle(self, *args, **options):
        tenant_schema = options.get('tenant_schema')
        dry_run = options['dry_run']

        try:
            TenantModel = get_tenant_model()
            
            if tenant_schema:
                # Fix specific tenant
                try:
                    tenant = TenantModel.objects.get(schema_name=tenant_schema)
                    self.fix_tenant_migrations(tenant, dry_run)
                except TenantModel.DoesNotExist:
                    raise CommandError(f"Tenant with schema '{tenant_schema}' does not exist")
            else:
                # Fix all tenants
                tenants = TenantModel.objects.exclude(schema_name=get_public_schema_name())
                for tenant in tenants:
                    self.stdout.write(f"\n--- Processing tenant: {tenant.schema_name} ---")
                    self.fix_tenant_migrations(tenant, dry_run)

        except Exception as e:
            raise CommandError(f"Error fixing migration dependency: {e}")

    def fix_tenant_migrations(self, tenant, dry_run):
        """Fix migration dependencies for a specific tenant"""
        # Switch to tenant schema
        connection.set_tenant(tenant)
        
        self.stdout.write(f"Working on tenant schema: {tenant.schema_name}")

        with connection.cursor() as cursor:
            # Check current migration state
            cursor.execute("""
                SELECT app, name, applied
                FROM django_migrations
                WHERE app IN ('fees', 'payments')
                ORDER BY applied;
            """)
            results = cursor.fetchall()
            applied_migrations = {(row[0], row[1]): row[2] for row in results}

            self.stdout.write("Current migration state for fees and payments:")
            for row in results:
                self.stdout.write(f"  {row[0]}.{row[1]} - {row[2]}")

            # Check if we have the problematic situation
            payments_0003_applied = ('payments', '0003_initial') in applied_migrations
            fees_0003_applied = ('fees', '0003_initial') in applied_migrations

            if not payments_0003_applied:
                self.stdout.write(self.style.SUCCESS("payments.0003_initial not found - no issue"))
                return

            if not fees_0003_applied:
                self.stdout.write(self.style.ERROR("fees.0003_initial not applied but payments.0003_initial is - this is the problem"))
                if dry_run:
                    self.stdout.write(self.style.WARNING("DRY RUN: Would mark fees.0003_initial as applied"))
                    return
                
                # Fix by marking fees.0003_initial as applied with an earlier timestamp
                payments_timestamp = applied_migrations[('payments', '0003_initial')]
                
                with transaction.atomic():
                    # Insert fees.0003_initial with a timestamp just before payments.0003_initial
                    cursor.execute("""
                        INSERT INTO django_migrations (app, name, applied)
                        VALUES ('fees', '0003_initial', %s - INTERVAL '1 second')
                        ON CONFLICT (app, name) DO NOTHING
                    """, [payments_timestamp])
                    
                    self.stdout.write(self.style.SUCCESS("Fixed: Marked fees.0003_initial as applied before payments.0003_initial"))
                return

            # Check if the timestamps are in the wrong order
            payments_timestamp = applied_migrations[('payments', '0003_initial')]
            fees_timestamp = applied_migrations[('fees', '0003_initial')]

            if payments_timestamp < fees_timestamp:
                self.stdout.write(self.style.ERROR(f"Migration order issue: payments.0003_initial ({payments_timestamp}) applied before fees.0003_initial ({fees_timestamp})"))
                
                if dry_run:
                    self.stdout.write(self.style.WARNING("DRY RUN: Would update fees.0003_initial timestamp to be earlier"))
                    return

                # Fix by updating the timestamp of fees.0003_initial to be earlier
                with transaction.atomic():
                    cursor.execute("""
                        UPDATE django_migrations 
                        SET applied = %s - INTERVAL '1 second'
                        WHERE app = 'fees' AND name = '0003_initial'
                    """, [payments_timestamp])
                    
                    self.stdout.write(self.style.SUCCESS("Fixed: Updated fees.0003_initial timestamp to be earlier than payments.0003_initial"))
            else:
                self.stdout.write(self.style.SUCCESS("Migration timestamps are in correct order - no issue found"))

            # Verify the fix
            cursor.execute("""
                SELECT app, name, applied
                FROM django_migrations
                WHERE app IN ('fees', 'payments') AND name LIKE '%0003_initial'
                ORDER BY applied;
            """)
            results = cursor.fetchall()

            self.stdout.write("Updated migration state:")
            for row in results:
                self.stdout.write(f"  {row[0]}.{row[1]} - {row[2]}")
